package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentManager;
import cn.acebrand.acekeysystem.punishment.PunishmentRecord;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 处罚记录Web处理器
 * 处理所有类型的处罚记录请求
 */
public class PunishmentHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final PunishmentManager punishmentManager;
    private final PunishmentPageGenerator pageGenerator;

    public PunishmentHandler(AceKeySystem plugin, WebServer webServer, PunishmentManager punishmentManager) {
        this.plugin = plugin;
        this.webServer = webServer;
        this.punishmentManager = punishmentManager;
        this.pageGenerator = new PunishmentPageGenerator(plugin);
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        try {
            String method = exchange.getRequestMethod();
            URI uri = exchange.getRequestURI();
            String path = uri.getPath();

            if ("GET".equals(method)) {
                handleGetRequest(exchange, path, uri.getQuery());
            } else {
                sendErrorResponse(exchange, 405, "方法不被允许");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理处罚记录请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "服务器内部错误");
        }
    }

    /**
     * 处理GET请求
     */
    private void handleGetRequest(HttpExchange exchange, String path, String query) throws IOException {
        // 解析查询参数
        Map<String, String> params = parseQuery(query);

        // 解析路径
        String[] pathParts = path.split("/");

        if (pathParts.length == 2 && "punishments".equals(pathParts[1])) {
            // /punishments - 所有处罚记录
            handleAllPunishments(exchange, params);
        } else if (pathParts.length == 3 && "punishments".equals(pathParts[1]) && "check-update".equals(pathParts[2])) {
            // /punishments/check-update - 检测数据更新
            handleCheckUpdate(exchange, params);
        } else if (pathParts.length == 2 && "bans".equals(pathParts[1])) {
            // /bans - 封禁记录（向后兼容，显示封禁类型）
            handleSpecificPunishment(exchange, "ban", params);
        } else if (pathParts.length == 3 && "punishments".equals(pathParts[1])) {
            // /punishments/{type} - 特定类型的处罚记录
            String typeStr = pathParts[2];
            handleSpecificPunishment(exchange, typeStr, params);
        } else if (pathParts.length == 4 && "punishments".equals(pathParts[1]) && "check-update".equals(pathParts[3])) {
            // /punishments/{type}/check-update - 检测特定类型数据更新
            String typeStr = pathParts[2];
            handleCheckTypeUpdate(exchange, typeStr, params);
        } else if (pathParts.length == 4 && "punishments".equals(pathParts[1]) && isNumeric(pathParts[3])) {
            // /punishments/{type}/{id} - 处罚详细信息页面
            String typeStr = pathParts[2];
            String idStr = pathParts[3];
            handlePunishmentDetail(exchange, typeStr, idStr);
        } else if (pathParts.length == 3 && "player".equals(pathParts[1])) {
            // /player/{playerName} - 玩家专用页面（按照 next-litebans /@{player} 逻辑）
            String playerName = pathParts[2];
            handlePlayerPage(exchange, playerName, params);
        } else {
            sendErrorResponse(exchange, 404, "页面未找到");
        }
    }

    /**
     * 处理所有处罚记录请求
     */
    private void handleAllPunishments(HttpExchange exchange, Map<String, String> params) throws IOException {
        try {
            // 获取分页参数
            int page = parseInt(params.get("page"), 1);
            int pageSize = plugin.getConfig().getInt("litebans.display.records-per-page", 10);

            // 检查是否有玩家参数（类似 next-litebans 的 ?player=PlayerName）
            String playerParam = params.get("player");
            String staffParam = params.get("staff");
            String searchQuery = params.get("search");
            // 过滤不等于玩家视图，过滤只是在当前页面添加过滤条件
            boolean isPlayerView = false;
            boolean isStaffFilter = staffParam != null && !staffParam.isEmpty();

            // 按照 next-litebans 逻辑，不再将过滤参数转换为搜索查询
            // 而是直接使用过滤参数进行数据库查询

            List<PunishmentRecord> records;
            int totalPages = 1;
            Map<String, Object> statistics;

            plugin.getLogger().info("=== 处理所有处罚记录请求 ===");
            plugin.getLogger().info("页码: " + page + ", 每页: " + pageSize);
            plugin.getLogger().info("搜索查询: " + searchQuery);
            plugin.getLogger().info("玩家参数: " + playerParam);
            plugin.getLogger().info("是否玩家视图: " + isPlayerView);
            plugin.getLogger().info("处罚管理器状态: " + (punishmentManager != null ? "存在" : "null"));
            plugin.getLogger()
                    .info("处罚管理器启用状态: " + (punishmentManager != null ? punishmentManager.isEnabled() : "N/A"));

            // 检查处罚管理器是否可用
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                plugin.getLogger().info("开始获取处罚记录...");

                // 按照 next-litebans 逻辑处理过滤
                if ((playerParam != null && !playerParam.trim().isEmpty())
                        || (staffParam != null && !staffParam.trim().isEmpty())) {
                    plugin.getLogger().info("执行过滤查询 - 玩家: " + playerParam + ", 执行者: " + staffParam);
                    // 获取所有类型的过滤记录
                    List<PunishmentRecord> allFilteredRecords = new java.util.ArrayList<>();
                    for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                        List<PunishmentRecord> typeRecords = punishmentManager.searchPunishmentRecords(type,
                                playerParam, staffParam, 1, pageSize * 10);
                        allFilteredRecords.addAll(typeRecords);
                    }

                    // 按时间排序
                    allFilteredRecords.sort((a, b) -> {
                        if (a.getTime() == null && b.getTime() == null)
                            return 0;
                        if (a.getTime() == null)
                            return 1;
                        if (b.getTime() == null)
                            return -1;
                        return b.getTime().compareTo(a.getTime());
                    });

                    // 分页
                    int start = (page - 1) * pageSize;
                    int end = Math.min(start + pageSize, allFilteredRecords.size());
                    records = (start < allFilteredRecords.size()) ? allFilteredRecords.subList(start, end)
                            : new java.util.ArrayList<>();

                    // 计算总页数
                    totalPages = (int) Math.ceil((double) allFilteredRecords.size() / pageSize);
                    plugin.getLogger().info("过滤结果总数: " + allFilteredRecords.size() + ", 总页数: " + totalPages);
                } else if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                    plugin.getLogger().info("执行搜索查询: " + searchQuery);
                    records = punishmentManager.searchPunishmentRecords(searchQuery, page, pageSize);

                    // 搜索模式下，简化处理总数
                    int totalRecords = records.size() + (page - 1) * pageSize;
                    if (records.size() < pageSize) {
                        totalRecords = (page - 1) * pageSize + records.size();
                    } else {
                        totalRecords = page * pageSize + 1; // 假设还有更多记录
                    }
                    totalPages = (int) Math.ceil((double) totalRecords / pageSize);
                    plugin.getLogger().info("搜索结果总数: " + totalRecords + ", 总页数: " + totalPages);
                } else {
                    plugin.getLogger().info("获取所有记录");
                    records = punishmentManager.getAllPunishmentRecords(page, pageSize);

                    // 正常模式下，计算所有类型的总数
                    int totalRecords = 0;
                    for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                        int typeCount = punishmentManager.getPunishmentCount(type);
                        totalRecords += typeCount;
                        plugin.getLogger().info(type.getDisplayName() + " 记录数: " + typeCount);
                    }
                    totalPages = (int) Math.ceil((double) totalRecords / pageSize);
                    plugin.getLogger().info("总记录数: " + totalRecords + ", 总页数: " + totalPages);
                }

                plugin.getLogger().info("获取到 " + records.size() + " 条记录");

                // 获取统计信息
                statistics = punishmentManager.getPunishmentStatistics();
                plugin.getLogger().info("统计信息: " + statistics.size() + " 项");
            } else {
                // 数据库未连接，显示空记录
                records = new java.util.ArrayList<>();
                statistics = new java.util.HashMap<>();
                plugin.getLogger().warning("处罚管理器未启用，显示空记录页面");
                plugin.getLogger().warning("punishmentManager == null: " + (punishmentManager == null));
                if (punishmentManager != null) {
                    plugin.getLogger().warning("punishmentManager.isEnabled(): " + punishmentManager.isEnabled());
                }
            }

            // 生成页面HTML（支持过滤器）
            String html = pageGenerator.generatePunishmentPage(null, records, page, totalPages, statistics,
                    searchQuery, isPlayerView, playerParam, staffParam);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取所有处罚记录时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取处罚记录失败");
        }
    }

    /**
     * 处理特定类型的处罚记录请求
     */
    private void handleSpecificPunishment(HttpExchange exchange, String typeStr, Map<String, String> params)
            throws IOException {
        try {
            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendErrorResponse(exchange, 404, "未知的处罚类型");
                return;
            }

            // 获取分页参数
            int page = parseInt(params.get("page"), 1);
            int pageSize = plugin.getConfig().getInt("litebans.display.records-per-page", 10);

            // 检查是否有玩家参数（类似 next-litebans 的 ?player=PlayerName）
            String playerParam = params.get("player");
            String staffParam = params.get("staff");
            String searchQuery = params.get("search");
            // 过滤不等于玩家视图，过滤只是在当前页面添加过滤条件
            boolean isPlayerView = false;
            boolean isStaffFilter = staffParam != null && !staffParam.isEmpty();

            // 按照 next-litebans 逻辑，不再将过滤参数转换为搜索查询
            // 而是直接使用过滤参数进行数据库查询

            List<PunishmentRecord> records;
            int totalPages = 1;
            Map<String, Object> statistics;

            plugin.getLogger().info("=== 处理处罚记录请求 ===");
            plugin.getLogger().info("请求类型: " + type.getDisplayName());
            plugin.getLogger().info("页码: " + page + ", 每页: " + pageSize);
            plugin.getLogger().info("搜索查询: " + searchQuery);
            plugin.getLogger().info("玩家参数: " + playerParam);
            plugin.getLogger().info("是否玩家视图: " + isPlayerView);
            plugin.getLogger().info("处罚管理器状态: " + (punishmentManager != null ? "存在" : "null"));
            plugin.getLogger()
                    .info("处罚管理器启用状态: " + (punishmentManager != null ? punishmentManager.isEnabled() : "N/A"));

            // 检查处罚管理器是否可用
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                plugin.getLogger().info("开始获取处罚记录...");

                // 按照 next-litebans 逻辑处理过滤
                if ((playerParam != null && !playerParam.trim().isEmpty())
                        || (staffParam != null && !staffParam.trim().isEmpty())) {
                    plugin.getLogger().info("执行过滤查询 - 玩家: " + playerParam + ", 执行者: " + staffParam);
                    records = punishmentManager.searchPunishmentRecords(type, playerParam, staffParam, page, pageSize);
                } else if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                    plugin.getLogger().info("执行搜索查询: " + searchQuery);
                    records = punishmentManager.searchPunishmentRecords(type, searchQuery, page, pageSize);
                } else {
                    plugin.getLogger().info("获取所有记录");
                    records = punishmentManager.getPunishmentRecords(type, page, pageSize);
                }

                plugin.getLogger().info("获取到 " + records.size() + " 条记录");

                // 计算总页数
                int totalRecords;
                if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                    // 搜索模式下，简化处理总数
                    totalRecords = records.size() + (page - 1) * pageSize;
                    if (records.size() < pageSize) {
                        totalRecords = (page - 1) * pageSize + records.size();
                    } else {
                        totalRecords = page * pageSize + 1; // 假设还有更多记录
                    }
                } else {
                    totalRecords = punishmentManager.getPunishmentCount(type);
                }
                totalPages = (int) Math.ceil((double) totalRecords / pageSize);

                plugin.getLogger().info("总记录数: " + totalRecords + ", 总页数: " + totalPages);

                // 获取统计信息
                statistics = punishmentManager.getPunishmentStatistics();
                plugin.getLogger().info("统计信息: " + statistics.size() + " 项");
            } else {
                // 数据库未连接，显示空记录
                records = new java.util.ArrayList<>();
                statistics = new java.util.HashMap<>();
                plugin.getLogger().warning("处罚管理器未启用，显示空" + type.getDisplayName() + "记录页面");
                plugin.getLogger().warning("punishmentManager == null: " + (punishmentManager == null));
                if (punishmentManager != null) {
                    plugin.getLogger().warning("punishmentManager.isEnabled(): " + punishmentManager.isEnabled());
                }
            }

            // 生成页面HTML（支持过滤器）
            String html = pageGenerator.generatePunishmentPage(type, records, page, totalPages, statistics,
                    searchQuery, isPlayerView, playerParam, staffParam);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取" + typeStr + "记录时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取处罚记录失败");
        }
    }

    /**
     * 解析处罚类型
     */
    private PunishmentRecord.PunishmentType parseType(String typeStr) {
        if (typeStr == null)
            return null;

        switch (typeStr.toLowerCase()) {
            case "ban":
            case "bans":
                return PunishmentRecord.PunishmentType.BAN;
            case "mute":
            case "mutes":
                return PunishmentRecord.PunishmentType.MUTE;
            case "warn":
            case "warns":
            case "warning":
            case "warnings":
                return PunishmentRecord.PunishmentType.WARN;
            case "kick":
            case "kicks":
                return PunishmentRecord.PunishmentType.KICK;
            default:
                return null;
        }
    }

    /**
     * 解析整数参数
     */
    private int parseInt(String value, int defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 解析查询参数
     */
    private Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    try {
                        String key = java.net.URLDecoder.decode(keyValue[0], "UTF-8");
                        String value = java.net.URLDecoder.decode(keyValue[1], "UTF-8");
                        params.put(key, value);
                    } catch (Exception e) {
                        // 忽略解码错误
                    }
                }
            }
        }
        return params;
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String content)
            throws IOException {
        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");
        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        String html = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>错误</title></head>" +
                "<body style=\"font-family: Arial, sans-serif; text-align: center; padding: 50px;\">" +
                "<h1>" + statusCode + " - " + message + "</h1>" +
                "<a href=\"/\">返回首页</a>" +
                "</body></html>";
        sendResponse(exchange, statusCode, "text/html; charset=utf-8", html);
    }

    /**
     * 检查字符串是否为数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 处理处罚详细信息页面请求（按照 next-litebans 详细页面）
     */
    private void handlePunishmentDetail(HttpExchange exchange, String typeStr, String idStr) throws IOException {
        try {
            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendErrorResponse(exchange, 404, "未知的处罚类型");
                return;
            }

            // 解析ID
            long id;
            try {
                id = Long.parseLong(idStr);
            } catch (NumberFormatException e) {
                sendErrorResponse(exchange, 400, "无效的记录ID");
                return;
            }

            plugin.getLogger().info("=== 处理处罚详细信息请求 ===");
            plugin.getLogger().info("类型: " + type.getDisplayName() + ", ID: " + id);

            // 获取处罚记录详情
            PunishmentRecord record = null;
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                record = punishmentManager.getPunishmentById(type, id);
            }

            if (record == null) {
                sendErrorResponse(exchange, 404, "未找到指定的处罚记录");
                return;
            }

            // 生成详细信息页面HTML（按照 next-litebans 详细页面布局）
            String html = pageGenerator.generatePunishmentDetailPage(record);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取处罚详细信息时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取处罚详细信息失败");
        }
    }

    /**
     * 处理玩家专用页面请求（按照 next-litebans /@{player} 逻辑）
     */
    private void handlePlayerPage(HttpExchange exchange, String playerName, Map<String, String> params)
            throws IOException {
        try {
            // URL解码玩家名称
            playerName = java.net.URLDecoder.decode(playerName, "UTF-8");

            plugin.getLogger().info("=== 处理玩家页面请求 ===");
            plugin.getLogger().info("玩家名称: " + playerName);

            // 检查玩家是否存在
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                // 简单检查：尝试获取玩家的处罚记录
                List<PunishmentRecord> playerRecords = punishmentManager.getPunishmentsByPlayer(playerName, 1, 1);

                // 如果没有记录，检查玩家名是否有效
                if (playerRecords.isEmpty()) {
                    // 检查玩家名格式
                    if (!isValidPlayerName(playerName)) {
                        sendErrorResponse(exchange, 404, "无效的玩家名称");
                        return;
                    }
                }
            }

            // 获取分页参数
            int page = parseInt(params.get("page"), 1);
            String staffParam = params.get("staff");
            String typeParam = params.get("type");

            // 解析类型参数
            PunishmentRecord.PunishmentType typeFilter = null;
            if (typeParam != null && !typeParam.trim().isEmpty()) {
                typeFilter = parseType(typeParam);
                plugin.getLogger().info("类型过滤: " + typeParam + " -> " + typeFilter);
            }

            // 生成玩家专用页面HTML（按照 next-litebans 玩家页面布局）
            String html = pageGenerator.generatePlayerPage(playerName, page, staffParam, typeFilter);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取玩家页面时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取玩家页面失败");
        }
    }

    /**
     * 检查玩家名是否有效
     */
    private boolean isValidPlayerName(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return false;
        }

        String cleanName = playerName.trim();
        return cleanName.length() >= 3 && cleanName.length() <= 16 &&
                cleanName.matches("[a-zA-Z0-9_]+");
    }

    /**
     * 处理数据更新检测请求（所有类型）
     */
    private void handleCheckUpdate(HttpExchange exchange, Map<String, String> params) throws IOException {
        try {
            if (punishmentManager == null || !punishmentManager.isEnabled()) {
                sendJsonResponse(exchange, "{\"error\": \"数据库未连接\"}");
                return;
            }

            // 计算所有类型的数据哈希
            StringBuilder hashBuilder = new StringBuilder();

            // 获取各种类型的数据哈希
            for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                String typeKey = "type_" + type.name().toLowerCase();
                String typeHash = punishmentManager.getDataHash(typeKey);
                if (typeHash != null) {
                    hashBuilder.append(typeHash);
                }
            }

            // 获取统计信息哈希
            String statsHash = punishmentManager.getDataHash("statistics");
            if (statsHash != null) {
                hashBuilder.append(statsHash);
            }

            // 计算综合哈希
            String combinedHash = String.valueOf(hashBuilder.toString().hashCode());

            // 返回JSON响应
            String jsonResponse = String.format(
                    "{\"dataHash\": \"%s\", \"timestamp\": %d}",
                    combinedHash,
                    System.currentTimeMillis());

            sendJsonResponse(exchange, jsonResponse);

        } catch (Exception e) {
            plugin.getLogger().severe("检测数据更新时发生错误: " + e.getMessage());
            e.printStackTrace();
            sendJsonResponse(exchange, "{\"error\": \"检测更新失败\"}");
        }
    }

    /**
     * 处理特定类型数据更新检测请求
     */
    private void handleCheckTypeUpdate(HttpExchange exchange, String typeStr, Map<String, String> params)
            throws IOException {
        try {
            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendJsonResponse(exchange, "{\"error\": \"未知的处罚类型\"}");
                return;
            }

            if (punishmentManager == null || !punishmentManager.isEnabled()) {
                sendJsonResponse(exchange, "{\"error\": \"数据库未连接\"}");
                return;
            }

            // 获取特定类型的数据哈希
            String typeKey = "type_" + type.name().toLowerCase();
            String typeHash = punishmentManager.getDataHash(typeKey);

            // 获取统计信息哈希
            String statsHash = punishmentManager.getDataHash("statistics");

            // 计算综合哈希
            String combinedHash = String.valueOf((typeHash + statsHash).hashCode());

            // 返回JSON响应
            String jsonResponse = String.format(
                    "{\"dataHash\": \"%s\", \"type\": \"%s\", \"timestamp\": %d}",
                    combinedHash,
                    type.name().toLowerCase(),
                    System.currentTimeMillis());

            sendJsonResponse(exchange, jsonResponse);

        } catch (Exception e) {
            plugin.getLogger().severe("检测特定类型数据更新时发生错误: " + e.getMessage());
            e.printStackTrace();
            sendJsonResponse(exchange, "{\"error\": \"检测更新失败\"}");
        }
    }

    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpExchange exchange, String jsonContent) throws IOException {
        byte[] bytes = jsonContent.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.sendResponseHeaders(200, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }
}
