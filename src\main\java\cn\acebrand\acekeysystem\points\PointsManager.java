package cn.acebrand.acekeysystem.points;

import cn.acebrand.acekeysystem.AceKeySystem;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 积分管理器
 * 负责管理玩家积分的增减、查询等操作
 */
public class PointsManager {

    private final AceKeySystem plugin;
    private final Map<String, Integer> playerPoints; // 改为使用玩家名
    private File pointsFile;
    private FileConfiguration pointsConfig;

    public PointsManager(AceKeySystem plugin) {
        this.plugin = plugin;
        this.playerPoints = new HashMap<>();
        initializePointsFile();
        loadPlayerPoints();
    }

    /**
     * 初始化积分文件
     */
    private void initializePointsFile() {
        pointsFile = new File(plugin.getDataFolder(), "points.yml");
        if (!pointsFile.exists()) {
            try {
                pointsFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建积分文件: " + e.getMessage());
            }
        }
        pointsConfig = YamlConfiguration.loadConfiguration(pointsFile);
    }

    /**
     * 加载玩家积分数据
     */
    private void loadPlayerPoints() {
        if (pointsConfig.getConfigurationSection("points") != null) {
            for (String key : pointsConfig.getConfigurationSection("points").getKeys(false)) {
                try {
                    // 检查是否是UUID格式（旧数据）
                    if (key.contains("-") && key.length() == 36) {
                        // 旧格式：UUID，需要转换为玩家名
                        UUID uuid = UUID.fromString(key);
                        String playerName = getPlayerNameFromUUID(uuid);
                        if (playerName != null) {
                            int points = pointsConfig.getInt("points." + key, 0);
                            playerPoints.put(playerName, points);
                            plugin.getLogger().info("转换旧数据: " + key + " -> " + playerName);
                        }
                    } else {
                        // 新格式：玩家名
                        int points = pointsConfig.getInt("points." + key, 0);
                        playerPoints.put(key, points);
                    }
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的数据格式: " + key);
                }
            }
        }
        plugin.getLogger().info("已加载 " + playerPoints.size() + " 个玩家的积分数据");
    }

    /**
     * 从UUID获取玩家名
     */
    private String getPlayerNameFromUUID(UUID uuid) {
        // 先尝试在线玩家
        Player player = plugin.getServer().getPlayer(uuid);
        if (player != null) {
            return player.getName();
        }

        // 再尝试离线玩家
        try {
            @SuppressWarnings("deprecation")
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(uuid);
            if (offlinePlayer.hasPlayedBefore()) {
                return offlinePlayer.getName();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法获取UUID " + uuid + " 对应的玩家名: " + e.getMessage());
        }

        return null;
    }

    /**
     * 保存积分数据到文件
     */
    public void savePlayerPoints() {
        // 清空旧数据
        pointsConfig.set("points", null);

        // 保存新数据（使用玩家名）
        for (Map.Entry<String, Integer> entry : playerPoints.entrySet()) {
            pointsConfig.set("points." + entry.getKey(), entry.getValue());
        }
        try {
            pointsConfig.save(pointsFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存积分数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取玩家积分（通过玩家名）
     */
    public int getPlayerPoints(String playerName) {
        return playerPoints.getOrDefault(playerName, 0);
    }

    /**
     * 获取玩家积分（通过UUID）
     */
    public int getPlayerPoints(UUID playerUuid) {
        // 先尝试在线玩家
        Player player = plugin.getServer().getPlayer(playerUuid);
        if (player != null) {
            return getPlayerPoints(player.getName());
        }

        // 再尝试离线玩家
        try {
            @SuppressWarnings("deprecation")
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerUuid);
            if (offlinePlayer.hasPlayedBefore() && offlinePlayer.getName() != null) {
                return getPlayerPoints(offlinePlayer.getName());
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法获取UUID " + playerUuid + " 对应的玩家积分: " + e.getMessage());
        }
        return 0;
    }

    /**
     * 设置玩家积分（通过玩家名）
     */
    public void setPlayerPoints(String playerName, int points) {
        playerPoints.put(playerName, Math.max(0, points));
        savePlayerPoints();
    }

    /**
     * 设置玩家积分（通过UUID）
     */
    public void setPlayerPoints(UUID playerUuid, int points) {
        // 先尝试在线玩家
        Player player = plugin.getServer().getPlayer(playerUuid);
        if (player != null) {
            setPlayerPoints(player.getName(), points);
            return;
        }

        // 再尝试离线玩家
        try {
            @SuppressWarnings("deprecation")
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerUuid);
            if (offlinePlayer.hasPlayedBefore() && offlinePlayer.getName() != null) {
                setPlayerPoints(offlinePlayer.getName(), points);
            } else {
                plugin.getLogger().warning("无法设置UUID " + playerUuid + " 的积分：玩家未找到");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法设置UUID " + playerUuid + " 的积分: " + e.getMessage());
        }
    }

    /**
     * 增加玩家积分（通过玩家名）
     */
    public void addPlayerPoints(String playerName, int points) {
        int currentPoints = getPlayerPoints(playerName);
        setPlayerPoints(playerName, currentPoints + points);
    }

    /**
     * 增加玩家积分（通过UUID）
     */
    public void addPlayerPoints(UUID playerUuid, int points) {
        // 先尝试在线玩家
        Player player = plugin.getServer().getPlayer(playerUuid);
        if (player != null) {
            addPlayerPoints(player.getName(), points);
            return;
        }

        // 再尝试离线玩家
        try {
            @SuppressWarnings("deprecation")
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerUuid);
            if (offlinePlayer.hasPlayedBefore() && offlinePlayer.getName() != null) {
                addPlayerPoints(offlinePlayer.getName(), points);
            } else {
                plugin.getLogger().warning("无法为UUID " + playerUuid + " 增加积分：玩家未找到");
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法为UUID " + playerUuid + " 增加积分: " + e.getMessage());
        }
    }

    /**
     * 扣除玩家积分（通过玩家名）
     */
    public boolean deductPlayerPoints(String playerName, int points) {
        plugin.getLogger().info("开始扣除积分 - 玩家: " + playerName + ", 扣除数量: " + points);

        int currentPoints = getPlayerPoints(playerName);
        plugin.getLogger().info("当前积分: " + currentPoints);

        if (currentPoints >= points) {
            int newPoints = currentPoints - points;
            plugin.getLogger().info("积分足够，设置新积分: " + newPoints);

            setPlayerPoints(playerName, newPoints);

            // 验证设置是否成功
            int verifyPoints = getPlayerPoints(playerName);
            plugin.getLogger().info("验证积分设置结果: " + verifyPoints + " (期望: " + newPoints + ")");

            boolean success = (verifyPoints == newPoints);
            plugin.getLogger().info("积分扣除" + (success ? "成功" : "失败"));
            return success;
        } else {
            plugin.getLogger().warning("积分不足 - 当前: " + currentPoints + ", 需要: " + points);
            return false;
        }
    }

    /**
     * 扣除玩家积分（通过UUID）
     */
    public boolean deductPlayerPoints(UUID playerUuid, int points) {
        // 先尝试在线玩家
        Player player = plugin.getServer().getPlayer(playerUuid);
        if (player != null) {
            return deductPlayerPoints(player.getName(), points);
        }

        // 再尝试离线玩家
        try {
            @SuppressWarnings("deprecation")
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(playerUuid);
            if (offlinePlayer.hasPlayedBefore() && offlinePlayer.getName() != null) {
                return deductPlayerPoints(offlinePlayer.getName(), points);
            } else {
                plugin.getLogger().warning("无法为UUID " + playerUuid + " 扣除积分：玩家未找到");
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法为UUID " + playerUuid + " 扣除积分: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取所有玩家积分数据（按玩家名）
     */
    public Map<String, Integer> getAllPlayerPoints() {
        return new HashMap<>(playerPoints);
    }

    /**
     * 获取所有玩家积分数据（按UUID格式，用于兼容性）
     */
    public Map<UUID, Integer> getAllPlayerPointsByUUID() {
        Map<UUID, Integer> uuidMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : playerPoints.entrySet()) {
            try {
                @SuppressWarnings("deprecation")
                OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(entry.getKey());
                if (offlinePlayer.hasPlayedBefore()) {
                    uuidMap.put(offlinePlayer.getUniqueId(), entry.getValue());
                }
            } catch (Exception e) {
                plugin.getLogger().warning("无法获取玩家 " + entry.getKey() + " 的UUID: " + e.getMessage());
            }
        }
        return uuidMap;
    }

    /**
     * 清空所有积分数据
     */
    public void clearAllPoints() {
        playerPoints.clear();
        pointsConfig.set("points", null);
        savePlayerPoints();
    }

    /**
     * 重新加载积分数据
     */
    public void reload() {
        pointsConfig = YamlConfiguration.loadConfiguration(pointsFile);
        playerPoints.clear();
        loadPlayerPoints();
    }
}
