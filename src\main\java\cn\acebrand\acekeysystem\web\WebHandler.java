package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 主Web处理器
 * 处理根路径请求和路由分发
 */
public class WebHandler implements HttpHandler {

    protected final AceKeySystem plugin;
    protected final WebServer webServer;

    public WebHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        URI uri = exchange.getRequestURI();
        String path = uri.getPath();

        if (webServer.isDebug()) {
            plugin.getLogger().info("Web请求: " + method + " " + path);
        }

        try {
            // 根据路径分发请求
            if (path.equals("/") || path.equals("/index.html")) {
                handleIndex(exchange);
            } else if (path.startsWith("/favicon.ico")) {
                handleFavicon(exchange);
            } else {
                handle404(exchange);
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理Web请求时出错: " + e.getMessage());
            e.printStackTrace();
            handle500(exchange, e);
        }
    }

    /**
     * 处理首页请求
     */
    private void handleIndex(HttpExchange exchange) throws IOException {
        String html = generateIndexPage();
        sendResponse(exchange, 200, "text/html; charset=utf-8", html);
    }

    /**
     * 生成首页HTML
     */
    private String generateIndexPage() {
        StringBuilder html = new StringBuilder();
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(webServer.getTitle()).append("</title>\n");
        html.append("    <style>\n");
        html.append(generateIndexPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <!-- 浮动装饰元素 -->\n");
        html.append("    <div class=\"floating-shapes\">\n");
        html.append("        <div class=\"shape shape-1\"></div>\n");
        html.append("        <div class=\"shape shape-2\"></div>\n");
        html.append("        <div class=\"shape shape-3\"></div>\n");
        html.append("        <div class=\"shape shape-4\"></div>\n");
        html.append("        <div class=\"shape shape-5\"></div>\n");
        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <div class=\"container\">\n");
        // 获取首页配置
        String homepageTitle = plugin.getConfig().getString("website.homepage.title", webServer.getTitle());
        String homepageSubtitle = plugin.getConfig().getString("website.homepage.subtitle", "欢迎来到游戏娱乐中心");
        String homepageDescription = plugin.getConfig().getString("website.homepage.description",
                "🎯 参与精彩抽奖活动，赢取丰厚奖品\n🏆 查看排行榜，与其他玩家一较高下\n🛒 使用积分购买心仪的游戏道具");

        html.append("        <!-- 主标题区域 -->\n");
        html.append("        <div class=\"hero-section\">\n");
        html.append("            <div class=\"hero-content\">\n");
        html.append("                <h1 class=\"hero-title\">").append(homepageTitle).append("</h1>\n");
        html.append("                <p class=\"hero-subtitle\">").append(homepageSubtitle).append("</p>\n");
        html.append("                <div class=\"hero-description\">\n");

        // 处理多行描述
        String[] descriptionLines = homepageDescription.split("\n");
        for (String line : descriptionLines) {
            if (!line.trim().isEmpty()) {
                html.append("                    <p>").append(line.trim()).append("</p>\n");
            }
        }

        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("        \n");
        // 获取导航卡片显示配置
        boolean showLotteryCard = plugin.getConfig().getBoolean("website.homepage.show-lottery-card", true);
        boolean showLeaderboardCard = plugin.getConfig().getBoolean("website.homepage.show-leaderboard-card", true);
        boolean showShopCard = plugin.getConfig().getBoolean("website.homepage.show-shop-card", true);
        boolean showPunishmentCard = plugin.getConfig().getBoolean("litebans.display.show-punishment-card", true);

        html.append("        <!-- 导航卡片区域 -->\n");
        html.append("        <div class=\"nav-grid\">\n");

        // 抽奖系统卡片
        if (showLotteryCard) {
            html.append("            <!-- 抽奖系统 -->\n");
            html.append("            <div class=\"nav-card lottery-card\">\n");
            html.append("                <div class=\"card-icon\">🎲</div>\n");
            html.append("                <h3 class=\"card-title\">抽奖系统</h3>\n");
            html.append("                <p class=\"card-description\">参与精彩的抽奖活动<br>赢取丰厚的游戏奖品</p>\n");
            html.append("                <div class=\"card-features\">\n");
            html.append("                    <span class=\"feature\">🎁 丰富奖品</span>\n");
            html.append("                    <span class=\"feature\">🎯 公平抽奖</span>\n");
            html.append("                </div>\n");
            html.append("                <a href=\"/user\" class=\"nav-btn lottery-btn\">\n");
            html.append("                    <span>开始抽奖</span>\n");
            html.append("                    <span class=\"btn-arrow\">→</span>\n");
            html.append("                </a>\n");
            html.append("            </div>\n");
            html.append("            \n");
        }

        // 排行榜卡片
        if (showLeaderboardCard) {
            html.append("            <!-- 排行榜 -->\n");
            html.append("            <div class=\"nav-card leaderboard-card\">\n");
            html.append("                <div class=\"card-icon\">🏆</div>\n");
            html.append("                <h3 class=\"card-title\">排行榜</h3>\n");
            html.append("                <p class=\"card-description\">查看积分和获奖排名<br>与其他玩家一较高下</p>\n");
            html.append("                <div class=\"card-features\">\n");
            html.append("                    <span class=\"feature\">💰 积分榜</span>\n");
            html.append("                    <span class=\"feature\">🥇 获奖榜</span>\n");
            html.append("                </div>\n");
            html.append("                <a href=\"/leaderboard\" class=\"nav-btn leaderboard-btn\">\n");
            html.append("                    <span>查看排名</span>\n");
            html.append("                    <span class=\"btn-arrow\">→</span>\n");
            html.append("                </a>\n");
            html.append("            </div>\n");
            html.append("            \n");
        }

        // 每日签到卡片
        html.append("            <!-- 每日签到 -->\n");
        html.append("            <div class=\"nav-card checkin-card\">\n");
        html.append("                <div class=\"card-icon\">📅</div>\n");
        html.append("                <h3 class=\"card-title\">每日签到</h3>\n");
        html.append("                <p class=\"card-description\">每天签到获得积分<br>连续签到获得更多奖励</p>\n");
        html.append("                <div class=\"card-features\">\n");
        html.append("                    <span class=\"feature\">💰 积分奖励</span>\n");
        html.append("                    <span class=\"feature\">🔥 连续奖励</span>\n");
        html.append("                </div>\n");
        html.append("                <a href=\"/checkin\" class=\"nav-btn checkin-btn\">\n");
        html.append("                    <span>立即签到</span>\n");
        html.append("                    <span class=\"btn-arrow\">→</span>\n");
        html.append("                </a>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 积分商店卡片
        if (showShopCard) {
            html.append("            <!-- 积分商店 -->\n");
            html.append("            <div class=\"nav-card shop-card\">\n");
            html.append("                <div class=\"card-icon\">🛒</div>\n");
            html.append("                <h3 class=\"card-title\">积分商店</h3>\n");
            html.append("                <p class=\"card-description\">使用积分购买道具<br>获取游戏增益和特权</p>\n");
            html.append("                <div class=\"card-features\">\n");
            html.append("                    <span class=\"feature\">⚔️ 游戏道具</span>\n");
            html.append("                    <span class=\"feature\">👑 专属特权</span>\n");
            html.append("                </div>\n");
            html.append("                <a href=\"/points-shop\" class=\"nav-btn shop-btn\">\n");
            html.append("                    <span>进入商店</span>\n");
            html.append("                    <span class=\"btn-arrow\">→</span>\n");
            html.append("                </a>\n");
            html.append("            </div>\n");
            html.append("            \n");
        }

        // 处罚记录卡片（无论数据库是否连接都显示）
        if (showPunishmentCard) {
            String cardTitle = plugin.getConfig().getString("litebans.display.punishment-card-title", "📋 处罚记录");
            String cardDescription = plugin.getConfig().getString("litebans.display.punishment-card-description",
                    "查看服务器所有处罚历史记录");

            html.append("            <!-- 处罚记录 -->\n");
            html.append("            <div class=\"nav-card punishment-card\">\n");
            html.append("                <div class=\"card-icon\">📋</div>\n");
            html.append("                <h3 class=\"card-title\">").append(escapeHtml(cardTitle)).append("</h3>\n");
            html.append("                <p class=\"card-description\">").append(escapeHtml(cardDescription))
                    .append("</p>\n");
            html.append("                <div class=\"card-features\">\n");
            html.append("                    <span class=\"feature\">🚫 封禁记录</span>\n");
            html.append("                    <span class=\"feature\">🔇 禁言记录</span>\n");
            html.append("                    <span class=\"feature\">⚠️ 警告记录</span>\n");
            html.append("                    <span class=\"feature\">👢 踢出记录</span>\n");
            html.append("                </div>\n");
            html.append("                <a href=\"/punishments\" class=\"nav-btn punishment-btn\">\n");
            html.append("                    <span>查看记录</span>\n");
            html.append("                    <span class=\"btn-arrow\">→</span>\n");
            html.append("                </a>\n");
            html.append("            </div>\n");
            html.append("            \n");
        }

        html.append("        </div>\n");
        html.append("        \n");

        // 系统状态区域
        boolean showSystemStatus = plugin.getConfig().getBoolean("website.homepage.show-system-status", true);
        if (showSystemStatus) {
            html.append("        <!-- 系统状态 -->\n");
            html.append("        <div class=\"status-section\">\n");
            html.append("            <div class=\"status-card\">\n");
            html.append("                <div class=\"status-header\">\n");
            html.append("                    <h3>🔧 系统状态</h3>\n");
            html.append("                    <span class=\"status-indicator online\">在线</span>\n");
            html.append("                </div>\n");
            html.append("                <div class=\"status-info\">\n");
            html.append("                    <div class=\"status-item\">\n");
            html.append("                        <span class=\"status-label\">服务器状态:</span>\n");
            html.append("                        <span class=\"status-value\">运行正常 ✅</span>\n");
            html.append("                    </div>\n");
            html.append("                    <div class=\"status-item\">\n");
            html.append("                        <span class=\"status-label\">当前时间:</span>\n");
            html.append("                        <span class=\"status-value\" id=\"currentTime\">")
                    .append(new java.util.Date().toString()).append("</span>\n");
            html.append("                    </div>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            html.append("        </div>\n");
        }

        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <script>\n");
        html.append("        // 更新时间\n");
        html.append("        function updateTime() {\n");
        html.append("            const now = new Date();\n");
        html.append("            const timeElement = document.getElementById('currentTime');\n");
        html.append("            if (timeElement) {\n");
        html.append("                timeElement.textContent = now.toLocaleString('zh-CN');\n");
        html.append("            }\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        // 每秒更新时间\n");
        html.append("        setInterval(updateTime, 1000);\n");
        html.append("        updateTime();\n");
        html.append("    </script>\n");
        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 处理favicon请求
     */
    private void handleFavicon(HttpExchange exchange) throws IOException {
        // 返回一个简单的透明图标
        byte[] favicon = new byte[0]; // 空图标
        exchange.getResponseHeaders().set("Content-Type", "image/x-icon");
        exchange.sendResponseHeaders(200, favicon.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(favicon);
        }
    }

    /**
     * 处理404错误
     */
    private void handle404(HttpExchange exchange) throws IOException {
        String html = generate404Page();
        sendResponse(exchange, 404, "text/html; charset=utf-8", html);
    }

    /**
     * 生成404页面
     */
    private String generate404Page() {
        return "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>页面未找到</title></head>" +
                "<body style=\"font-family: Arial, sans-serif; text-align: center; padding: 50px;\">" +
                "<h1>404 - 页面未找到</h1>" +
                "<p>您访问的页面不存在。</p>" +
                "<a href=\"/\">返回首页</a>" +
                "</body></html>";
    }

    /**
     * 处理500错误
     */
    private void handle500(HttpExchange exchange, Exception e) throws IOException {
        String html = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>服务器错误</title></head>" +
                "<body style=\"font-family: Arial, sans-serif; text-align: center; padding: 50px;\">" +
                "<h1>500 - 服务器内部错误</h1>" +
                "<p>服务器处理请求时发生错误。</p>" +
                (webServer.isDebug() ? "<pre>" + e.getMessage() + "</pre>" : "") +
                "<a href=\"/\">返回首页</a>" +
                "</body></html>";
        sendResponse(exchange, 500, "text/html; charset=utf-8", html);
    }

    /**
     * 发送HTTP响应
     */
    protected void sendResponse(HttpExchange exchange, int statusCode, String contentType, String content)
            throws IOException {
        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");
        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }

    /**
     * 解析查询参数
     */
    protected Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return params;
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null)
            return "";
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    /**
     * 生成首页CSS样式
     */
    private String generateIndexPageCSS() {
        // 获取首页背景图片配置
        String homepageBackgroundImage = plugin.getConfig().getString("website.homepage.background-image", "");

        String backgroundStyle;
        if (homepageBackgroundImage != null && !homepageBackgroundImage.trim().isEmpty()) {
            // 获取首页背景透明度设置
            int opacity = plugin.getConfig().getInt("website.homepage.background-opacity", 30);
            // 将透明度转换为0-1之间的值
            double opacityValue = opacity / 100.0;

            backgroundStyle = "    background: linear-gradient(135deg, rgba(102, 126, 234, " + opacityValue
                    + ") 0%, rgba(118, 75, 162, " + (opacityValue + 0.1) + ") 100%), url('"
                    + homepageBackgroundImage.trim() + "');\n" +
                    "    background-size: cover;\n" +
                    "    background-position: center;\n" +
                    "    background-attachment: fixed;\n";
        } else {
            backgroundStyle = "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n";
        }

        return "/* ==================== 基础样式 ==================== */\n" +
                "* {\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                "body {\n" +
                "    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n" +
                backgroundStyle +
                "    min-height: 100vh;\n" +
                "    overflow-x: hidden;\n" +
                "    position: relative;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 浮动装饰元素 ==================== */\n" +
                ".floating-shapes {\n" +
                "    position: fixed;\n" +
                "    top: 0;\n" +
                "    left: 0;\n" +
                "    width: 100%;\n" +
                "    height: 100%;\n" +
                "    pointer-events: none;\n" +
                "    z-index: 1;\n" +
                "}\n" +
                "\n" +
                ".shape {\n" +
                "    position: absolute;\n" +
                "    background: rgba(255, 255, 255, 0.1);\n" +
                "    border-radius: 50%;\n" +
                "    animation: float 20s infinite linear;\n" +
                "}\n" +
                "\n" +
                ".shape-1 {\n" +
                "    width: 80px;\n" +
                "    height: 80px;\n" +
                "    top: 20%;\n" +
                "    left: 10%;\n" +
                "    animation-delay: 0s;\n" +
                "}\n" +
                "\n" +
                ".shape-2 {\n" +
                "    width: 120px;\n" +
                "    height: 120px;\n" +
                "    top: 60%;\n" +
                "    right: 15%;\n" +
                "    animation-delay: -5s;\n" +
                "}\n" +
                "\n" +
                ".shape-3 {\n" +
                "    width: 60px;\n" +
                "    height: 60px;\n" +
                "    top: 80%;\n" +
                "    left: 20%;\n" +
                "    animation-delay: -10s;\n" +
                "}\n" +
                "\n" +
                ".shape-4 {\n" +
                "    width: 100px;\n" +
                "    height: 100px;\n" +
                "    top: 30%;\n" +
                "    right: 30%;\n" +
                "    animation-delay: -15s;\n" +
                "}\n" +
                "\n" +
                ".shape-5 {\n" +
                "    width: 40px;\n" +
                "    height: 40px;\n" +
                "    top: 10%;\n" +
                "    left: 60%;\n" +
                "    animation-delay: -8s;\n" +
                "}\n" +
                "\n" +
                "@keyframes float {\n" +
                "    0%, 100% {\n" +
                "        transform: translateY(0px) rotate(0deg);\n" +
                "        opacity: 0.7;\n" +
                "    }\n" +
                "    50% {\n" +
                "        transform: translateY(-20px) rotate(180deg);\n" +
                "        opacity: 1;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* ==================== 容器布局 ==================== */\n" +
                ".container {\n" +
                "    max-width: 1200px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 40px 20px;\n" +
                "    position: relative;\n" +
                "    z-index: 2;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 主标题区域 ==================== */\n" +
                ".hero-section {\n" +
                "    text-align: center;\n" +
                "    margin-bottom: 60px;\n" +
                "    padding: 60px 40px;\n" +
                "    background: rgba(255, 255, 255, 0.1);\n" +
                "    backdrop-filter: blur(20px);\n" +
                "    border-radius: 30px;\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n" +
                "    position: relative;\n" +
                "    overflow: hidden;\n" +
                "}\n" +
                "\n" +
                ".hero-section::before {\n" +
                "    content: '';\n" +
                "    position: absolute;\n" +
                "    top: -50%;\n" +
                "    left: -50%;\n" +
                "    width: 200%;\n" +
                "    height: 200%;\n" +
                "    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);\n" +
                "    transform: rotate(45deg);\n" +
                "    animation: shimmer 6s infinite;\n" +
                "    pointer-events: none;\n" +
                "}\n" +
                "\n" +
                "@keyframes shimmer {\n" +
                "    0%, 100% {\n" +
                "        transform: translateX(-100%) translateY(-100%) rotate(45deg);\n" +
                "    }\n" +
                "    50% {\n" +
                "        transform: translateX(100%) translateY(100%) rotate(45deg);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".hero-content {\n" +
                "    position: relative;\n" +
                "    z-index: 1;\n" +
                "}\n" +
                "\n" +
                ".hero-title {\n" +
                "    font-size: 3.5em;\n" +
                "    font-weight: 700;\n" +
                "    color: #ffffff;\n" +
                "    margin-bottom: 20px;\n" +
                "    text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);\n" +
                "    letter-spacing: 1px;\n" +
                "}\n" +
                "\n" +
                ".hero-subtitle {\n" +
                "    font-size: 1.4em;\n" +
                "    color: rgba(255, 255, 255, 0.9);\n" +
                "    margin-bottom: 30px;\n" +
                "    font-weight: 300;\n" +
                "}\n" +
                "\n" +
                ".hero-description {\n" +
                "    max-width: 600px;\n" +
                "    margin: 0 auto;\n" +
                "}\n" +
                "\n" +
                ".hero-description p {\n" +
                "    font-size: 1.1em;\n" +
                "    color: rgba(255, 255, 255, 0.8);\n" +
                "    margin-bottom: 10px;\n" +
                "    line-height: 1.6;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 导航卡片网格 ==================== */\n" +
                ".nav-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n" +
                "    gap: 30px;\n" +
                "    margin-bottom: 50px;\n" +
                "}\n" +
                "\n" +
                ".nav-card {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 25px;\n" +
                "    padding: 40px 30px;\n" +
                "    text-align: center;\n" +
                "    box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.3);\n" +
                "    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);\n" +
                "    position: relative;\n" +
                "    overflow: hidden;\n" +
                "}\n" +
                "\n" +
                ".nav-card::before {\n" +
                "    content: '';\n" +
                "    position: absolute;\n" +
                "    top: 0;\n" +
                "    left: -100%;\n" +
                "    width: 100%;\n" +
                "    height: 100%;\n" +
                "    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);\n" +
                "    transition: left 0.6s;\n" +
                "}\n" +
                "\n" +
                ".nav-card:hover::before {\n" +
                "    left: 100%;\n" +
                "}\n" +
                "\n" +
                ".nav-card:hover {\n" +
                "    transform: translateY(-10px) scale(1.02);\n" +
                "    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".card-icon {\n" +
                "    font-size: 4em;\n" +
                "    margin-bottom: 20px;\n" +
                "    display: block;\n" +
                "    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));\n" +
                "}\n" +
                "\n" +
                ".card-title {\n" +
                "    font-size: 1.8em;\n" +
                "    font-weight: 700;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".card-description {\n" +
                "    font-size: 1.1em;\n" +
                "    color: #4a5568;\n" +
                "    line-height: 1.6;\n" +
                "    margin-bottom: 25px;\n" +
                "}\n" +
                "\n" +
                ".card-features {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 15px;\n" +
                "    margin-bottom: 30px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".feature {\n" +
                "    background: rgba(102, 126, 234, 0.1);\n" +
                "    color: #667eea;\n" +
                "    padding: 8px 16px;\n" +
                "    border-radius: 20px;\n" +
                "    font-size: 0.9em;\n" +
                "    font-weight: 600;\n" +
                "    border: 1px solid rgba(102, 126, 234, 0.2);\n" +
                "}\n" +
                "\n" +
                ".nav-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    gap: 10px;\n" +
                "    padding: 15px 30px;\n" +
                "    border-radius: 50px;\n" +
                "    text-decoration: none;\n" +
                "    font-weight: 600;\n" +
                "    font-size: 1.1em;\n" +
                "    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n" +
                "    position: relative;\n" +
                "    overflow: hidden;\n" +
                "    min-width: 160px;\n" +
                "}\n" +
                "\n" +
                ".btn-arrow {\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".nav-btn:hover .btn-arrow {\n" +
                "    transform: translateX(5px);\n" +
                "}\n" +
                "\n" +
                "/* 抽奖按钮样式 */\n" +
                ".lottery-btn {\n" +
                "    background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);\n" +
                "}\n" +
                "\n" +
                ".lottery-btn:hover {\n" +
                "    background: linear-gradient(135deg, #ff5252, #d84315);\n" +
                "    box-shadow: 0 12px 35px rgba(255, 107, 107, 0.4);\n" +
                "    transform: translateY(-3px);\n" +
                "}\n" +
                "\n" +
                "/* 排行榜按钮样式 */\n" +
                ".leaderboard-btn {\n" +
                "    background: linear-gradient(135deg, #feca57, #ff9ff3);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 8px 25px rgba(254, 202, 87, 0.3);\n" +
                "}\n" +
                "\n" +
                ".leaderboard-btn:hover {\n" +
                "    background: linear-gradient(135deg, #ff9f43, #f368e0);\n" +
                "    box-shadow: 0 12px 35px rgba(254, 202, 87, 0.4);\n" +
                "    transform: translateY(-3px);\n" +
                "}\n" +
                "\n" +
                "/* 商店按钮样式 */\n" +
                ".shop-btn {\n" +
                "    background: linear-gradient(135deg, #5f27cd, #00d2d3);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 8px 25px rgba(95, 39, 205, 0.3);\n" +
                "}\n" +
                "\n" +
                ".shop-btn:hover {\n" +
                "    background: linear-gradient(135deg, #341f97, #0abde3);\n" +
                "    box-shadow: 0 12px 35px rgba(95, 39, 205, 0.4);\n" +
                "    transform: translateY(-3px);\n" +
                "}\n" +
                "\n" +
                "/* 封禁按钮样式 */\n" +
                ".ban-btn {\n" +
                "    background: linear-gradient(135deg, #e74c3c, #c0392b);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3);\n" +
                "}\n" +
                "\n" +
                ".ban-btn:hover {\n" +
                "    background: linear-gradient(135deg, #c0392b, #a93226);\n" +
                "    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.4);\n" +
                "    transform: translateY(-3px);\n" +
                "}\n" +
                "\n" +
                "/* 处罚记录按钮样式 */\n" +
                ".punishment-btn {\n" +
                "    background: linear-gradient(135deg, #8e44ad, #3742fa);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 8px 25px rgba(142, 68, 173, 0.3);\n" +
                "}\n" +
                "\n" +
                ".punishment-btn:hover {\n" +
                "    background: linear-gradient(135deg, #732d91, #2f3542);\n" +
                "    box-shadow: 0 12px 35px rgba(142, 68, 173, 0.4);\n" +
                "    transform: translateY(-3px);\n" +
                "}\n" +
                "\n" +
                "/* ==================== 状态区域 ==================== */\n" +
                ".status-section {\n" +
                "    margin-top: 40px;\n" +
                "}\n" +
                "\n" +
                ".status-card {\n" +
                "    background: rgba(255, 255, 255, 0.9);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 30px;\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.3);\n" +
                "}\n" +
                "\n" +
                ".status-header {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    margin-bottom: 20px;\n" +
                "    padding-bottom: 15px;\n" +
                "    border-bottom: 2px solid #f1f5f9;\n" +
                "}\n" +
                "\n" +
                ".status-header h3 {\n" +
                "    color: #2d3748;\n" +
                "    font-size: 1.4em;\n" +
                "    font-weight: 600;\n" +
                "    margin: 0;\n" +
                "}\n" +
                "\n" +
                ".status-indicator {\n" +
                "    padding: 8px 16px;\n" +
                "    border-radius: 20px;\n" +
                "    font-size: 0.9em;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".status-indicator.online {\n" +
                "    background: linear-gradient(135deg, #10ac84, #1dd1a1);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 4px 15px rgba(16, 172, 132, 0.3);\n" +
                "}\n" +
                "\n" +
                ".status-info {\n" +
                "    display: grid;\n" +
                "    gap: 15px;\n" +
                "}\n" +
                "\n" +
                ".status-item {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    padding: 12px 0;\n" +
                "}\n" +
                "\n" +
                ".status-label {\n" +
                "    color: #4a5568;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                ".status-value {\n" +
                "    color: #2d3748;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 响应式设计 ==================== */\n" +
                "@media (max-width: 768px) {\n" +
                "    .container {\n" +
                "        padding: 20px 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .hero-section {\n" +
                "        padding: 40px 20px;\n" +
                "        margin-bottom: 40px;\n" +
                "    }\n" +
                "    \n" +
                "    .hero-title {\n" +
                "        font-size: 2.5em;\n" +
                "    }\n" +
                "    \n" +
                "    .nav-grid {\n" +
                "        grid-template-columns: 1fr;\n" +
                "        gap: 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .nav-card {\n" +
                "        padding: 30px 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .card-features {\n" +
                "        flex-direction: column;\n" +
                "        gap: 10px;\n" +
                "    }\n" +
                "    \n" +
                "    .status-header {\n" +
                "        flex-direction: column;\n" +
                "        gap: 15px;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .status-item {\n" +
                "        flex-direction: column;\n" +
                "        gap: 5px;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "}\n";
    }
}
