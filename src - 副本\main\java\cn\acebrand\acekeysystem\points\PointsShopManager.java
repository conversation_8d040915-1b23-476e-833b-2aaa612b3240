package cn.acebrand.acekeysystem.points;

import cn.acebrand.acekeysystem.AceKeySystem;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import org.bukkit.OfflinePlayer;

/**
 * 积分商店管理器
 * 负责管理积分商店物品和购买记录
 */
public class PointsShopManager {

    private final AceKeySystem plugin;
    private final Map<String, PointsShopItem> shopItems;
    private final Map<String, Map<String, Integer>> playerPurchaseHistory; // 改为使用玩家名
    private final Map<String, Map<String, String>> playerPurchaseTimestamps; // 购买时间戳，改为可读格式
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 日期格式化器

    private File shopFile;
    private FileConfiguration shopConfig;
    private File purchaseFile;
    private FileConfiguration purchaseConfig;

    public PointsShopManager(AceKeySystem plugin) {
        this.plugin = plugin;
        this.shopItems = new HashMap<>();
        this.playerPurchaseHistory = new HashMap<>();
        this.playerPurchaseTimestamps = new HashMap<>();

        initializeFiles();
        loadShopItems();
        loadPurchaseHistory();
    }

    /**
     * 初始化文件
     */
    private void initializeFiles() {
        // 商店物品文件
        shopFile = new File(plugin.getDataFolder(), "points_shop.yml");
        boolean isNewFile = false;
        if (!shopFile.exists()) {
            try {
                shopFile.createNewFile();
                isNewFile = true;
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建积分商店文件: " + e.getMessage());
            }
        }
        shopConfig = YamlConfiguration.loadConfiguration(shopFile);

        // 如果是新文件，创建默认物品
        if (isNewFile) {
            createDefaultShopItems();
        }

        // 购买记录文件
        purchaseFile = new File(plugin.getDataFolder(), "points_purchases.yml");
        if (!purchaseFile.exists()) {
            try {
                purchaseFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建购买记录文件: " + e.getMessage());
            }
        }
        purchaseConfig = YamlConfiguration.loadConfiguration(purchaseFile);
    }

    /**
     * 创建默认商店物品
     */
    private void createDefaultShopItems() {
        // 创建一些默认的商店物品
        shopConfig.set("items.diamond_sword.name", "💎 钻石剑");
        shopConfig.set("items.diamond_sword.description", "锋利的钻石剑，战斗必备");
        shopConfig.set("items.diamond_sword.cost", 100);
        shopConfig.set("items.diamond_sword.icon", "💎");
        shopConfig.set("items.diamond_sword.enabled", true);
        shopConfig.set("items.diamond_sword.stock", -1);
        shopConfig.set("items.diamond_sword.max_purchase_per_player", -1);
        shopConfig.set("items.diamond_sword.commands", Arrays.asList(
                "give {player} diamond_sword 1",
                "tellraw {player} {\"text\":\"恭喜获得钻石剑！\",\"color\":\"green\"}"));

        shopConfig.set("items.gold_ingot.name", "⚔️ 金锭礼包");
        shopConfig.set("items.gold_ingot.description", "包含10个金锭的礼包");
        shopConfig.set("items.gold_ingot.cost", 50);
        shopConfig.set("items.gold_ingot.icon", "⚔️");
        shopConfig.set("items.gold_ingot.enabled", true);
        shopConfig.set("items.gold_ingot.stock", -1);
        shopConfig.set("items.gold_ingot.max_purchase_per_player", -1);
        shopConfig.set("items.gold_ingot.commands", Arrays.asList(
                "give {player} gold_ingot 10",
                "tellraw {player} {\"text\":\"恭喜获得金锭礼包！\",\"color\":\"gold\"}"));

        shopConfig.set("items.experience.name", "⭐ 经验礼包");
        shopConfig.set("items.experience.description", "获得1000点经验值");
        shopConfig.set("items.experience.cost", 30);
        shopConfig.set("items.experience.icon", "⭐");
        shopConfig.set("items.experience.enabled", true);
        shopConfig.set("items.experience.stock", -1);
        shopConfig.set("items.experience.max_purchase_per_player", 5);
        shopConfig.set("items.experience.reset_interval_hours", 24); // 每24小时重置一次
        shopConfig.set("items.experience.commands", Arrays.asList(
                "xp add {player} 1000",
                "tellraw {player} {\"text\":\"恭喜获得1000点经验！\",\"color\":\"yellow\"}"));

        try {
            shopConfig.save(shopFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存默认商店物品失败: " + e.getMessage());
        }
    }

    /**
     * 加载商店物品
     */
    private void loadShopItems() {
        shopItems.clear();
        ConfigurationSection itemsSection = shopConfig.getConfigurationSection("items");
        if (itemsSection != null) {
            for (String itemId : itemsSection.getKeys(false)) {
                ConfigurationSection itemSection = itemsSection.getConfigurationSection(itemId);
                if (itemSection != null) {
                    PointsShopItem item = new PointsShopItem();
                    item.setId(itemId);
                    item.setName(itemSection.getString("name", "未命名物品"));
                    item.setDescription(itemSection.getString("description", "无描述"));
                    item.setCost(itemSection.getInt("cost", 0));
                    item.setIcon(itemSection.getString("icon", "📦"));
                    item.setEnabled(itemSection.getBoolean("enabled", true));
                    item.setStock(itemSection.getInt("stock", -1));
                    item.setMaxPurchasePerPlayer(itemSection.getInt("max_purchase_per_player", -1));
                    item.setResetIntervalHours(itemSection.getInt("reset_interval_hours", -1));
                    item.setCommands(itemSection.getStringList("commands"));

                    shopItems.put(itemId, item);
                }
            }
        }
        plugin.getLogger().info("已加载 " + shopItems.size() + " 个积分商店物品");
    }

    /**
     * 加载购买记录
     */
    private void loadPurchaseHistory() {
        playerPurchaseHistory.clear();
        playerPurchaseTimestamps.clear();

        ConfigurationSection purchasesSection = purchaseConfig.getConfigurationSection("purchases");
        if (purchasesSection != null) {
            for (String key : purchasesSection.getKeys(false)) {
                try {
                    String playerName;

                    // 检查是否是UUID格式（旧数据）
                    if (key.contains("-") && key.length() == 36) {
                        // 旧格式：UUID，需要转换为玩家名
                        UUID playerUuid = UUID.fromString(key);
                        playerName = getPlayerNameFromUUID(playerUuid);
                        if (playerName == null) {
                            plugin.getLogger().warning("无法获取UUID " + key + " 对应的玩家名，跳过此记录");
                            continue;
                        }
                        plugin.getLogger().info("转换购买记录: " + key + " -> " + playerName);
                    } else {
                        // 新格式：玩家名
                        playerName = key;
                    }

                    ConfigurationSection playerSection = purchasesSection.getConfigurationSection(key);
                    if (playerSection != null) {
                        Map<String, Integer> purchases = new HashMap<>();
                        Map<String, String> timestamps = new HashMap<>();

                        for (String itemId : playerSection.getKeys(false)) {
                            ConfigurationSection itemSection = playerSection.getConfigurationSection(itemId);
                            if (itemSection != null) {
                                // 新格式：包含count和timestamp
                                purchases.put(itemId, itemSection.getInt("count", 0));

                                // 处理时间戳
                                String timestampStr = itemSection.getString("timestamp");
                                if (timestampStr != null && !timestampStr.isEmpty()) {
                                    // 检查是否是数字格式的时间戳
                                    try {
                                        long timestamp = Long.parseLong(timestampStr);
                                        timestamps.put(itemId, dateFormat.format(new Date(timestamp)));
                                    } catch (NumberFormatException e) {
                                        // 已经是可读格式
                                        timestamps.put(itemId, timestampStr);
                                    }
                                } else {
                                    // 使用当前时间
                                    timestamps.put(itemId, dateFormat.format(new Date()));
                                }
                            } else {
                                // 旧格式：只有数量
                                purchases.put(itemId, playerSection.getInt(itemId, 0));
                                timestamps.put(itemId, dateFormat.format(new Date()));
                            }
                        }

                        playerPurchaseHistory.put(playerName, purchases);
                        playerPurchaseTimestamps.put(playerName, timestamps);
                    }
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("无效的数据格式: " + key);
                }
            }
        }
    }

    /**
     * 从UUID获取玩家名
     */
    private String getPlayerNameFromUUID(UUID uuid) {
        // 先尝试在线玩家
        Player player = plugin.getServer().getPlayer(uuid);
        if (player != null) {
            return player.getName();
        }

        // 再尝试离线玩家
        try {
            OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(uuid);
            if (offlinePlayer.hasPlayedBefore()) {
                return offlinePlayer.getName();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法获取UUID " + uuid + " 对应的玩家名: " + e.getMessage());
        }

        return null;
    }

    /**
     * 保存商店物品
     */
    public void saveShopItems() {
        shopConfig.set("items", null); // 清空现有数据
        for (PointsShopItem item : shopItems.values()) {
            String path = "items." + item.getId();
            shopConfig.set(path + ".name", item.getName());
            shopConfig.set(path + ".description", item.getDescription());
            shopConfig.set(path + ".cost", item.getCost());
            shopConfig.set(path + ".icon", item.getIcon());
            shopConfig.set(path + ".enabled", item.isEnabled());
            shopConfig.set(path + ".stock", item.getStock());
            shopConfig.set(path + ".max_purchase_per_player", item.getMaxPurchasePerPlayer());
            shopConfig.set(path + ".reset_interval_hours", item.getResetIntervalHours());
            shopConfig.set(path + ".commands", item.getCommands());
        }
        try {
            shopConfig.save(shopFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存商店物品失败: " + e.getMessage());
        }
    }

    /**
     * 保存购买记录
     */
    public void savePurchaseHistory() {
        purchaseConfig.set("purchases", null); // 清空现有数据
        for (Map.Entry<String, Map<String, Integer>> entry : playerPurchaseHistory.entrySet()) {
            String playerName = entry.getKey();
            Map<String, String> timestamps = playerPurchaseTimestamps.get(entry.getKey());

            for (Map.Entry<String, Integer> purchase : entry.getValue().entrySet()) {
                String itemId = purchase.getKey();
                String path = "purchases." + playerName + "." + itemId;

                purchaseConfig.set(path + ".count", purchase.getValue());
                if (timestamps != null && timestamps.containsKey(itemId)) {
                    purchaseConfig.set(path + ".timestamp", timestamps.get(itemId));
                } else {
                    purchaseConfig.set(path + ".timestamp", dateFormat.format(new Date()));
                }
            }
        }
        try {
            purchaseConfig.save(purchaseFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存购买记录失败: " + e.getMessage());
        }
    }

    /**
     * 获取所有商店物品
     */
    public Map<String, PointsShopItem> getAllItems() {
        return new HashMap<>(shopItems);
    }

    /**
     * 获取启用的商店物品
     */
    public Map<String, PointsShopItem> getEnabledItems() {
        Map<String, PointsShopItem> enabledItems = new HashMap<>();
        for (Map.Entry<String, PointsShopItem> entry : shopItems.entrySet()) {
            if (entry.getValue().isEnabled()) {
                enabledItems.put(entry.getKey(), entry.getValue());
            }
        }
        return enabledItems;
    }

    /**
     * 获取商店物品
     */
    public PointsShopItem getItem(String itemId) {
        return shopItems.get(itemId);
    }

    /**
     * 添加或更新商店物品
     */
    public void addOrUpdateItem(PointsShopItem item) {
        shopItems.put(item.getId(), item);
        saveShopItems();
    }

    /**
     * 删除商店物品
     */
    public boolean removeItem(String itemId) {
        if (shopItems.containsKey(itemId)) {
            shopItems.remove(itemId);
            saveShopItems();
            return true;
        }
        return false;
    }

    /**
     * 购买物品（单个）
     */
    public PurchaseResult purchaseItem(String playerName, String itemId) {
        return purchaseItem(playerName, itemId, 1);
    }

    /**
     * 购买物品（指定数量）
     */
    public PurchaseResult purchaseItem(String playerName, String itemId, int quantity) {
        if (quantity <= 0) {
            return new PurchaseResult(false, "购买数量必须大于0");
        }

        Player player = Bukkit.getPlayer(playerName);
        boolean isOnline = player != null;

        PointsShopItem item = shopItems.get(itemId);
        if (item == null) {
            return new PurchaseResult(false, "物品不存在");
        }

        if (!item.isEnabled()) {
            return new PurchaseResult(false, "物品已禁用");
        }

        // 检查库存是否足够
        if (item.getStock() != -1 && item.getStock() < quantity) {
            return new PurchaseResult(false, "库存不足，当前库存: " + item.getStock() + "，需要: " + quantity);
        }

        // 获取玩家UUID（支持离线玩家）
        UUID playerUuid = null;
        if (isOnline) {
            playerUuid = player.getUniqueId();
        } else {
            // 尝试从缓存或数据库获取UUID
            playerUuid = getPlayerUUIDFromName(playerName);
            if (playerUuid == null) {
                return new PurchaseResult(false, "无法找到玩家 " + playerName + " 的数据");
            }
        }

        // 检查玩家积分
        PointsManager pointsManager = plugin.getPointsManager();
        int playerPoints = pointsManager.getPlayerPoints(playerUuid);
        int totalCost = item.getCost() * quantity;
        if (playerPoints < totalCost) {
            return new PurchaseResult(false, "积分不足，需要 " + totalCost + " 积分，当前只有 " + playerPoints + " 积分");
        }

        // 检查购买限制
        if (item.getMaxPurchasePerPlayer() > 0) {
            int purchased = getPlayerPurchaseCount(playerUuid, itemId);
            if (purchased + quantity > item.getMaxPurchasePerPlayer()) {
                int remaining = item.getMaxPurchasePerPlayer() - purchased;
                return new PurchaseResult(false, "购买数量超过限制，最多还能购买 " + remaining + " 个");
            }
        }

        // 扣除积分
        if (!pointsManager.deductPlayerPoints(playerUuid, totalCost)) {
            return new PurchaseResult(false, "扣除积分失败");
        }

        // 减少库存
        if (item.getStock() != -1) {
            item.setStock(item.getStock() - quantity);
        }

        // 记录购买（按数量记录）
        for (int i = 0; i < quantity; i++) {
            recordPurchase(playerUuid, itemId);
        }

        // 处理命令执行
        if (item.hasCommands()) {
            if (isOnline) {
                // 玩家在线，直接执行命令
                for (int i = 0; i < quantity; i++) {
                    for (String command : item.getCommands()) {
                        String processedCommand = command.replace("{player}", playerName);
                        Bukkit.getScheduler().runTask(plugin, () -> {
                            boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
                            if (!success) {
                                plugin.getLogger().warning("执行购买命令失败: " + processedCommand);
                            }
                        });
                    }
                }

                // 发送游戏内购买成功消息
                Bukkit.getScheduler().runTask(plugin, () -> {
                    if (quantity == 1) {
                        player.sendMessage("§a[积分商店] §f您购买的 §e" + item.getName() + " §f已发放！");
                    } else {
                        player.sendMessage("§a[积分商店] §f您购买的 §e" + item.getName() + " §fx" + quantity + " §f已发放！");
                    }
                });
            } else {
                // 玩家离线，添加到待处理购买
                PendingPurchaseManager pendingManager = plugin.getPendingPurchaseManager();
                if (pendingManager != null) {
                    pendingManager.addPendingPurchase(playerName, itemId, item.getName(), quantity, item.getCommands());
                }
            }
        }

        saveShopItems();
        savePurchaseHistory();

        String message;
        if (isOnline) {
            message = quantity == 1 ? "购买成功！" : "购买成功！数量: " + quantity;
        } else {
            message = quantity == 1 ? "购买成功！奖励将在您上线时发放。" : "购买成功！数量: " + quantity + "，奖励将在您上线时发放。";
        }
        return new PurchaseResult(true, message);
    }

    /**
     * 获取玩家购买次数（通过UUID）
     */
    public int getPlayerPurchaseCount(UUID playerUuid, String itemId) {
        // 转换UUID为玩家名
        String playerName = getPlayerNameFromUUID(playerUuid);
        if (playerName == null) {
            return 0;
        }
        return getPlayerPurchaseCount(playerName, itemId);
    }

    /**
     * 获取玩家购买次数（通过玩家名）
     */
    public int getPlayerPurchaseCount(String playerName, String itemId) {
        // 先检查是否需要重置
        checkAndResetPlayerPurchase(playerName, itemId);

        Map<String, Integer> purchases = playerPurchaseHistory.get(playerName);
        if (purchases != null) {
            return purchases.getOrDefault(itemId, 0);
        }
        return 0;
    }

    /**
     * 记录购买（通过UUID）
     */
    private void recordPurchase(UUID playerUuid, String itemId) {
        // 转换UUID为玩家名
        String playerName = getPlayerNameFromUUID(playerUuid);
        if (playerName != null) {
            recordPurchase(playerName, itemId);
        }
    }

    /**
     * 记录购买（通过玩家名）
     */
    private void recordPurchase(String playerName, String itemId) {
        playerPurchaseHistory.computeIfAbsent(playerName, k -> new HashMap<>());
        playerPurchaseTimestamps.computeIfAbsent(playerName, k -> new HashMap<>());

        Map<String, Integer> purchases = playerPurchaseHistory.get(playerName);
        Map<String, String> timestamps = playerPurchaseTimestamps.get(playerName);

        purchases.put(itemId, purchases.getOrDefault(itemId, 0) + 1);
        timestamps.put(itemId, dateFormat.format(new Date()));
    }

    /**
     * 检查并重置玩家购买记录（通过玩家名）
     */
    private void checkAndResetPlayerPurchase(String playerName, String itemId) {
        PointsShopItem item = shopItems.get(itemId);
        if (item == null || item.getResetIntervalHours() <= 0) {
            return; // 物品不存在或不需要重置
        }

        Map<String, String> timestamps = playerPurchaseTimestamps.get(playerName);
        if (timestamps == null || !timestamps.containsKey(itemId)) {
            return; // 没有购买记录
        }

        String lastPurchaseTimeStr = timestamps.get(itemId);
        try {
            Date lastPurchaseDate = dateFormat.parse(lastPurchaseTimeStr);
            long lastPurchaseTime = lastPurchaseDate.getTime();
            long currentTime = System.currentTimeMillis();
            long resetInterval = item.getResetIntervalHours() * 60 * 60 * 1000L; // 转换为毫秒

            if (currentTime - lastPurchaseTime >= resetInterval) {
                // 重置购买记录
                Map<String, Integer> purchases = playerPurchaseHistory.get(playerName);
                if (purchases != null) {
                    purchases.remove(itemId);
                    if (purchases.isEmpty()) {
                        playerPurchaseHistory.remove(playerName);
                    }
                }

                timestamps.remove(itemId);
                if (timestamps.isEmpty()) {
                    playerPurchaseTimestamps.remove(playerName);
                }

                // 保存更改
                savePurchaseHistory();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("解析购买时间失败: " + lastPurchaseTimeStr + ", 错误: " + e.getMessage());
        }
    }

    /**
     * 获取玩家购买记录的下次重置时间（通过UUID）
     */
    public long getNextResetTime(UUID playerUuid, String itemId) {
        // 转换UUID为玩家名
        String playerName = getPlayerNameFromUUID(playerUuid);
        if (playerName == null) {
            return -1;
        }
        return getNextResetTime(playerName, itemId);
    }

    /**
     * 获取玩家购买记录的下次重置时间（通过玩家名）
     */
    public long getNextResetTime(String playerName, String itemId) {
        PointsShopItem item = shopItems.get(itemId);
        if (item == null || item.getResetIntervalHours() <= 0) {
            return -1; // 永不重置
        }

        Map<String, String> timestamps = playerPurchaseTimestamps.get(playerName);
        if (timestamps == null || !timestamps.containsKey(itemId)) {
            return -1; // 没有购买记录
        }

        String lastPurchaseTimeStr = timestamps.get(itemId);
        try {
            Date lastPurchaseDate = dateFormat.parse(lastPurchaseTimeStr);
            long lastPurchaseTime = lastPurchaseDate.getTime();
            long resetInterval = item.getResetIntervalHours() * 60 * 60 * 1000L; // 转换为毫秒
            return lastPurchaseTime + resetInterval;
        } catch (Exception e) {
            plugin.getLogger().warning("解析购买时间失败: " + lastPurchaseTimeStr + ", 错误: " + e.getMessage());
            return -1;
        }
    }

    /**
     * 获取玩家剩余购买次数
     */
    public int getRemainingPurchases(UUID playerUuid, String itemId) {
        PointsShopItem item = shopItems.get(itemId);
        if (item == null || item.getMaxPurchasePerPlayer() <= 0) {
            return -1; // 无限制
        }

        int purchased = getPlayerPurchaseCount(playerUuid, itemId);
        return Math.max(0, item.getMaxPurchasePerPlayer() - purchased);
    }

    /**
     * 重新加载
     */
    public void reload() {
        shopConfig = YamlConfiguration.loadConfiguration(shopFile);
        purchaseConfig = YamlConfiguration.loadConfiguration(purchaseFile);
        loadShopItems();
        loadPurchaseHistory();
    }

    /**
     * 获取玩家UUID（支持离线玩家）
     */
    private UUID getPlayerUUIDFromName(String playerName) {
        // 首先尝试从在线玩家获取
        Player onlinePlayer = Bukkit.getPlayer(playerName);
        if (onlinePlayer != null) {
            return onlinePlayer.getUniqueId();
        }

        // 尝试从Bukkit的离线玩家API获取
        try {
            OfflinePlayer offlinePlayer = Bukkit.getOfflinePlayer(playerName);
            if (offlinePlayer != null && offlinePlayer.hasPlayedBefore()) {
                return offlinePlayer.getUniqueId();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("获取离线玩家 " + playerName + " 的UUID失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 购买结果类
     */
    public static class PurchaseResult {
        private final boolean success;
        private final String message;

        public PurchaseResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }
}
