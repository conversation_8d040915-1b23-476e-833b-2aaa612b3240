package cn.acebrand.acekeysystem.task;

import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 定时同步任务
 * 定期自动同步卡密到网站
 */
public final class ScheduledSyncTask extends BukkitRunnable {

    private final AceKeySystem plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ScheduledSyncTask(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void run() {
        // 执行定时卡密同步
        this.plugin.syncKeysToWebsite();
    }
}
