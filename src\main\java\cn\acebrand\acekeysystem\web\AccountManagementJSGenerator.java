package cn.acebrand.acekeysystem.web;

/**
 * 账号管理JavaScript代码生成器
 * 负责生成账号管理相关的JavaScript功能
 */
public class AccountManagementJSGenerator {
    private final String adminKey;

    public AccountManagementJSGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成账号管理JavaScript代码
     */
    public String generateAccountManagementJS() {
        return generateVariablesAndInit() +
                generateLoadAndDisplayFunctions() +
                generateModalFunctions() +
                generateFormHandlers() +
                generateCRUDFunctions() +
                generateRoleManagementFunctions() +
                generateLogoutJS();
    }

    /**
     * 生成变量定义和初始化代码
     */
    private String generateVariablesAndInit() {
        return "\n// ==================== 账号管理功能 ====================\n" +
                "let allAccounts = [];\n" +
                "let filteredAccounts = [];\n" +
                "let allRoles = [];\n" +
                "let filteredRoles = [];\n" +
                "\n" +
                "// 页面加载时初始化账号管理\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    if (document.getElementById('accountsContainer')) {\n" +
                "        loadAccounts();\n" +
                "        loadRoles();\n" +
                "        setupAccountForms();\n" +
                "        setupRoleForms();\n" +
                "    }\n" +
                "});\n" +
                "\n" +
                "// 标签页切换功能\n" +
                "function switchTab(tabName) {\n" +
                "    // 隐藏所有标签页内容\n" +
                "    const tabContents = document.querySelectorAll('.account-tab-content');\n" +
                "    tabContents.forEach(tab => tab.classList.remove('active'));\n" +
                "    \n" +
                "    // 移除所有标签按钮的激活状态\n" +
                "    const tabBtns = document.querySelectorAll('.tab-btn');\n" +
                "    tabBtns.forEach(btn => btn.classList.remove('active'));\n" +
                "    \n" +
                "    // 显示选中的标签页\n" +
                "    const targetTab = document.getElementById(tabName + 'Tab');\n" +
                "    if (targetTab) {\n" +
                "        targetTab.classList.add('active');\n" +
                "    }\n" +
                "    \n" +
                "    // 激活对应的标签按钮\n" +
                "    const targetBtn = event.target;\n" +
                "    if (targetBtn) {\n" +
                "        targetBtn.classList.add('active');\n" +
                "    }\n" +
                "}\n\n";
    }

    /**
     * 生成加载和显示函数
     */
    private String generateLoadAndDisplayFunctions() {
        return "// 加载账号列表\n" +
                "function loadAccounts() {\n" +
                "    fetch('/admin/accounts?action=list&key=" + adminKey + "')\n" +
                "        .then(response => response.json())\n" +
                "        .then(data => {\n" +
                "            if (data.success) {\n" +
                "                allAccounts = data.accounts;\n" +
                "                filteredAccounts = [...allAccounts];\n" +
                "                displayAccounts();\n" +
                "                updateAccountCount();\n" +
                "            } else {\n" +
                "                showMessage('加载账号列表失败: ' + data.message, 'error');\n" +
                "            }\n" +
                "        })\n" +
                "        .catch(error => {\n" +
                "            console.error('加载账号列表出错:', error);\n" +
                "            showMessage('加载账号列表出错', 'error');\n" +
                "        });\n" +
                "}\n" +
                "\n" +
                "// 显示账号列表\n" +
                "function displayAccounts() {\n" +
                "    const container = document.getElementById('accountsContainer');\n" +
                "    if (!container) return;\n" +
                "\n" +
                "    if (filteredAccounts.length === 0) {\n" +
                "        container.innerHTML = '<div class=\"no-data\">暂无账号数据</div>';\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    let html = '<div class=\"accounts-grid\">';\n" +
                "    filteredAccounts.forEach(account => {\n" +
                "        const createdDate = new Date(account.created_time).toLocaleDateString();\n" +
                "        const lastLoginDate = account.last_login > 0 ? new Date(account.last_login).toLocaleDateString() + ' ' + new Date(account.last_login).toLocaleTimeString() : '从未登录';\n"
                +
                "        const roleText = account.role === 'super_admin' ? '超级管理员' : '管理员';\n" +
                "        \n" +
                "        // 处理封禁状态\n" +
                "        const banInfo = account.banInfo || {};\n" +
                "        const isBanned = banInfo.banned || false;\n" +
                "        let statusClass, statusText;\n" +
                "        \n" +
                "        if (isBanned) {\n" +
                "            statusClass = 'banned';\n" +
                "            const banEndTime = banInfo.ban_end_time;\n" +
                "            if (banEndTime === -1) {\n" +
                "                statusText = '永久封禁';\n" +
                "            } else {\n" +
                "                const endDate = new Date(banEndTime);\n" +
                "                statusText = '封禁至 ' + endDate.toLocaleDateString();\n" +
                "            }\n" +
                "        } else {\n" +
                "            statusClass = account.enabled ? 'enabled' : 'disabled';\n" +
                "            statusText = account.enabled ? '启用' : '禁用';\n" +
                "        }\n" +
                "\n" +
                "        // 处理IP信息\n" +
                "        const ipInfo = account.ipInfo || {};\n" +
                "        const activeSessions = ipInfo.activeSessions || {};\n" +
                "        const totalSessions = activeSessions.totalSessions || 0;\n" +
                "        const uniqueIPs = activeSessions.uniqueIPs || 0;\n" +
                "        const currentIP = ipInfo.currentIP || (totalSessions > 0 ? '未知' : '未登录');\n" +
                "        const ipCounts = activeSessions.ipCounts || {};\n" +
                "\n" +
                "        // 生成IP列表\n" +
                "        let ipListHtml = '';\n" +
                "        if (uniqueIPs > 0) {\n" +
                "            const ipEntries = Object.entries(ipCounts);\n" +
                "            ipListHtml = ipEntries.map(([ip, count]) => \n" +
                "                `<span class=\"ip-tag\" title=\"${count}个会话\">${ip}</span>`\n" +
                "            ).join('');\n" +
                "        } else {\n" +
                "            ipListHtml = '<span class=\"no-login-info\">暂无登录记录</span>';\n" +
                "        }\n" +
                "\n" +
                "        // 计算权限数量\n" +
                "        let permissionCount = 0;\n" +
                "        let totalPermissions = 10; // 总权限数量\n" +
                "        if (account.permissions && typeof account.permissions === 'object') {\n" +
                "            permissionCount = Object.values(account.permissions).filter(p => p === true).length;\n" +
                "        }\n" +
                "\n" +
                "        // 多IP警告\n" +
                "        const multiIPWarning = uniqueIPs > 1 ? '<span class=\"multi-ip-warning\" title=\"检测到多IP登录\">⚠️</span>' : '';\n"
                +
                "\n" +
                "        html += `\n" +
                "            <div class=\"account-card ${statusClass}\">\n" +
                "                <div class=\"account-header\">\n" +
                "                    <h4>${account.username} ${multiIPWarning}</h4>\n" +
                "                    <span class=\"account-role ${account.role}\">${roleText}</span>\n" +
                "                </div>\n" +
                "                <div class=\"account-info\">\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">状态:</span>\n" +
                "                        <span class=\"account-status ${statusClass}\">${statusText}</span>\n" +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">创建时间:</span>\n" +
                "                        <span class=\"value\">${createdDate}</span>\n" +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">最后登录:</span>\n" +
                "                        <span class=\"value\">${lastLoginDate}</span>\n" +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">当前IP:</span>\n" +
                "                        <span class=\"value ip-address\">${currentIP}</span>\n" +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">活跃会话:</span>\n" +
                "                        <span class=\"value\">${totalSessions}个 (${uniqueIPs}个IP)</span>\n" +
                "                    </div>\n" +
                "                    <div class=\"info-item ip-list\">\n" +
                "                        <span class=\"label\">登录IP:</span>\n" +
                "                        <div class=\"ip-tags\">${ipListHtml}</div>\n" +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">权限数量:</span>\n" +
                "                        <span class=\"value permission-count\">\n" +
                "                            <span class=\"permission-stats\">${permissionCount}/${totalPermissions}</span>\n"
                +
                "                            <span class=\"permission-percentage\">(${Math.round((permissionCount/totalPermissions)*100)}%)</span>\n"
                +
                "                        </span>\n" +
                "                    </div>\n" +
                "                    ${isBanned ? `\n" +
                "                    <div class=\"info-item ban-info\">\n" +
                "                        <span class=\"label\">封禁原因:</span>\n" +
                "                        <span class=\"value ban-reason\">${banInfo.reason || '未知原因'}</span>\n" +
                "                    </div>\n" +
                "                    ${banInfo.custom_reason ? `\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">详细说明:</span>\n" +
                "                        <span class=\"value\">${banInfo.custom_reason}</span>\n" +
                "                    </div>\n" +
                "                    ` : ''}\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">封禁时间:</span>\n" +
                "                        <span class=\"value\">${new Date(banInfo.ban_time).toLocaleString()}</span>\n"
                +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">解封时间:</span>\n" +
                "                        <span class=\"value\">${banInfo.ban_end_time === -1 ? '永不解封' : new Date(banInfo.ban_end_time).toLocaleString()}</span>\n"
                +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">封禁时长:</span>\n" +
                "                        <span class=\"value\">${banInfo.ban_end_time === -1 ? '永久' : Math.ceil((banInfo.ban_end_time - banInfo.ban_time) / (1000 * 60 * 60)) + '小时'}</span>\n"
                +
                "                    </div>\n" +
                "                    <div class=\"info-item\">\n" +
                "                        <span class=\"label\">执行人:</span>\n" +
                "                        <span class=\"value\">${banInfo.banned_by || '未知'}</span>\n" +
                "                    </div>\n" +
                "                    ` : ''}\n" +
                "                </div>\n" +
                "                <div class=\"account-actions\">\n" +
                "                    <div class=\"action-row\">\n" +
                "                        <button onclick=\"editAccount('${account.username}', '${account.role}', '${account.permissions ? JSON.stringify(account.permissions).replace(/'/g, '&apos;').replace(/\"/g, '&quot;') : '{}'}')\" class=\"btn btn-sm btn-secondary\">编辑</button>\n"
                +
                "                        <button onclick=\"changeAccountPassword('${account.username}')\" class=\"btn btn-sm btn-info\">改密</button>\n"
                +
                "                        <button onclick=\"showIPManagement('${account.username}')\" class=\"btn btn-sm btn-warning\">IP管理</button>\n"
                +
                "                    </div>\n" +
                "                    <div class=\"action-row\">\n" +
                "                        ${isBanned ? `<button onclick=\"unbanAccount('${account.username}')\" class=\"btn btn-sm btn-success\">解封</button>` : `<button onclick=\"showBanAccountModal('${account.username}')\" class=\"btn btn-sm btn-danger\">封禁</button>`}\n"
                +
                "                        <button onclick=\"toggleAccount('${account.username}', ${!account.enabled})\" class=\"btn btn-sm ${account.enabled ? 'btn-warning' : 'btn-success'}\">${account.enabled ? '禁用' : '启用'}</button>\n"
                +
                "                        <button onclick=\"deleteAccount('${account.username}')\" class=\"btn btn-sm btn-danger\">删除</button>\n"
                +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "        `;\n" +
                "    });\n" +
                "    html += '</div>';\n" +
                "    container.innerHTML = html;\n" +
                "}\n" +
                "\n" +
                "// 更新账号数量\n" +
                "function updateAccountCount() {\n" +
                "    const countElement = document.getElementById('totalAccountsCount');\n" +
                "    if (countElement) {\n" +
                "        countElement.textContent = allAccounts.length;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 搜索过滤账号\n" +
                "function filterAccounts() {\n" +
                "    const searchInput = document.getElementById('accountSearchInput');\n" +
                "    if (!searchInput) return;\n" +
                "\n" +
                "    const searchTerm = searchInput.value.toLowerCase().trim();\n" +
                "    if (searchTerm === '') {\n" +
                "        filteredAccounts = [...allAccounts];\n" +
                "    } else {\n" +
                "        filteredAccounts = allAccounts.filter(account => \n" +
                "            account.username.toLowerCase().includes(searchTerm)\n" +
                "        );\n" +
                "    }\n" +
                "    displayAccounts();\n" +
                "}\n" +
                "\n" +
                "// 刷新账号列表\n" +
                "function refreshAccounts() {\n" +
                "    loadAccounts();\n" +
                "    showMessage('账号列表已刷新', 'success');\n" +
                "}\n\n";
    }

    /**
     * 生成弹窗相关函数
     */
    private String generateModalFunctions() {
        return "// 显示创建账号弹窗\n" +
                "function showCreateAccountModal() {\n" +
                "    const modal = document.getElementById('createAccountModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'block';\n" +
                "        document.getElementById('newUsername').focus();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭创建账号弹窗\n" +
                "function closeCreateAccountModal() {\n" +
                "    const modal = document.getElementById('createAccountModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'none';\n" +
                "        document.getElementById('createAccountForm').reset();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 编辑账号\n" +
                "function editAccount(username, role, permissionsStr) {\n" +
                "    try {\n" +
                "        // 检查必需的DOM元素是否存在\n" +
                "        const usernameInput = document.getElementById('editUsername');\n" +
                "        const roleSelect = document.getElementById('editRole');\n" +
                "        const modal = document.getElementById('editAccountModal');\n" +
                "        \n" +
                "        if (!usernameInput || !roleSelect || !modal) {\n" +
                "            console.error('编辑账号弹窗的必需元素不存在');\n" +
                "            showMessage('编辑账号功能初始化失败，请刷新页面重试', 'error');\n" +
                "            return;\n" +
                "        }\n" +
                "        \n" +
                "        // 设置基本信息\n" +
                "        usernameInput.value = username || '';\n" +
                "        roleSelect.value = role || 'admin';\n" +
                "        \n" +
                "        // 根据角色自动设置权限（忽略传入的权限参数）\n" +
                "        onRoleChange('edit');\n" +
                "        \n" +
                "        // 显示弹窗\n" +
                "        modal.style.display = 'block';\n" +
                "        \n" +
                "    } catch (error) {\n" +
                "        console.error('编辑账号功能出错:', error);\n" +
                "        showMessage('编辑账号功能出错: ' + error.message, 'error');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭编辑账号弹窗\n" +
                "function closeEditAccountModal() {\n" +
                "    const modal = document.getElementById('editAccountModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'none';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 修改账号密码\n" +
                "function changeAccountPassword(username) {\n" +
                "    document.getElementById('changePasswordUsername').value = username;\n" +
                "    document.getElementById('newPasswordInput').value = '';\n" +
                "    document.getElementById('confirmPasswordInput').value = '';\n" +
                "    \n" +
                "    const modal = document.getElementById('changePasswordModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'block';\n" +
                "        document.getElementById('newPasswordInput').focus();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭修改密码弹窗\n" +
                "function closeChangePasswordModal() {\n" +
                "    const modal = document.getElementById('changePasswordModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'none';\n" +
                "        document.getElementById('changePasswordForm').reset();\n" +
                "    }\n" +
                "}\n\n";
    }

    /**
     * 生成表单处理函数
     */
    private String generateFormHandlers() {
        return "// 设置表单事件\n" +
                "function setupAccountForms() {\n" +
                "    // 创建账号表单\n" +
                "    const createForm = document.getElementById('createAccountForm');\n" +
                "    if (createForm) {\n" +
                "        createForm.addEventListener('submit', function(e) {\n" +
                "            e.preventDefault();\n" +
                "            createAccount();\n" +
                "        });\n" +
                "    }\n" +
                "\n" +
                "    // 编辑账号表单\n" +
                "    const editForm = document.getElementById('editAccountForm');\n" +
                "    if (editForm) {\n" +
                "        editForm.addEventListener('submit', function(e) {\n" +
                "            e.preventDefault();\n" +
                "            updateAccount();\n" +
                "        });\n" +
                "    }\n" +
                "\n" +
                "    // 修改密码表单\n" +
                "    const passwordForm = document.getElementById('changePasswordForm');\n" +
                "    if (passwordForm) {\n" +
                "        passwordForm.addEventListener('submit', function(e) {\n" +
                "            e.preventDefault();\n" +
                "            submitPasswordChange();\n" +
                "        });\n" +
                "    }\n" +
                "}\n\n";
    }

    /**
     * 生成CRUD操作函数
     */
    private String generateCRUDFunctions() {
        return "// 创建账号\n" +
                "function createAccount() {\n" +
                "    const username = document.getElementById('newUsername').value.trim();\n" +
                "    const password = document.getElementById('newPassword').value;\n" +
                "    const role = document.getElementById('newRole').value;\n" +
                "\n" +
                "    if (!username || !password) {\n" +
                "        showMessage('请填写完整信息', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    // 根据角色获取权限配置\n" +
                "    let permissions = {};\n" +
                "    if (role === 'admin') {\n" +
                "        permissions = {\n" +
                "            punishment: false,\n" +
                "            accountManagement: false,\n" +
                "            systemSettings: false,\n" +
                "            interfaceSettings: false,\n" +
                "            dataView: true,\n" +
                "            keyManagement: false,\n" +
                "            rewardManagement: false,\n" +
                "            pointsShop: false,\n" +
                "            winnersView: false,\n" +
                "            statisticsView: false\n" +
                "        };\n" +
                "    } else if (role === 'super_admin') {\n" +
                "        permissions = {\n" +
                "            punishment: true,\n" +
                "            accountManagement: true,\n" +
                "            systemSettings: true,\n" +
                "            interfaceSettings: true,\n" +
                "            dataView: true,\n" +
                "            keyManagement: true,\n" +
                "            rewardManagement: true,\n" +
                "            pointsShop: true,\n" +
                "            winnersView: true,\n" +
                "            statisticsView: true\n" +
                "        };\n" +
                "    } else {\n" +
                "        // 查找自定义角色权限\n" +
                "        const customRole = allRoles.find(r => r.id === role);\n" +
                "        if (customRole) {\n" +
                "            permissions = customRole.permissions;\n" +
                "        } else {\n" +
                "            showMessage('未找到角色配置', 'error');\n" +
                "            return;\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "    const data = {\n" +
                "        action: 'create',\n" +
                "        username: username,\n" +
                "        password: password,\n" +
                "        role: role,\n" +
                "        permissions: permissions\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeCreateAccountModal();\n" +
                "            loadAccounts();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('创建账号出错:', error);\n" +
                "        showMessage('创建账号出错', 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新账号\n" +
                "function updateAccount() {\n" +
                "    const username = document.getElementById('editUsername').value;\n" +
                "    const role = document.getElementById('editRole').value;\n" +
                "\n" +
                "    // 根据角色获取权限配置\n" +
                "    let permissions = {};\n" +
                "    if (role === 'admin') {\n" +
                "        permissions = {\n" +
                "            punishment: false,\n" +
                "            accountManagement: false,\n" +
                "            systemSettings: false,\n" +
                "            interfaceSettings: false,\n" +
                "            dataView: true,\n" +
                "            keyManagement: false,\n" +
                "            rewardManagement: false,\n" +
                "            pointsShop: false,\n" +
                "            winnersView: false,\n" +
                "            statisticsView: false\n" +
                "        };\n" +
                "    } else if (role === 'super_admin') {\n" +
                "        permissions = {\n" +
                "            punishment: true,\n" +
                "            accountManagement: true,\n" +
                "            systemSettings: true,\n" +
                "            interfaceSettings: true,\n" +
                "            dataView: true,\n" +
                "            keyManagement: true,\n" +
                "            rewardManagement: true,\n" +
                "            pointsShop: true,\n" +
                "            winnersView: true,\n" +
                "            statisticsView: true\n" +
                "        };\n" +
                "    } else {\n" +
                "        // 查找自定义角色权限\n" +
                "        const customRole = allRoles.find(r => r.id === role);\n" +
                "        if (customRole) {\n" +
                "            permissions = customRole.permissions;\n" +
                "        } else {\n" +
                "            showMessage('未找到角色配置', 'error');\n" +
                "            return;\n" +
                "        }\n" +
                "    }\n" +
                "\n" +
                "    const data = {\n" +
                "        action: 'update',\n" +
                "        username: username,\n" +
                "        role: role,\n" +
                "        permissions: permissions\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeEditAccountModal();\n" +
                "            loadAccounts();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('更新账号出错:', error);\n" +
                "        showMessage('更新账号出错', 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 删除账号\n" +
                "function deleteAccount(username) {\n" +
                "    showConfirmModal(\n" +
                "        '确认删除',\n" +
                "        `您确定要删除账号 \"${username}\" 吗？此操作不可撤销！`,\n" +
                "        function() {\n" +
                "            const data = {\n" +
                "                action: 'delete',\n" +
                "                username: username\n" +
                "            };\n" +
                "\n" +
                "            fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                },\n" +
                "                body: JSON.stringify(data)\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                if (data.success) {\n" +
                "                    showMessage(data.message, 'success');\n" +
                "                    loadAccounts();\n" +
                "                } else {\n" +
                "                    showMessage(data.message, 'error');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('删除账号出错:', error);\n" +
                "                showMessage('删除账号出错', 'error');\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n" +
                "\n" +
                "// 切换账号状态\n" +
                "function toggleAccount(username, enabled) {\n" +
                "    const action = enabled ? '启用' : '禁用';\n" +
                "    showConfirmModal(\n" +
                "        `确认${action}`,\n" +
                "        `您确定要${action}账号 \"${username}\" 吗？`,\n" +
                "        function() {\n" +
                "            const data = {\n" +
                "                action: 'toggle',\n" +
                "                username: username,\n" +
                "                enabled: enabled\n" +
                "            };\n" +
                "\n" +
                "            fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                },\n" +
                "                body: JSON.stringify(data)\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                if (data.success) {\n" +
                "                    showMessage(data.message, 'success');\n" +
                "                    loadAccounts();\n" +
                "                } else {\n" +
                "                    showMessage(data.message, 'error');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('切换账号状态出错:', error);\n" +
                "                showMessage('切换账号状态出错', 'error');\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n" +
                "\n" +
                "// 提交密码修改\n" +
                "function submitPasswordChange() {\n" +
                "    const username = document.getElementById('changePasswordUsername').value;\n" +
                "    const newPassword = document.getElementById('newPasswordInput').value;\n" +
                "    const confirmPassword = document.getElementById('confirmPasswordInput').value;\n" +
                "\n" +
                "    if (newPassword !== confirmPassword) {\n" +
                "        showMessage('两次输入的密码不一致', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    if (newPassword.length < 6) {\n" +
                "        showMessage('密码长度不能少于6位', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    const data = {\n" +
                "        action: 'change_password',\n" +
                "        username: username,\n" +
                "        new_password: newPassword\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeChangePasswordModal();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('修改密码出错:', error);\n" +
                "        showMessage('修改密码出错', 'error');\n" +
                "    });\n" +
                "}\n\n";
    }

    /**
     * 生成角色管理功能
     */
    private String generateRoleManagementFunctions() {
        return "// ==================== 角色管理功能 ====================\n" +
                "// 加载角色列表\n" +
                "function loadRoles() {\n" +
                "    fetch('/admin/accounts?action=roles&key=" + adminKey + "')\n" +
                "        .then(response => response.json())\n" +
                "        .then(data => {\n" +
                "            if (data.success) {\n" +
                "                allRoles = data.roles;\n" +
                "                filteredRoles = [...allRoles];\n" +
                "                displayRoles();\n" +
                "                updateRoleCount();\n" +
                "                updateRoleSelects();\n" +
                "            } else {\n" +
                "                showMessage('加载角色列表失败: ' + data.message, 'error');\n" +
                "            }\n" +
                "        })\n" +
                "        .catch(error => {\n" +
                "            console.error('加载角色列表出错:', error);\n" +
                "            showMessage('加载角色列表出错', 'error');\n" +
                "        });\n" +
                "}\n" +
                "\n" +
                "// 显示角色列表\n" +
                "function displayRoles() {\n" +
                "    const container = document.getElementById('rolesContainer');\n" +
                "    if (!container) return;\n" +
                "\n" +
                "    if (filteredRoles.length === 0) {\n" +
                "        container.innerHTML = '<div class=\"no-data\">暂无角色数据</div>';\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    let html = '<div class=\"roles-grid\">';\n" +
                "    filteredRoles.forEach(role => {\n" +
                "        const permissionsList = [];\n" +
                "        if (role.permissions.punishment) permissionsList.push('封禁管理');\n" +
                "        if (role.permissions.accountManagement) permissionsList.push('账号管理');\n" +
                "        if (role.permissions.systemSettings) permissionsList.push('系统设置');\n" +
                "        if (role.permissions.interfaceSettings) permissionsList.push('界面设置');\n" +
                "        if (role.permissions.dataView) permissionsList.push('数据查看');\n" +
                "        if (role.permissions.keyManagement) permissionsList.push('卡密管理');\n" +
                "        if (role.permissions.rewardManagement) permissionsList.push('奖品管理');\n" +
                "        if (role.permissions.pointsShop) permissionsList.push('积分商店');\n" +
                "        if (role.permissions.winnersView) permissionsList.push('中奖记录');\n" +
                "        if (role.permissions.statisticsView) permissionsList.push('统计分析');\n" +
                "\n" +
                "        html += `\n" +
                "            <div class=\"role-card\">\n" +
                "                <div class=\"role-header\">\n" +
                "                    <h4>${role.name}</h4>\n" +
                "                    <span class=\"role-id\">${role.id}</span>\n" +
                "                </div>\n" +
                "                <div class=\"role-description\">\n" +
                "                    <p>${role.description || '暂无描述'}</p>\n" +
                "                </div>\n" +
                "                <div class=\"role-permissions\">\n" +
                "                    <strong>权限:</strong>\n" +
                "                    <div class=\"permissions-tags\">\n" +
                "                        ${permissionsList.map(p => `<span class=\"permission-tag\">${p}</span>`).join('')}\n"
                +
                "                    </div>\n" +
                "                </div>\n" +
                "                <div class=\"role-actions\">\n" +
                "                    <button onclick=\"editRole('${role.id}')\" class=\"btn btn-sm btn-secondary\">编辑</button>\n"
                +
                "                    <button onclick=\"deleteRole('${role.id}')\" class=\"btn btn-sm btn-danger\">删除</button>\n"
                +
                "                </div>\n" +
                "            </div>\n" +
                "        `;\n" +
                "    });\n" +
                "    html += '</div>';\n" +
                "    container.innerHTML = html;\n" +
                "}\n" +
                "\n" +
                "// 更新角色数量\n" +
                "function updateRoleCount() {\n" +
                "    const countElement = document.getElementById('totalRolesCount');\n" +
                "    if (countElement) {\n" +
                "        countElement.textContent = allRoles.length;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新角色选择下拉框\n" +
                "function updateRoleSelects() {\n" +
                "    // 更新分配角色选择框\n" +
                "    const assignRoleSelect = document.getElementById('assignRoleSelect');\n" +
                "    if (assignRoleSelect) {\n" +
                "        let options = '<option value=\"\">请选择角色...</option>';\n" +
                "        allRoles.forEach(role => {\n" +
                "            options += `<option value=\"${role.id}\">${role.name}</option>`;\n" +
                "        });\n" +
                "        assignRoleSelect.innerHTML = options;\n" +
                "    }\n" +
                "\n" +
                "    // 更新创建账号角色选择框\n" +
                "    const newRoleSelect = document.getElementById('newRole');\n" +
                "    if (newRoleSelect) {\n" +
                "        // 保留基础角色选项\n" +
                "        let baseOptions = '<option value=\"admin\">管理员</option><option value=\"super_admin\">超级管理员</option>';\n"
                +
                "        // 添加自定义角色\n" +
                "        if (allRoles && allRoles.length > 0) {\n" +
                "            baseOptions += '<optgroup label=\"自定义角色\">';\n" +
                "            allRoles.forEach(role => {\n" +
                "                baseOptions += `<option value=\"${role.id}\">${role.name}</option>`;\n" +
                "            });\n" +
                "            baseOptions += '</optgroup>';\n" +
                "        }\n" +
                "        newRoleSelect.innerHTML = baseOptions;\n" +
                "    }\n" +
                "\n" +
                "    // 更新编辑账号角色选择框\n" +
                "    const editRoleSelect = document.getElementById('editRole');\n" +
                "    if (editRoleSelect) {\n" +
                "        // 保留基础角色选项\n" +
                "        let baseOptions = '<option value=\"admin\">管理员</option><option value=\"super_admin\">超级管理员</option>';\n"
                +
                "        // 添加自定义角色\n" +
                "        if (allRoles && allRoles.length > 0) {\n" +
                "            baseOptions += '<optgroup label=\"自定义角色\">';\n" +
                "            allRoles.forEach(role => {\n" +
                "                baseOptions += `<option value=\"${role.id}\">${role.name}</option>`;\n" +
                "            });\n" +
                "            baseOptions += '</optgroup>';\n" +
                "        }\n" +
                "        editRoleSelect.innerHTML = baseOptions;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 角色选择变化时自动设置权限\n" +
                "function onRoleChange(type) {\n" +
                "    const roleSelect = document.getElementById(type + 'Role');\n" +
                "    if (!roleSelect) return;\n" +
                "    \n" +
                "    const selectedRole = roleSelect.value;\n" +
                "    let permissions = {};\n" +
                "    \n" +
                "    // 获取角色权限配置\n" +
                "    if (selectedRole === 'admin') {\n" +
                "        permissions = {\n" +
                "            punishment: false,\n" +
                "            accountManagement: false,\n" +
                "            systemSettings: false,\n" +
                "            interfaceSettings: false,\n" +
                "            dataView: true,\n" +
                "            keyManagement: false,\n" +
                "            rewardManagement: false,\n" +
                "            pointsShop: false,\n" +
                "            winnersView: false,\n" +
                "            statisticsView: false\n" +
                "        };\n" +
                "    } else if (selectedRole === 'super_admin') {\n" +
                "        permissions = {\n" +
                "            punishment: true,\n" +
                "            accountManagement: true,\n" +
                "            systemSettings: true,\n" +
                "            interfaceSettings: true,\n" +
                "            dataView: true,\n" +
                "            keyManagement: true,\n" +
                "            rewardManagement: true,\n" +
                "            pointsShop: true,\n" +
                "            winnersView: true,\n" +
                "            statisticsView: true\n" +
                "        };\n" +
                "    } else {\n" +
                "        // 查找自定义角色权限\n" +
                "        const customRole = allRoles.find(role => role.id === selectedRole);\n" +
                "        if (customRole) {\n" +
                "            permissions = customRole.permissions;\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    // 设置权限复选框\n" +
                "    const permissionElements = [\n" +
                "        'PunishmentPermission',\n" +
                "        'AccountManagementPermission',\n" +
                "        'SystemSettingsPermission',\n" +
                "        'InterfaceSettingsPermission',\n" +
                "        'DataViewPermission',\n" +
                "        'KeyManagementPermission',\n" +
                "        'RewardManagementPermission',\n" +
                "        'PointsShopPermission',\n" +
                "        'WinnersViewPermission',\n" +
                "        'StatisticsViewPermission'\n" +
                "    ];\n" +
                "    \n" +
                "    const permissionKeys = [\n" +
                "        'punishment',\n" +
                "        'accountManagement',\n" +
                "        'systemSettings',\n" +
                "        'interfaceSettings',\n" +
                "        'dataView',\n" +
                "        'keyManagement',\n" +
                "        'rewardManagement',\n" +
                "        'pointsShop',\n" +
                "        'winnersView',\n" +
                "        'statisticsView'\n" +
                "    ];\n" +
                "    \n" +
                "    permissionElements.forEach((element, index) => {\n" +
                "        const checkbox = document.getElementById(type + element);\n" +
                "        if (checkbox) {\n" +
                "            checkbox.checked = permissions[permissionKeys[index]] || false;\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 搜索过滤角色\n" +
                "function filterRoles() {\n" +
                "    const searchInput = document.getElementById('roleSearchInput');\n" +
                "    if (!searchInput) return;\n" +
                "\n" +
                "    const searchTerm = searchInput.value.toLowerCase().trim();\n" +
                "    if (searchTerm === '') {\n" +
                "        filteredRoles = [...allRoles];\n" +
                "    } else {\n" +
                "        filteredRoles = allRoles.filter(role => \n" +
                "            role.name.toLowerCase().includes(searchTerm) ||\n" +
                "            role.id.toLowerCase().includes(searchTerm)\n" +
                "        );\n" +
                "    }\n" +
                "    displayRoles();\n" +
                "}\n" +
                "\n" +
                "// 刷新角色列表\n" +
                "function refreshRoles() {\n" +
                "    loadRoles();\n" +
                "    showMessage('角色列表已刷新', 'success');\n" +
                "}\n" +
                "\n" +
                "// 显示创建角色弹窗\n" +
                "function showCreateRoleModal() {\n" +
                "    const modal = document.getElementById('createRoleModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'block';\n" +
                "        document.getElementById('newRoleId').focus();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭创建角色弹窗\n" +
                "function closeCreateRoleModal() {\n" +
                "    const modal = document.getElementById('createRoleModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'none';\n" +
                "        document.getElementById('createRoleForm').reset();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 编辑角色\n" +
                "function editRole(roleId) {\n" +
                "    const role = allRoles.find(r => r.id === roleId);\n" +
                "    if (!role) return;\n" +
                "    \n" +
                "    document.getElementById('editRoleId').value = role.id;\n" +
                "    document.getElementById('editRoleName').value = role.name;\n" +
                "    document.getElementById('editRoleDescription').value = role.description || '';\n" +
                "    \n" +
                "    // 设置权限复选框\n" +
                "    document.getElementById('editRolePunishmentPermission').checked = role.permissions.punishment || false;\n"
                +
                "    document.getElementById('editRoleAccountManagementPermission').checked = role.permissions.accountManagement || false;\n"
                +
                "    document.getElementById('editRoleSystemSettingsPermission').checked = role.permissions.systemSettings || false;\n"
                +
                "    document.getElementById('editRoleInterfaceSettingsPermission').checked = role.permissions.interfaceSettings || false;\n"
                +
                "    document.getElementById('editRoleDataViewPermission').checked = role.permissions.dataView || false;\n"
                +
                "    document.getElementById('editRoleKeyManagementPermission').checked = role.permissions.keyManagement || false;\n"
                +
                "    document.getElementById('editRoleRewardManagementPermission').checked = role.permissions.rewardManagement || false;\n"
                +
                "    document.getElementById('editRolePointsShopPermission').checked = role.permissions.pointsShop || false;\n"
                +
                "    document.getElementById('editRoleWinnersViewPermission').checked = role.permissions.winnersView || false;\n"
                +
                "    document.getElementById('editRoleStatisticsViewPermission').checked = role.permissions.statisticsView || false;\n"
                +
                "    \n" +
                "    const modal = document.getElementById('editRoleModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'block';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭编辑角色弹窗\n" +
                "function closeEditRoleModal() {\n" +
                "    const modal = document.getElementById('editRoleModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'none';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭分配角色弹窗\n" +
                "function closeAssignRoleModal() {\n" +
                "    const modal = document.getElementById('assignRoleModal');\n" +
                "    if (modal) {\n" +
                "        modal.style.display = 'none';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 设置角色表单事件\n" +
                "function setupRoleForms() {\n" +
                "    // 创建角色表单\n" +
                "    const createRoleForm = document.getElementById('createRoleForm');\n" +
                "    if (createRoleForm) {\n" +
                "        createRoleForm.addEventListener('submit', function(e) {\n" +
                "            e.preventDefault();\n" +
                "            createRole();\n" +
                "        });\n" +
                "    }\n" +
                "\n" +
                "    // 编辑角色表单\n" +
                "    const editRoleForm = document.getElementById('editRoleForm');\n" +
                "    if (editRoleForm) {\n" +
                "        editRoleForm.addEventListener('submit', function(e) {\n" +
                "            e.preventDefault();\n" +
                "            updateRole();\n" +
                "        });\n" +
                "    }\n" +
                "\n" +
                "    // 分配角色表单\n" +
                "    const assignRoleForm = document.getElementById('assignRoleForm');\n" +
                "    if (assignRoleForm) {\n" +
                "        assignRoleForm.addEventListener('submit', function(e) {\n" +
                "            e.preventDefault();\n" +
                "            assignRole();\n" +
                "        });\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 创建角色\n" +
                "function createRole() {\n" +
                "    const roleId = document.getElementById('newRoleId').value.trim();\n" +
                "    const name = document.getElementById('newRoleName').value.trim();\n" +
                "    const description = document.getElementById('newRoleDescription').value.trim();\n" +
                "    const punishmentPermission = document.getElementById('newRolePunishmentPermission').checked;\n" +
                "    const accountManagementPermission = document.getElementById('newRoleAccountManagementPermission').checked;\n"
                +
                "    const systemSettingsPermission = document.getElementById('newRoleSystemSettingsPermission').checked;\n"
                +
                "    const interfaceSettingsPermission = document.getElementById('newRoleInterfaceSettingsPermission').checked;\n"
                +
                "    const dataViewPermission = document.getElementById('newRoleDataViewPermission').checked;\n" +
                "    const keyManagementPermission = document.getElementById('newRoleKeyManagementPermission').checked;\n"
                +
                "    const rewardManagementPermission = document.getElementById('newRoleRewardManagementPermission').checked;\n"
                +
                "    const pointsShopPermission = document.getElementById('newRolePointsShopPermission').checked;\n" +
                "    const winnersViewPermission = document.getElementById('newRoleWinnersViewPermission').checked;\n" +
                "    const statisticsViewPermission = document.getElementById('newRoleStatisticsViewPermission').checked;\n"
                +
                "\n" +
                "    if (!roleId || !name) {\n" +
                "        showMessage('请填写角色ID和名称', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    const data = {\n" +
                "        action: 'create_role',\n" +
                "        roleId: roleId,\n" +
                "        name: name,\n" +
                "        description: description,\n" +
                "        permissions: {\n" +
                "            punishment: punishmentPermission,\n" +
                "            accountManagement: accountManagementPermission,\n" +
                "            systemSettings: systemSettingsPermission,\n" +
                "            interfaceSettings: interfaceSettingsPermission,\n" +
                "            dataView: dataViewPermission,\n" +
                "            keyManagement: keyManagementPermission,\n" +
                "            rewardManagement: rewardManagementPermission,\n" +
                "            pointsShop: pointsShopPermission,\n" +
                "            winnersView: winnersViewPermission,\n" +
                "            statisticsView: statisticsViewPermission\n" +
                "        }\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeCreateRoleModal();\n" +
                "            loadRoles();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('创建角色出错:', error);\n" +
                "        showMessage('创建角色出错', 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新角色\n" +
                "function updateRole() {\n" +
                "    const roleId = document.getElementById('editRoleId').value;\n" +
                "    const name = document.getElementById('editRoleName').value.trim();\n" +
                "    const description = document.getElementById('editRoleDescription').value.trim();\n" +
                "    const punishmentPermission = document.getElementById('editRolePunishmentPermission').checked;\n" +
                "    const accountManagementPermission = document.getElementById('editRoleAccountManagementPermission').checked;\n"
                +
                "    const systemSettingsPermission = document.getElementById('editRoleSystemSettingsPermission').checked;\n"
                +
                "    const interfaceSettingsPermission = document.getElementById('editRoleInterfaceSettingsPermission').checked;\n"
                +
                "    const dataViewPermission = document.getElementById('editRoleDataViewPermission').checked;\n" +
                "    const keyManagementPermission = document.getElementById('editRoleKeyManagementPermission').checked;\n"
                +
                "    const rewardManagementPermission = document.getElementById('editRoleRewardManagementPermission').checked;\n"
                +
                "    const pointsShopPermission = document.getElementById('editRolePointsShopPermission').checked;\n" +
                "    const winnersViewPermission = document.getElementById('editRoleWinnersViewPermission').checked;\n"
                +
                "    const statisticsViewPermission = document.getElementById('editRoleStatisticsViewPermission').checked;\n"
                +
                "\n" +
                "    const data = {\n" +
                "        action: 'update_role',\n" +
                "        roleId: roleId,\n" +
                "        name: name,\n" +
                "        description: description,\n" +
                "        permissions: {\n" +
                "            punishment: punishmentPermission,\n" +
                "            accountManagement: accountManagementPermission,\n" +
                "            systemSettings: systemSettingsPermission,\n" +
                "            interfaceSettings: interfaceSettingsPermission,\n" +
                "            dataView: dataViewPermission,\n" +
                "            keyManagement: keyManagementPermission,\n" +
                "            rewardManagement: rewardManagementPermission,\n" +
                "            pointsShop: pointsShopPermission,\n" +
                "            winnersView: winnersViewPermission,\n" +
                "            statisticsView: statisticsViewPermission\n" +
                "        }\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeEditRoleModal();\n" +
                "            loadRoles();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('更新角色出错:', error);\n" +
                "        showMessage('更新角色出错', 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 删除角色\n" +
                "function deleteRole(roleId) {\n" +
                "    showConfirmModal(\n" +
                "        '确认删除',\n" +
                "        `您确定要删除角色 \"${roleId}\" 吗？此操作不可撤销！`,\n" +
                "        function() {\n" +
                "            const data = {\n" +
                "                action: 'delete_role',\n" +
                "                roleId: roleId\n" +
                "            };\n" +
                "\n" +
                "            fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                },\n" +
                "                body: JSON.stringify(data)\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                if (data.success) {\n" +
                "                    showMessage(data.message, 'success');\n" +
                "                    loadRoles();\n" +
                "                } else {\n" +
                "                    showMessage(data.message, 'error');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('删除角色出错:', error);\n" +
                "                showMessage('删除角色出错', 'error');\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n" +
                "\n" +
                "// 分配角色\n" +
                "function assignRole() {\n" +
                "    const username = document.getElementById('assignUsername').value.trim();\n" +
                "    const roleId = document.getElementById('assignRoleSelect').value;\n" +
                "\n" +
                "    if (!username || !roleId) {\n" +
                "        showMessage('请填写用户名并选择角色', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    const data = {\n" +
                "        action: 'assign_role',\n" +
                "        username: username,\n" +
                "        roleId: roleId\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeAssignRoleModal();\n" +
                "            loadAccounts();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('分配角色出错:', error);\n" +
                "        showMessage('分配角色出错', 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// ==================== IP管理功能 ====================\n" +
                "// 显示IP管理弹窗\n" +
                "function showIPManagement(username) {\n" +
                "    // 获取账号的详细IP信息\n" +
                "    fetch(`/admin/accounts?action=get_ip_info&username=${username}&key=" + adminKey + "`)\n" +
                "        .then(response => response.json())\n" +
                "        .then(data => {\n" +
                "            if (data.success) {\n" +
                "                displayIPManagementModal(username, data.ipInfo);\n" +
                "            } else {\n" +
                "                showMessage('获取IP信息失败: ' + data.message, 'error');\n" +
                "            }\n" +
                "        })\n" +
                "        .catch(error => {\n" +
                "            console.error('获取IP信息出错:', error);\n" +
                "            showMessage('获取IP信息出错', 'error');\n" +
                "        });\n" +
                "}\n" +
                "\n" +
                "// 显示IP管理弹窗内容\n" +
                "function displayIPManagementModal(username, ipInfo) {\n" +
                "    const activeSessions = ipInfo.activeSessions || {};\n" +
                "    const ipCounts = activeSessions.ipCounts || {};\n" +
                "    const ipLastSeen = ipInfo.ipLastSeen || {};\n" +
                "\n" +
                "    let ipListHtml = '';\n" +
                "    Object.entries(ipCounts).forEach(([ip, count]) => {\n" +
                "        const lastSeen = ipLastSeen[ip] ? new Date(ipLastSeen[ip]).toLocaleString() : '未知';\n" +
                "        ipListHtml += `\n" +
                "            <div class=\"ip-management-item\">\n" +
                "                <div class=\"ip-info\">\n" +
                "                    <strong>${ip}</strong>\n" +
                "                    <span class=\"ip-details\">活跃会话: ${count}个 | 最后登录: ${lastSeen}</span>\n" +
                "                </div>\n" +
                "                <div class=\"ip-actions\">\n" +
                "                    <button onclick=\"kickIP('${username}', '${ip}')\" class=\"btn btn-sm btn-warning\">踢出</button>\n"
                +
                "                    <button onclick=\"banIP('${ip}')\" class=\"btn btn-sm btn-danger\">封禁IP</button>\n"
                +
                "                </div>\n" +
                "            </div>\n" +
                "        `;\n" +
                "    });\n" +
                "\n" +
                "    const modalHtml = `\n" +
                "        <div id=\"ipManagementModal\" class=\"modal\" style=\"display: block;\">\n" +
                "            <div class=\"modal-content\">\n" +
                "                <div class=\"modal-header\">\n" +
                "                    <h3>IP管理 - ${username}</h3>\n" +
                "                    <span class=\"close\" onclick=\"closeIPManagementModal()\">&times;</span>\n" +
                "                </div>\n" +
                "                <div class=\"modal-body\">\n" +
                "                    <div class=\"ip-summary\">\n" +
                "                        <p><strong>当前IP:</strong> ${ipInfo.currentIP || '未知'}</p>\n" +
                "                        <p><strong>活跃会话:</strong> ${activeSessions.totalSessions || 0}个</p>\n" +
                "                        <p><strong>不同IP数:</strong> ${activeSessions.uniqueIPs || 0}个</p>\n" +
                "                    </div>\n" +
                "                    <div class=\"ip-list\">\n" +
                "                        <h4>活跃IP列表:</h4>\n" +
                "                        ${ipListHtml || '<p class=\"text-muted\">暂无活跃IP</p>'}\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    `;\n" +
                "\n" +
                "    // 移除已存在的弹窗\n" +
                "    const existingModal = document.getElementById('ipManagementModal');\n" +
                "    if (existingModal) {\n" +
                "        existingModal.remove();\n" +
                "    }\n" +
                "\n" +
                "    // 添加新弹窗\n" +
                "    document.body.insertAdjacentHTML('beforeend', modalHtml);\n" +
                "}\n" +
                "\n" +
                "// 关闭IP管理弹窗\n" +
                "function closeIPManagementModal() {\n" +
                "    const modal = document.getElementById('ipManagementModal');\n" +
                "    if (modal) {\n" +
                "        modal.remove();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 踢出指定IP的会话\n" +
                "function kickIP(username, ip) {\n" +
                "    showConfirmModal(\n" +
                "        '确认踢出',\n" +
                "        `您确定要踢出用户 \"${username}\" 在IP \"${ip}\" 上的所有会话吗？`,\n" +
                "        function() {\n" +
                "            const data = {\n" +
                "                action: 'kick_ip',\n" +
                "                username: username,\n" +
                "                ip: ip\n" +
                "            };\n" +
                "\n" +
                "            fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                },\n" +
                "                body: JSON.stringify(data)\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                if (data.success) {\n" +
                "                    showMessage(data.message, 'success');\n" +
                "                    closeIPManagementModal();\n" +
                "                    loadAccounts();\n" +
                "                } else {\n" +
                "                    showMessage(data.message, 'error');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('踢出IP出错:', error);\n" +
                "                showMessage('踢出IP出错', 'error');\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n" +
                "\n" +
                "// 显示封禁账号弹窗\n" +
                "function showBanAccountModal(username) {\n" +
                "    const modalHtml = `\n" +
                "        <div id=\"banAccountModal\" class=\"modal\" style=\"display: block;\">\n" +
                "            <div class=\"modal-content\">\n" +
                "                <div class=\"modal-header\">\n" +
                "                    <h3>封禁账号 - ${username}</h3>\n" +
                "                    <span class=\"close\" onclick=\"closeBanAccountModal()\">&times;</span>\n" +
                "                </div>\n" +
                "                <div class=\"modal-body\">\n" +
                "                    <form id=\"banAccountForm\">\n" +
                "                        <input type=\"hidden\" id=\"banUsername\" value=\"${username}\">\n" +
                "                        <div class=\"form-group\">\n" +
                "                            <label for=\"banReason\">封禁原因:</label>\n" +
                "                            <select id=\"banReason\" class=\"form-input\" required>\n" +
                "                                <option value=\"\">请选择封禁原因...</option>\n" +
                "                                <option value=\"违规操作\">违规操作</option>\n" +
                "                                <option value=\"安全风险\">安全风险</option>\n" +
                "                                <option value=\"滥用权限\">滥用权限</option>\n" +
                "                                <option value=\"多IP异常登录\">多IP异常登录</option>\n" +
                "                                <option value=\"恶意行为\">恶意行为</option>\n" +
                "                                <option value=\"其他\">其他</option>\n" +
                "                            </select>\n" +
                "                        </div>\n" +
                "                        <div class=\"form-group\">\n" +
                "                            <label for=\"banCustomReason\">详细说明:</label>\n" +
                "                            <textarea id=\"banCustomReason\" class=\"form-input\" rows=\"3\" placeholder=\"请详细说明封禁原因...\"></textarea>\n"
                +
                "                        </div>\n" +
                "                        <div class=\"form-group\">\n" +
                "                            <label for=\"banDuration\">封禁时长:</label>\n" +
                "                            <select id=\"banDuration\" class=\"form-input\" required>\n" +
                "                                <option value=\"1\">1小时</option>\n" +
                "                                <option value=\"24\">1天</option>\n" +
                "                                <option value=\"168\">7天</option>\n" +
                "                                <option value=\"720\">30天</option>\n" +
                "                                <option value=\"-1\">永久封禁</option>\n" +
                "                            </select>\n" +
                "                        </div>\n" +
                "                        <div class=\"form-actions\">\n" +
                "                            <button type=\"button\" onclick=\"closeBanAccountModal()\" class=\"btn btn-secondary\">取消</button>\n"
                +
                "                            <button type=\"submit\" class=\"btn btn-danger\">确认封禁</button>\n" +
                "                        </div>\n" +
                "                    </form>\n" +
                "                </div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    `;\n" +
                "\n" +
                "    // 移除已存在的弹窗\n" +
                "    const existingModal = document.getElementById('banAccountModal');\n" +
                "    if (existingModal) {\n" +
                "        existingModal.remove();\n" +
                "    }\n" +
                "\n" +
                "    // 添加新弹窗\n" +
                "    document.body.insertAdjacentHTML('beforeend', modalHtml);\n" +
                "\n" +
                "    // 设置表单事件\n" +
                "    document.getElementById('banAccountForm').addEventListener('submit', function(e) {\n" +
                "        e.preventDefault();\n" +
                "        banAccount();\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 关闭封禁账号弹窗\n" +
                "function closeBanAccountModal() {\n" +
                "    const modal = document.getElementById('banAccountModal');\n" +
                "    if (modal) {\n" +
                "        modal.remove();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 执行封禁账号\n" +
                "function banAccount() {\n" +
                "    const username = document.getElementById('banUsername').value;\n" +
                "    const reason = document.getElementById('banReason').value;\n" +
                "    const customReason = document.getElementById('banCustomReason').value;\n" +
                "    const duration = document.getElementById('banDuration').value;\n" +
                "\n" +
                "    if (!reason) {\n" +
                "        showMessage('请选择封禁原因', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    const finalReason = reason === '其他' ? customReason : reason;\n" +
                "    if (reason === '其他' && !customReason.trim()) {\n" +
                "        showMessage('请填写详细的封禁原因', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "\n" +
                "    const data = {\n" +
                "        action: 'ban_account',\n" +
                "        username: username,\n" +
                "        reason: finalReason,\n" +
                "        customReason: customReason,\n" +
                "        duration: parseInt(duration)\n" +
                "    };\n" +
                "\n" +
                "    fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify(data)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showMessage(data.message, 'success');\n" +
                "            closeBanAccountModal();\n" +
                "            loadAccounts();\n" +
                "        } else {\n" +
                "            showMessage(data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('封禁账号出错:', error);\n" +
                "        showMessage('封禁账号出错', 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 封禁IP\n" +
                "function banIP(ip) {\n" +
                "    showConfirmModal(\n" +
                "        '确认封禁IP',\n" +
                "        `您确定要封禁IP \"${ip}\" 吗？封禁后该IP将无法访问管理后台。`,\n" +
                "        function() {\n" +
                "            const data = {\n" +
                "                action: 'ban_ip',\n" +
                "                ip: ip\n" +
                "            };\n" +
                "\n" +
                "            fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                },\n" +
                "                body: JSON.stringify(data)\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                if (data.success) {\n" +
                "                    showMessage(data.message, 'success');\n" +
                "                    closeIPManagementModal();\n" +
                "                    loadAccounts();\n" +
                "                } else {\n" +
                "                    showMessage(data.message, 'error');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('封禁IP出错:', error);\n" +
                "                showMessage('封禁IP出错', 'error');\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n" +
                "\n" +
                "// 解封账号\n" +
                "function unbanAccount(username) {\n" +
                "    showConfirmModal(\n" +
                "        '确认解封',\n" +
                "        `您确定要解封账号 \"${username}\" 吗？解封后该账号将恢复正常使用。`,\n" +
                "        function() {\n" +
                "            const data = {\n" +
                "                action: 'unban_account',\n" +
                "                username: username\n" +
                "            };\n" +
                "\n" +
                "            fetch('/admin/accounts?key=" + adminKey + "', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                },\n" +
                "                body: JSON.stringify(data)\n" +
                "            })\n" +
                "            .then(response => response.json())\n" +
                "            .then(data => {\n" +
                "                if (data.success) {\n" +
                "                    showMessage(data.message, 'success');\n" +
                "                    loadAccounts();\n" +
                "                } else {\n" +
                "                    showMessage(data.message, 'error');\n" +
                "                }\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('解封账号出错:', error);\n" +
                "                showMessage('解封账号出错', 'error');\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n\n";
    }

    /**
     * 生成登出功能JavaScript代码
     */
    public String generateLogoutJS() {
        return "// ==================== 登出功能 ====================\n" +
                "function logout() {\n" +
                "    showConfirmModal(\n" +
                "        '确认登出',\n" +
                "        '您确定要登出管理员账号吗？',\n" +
                "        function() {\n" +
                "            // 发送登出请求\n" +
                "            fetch('/admin/logout', {\n" +
                "                method: 'POST',\n" +
                "                headers: {\n" +
                "                    'Content-Type': 'application/json'\n" +
                "                }\n" +
                "            })\n" +
                "            .then(response => {\n" +
                "                // 无论响应如何，都重定向到登录页面\n" +
                "                window.location.href = '/admin/login';\n" +
                "            })\n" +
                "            .catch(error => {\n" +
                "                console.error('登出请求出错:', error);\n" +
                "                // 即使出错也重定向到登录页面\n" +
                "                window.location.href = '/admin/login';\n" +
                "            });\n" +
                "        },\n" +
                "        null\n" +
                "    );\n" +
                "}\n";
    }
}
