package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 界面设置页面生成器
 * 负责生成界面设置页面的HTML内容和相关JavaScript代码
 */
public class InterfacePageGenerator {

        private final AceKeySystem plugin;
        private final WebServer webServer;
        private final String adminKey;

        public InterfacePageGenerator(AceKeySystem plugin, WebServer webServer, String adminKey) {
                this.plugin = plugin;
                this.webServer = webServer;
                this.adminKey = adminKey;
        }

        /**
         * 生成界面设置页面
         */
        public String generateInterfacePage() {
                StringBuilder html = new StringBuilder();

                html.append("            <div class=\"page-header\">\n");
                html.append("                <h1>🎨 界面设置</h1>\n");
                html.append("                <p>配置网站界面外观和背景图片</p>\n");
                html.append("            </div>\n");
                html.append("            \n");

                // 添加标签页样式和预览模态框样式
                html.append("            <style>\n");
                html.append("                /* CSS变量定义 */\n");
                html.append("                :root {\n");
                html.append("                    --primary-color: #667eea;\n");
                html.append("                    --accent-color: #764ba2;\n");
                html.append("                    --bg-card: #ffffff;\n");
                html.append("                    --bg-secondary: #f8f9fa;\n");
                html.append("                    --text-primary: #2d3748;\n");
                html.append("                    --text-secondary: #718096;\n");
                html.append("                    --border-color: #e2e8f0;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                /* 暗色主题 */\n");
                html.append("                @media (prefers-color-scheme: dark) {\n");
                html.append("                    :root {\n");
                html.append("                        --bg-card: #2d3748;\n");
                html.append("                        --bg-secondary: #4a5568;\n");
                html.append("                        --text-primary: #f7fafc;\n");
                html.append("                        --text-secondary: #cbd5e0;\n");
                html.append("                        --border-color: #4a5568;\n");
                html.append("                    }\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .interface-tabs { margin-bottom: 20px; }\n");
                html.append("                .tab-nav { display: flex; gap: 10px; margin-bottom: 20px; border-bottom: 2px solid var(--border-color); }\n");
                html.append("                .tab-btn { padding: 12px 20px; border: none; background: var(--bg-secondary); color: var(--text-primary); cursor: pointer; border-radius: 8px 8px 0 0; transition: all 0.3s; }\n");
                html.append("                .tab-btn:hover { background: var(--bg-card); }\n");
                html.append("                .tab-btn.active { background: var(--accent-color); color: white; }\n");
                html.append("                .tab-pane { display: none !important; }\n");
                html.append("                .tab-pane.active { display: block !important; }\n");
                html.append("                \n");
                html.append("                /* 预览模态框样式 */\n");
                html.append("                .preview-modal {\n");
                html.append("                    position: fixed;\n");
                html.append("                    top: 0;\n");
                html.append("                    left: 0;\n");
                html.append("                    width: 100%;\n");
                html.append("                    height: 100%;\n");
                html.append("                    background: rgba(0, 0, 0, 0.8);\n");
                html.append("                    backdrop-filter: blur(10px);\n");
                html.append("                    z-index: 10000;\n");
                html.append("                    display: flex;\n");
                html.append("                    align-items: center;\n");
                html.append("                    justify-content: center;\n");
                html.append("                    opacity: 0;\n");
                html.append("                    visibility: hidden;\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-modal.show {\n");
                html.append("                    opacity: 1;\n");
                html.append("                    visibility: visible;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-modal-content {\n");
                html.append("                    background: var(--bg-card);\n");
                html.append("                    border-radius: 20px;\n");
                html.append("                    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);\n");
                html.append("                    max-width: 90vw;\n");
                html.append("                    max-height: 90vh;\n");
                html.append("                    overflow: hidden;\n");
                html.append("                    transform: scale(0.9);\n");
                html.append("                    transition: transform 0.3s ease;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-modal.show .preview-modal-content {\n");
                html.append("                    transform: scale(1);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-modal-header {\n");
                html.append("                    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));\n");
                html.append("                    color: white;\n");
                html.append("                    padding: 20px 30px;\n");
                html.append("                    display: flex;\n");
                html.append("                    justify-content: space-between;\n");
                html.append("                    align-items: center;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-modal-header h3 {\n");
                html.append("                    margin: 0;\n");
                html.append("                    font-size: 24px;\n");
                html.append("                    font-weight: 600;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-close-btn {\n");
                html.append("                    background: rgba(255, 255, 255, 0.2);\n");
                html.append("                    border: none;\n");
                html.append("                    color: white;\n");
                html.append("                    width: 40px;\n");
                html.append("                    height: 40px;\n");
                html.append("                    border-radius: 50%;\n");
                html.append("                    font-size: 24px;\n");
                html.append("                    cursor: pointer;\n");
                html.append("                    display: flex;\n");
                html.append("                    align-items: center;\n");
                html.append("                    justify-content: center;\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-close-btn:hover {\n");
                html.append("                    background: rgba(255, 255, 255, 0.3);\n");
                html.append("                    transform: rotate(90deg);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-modal-body {\n");
                html.append("                    padding: 30px;\n");
                html.append("                    max-height: 70vh;\n");
                html.append("                    overflow-y: auto;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-grid {\n");
                html.append("                    display: grid;\n");
                html.append("                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n");
                html.append("                    gap: 20px;\n");
                html.append("                    margin-bottom: 20px;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-card {\n");
                html.append("                    background: var(--bg-secondary);\n");
                html.append("                    border-radius: 15px;\n");
                html.append("                    overflow: hidden;\n");
                html.append("                    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-card:hover {\n");
                html.append("                    transform: translateY(-5px);\n");
                html.append("                    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-image {\n");
                html.append("                    width: 100%;\n");
                html.append("                    height: 200px;\n");
                html.append("                    background-size: cover;\n");
                html.append("                    background-position: center;\n");
                html.append("                    background-repeat: no-repeat;\n");
                html.append("                    position: relative;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-image::after {\n");
                html.append("                    content: '';\n");
                html.append("                    position: absolute;\n");
                html.append("                    top: 0;\n");
                html.append("                    left: 0;\n");
                html.append("                    right: 0;\n");
                html.append("                    bottom: 0;\n");
                html.append("                    background: linear-gradient(45deg, rgba(102, 126, 234, 0.3), rgba(118, 75, 162, 0.3));\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-info {\n");
                html.append("                    padding: 20px;\n");
                html.append("                    text-align: center;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-info h4 {\n");
                html.append("                    margin: 0 0 15px 0;\n");
                html.append("                    color: var(--text-primary);\n");
                html.append("                    font-size: 18px;\n");
                html.append("                    font-weight: 600;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-link {\n");
                html.append("                    display: inline-block;\n");
                html.append("                    background: linear-gradient(135deg, #667eea, #764ba2);\n");
                html.append("                    color: white !important;\n");
                html.append("                    text-decoration: none;\n");
                html.append("                    padding: 10px 20px;\n");
                html.append("                    border-radius: 25px;\n");
                html.append("                    font-weight: 600;\n");
                html.append("                    font-size: 14px;\n");
                html.append("                    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n");
                html.append("                    border: 2px solid rgba(255, 255, 255, 0.2);\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-link:hover {\n");
                html.append("                    transform: translateY(-2px);\n");
                html.append("                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n");
                html.append("                    background: linear-gradient(135deg, #5a67d8, #6b46c1);\n");
                html.append("                    border-color: rgba(255, 255, 255, 0.3);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-link:visited {\n");
                html.append("                    color: white !important;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-tips {\n");
                html.append("                    background: var(--bg-secondary);\n");
                html.append("                    border-radius: 10px;\n");
                html.append("                    padding: 15px 20px;\n");
                html.append("                    text-align: center;\n");
                html.append("                    border-left: 4px solid var(--accent-color);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-tips p {\n");
                html.append("                    margin: 0;\n");
                html.append("                    color: var(--text-secondary);\n");
                html.append("                    font-size: 14px;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                /* 装饰性模态框样式 */\n");
                html.append("                .custom-modal {\n");
                html.append("                    position: fixed;\n");
                html.append("                    top: 0;\n");
                html.append("                    left: 0;\n");
                html.append("                    width: 100%;\n");
                html.append("                    height: 100%;\n");
                html.append("                    background: rgba(0, 0, 0, 0.7);\n");
                html.append("                    backdrop-filter: blur(8px);\n");
                html.append("                    z-index: 10001;\n");
                html.append("                    display: flex;\n");
                html.append("                    align-items: center;\n");
                html.append("                    justify-content: center;\n");
                html.append("                    opacity: 0;\n");
                html.append("                    visibility: hidden;\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal.show {\n");
                html.append("                    opacity: 1;\n");
                html.append("                    visibility: visible;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-content {\n");
                html.append("                    background: var(--bg-card);\n");
                html.append("                    border-radius: 20px;\n");
                html.append("                    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1);\n");
                html.append("                    min-width: 400px;\n");
                html.append("                    max-width: 500px;\n");
                html.append("                    overflow: hidden;\n");
                html.append("                    transform: scale(0.8) translateY(20px);\n");
                html.append("                    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal.show .custom-modal-content {\n");
                html.append("                    transform: scale(1) translateY(0);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-header {\n");
                html.append("                    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));\n");
                html.append("                    color: white;\n");
                html.append("                    padding: 25px 30px;\n");
                html.append("                    text-align: center;\n");
                html.append("                    position: relative;\n");
                html.append("                    overflow: hidden;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-header::before {\n");
                html.append("                    content: '';\n");
                html.append("                    position: absolute;\n");
                html.append("                    top: -50%;\n");
                html.append("                    left: -50%;\n");
                html.append("                    width: 200%;\n");
                html.append("                    height: 200%;\n");
                html.append("                    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n");
                html.append("                    animation: shimmer 3s ease-in-out infinite;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .success-header {\n");
                html.append("                    background: linear-gradient(135deg, #10b981, #059669) !important;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .error-header {\n");
                html.append("                    background: linear-gradient(135deg, #ef4444, #dc2626) !important;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-header h3 {\n");
                html.append("                    margin: 0;\n");
                html.append("                    font-size: 22px;\n");
                html.append("                    font-weight: 600;\n");
                html.append("                    position: relative;\n");
                html.append("                    z-index: 2;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-body {\n");
                html.append("                    padding: 30px;\n");
                html.append("                    text-align: center;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .modal-icon {\n");
                html.append("                    font-size: 48px;\n");
                html.append("                    margin-bottom: 20px;\n");
                html.append("                    animation: bounce 1s ease-in-out;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .success-icon {\n");
                html.append("                    animation: successPulse 1s ease-in-out;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .error-icon {\n");
                html.append("                    animation: shake 0.5s ease-in-out;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-body p {\n");
                html.append("                    margin: 0;\n");
                html.append("                    color: var(--text-primary);\n");
                html.append("                    font-size: 16px;\n");
                html.append("                    line-height: 1.6;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .custom-modal-footer {\n");
                html.append("                    padding: 20px 30px 30px;\n");
                html.append("                    display: flex;\n");
                html.append("                    gap: 15px;\n");
                html.append("                    justify-content: center;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .modal-btn {\n");
                html.append("                    padding: 12px 30px;\n");
                html.append("                    border: none;\n");
                html.append("                    border-radius: 25px;\n");
                html.append("                    font-size: 16px;\n");
                html.append("                    font-weight: 600;\n");
                html.append("                    cursor: pointer;\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                    min-width: 100px;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .cancel-btn {\n");
                html.append("                    background: var(--bg-secondary);\n");
                html.append("                    color: var(--text-primary);\n");
                html.append("                    border: 2px solid var(--border-color);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .cancel-btn:hover {\n");
                html.append("                    background: var(--bg-card);\n");
                html.append("                    transform: translateY(-2px);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .confirm-btn {\n");
                html.append("                    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));\n");
                html.append("                    color: white;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .confirm-btn:hover {\n");
                html.append("                    transform: translateY(-2px);\n");
                html.append("                    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .success-btn {\n");
                html.append("                    background: linear-gradient(135deg, #10b981, #059669);\n");
                html.append("                    color: white;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .success-btn:hover {\n");
                html.append("                    transform: translateY(-2px);\n");
                html.append("                    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .error-btn {\n");
                html.append("                    background: linear-gradient(135deg, #ef4444, #dc2626);\n");
                html.append("                    color: white;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .error-btn:hover {\n");
                html.append("                    transform: translateY(-2px);\n");
                html.append("                    box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                /* 动画效果 */\n");
                html.append("                @keyframes shimmer {\n");
                html.append("                    0%, 100% { transform: rotate(0deg); }\n");
                html.append("                    50% { transform: rotate(180deg); }\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                @keyframes bounce {\n");
                html.append("                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n");
                html.append("                    40% { transform: translateY(-10px); }\n");
                html.append("                    60% { transform: translateY(-5px); }\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                @keyframes successPulse {\n");
                html.append("                    0% { transform: scale(0.8); opacity: 0.5; }\n");
                html.append("                    50% { transform: scale(1.2); opacity: 1; }\n");
                html.append("                    100% { transform: scale(1); opacity: 1; }\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                @keyframes shake {\n");
                html.append("                    0%, 100% { transform: translateX(0); }\n");
                html.append("                    25% { transform: translateX(-5px); }\n");
                html.append("                    75% { transform: translateX(5px); }\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                /* 预览模态框特殊样式 */\n");
                html.append("                .preview-header {\n");
                html.append("                    background: linear-gradient(135deg, #6366f1, #8b5cf6) !important;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-icon {\n");
                html.append("                    animation: pulse 2s ease-in-out infinite;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-actions {\n");
                html.append("                    margin: 20px 0;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-action-btn {\n");
                html.append("                    display: inline-block;\n");
                html.append("                    background: linear-gradient(135deg, #6366f1, #8b5cf6);\n");
                html.append("                    color: white;\n");
                html.append("                    text-decoration: none;\n");
                html.append("                    padding: 12px 30px;\n");
                html.append("                    border-radius: 25px;\n");
                html.append("                    font-weight: 600;\n");
                html.append("                    transition: all 0.3s ease;\n");
                html.append("                    box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-action-btn:hover {\n");
                html.append("                    transform: translateY(-2px);\n");
                html.append("                    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-tip {\n");
                html.append("                    margin-top: 15px;\n");
                html.append("                    padding: 10px 15px;\n");
                html.append("                    background: var(--bg-secondary);\n");
                html.append("                    border-radius: 8px;\n");
                html.append("                    border-left: 3px solid #6366f1;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .preview-tip small {\n");
                html.append("                    color: var(--text-secondary);\n");
                html.append("                    font-size: 13px;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                @keyframes pulse {\n");
                html.append("                    0%, 100% { transform: scale(1); }\n");
                html.append("                    50% { transform: scale(1.1); }\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                /* 音效设置两列布局样式 */\n");
                html.append("                .content-grid.two-columns {\n");
                html.append("                    display: grid;\n");
                html.append("                    grid-template-columns: 1fr 1fr;\n");
                html.append("                    gap: 30px;\n");
                html.append("                    align-items: start;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                .sound-card {\n");
                html.append("                    height: auto;\n");
                html.append("                    min-height: 400px;\n");
                html.append("                }\n");
                html.append("                \n");
                html.append("                /* 响应式设计 - 小屏幕时改为单列 */\n");
                html.append("                @media (max-width: 768px) {\n");
                html.append("                    .content-grid.two-columns {\n");
                html.append("                        grid-template-columns: 1fr;\n");
                html.append("                        gap: 20px;\n");
                html.append("                    }\n");
                html.append("                }\n");
                html.append("            </style>\n");
                html.append("            \n");

                // 添加导航标签页
                html.append("            <div class=\"interface-tabs\">\n");
                html.append("                <div class=\"tab-nav\">\n");
                html.append("                    <button class=\"tab-btn active\" onclick=\"switchInterfaceTab('homepage')\">🏠 首页设置</button>\n");
                html.append("                    <button class=\"tab-btn\" onclick=\"switchInterfaceTab('background')\">🖼️ 背景设置</button>\n");
                html.append("                    <button class=\"tab-btn\" onclick=\"switchInterfaceTab('punishment')\">⚖️ 封禁设置</button>\n");
                html.append("                    <button class=\"tab-btn\" onclick=\"switchInterfaceTab('animation')\">🎬 动画设置</button>\n");
                html.append("                    <button class=\"tab-btn\" onclick=\"switchInterfaceTab('sound')\">🔊 音效设置</button>\n");
                html.append("                    <button class=\"tab-btn\" onclick=\"switchInterfaceTab('shop')\">🛒 积分商店</button>\n");
                html.append("                    <button class=\"tab-btn\" onclick=\"switchInterfaceTab('advanced')\">⚙️ 高级设置</button>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <div class=\"tab-content\">\n");

                // 生成所有标签页内容
                html.append(generateHomepageTab());
                html.append(generateBackgroundTab());
                html.append(generatePunishmentTab());
                html.append(generateAnimationTab());
                html.append(generateSoundTab());
                html.append(generateShopTab());
                html.append(generateAdvancedTab());

                // 关闭标签页容器
                html.append("                </div>\n");
                html.append("            </div>\n");

                return html.toString();
        }

        /**
         * 生成界面设置相关的JavaScript代码
         */
        public String generateInterfaceJavaScript() {
                return "// 背景设置相关函数\n" +
                                "function saveBackgroundSettings() {\n" +
                                "    const backgroundImage = document.getElementById('backgroundImage').value.trim();\n"
                                +
                                "    const loginBackgroundImage = document.getElementById('loginBackgroundImage').value.trim();\n"
                                +
                                "    const loginLogoUrl = document.getElementById('loginLogoUrl').value.trim();\n" +
                                "    const leaderboardBackgroundImage = document.getElementById('leaderboardBackgroundImage').value.trim();\n"
                                +
                                "    const punishmentBackgroundImage = document.getElementById('punishmentBackgroundImage').value.trim();\n"
                                +
                                "    const backgroundOpacity = document.getElementById('backgroundOpacity').value;\n" +
                                "    const leaderboardBackgroundOpacity = document.getElementById('leaderboardBackgroundOpacity').value;\n"
                                +
                                "    const punishmentBackgroundOpacity = document.getElementById('punishmentBackgroundOpacity').value;\n"
                                +
                                "    const loginBackgroundOpacity = document.getElementById('loginBackgroundOpacity').value;\n"
                                +
                                "    const shopBackgroundOpacity = document.getElementById('shopBackgroundOpacity').value;\n"
                                +
                                "    \n" +
                                "    // 不显示加载提示，直接进行保存\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_background_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            background_image: backgroundImage,\n" +
                                "            login_background_image: loginBackgroundImage,\n" +
                                "            login_logo_url: loginLogoUrl,\n" +
                                "            leaderboard_background_image: leaderboardBackgroundImage,\n" +
                                "            punishment_background_image: punishmentBackgroundImage,\n" +
                                "            background_opacity: parseInt(backgroundOpacity),\n" +
                                "            leaderboard_background_opacity: parseInt(leaderboardBackgroundOpacity),\n"
                                +
                                "            punishment_background_opacity: parseInt(punishmentBackgroundOpacity),\n" +
                                "            login_background_opacity: parseInt(loginBackgroundOpacity),\n" +
                                "            shop_background_opacity: parseInt(shopBackgroundOpacity)\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', '背景设置已成功保存！\\n\\n所有页面的背景配置已更新');\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function resetBackgroundSettings() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🔄 重置背景设置',\n" +
                                "        '确定要重置所有背景设置为默认值吗？\\n\\n这将清空所有背景图片URL并重置透明度为30%',\n" +
                                "        function() {\n" +
                                "            // 重置所有背景图片URL\n" +
                                "            document.getElementById('backgroundImage').value = '';\n" +
                                "            document.getElementById('leaderboardBackgroundImage').value = '';\n" +
                                "            document.getElementById('punishmentBackgroundImage').value = '';\n" +
                                "            document.getElementById('loginBackgroundImage').value = '';\n" +
                                "            document.getElementById('shopBackgroundImage').value = '';\n" +
                                "            \n" +
                                "            // 重置所有透明度为默认值30%\n" +
                                "            document.getElementById('backgroundOpacity').value = '30';\n" +
                                "            document.getElementById('leaderboardBackgroundOpacity').value = '30';\n" +
                                "            document.getElementById('punishmentBackgroundOpacity').value = '30';\n" +
                                "            document.getElementById('loginBackgroundOpacity').value = '30';\n" +
                                "            document.getElementById('shopBackgroundOpacity').value = '30';\n" +
                                "            \n" +
                                "            // 更新透明度显示\n" +
                                "            updateBackgroundOpacityDisplay(30);\n" +
                                "            updateLeaderboardOpacityDisplay(30);\n" +
                                "            updatePunishmentOpacityDisplay(30);\n" +
                                "            updateLoginOpacityDisplay(30);\n" +
                                "            updateShopOpacityDisplay(30);\n" +
                                "            \n" +
                                "            showSuccessModal('✅ 重置成功', '所有背景设置已重置为默认值\\n请点击保存按钮应用更改');\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function previewBackgrounds() {\n" +
                                "    const lotteryBg = document.getElementById('backgroundImage').value.trim();\n" +
                                "    const leaderboardBg = document.getElementById('leaderboardBackgroundImage').value.trim();\n"
                                +
                                "    const punishmentBg = document.getElementById('punishmentBackgroundImage').value.trim();\n"
                                +
                                "    const loginBg = document.getElementById('loginBackgroundImage').value.trim();\n" +
                                "    const shopBg = document.getElementById('shopBackgroundImage').value.trim();\n" +
                                "    \n" +
                                "    if (!lotteryBg && !leaderboardBg && !punishmentBg && !loginBg && !shopBg) {\n" +
                                "        showErrorModal('⚠️ 无法预览', '请先设置背景图片URL或上传文件后再预览');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    showBackgroundPreviewModal(lotteryBg, leaderboardBg, punishmentBg, loginBg, shopBg);\n"
                                +
                                "}\n" +
                                "\n" +
                                "function showBackgroundPreviewModal(lotteryBg, leaderboardBg, punishmentBg, loginBg, shopBg) {\n"
                                +
                                "    // 创建模态框\n" +
                                "    const modal = document.createElement('div');\n" +
                                "    modal.className = 'preview-modal';\n" +
                                "    modal.innerHTML = `\n" +
                                "        <div class=\"preview-modal-content\">\n" +
                                "            <div class=\"preview-modal-header\">\n" +
                                "                <h3>🎨 背景预览</h3>\n" +
                                "                <button class=\"preview-close-btn\" onclick=\"closePreviewModal()\">&times;</button>\n"
                                +
                                "            </div>\n" +
                                "            <div class=\"preview-modal-body\">\n" +
                                "                <div class=\"preview-grid\">\n" +
                                "                    ${lotteryBg ? `\n" +
                                "                        <div class=\"preview-card\">\n" +
                                "                            <div class=\"preview-image\" style=\"background-image: url('${lotteryBg}');\"></div>\n"
                                +
                                "                            <div class=\"preview-info\">\n" +
                                "                                <h4>🎮 抽奖页面</h4>\n" +
                                "                                <a href=\"/user\" target=\"_blank\" class=\"preview-link\">查看实际效果</a>\n"
                                +
                                "                            </div>\n" +
                                "                        </div>\n" +
                                "                    ` : ''}\n" +
                                "                    ${leaderboardBg ? `\n" +
                                "                        <div class=\"preview-card\">\n" +
                                "                            <div class=\"preview-image\" style=\"background-image: url('${leaderboardBg}');\"></div>\n"
                                +
                                "                            <div class=\"preview-info\">\n" +
                                "                                <h4>🏆 排行榜页面</h4>\n" +
                                "                                <a href=\"/leaderboard\" target=\"_blank\" class=\"preview-link\">查看实际效果</a>\n"
                                +
                                "                            </div>\n" +
                                "                        </div>\n" +
                                "                    ` : ''}\n" +
                                "                    ${punishmentBg ? `\n" +
                                "                        <div class=\"preview-card\">\n" +
                                "                            <div class=\"preview-image\" style=\"background-image: url('${punishmentBg}');\"></div>\n"
                                +
                                "                            <div class=\"preview-info\">\n" +
                                "                                <h4>📋 处罚记录页面</h4>\n" +
                                "                                <a href=\"/punishment\" target=\"_blank\" class=\"preview-link\">查看实际效果</a>\n"
                                +
                                "                            </div>\n" +
                                "                        </div>\n" +
                                "                    ` : ''}\n" +
                                "                    ${loginBg ? `\n" +
                                "                        <div class=\"preview-card\">\n" +
                                "                            <div class=\"preview-image\" style=\"background-image: url('${loginBg}');\"></div>\n"
                                +
                                "                            <div class=\"preview-info\">\n" +
                                "                                <h4>🔐 管理员登录页面</h4>\n" +
                                "                                <a href=\"/admin/login\" target=\"_blank\" class=\"preview-link\">查看实际效果</a>\n"
                                +
                                "                            </div>\n" +
                                "                        </div>\n" +
                                "                    ` : ''}\n" +
                                "                    ${shopBg ? `\n" +
                                "                        <div class=\"preview-card\">\n" +
                                "                            <div class=\"preview-image\" style=\"background-image: url('${shopBg}');\"></div>\n"
                                +
                                "                            <div class=\"preview-info\">\n" +
                                "                                <h4>🛒 积分商店页面</h4>\n" +
                                "                                <a href=\"/shop\" target=\"_blank\" class=\"preview-link\">查看实际效果</a>\n"
                                +
                                "                            </div>\n" +
                                "                        </div>\n" +
                                "                    ` : ''}\n" +
                                "                </div>\n" +
                                "                <div class=\"preview-tips\">\n" +
                                "                    <p>💡 提示：如果刚刚修改了设置，请先保存后再预览实际效果</p>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(modal);\n" +
                                "    \n" +
                                "    // 添加动画效果\n" +
                                "    setTimeout(() => {\n" +
                                "        modal.classList.add('show');\n" +
                                "    }, 10);\n" +
                                "    \n" +
                                "    // 点击背景关闭\n" +
                                "    modal.addEventListener('click', (e) => {\n" +
                                "        if (e.target === modal) {\n" +
                                "            closePreviewModal();\n" +
                                "        }\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function closePreviewModal() {\n" +
                                "    const modal = document.querySelector('.preview-modal');\n" +
                                "    if (modal) {\n" +
                                "        modal.classList.remove('show');\n" +
                                "        setTimeout(() => {\n" +
                                "            modal.remove();\n" +
                                "        }, 300);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function showSuccessModal(title, message) {\n" +
                                "    const modal = document.createElement('div');\n" +
                                "    modal.className = 'custom-modal';\n" +
                                "    modal.innerHTML = `\n" +
                                "        <div class=\"custom-modal-content success-modal\">\n" +
                                "            <div class=\"custom-modal-header success-header\">\n" +
                                "                <h3>${title}</h3>\n" +
                                "            </div>\n" +
                                "            <div class=\"custom-modal-body\">\n" +
                                "                <div class=\"modal-icon success-icon\">✅</div>\n" +
                                "                <p>${message.replace(/\\\\n/g, '<br>')}</p>\n" +
                                "            </div>\n" +
                                "            <div class=\"custom-modal-footer\">\n" +
                                "                <button class=\"modal-btn success-btn\" onclick=\"closeCustomModal()\">确定</button>\n"
                                +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(modal);\n" +
                                "    \n" +
                                "    // 添加点击背景关闭功能\n" +
                                "    modal.addEventListener('click', (e) => {\n" +
                                "        if (e.target === modal) {\n" +
                                "            closeCustomModal();\n" +
                                "        }\n" +
                                "    });\n" +
                                "    \n" +
                                "    setTimeout(() => modal.classList.add('show'), 10);\n" +
                                "    \n" +
                                "    // 3秒后自动关闭\n" +
                                "    setTimeout(() => {\n" +
                                "        if (modal && modal.parentNode) {\n" +
                                "            closeCustomModal();\n" +
                                "        }\n" +
                                "    }, 3000);\n" +
                                "}\n" +
                                "\n" +
                                "function showErrorModal(title, message) {\n" +
                                "    const modal = document.createElement('div');\n" +
                                "    modal.className = 'custom-modal';\n" +
                                "    modal.innerHTML = `\n" +
                                "        <div class=\"custom-modal-content error-modal\">\n" +
                                "            <div class=\"custom-modal-header error-header\">\n" +
                                "                <h3>${title}</h3>\n" +
                                "            </div>\n" +
                                "            <div class=\"custom-modal-body\">\n" +
                                "                <div class=\"modal-icon error-icon\">❌</div>\n" +
                                "                <p>${message.replace(/\\\\n/g, '<br>')}</p>\n" +
                                "            </div>\n" +
                                "            <div class=\"custom-modal-footer\">\n" +
                                "                <button class=\"modal-btn error-btn\" onclick=\"closeCustomModal()\">确定</button>\n"
                                +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(modal);\n" +
                                "    \n" +
                                "    // 添加点击背景关闭功能\n" +
                                "    modal.addEventListener('click', (e) => {\n" +
                                "        if (e.target === modal) {\n" +
                                "            closeCustomModal();\n" +
                                "        }\n" +
                                "    });\n" +
                                "    \n" +
                                "    setTimeout(() => modal.classList.add('show'), 10);\n" +
                                "}\n" +
                                "\n" +
                                "function closeCustomModal() {\n" +
                                "    const modal = document.querySelector('.custom-modal');\n" +
                                "    if (modal) {\n" +
                                "        modal.classList.remove('show');\n" +
                                "        setTimeout(() => {\n" +
                                "            modal.remove();\n" +
                                "            // 清理全局函数\n" +
                                "            if (window.confirmAction) {\n" +
                                "                delete window.confirmAction;\n" +
                                "            }\n" +
                                "        }, 300);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 兼容旧版本的showResult函数，使用装饰性弹窗\n" +
                                "function showResult(message, type) {\n" +
                                "    if (type === 'success') {\n" +
                                "        showSuccessModal('✅ 操作成功', message);\n" +
                                "    } else if (type === 'error') {\n" +
                                "        showErrorModal('❌ 操作失败', message);\n" +
                                "    } else {\n" +
                                "        showSuccessModal('💡 提示信息', message);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 透明度显示更新函数\n" +
                                "function updateHomepageOpacityDisplay(value) {\n" +
                                "    document.getElementById('homepageOpacityValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "function updateBackgroundOpacityDisplay(value) {\n" +
                                "    document.getElementById('backgroundOpacityValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "function updateLeaderboardOpacityDisplay(value) {\n" +
                                "    document.getElementById('leaderboardOpacityValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "function updatePunishmentOpacityDisplay(value) {\n" +
                                "    document.getElementById('punishmentOpacityValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "function updateLoginOpacityDisplay(value) {\n" +
                                "    document.getElementById('loginOpacityValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "function updateShopOpacityDisplay(value) {\n" +
                                "    document.getElementById('shopOpacityValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                this.generateHomepageSettingsFunctions() +
                                this.generatePunishmentSettingsFunctions() +
                                this.generateFileUploadFunctions() +
                                this.generateInterfaceSettingsFunctions() +
                                this.generateAnimationFunctions() +
                                this.generateAdvancedSettingsFunctions() +
                                this.generateShopSettingsFunctions() +
                                this.generateTabSwitchFunction();
        }

        /**
         * 生成首页设置相关的JavaScript函数
         */
        private String generateHomepageSettingsFunctions() {
                return "// 首页设置相关函数\n" +
                                "function saveHomepageSettings() {\n" +
                                "    const homepageTitle = document.getElementById('homepageTitle').value.trim();\n" +
                                "    const homepageSubtitle = document.getElementById('homepageSubtitle').value.trim();\n"
                                +
                                "    const homepageDescription = document.getElementById('homepageDescription').value.trim();\n"
                                +
                                "    const homepageBackgroundImage = document.getElementById('homepageBackgroundImage').value.trim();\n"
                                +
                                "    const homepageBackgroundOpacity = document.getElementById('homepageBackgroundOpacity').value;\n"
                                +
                                "    const showLotteryCard = document.getElementById('showLotteryCard').checked;\n" +
                                "    const showLeaderboardCard = document.getElementById('showLeaderboardCard').checked;\n"
                                +
                                "    const showShopCard = document.getElementById('showShopCard').checked;\n" +
                                "    const showPunishmentCard = document.getElementById('showPunishmentCard').checked;\n"
                                +
                                "    const showSystemStatus = document.getElementById('showSystemStatus').checked;\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行保存\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_homepage_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            homepage_title: homepageTitle,\n" +
                                "            homepage_subtitle: homepageSubtitle,\n" +
                                "            homepage_description: homepageDescription,\n" +
                                "            homepage_background_image: homepageBackgroundImage,\n" +
                                "            homepage_background_opacity: parseInt(homepageBackgroundOpacity),\n" +
                                "            show_lottery_card: showLotteryCard,\n" +
                                "            show_leaderboard_card: showLeaderboardCard,\n" +
                                "            show_shop_card: showShopCard,\n" +
                                "            show_punishment_card: showPunishmentCard,\n" +
                                "            show_system_status: showSystemStatus\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', '首页设置已成功保存！\\n\\n首页内容和卡片显示配置已更新');\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function resetHomepageSettings() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🔄 重置首页设置',\n" +
                                "        '确定要重置首页设置为默认值吗？\\n\\n这将恢复默认的标题、描述和卡片显示设置',\n" +
                                "        function() {\n" +
                                "            document.getElementById('homepageTitle').value = '" + webServer.getTitle()
                                + "';\n" +
                                "            document.getElementById('homepageSubtitle').value = '欢迎来到游戏娱乐中心';\n" +
                                "            document.getElementById('homepageDescription').value = '🎯 参与精彩抽奖活动，赢取丰厚奖品\\n🏆 查看排行榜，与其他玩家一较高下\\n🛒 使用积分购买心仪的游戏道具';\n"
                                +
                                "            document.getElementById('homepageBackgroundImage').value = '';\n" +
                                "            document.getElementById('homepageBackgroundOpacity').value = '30';\n" +
                                "            updateHomepageOpacityDisplay(30);\n" +
                                "            document.getElementById('showLotteryCard').checked = true;\n" +
                                "            document.getElementById('showLeaderboardCard').checked = true;\n" +
                                "            document.getElementById('showShopCard').checked = true;\n" +
                                "            document.getElementById('showPunishmentCard').checked = true;\n" +
                                "            document.getElementById('showSystemStatus').checked = true;\n" +
                                "            showSuccessModal('✅ 重置成功', '首页设置已重置为默认值\\n请点击保存按钮应用更改');\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function previewHomepage() {\n" +
                                "    // 直接跳转到首页\n" +
                                "    window.open('/', '_blank');\n" +
                                "}\n" +
                                "\n" +
                                "function showPreviewModal(title, message, url, linkText) {\n" +
                                "    const modal = document.createElement('div');\n" +
                                "    modal.className = 'custom-modal';\n" +
                                "    modal.innerHTML = `\n" +
                                "        <div class=\"custom-modal-content preview-modal-style\">\n" +
                                "            <div class=\"custom-modal-header preview-header\">\n" +
                                "                <h3>${title}</h3>\n" +
                                "            </div>\n" +
                                "            <div class=\"custom-modal-body\">\n" +
                                "                <div class=\"modal-icon preview-icon\">👁️</div>\n" +
                                "                <p>${message}</p>\n" +
                                "                <div class=\"preview-actions\">\n" +
                                "                    <a href=\"${url}\" target=\"_blank\" class=\"preview-action-btn\" onclick=\"setTimeout(closeCustomModal, 500)\">${linkText}</a>\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"preview-tip\">\n" +
                                "                    <small>💡 提示：如果刚刚修改了设置，请先保存后再预览</small>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "            <div class=\"custom-modal-footer\">\n" +
                                "                <button class=\"modal-btn cancel-btn\" onclick=\"closeCustomModal()\">关闭</button>\n"
                                +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(modal);\n" +
                                "    \n" +
                                "    // 添加点击背景关闭功能\n" +
                                "    modal.addEventListener('click', (e) => {\n" +
                                "        if (e.target === modal) {\n" +
                                "            closeCustomModal();\n" +
                                "        }\n" +
                                "    });\n" +
                                "    \n" +
                                "    setTimeout(() => modal.classList.add('show'), 10);\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成封禁设置相关的JavaScript函数
         */
        private String generatePunishmentSettingsFunctions() {
                return "// 封禁设置相关函数\n" +
                                "function savePunishmentSettings() {\n" +
                                "    const punishmentBackgroundImage = document.getElementById('punishmentBackgroundImage').value.trim();\n"
                                +
                                "    const adminLogoUrl = document.getElementById('adminLogoUrl').value.trim();\n" +
                                "    const userLogoUrl = document.getElementById('userLogoUrl').value.trim();\n" +
                                "    const punishmentBackgroundOpacity = document.getElementById('punishmentBackgroundOpacity').value;\n"
                                +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_punishment_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            punishment_background_image: punishmentBackgroundImage,\n" +
                                "            admin_logo_url: adminLogoUrl,\n" +
                                "            user_logo_url: userLogoUrl,\n" +
                                "            punishment_background_opacity: parseInt(punishmentBackgroundOpacity)\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', '封禁设置已成功保存！\\n\\n处罚记录页面背景和Logo配置已更新');\n"
                                +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function resetPunishmentSettings() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🔄 重置封禁设置',\n" +
                                "        '确定要重置封禁设置为默认值吗？\\n\\n这将清空处罚记录页面背景图片URL、Logo设置并重置透明度为30%',\n"
                                +
                                "        function() {\n" +
                                "            document.getElementById('punishmentBackgroundImage').value = '';\n" +
                                "            document.getElementById('adminLogoUrl').value = '';\n" +
                                "            document.getElementById('userLogoUrl').value = '';\n" +
                                "            document.getElementById('punishmentBackgroundOpacity').value = '30';\n" +
                                "            \n" +
                                "            updatePunishmentOpacityDisplay(30);\n" +
                                "            \n" +
                                "            showSuccessModal('✅ 重置成功', '封禁设置已重置为默认值\\n请点击保存按钮应用更改');\n"
                                +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function previewPunishmentPages() {\n" +
                                "    const punishmentBg = document.getElementById('punishmentBackgroundImage').value.trim();\n"
                                +
                                "    \n" +
                                "    if (!punishmentBg) {\n" +
                                "        showErrorModal('⚠️ 无法预览', '请先设置处罚记录页面背景图片URL后再预览');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    showPunishmentPreviewModal(punishmentBg);\n" +
                                "}\n" +
                                "\n" +
                                "function showPunishmentPreviewModal(punishmentBg) {\n" +
                                "    const modal = document.createElement('div');\n" +
                                "    modal.className = 'preview-modal';\n" +
                                "    modal.innerHTML = `\n" +
                                "        <div class=\"preview-modal-content\">\n" +
                                "            <div class=\"preview-modal-header\">\n" +
                                "                <h3>🎨 封禁设置预览</h3>\n" +
                                "                <button class=\"preview-close-btn\" onclick=\"closePreviewModal()\">&times;</button>\n"
                                +
                                "            </div>\n" +
                                "            <div class=\"preview-modal-body\">\n" +
                                "                <div class=\"preview-grid\">\n" +
                                "                    <div class=\"preview-card\">\n" +
                                "                        <div class=\"preview-image\" style=\"background-image: url('${punishmentBg}');\"></div>\n"
                                +
                                "                        <div class=\"preview-info\">\n" +
                                "                            <h4>📋 处罚记录页面</h4>\n" +
                                "                            <a href=\"/punishments\" target=\"_blank\" class=\"preview-link\">查看实际效果</a>\n"
                                +
                                "                        </div>\n" +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "                <div class=\"preview-tips\">\n" +
                                "                    <p>💡 提示：如果刚刚修改了设置，请先保存后再预览实际效果</p>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(modal);\n" +
                                "    setTimeout(() => modal.classList.add('show'), 10);\n" +
                                "    \n" +
                                "    modal.addEventListener('click', (e) => {\n" +
                                "        if (e.target === modal) {\n" +
                                "            closePreviewModal();\n" +
                                "        }\n" +
                                "    });\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成文件上传相关的JavaScript函数
         */
        private String generateFileUploadFunctions() {
                return "// 文件上传功能\n" +
                                "function uploadBackgroundImage(type) {\n" +
                                "    let fileInputId, urlInputId;\n" +
                                "    \n" +
                                "    if (type === 'lottery') {\n" +
                                "        fileInputId = 'backgroundImageFile';\n" +
                                "        urlInputId = 'backgroundImage';\n" +
                                "    } else if (type === 'leaderboard') {\n" +
                                "        fileInputId = 'leaderboardBackgroundImageFile';\n" +
                                "        urlInputId = 'leaderboardBackgroundImage';\n" +
                                "    } else if (type === 'login') {\n" +
                                "        fileInputId = 'loginBackgroundFile';\n" +
                                "        urlInputId = 'loginBackgroundImage';\n" +
                                "    } else if (type === 'shop') {\n" +
                                "        fileInputId = 'shopBackgroundImageFile';\n" +
                                "        urlInputId = 'shopBackgroundImage';\n" +
                                "    } else if (type === 'homepage') {\n" +
                                "        fileInputId = 'homepageBackgroundImageFile';\n" +
                                "        urlInputId = 'homepageBackgroundImage';\n" +
                                "    } else if (type === 'punishment') {\n" +
                                "        fileInputId = 'punishmentBackgroundImageFile';\n" +
                                "        urlInputId = 'punishmentBackgroundImage';\n" +
                                "    } else if (type === 'admin') {\n" +
                                "        fileInputId = 'adminBackgroundImageFile';\n" +
                                "        urlInputId = 'adminBackgroundImage';\n" +
                                "    } else {\n" +
                                "        showResult('❌ 未知的背景类型', 'error');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const fileInput = document.getElementById(fileInputId);\n" +
                                "    const file = fileInput.files[0];\n" +
                                "    \n" +
                                "    if (!file) {\n" +
                                "        showErrorModal('⚠️ 未选择文件', '请先选择要上传的文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件类型\n" +
                                "    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];\n" +
                                "    if (!allowedTypes.includes(file.type)) {\n" +
                                "        showErrorModal('❌ 文件格式错误', '只支持JPG、PNG、GIF格式的图片文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件大小（10MB）\n" +
                                "    if (file.size > 10 * 1024 * 1024) {\n" +
                                "        showErrorModal('❌ 文件过大', '文件大小不能超过10MB');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行上传\n" +
                                "    \n" +
                                "    const formData = new FormData();\n" +
                                "    formData.append('file', file);\n" +
                                "    \n" +
                                "    fetch('/api?action=upload_background_image&api_key=" + adminKey
                                + "&type=' + type, {\n" +
                                "        method: 'POST',\n" +
                                "        body: formData\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 上传成功', '文件上传成功！\\n\\n背景图片URL已自动填入');\n" +
                                "            // 更新对应的URL输入框\n" +
                                "            document.getElementById(urlInputId).value = data.file_url;\n" +
                                "            // 清空文件选择\n" +
                                "            fileInput.value = '';\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 上传失败', '上传失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// Logo文件上传功能\n" +
                                "function uploadLoginLogo() {\n" +
                                "    const fileInput = document.getElementById('loginLogoFile');\n" +
                                "    const file = fileInput.files[0];\n" +
                                "    \n" +
                                "    if (!file) {\n" +
                                "        showErrorModal('⚠️ 未选择文件', '请先选择要上传的Logo文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件类型\n" +
                                "    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];\n"
                                +
                                "    if (!allowedTypes.includes(file.type)) {\n" +
                                "        showErrorModal('❌ 文件格式错误', '只支持JPG、PNG、GIF、SVG格式的Logo文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件大小（2MB）\n" +
                                "    if (file.size > 2 * 1024 * 1024) {\n" +
                                "        showErrorModal('❌ 文件过大', 'Logo文件大小不能超过2MB');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行上传\n" +
                                "    \n" +
                                "    const formData = new FormData();\n" +
                                "    formData.append('file', file);\n" +
                                "    \n" +
                                "    fetch('/api?action=upload_login_logo&api_key=" + adminKey + "', {\n" +
                                "        method: 'POST',\n" +
                                "        body: formData\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 上传成功', 'Logo文件上传成功！\\n\\nLogo URL已自动填入');\n" +
                                "            // 更新Logo URL输入框\n" +
                                "            document.getElementById('loginLogoUrl').value = data.file_url;\n" +
                                "            // 清空文件选择\n" +
                                "            fileInput.value = '';\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 上传失败', '上传失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 通用Logo文件上传功能\n" +
                                "function uploadLogo(type) {\n" +
                                "    let fileInputId, urlInputId;\n" +
                                "    \n" +
                                "    if (type === 'admin') {\n" +
                                "        fileInputId = 'adminLogoFile';\n" +
                                "        urlInputId = 'adminLogoUrl';\n" +
                                "    } else if (type === 'user') {\n" +
                                "        fileInputId = 'userLogoFile';\n" +
                                "        urlInputId = 'userLogoUrl';\n" +
                                "    } else {\n" +
                                "        showErrorModal('❌ 未知的Logo类型', '未知的Logo类型: ' + type);\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const fileInput = document.getElementById(fileInputId);\n" +
                                "    const file = fileInput.files[0];\n" +
                                "    \n" +
                                "    if (!file) {\n" +
                                "        showErrorModal('⚠️ 未选择文件', '请先选择要上传的Logo文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件类型\n" +
                                "    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];\n"
                                +
                                "    if (!allowedTypes.includes(file.type)) {\n" +
                                "        showErrorModal('❌ 文件格式错误', '只支持JPG、PNG、GIF、SVG格式的Logo文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件大小（2MB）\n" +
                                "    if (file.size > 2 * 1024 * 1024) {\n" +
                                "        showErrorModal('❌ 文件过大', 'Logo文件大小不能超过2MB');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const formData = new FormData();\n" +
                                "    formData.append('file', file);\n" +
                                "    \n" +
                                "    fetch('/api?action=upload_logo&api_key=" + adminKey + "&type=' + type, {\n" +
                                "        method: 'POST',\n" +
                                "        body: formData\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            const logoTypeName = type === 'admin' ? '管理员' : '普通用户';\n" +
                                "            showSuccessModal('✅ 上传成功', logoTypeName + 'Logo文件上传成功！\\n\\nLogo URL已自动填入');\n"
                                +
                                "            // 更新Logo URL输入框\n" +
                                "            document.getElementById(urlInputId).value = data.file_url;\n" +
                                "            // 清空文件选择\n" +
                                "            fileInput.value = '';\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 上传失败', '上传失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 音效文件上传功能\n" +
                                "function uploadSoundFile(type) {\n" +
                                "    const fileInputId = type === 'opening' ? 'openingSoundFile' : 'winningSoundFile';\n"
                                +
                                "    const fileInput = document.getElementById(fileInputId);\n" +
                                "    const file = fileInput.files[0];\n" +
                                "    \n" +
                                "    if (!file) {\n" +
                                "        showErrorModal('⚠️ 未选择文件', '请先选择要上传的音效文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件类型\n" +
                                "    const allowedTypes = ['audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg'];\n" +
                                "    if (!allowedTypes.includes(file.type)) {\n" +
                                "        showErrorModal('❌ 文件格式错误', '只支持MP3、WAV、OGG格式的音效文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件大小（2MB）\n" +
                                "    if (file.size > 2 * 1024 * 1024) {\n" +
                                "        showErrorModal('❌ 文件过大', '音效文件大小不能超过2MB');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行上传\n" +
                                "    \n" +
                                "    const formData = new FormData();\n" +
                                "    formData.append('file', file);\n" +
                                "    \n" +
                                "    fetch('/api?action=upload_sound_file&api_key=" + adminKey + "&type=' + type, {\n" +
                                "        method: 'POST',\n" +
                                "        body: formData\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 上传成功', '音效文件上传成功！\\n\\n音效URL已自动填入');\n" +
                                "            // 更新对应的URL输入框\n" +
                                "            const urlInputId = type === 'opening' ? 'openingSoundUrl' : 'winningSoundUrl';\n"
                                +
                                "            document.getElementById(urlInputId).value = data.file_url;\n" +
                                "            // 清空文件选择\n" +
                                "            fileInput.value = '';\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 上传失败', '上传失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成界面设置相关的JavaScript函数
         */
        private String generateInterfaceSettingsFunctions() {
                return "// 界面设置相关函数\n" +
                                "function saveInterfaceSettings() {\n" +
                                "    const backgroundImage = document.getElementById('backgroundImage').value.trim();\n"
                                +
                                "    const leaderboardBackgroundImage = document.getElementById('leaderboardBackgroundImage').value.trim();\n"
                                +
                                "    const enableOpeningAnimation = document.getElementById('enableOpeningAnimation').checked;\n"
                                +
                                "    const animationDuration = document.getElementById('animationDuration').value;\n" +
                                "    const animationStyle = document.getElementById('animationStyle').value;\n" +
                                "    const customAnimationUrl = document.getElementById('customAnimationUrl').value.trim();\n"
                                +
                                "    const enableSoundEffects = document.getElementById('enableSoundEffects').checked;\n"
                                +
                                "    const openingSoundUrl = document.getElementById('openingSoundUrl').value.trim();\n"
                                +
                                "    const winningSoundUrl = document.getElementById('winningSoundUrl').value.trim();\n"
                                +
                                "    const soundVolume = document.getElementById('soundVolume').value;\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行保存\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_interface_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            background_image: backgroundImage,\n" +
                                "            leaderboard_background_image: leaderboardBackgroundImage,\n" +
                                "            enable_opening_animation: enableOpeningAnimation,\n" +
                                "            animation_duration: parseFloat(animationDuration),\n" +
                                "            animation_style: animationStyle,\n" +
                                "            custom_animation_url: customAnimationUrl,\n" +
                                "            enable_sound_effects: enableSoundEffects,\n" +
                                "            opening_sound_url: openingSoundUrl,\n" +
                                "            winning_sound_url: winningSoundUrl,\n" +
                                "            sound_volume: parseInt(soundVolume)\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', '界面设置已成功保存！\\n\\n背景、动画和音效配置已更新');\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function resetInterfaceSettings() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🔄 重置界面设置',\n" +
                                "        '确定要重置所有界面设置为默认值吗？\\n\\n这将恢复默认的背景、动画和音效设置',\n" +
                                "        function() {\n" +
                                "            document.getElementById('backgroundImage').value = '';\n" +
                                "            document.getElementById('leaderboardBackgroundImage').value = '';\n" +
                                "            document.getElementById('enableOpeningAnimation').checked = true;\n" +
                                "            document.getElementById('animationDuration').value = '3.0';\n" +
                                "            document.getElementById('animationStyle').value = 'classic';\n" +
                                "            document.getElementById('customAnimationUrl').value = '';\n" +
                                "            toggleCustomAnimation();\n" +
                                "            document.getElementById('enableSoundEffects').checked = true;\n" +
                                "            document.getElementById('openingSoundUrl').value = '';\n" +
                                "            document.getElementById('winningSoundUrl').value = '';\n" +
                                "            document.getElementById('soundVolume').value = '50';\n" +
                                "            updateVolumeDisplay(50);\n" +
                                "            showSuccessModal('✅ 重置成功', '界面设置已重置为默认值\\n请点击保存按钮应用更改');\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function previewInterface() {\n" +
                                "    showPreviewModal('🎨 界面预览', '点击下方按钮查看界面效果', '/user', '查看抽奖页面');\n" +
                                "}\n" +
                                "\n" +
                                "function testAnimation() {\n" +
                                "    showResult('🎬 动画测试功能将在抽奖页面中实现，请前往抽奖页面体验开箱动画效果', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "// 音效音量显示更新函数\n" +
                                "function updateOpeningVolumeDisplay(value) {\n" +
                                "    document.getElementById('openingVolumeValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "function updateWinningVolumeDisplay(value) {\n" +
                                "    document.getElementById('winningVolumeValue').textContent = value + '%';\n" +
                                "}\n" +
                                "\n" +
                                "// 音效测试函数\n" +
                                "function testOpeningSound() {\n" +
                                "    const openingSoundUrl = document.getElementById('openingSoundUrl').value.trim();\n"
                                +
                                "    const volume = document.getElementById('openingSoundVolume').value / 100;\n" +
                                "    const enabled = document.getElementById('enableOpeningSoundEffects').checked;\n" +
                                "    \n" +
                                "    if (!enabled) {\n" +
                                "        showResult('请先启用开箱音效', 'info');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    if (!openingSoundUrl) {\n" +
                                "        showResult('请先设置开箱音效URL或上传音效文件', 'info');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    try {\n" +
                                "        const audio = new Audio(openingSoundUrl);\n" +
                                "        audio.volume = volume;\n" +
                                "        audio.play().then(() => {\n" +
                                "            showResult('🔊 开箱音效测试播放中...', 'success');\n" +
                                "        }).catch(error => {\n" +
                                "            showResult('❌ 开箱音效播放失败，请检查音效文件URL', 'error');\n" +
                                "        });\n" +
                                "    } catch (error) {\n" +
                                "        showResult('❌ 开箱音效播放失败: ' + error.message, 'error');\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function testWinningSound() {\n" +
                                "    const winningSoundUrl = document.getElementById('winningSoundUrl').value.trim();\n"
                                +
                                "    const volume = document.getElementById('winningSoundVolume').value / 100;\n" +
                                "    const enabled = document.getElementById('enableWinningSoundEffects').checked;\n" +
                                "    \n" +
                                "    if (!enabled) {\n" +
                                "        showResult('请先启用中奖音效', 'info');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    if (!winningSoundUrl) {\n" +
                                "        showResult('请先设置中奖音效URL或上传音效文件', 'info');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    try {\n" +
                                "        const audio = new Audio(winningSoundUrl);\n" +
                                "        audio.volume = volume;\n" +
                                "        audio.play().then(() => {\n" +
                                "            showResult('🎉 中奖音效测试播放中...', 'success');\n" +
                                "        }).catch(error => {\n" +
                                "            showResult('❌ 中奖音效播放失败，请检查音效文件URL', 'error');\n" +
                                "        });\n" +
                                "    } catch (error) {\n" +
                                "        showResult('❌ 中奖音效播放失败: ' + error.message, 'error');\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 音效开关切换函数\n" +
                                "function toggleOpeningSoundEffects() {\n" +
                                "    const enabled = document.getElementById('enableOpeningSoundEffects').checked;\n" +
                                "    showResult(enabled ? '✅ 开箱音效已启用' : '❌ 开箱音效已禁用', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function toggleWinningSoundEffects() {\n" +
                                "    const enabled = document.getElementById('enableWinningSoundEffects').checked;\n" +
                                "    showResult(enabled ? '✅ 中奖音效已启用' : '❌ 中奖音效已禁用', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function toggleOpeningAnimation() {\n" +
                                "    const enabled = document.getElementById('enableOpeningAnimation').checked;\n" +
                                "    showResult(enabled ? '✅ 开箱动画已启用' : '❌ 开箱动画已禁用', 'info');\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成动画相关的JavaScript函数
         */
        private String generateAnimationFunctions() {
                return "function toggleCustomAnimation() {\n" +
                                "    const animationStyle = document.getElementById('animationStyle').value;\n" +
                                "    const customGroup = document.getElementById('customAnimationGroup');\n" +
                                "    \n" +
                                "    if (animationStyle === 'custom') {\n" +
                                "        customGroup.style.display = 'block';\n" +
                                "        showResult('💡 请上传自定义动画文件或输入动画URL', 'info');\n" +
                                "        // 检查是否有现有的动画URL，如果有则显示预览\n" +
                                "        const customUrl = document.getElementById('customAnimationUrl').value.trim();\n"
                                +
                                "        if (customUrl) {\n" +
                                "            previewCustomAnimation(customUrl);\n" +
                                "        }\n" +
                                "    } else {\n" +
                                "        customGroup.style.display = 'none';\n" +
                                "        hideAnimationPreview();\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function uploadAnimationFile() {\n" +
                                "    const fileInput = document.getElementById('customAnimationFile');\n" +
                                "    const file = fileInput.files[0];\n" +
                                "    \n" +
                                "    if (!file) {\n" +
                                "        showErrorModal('⚠️ 未选择文件', '请先选择要上传的动画文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件类型\n" +
                                "    const allowedTypes = ['image/gif', 'video/mp4', 'video/webm'];\n" +
                                "    if (!allowedTypes.includes(file.type)) {\n" +
                                "        showErrorModal('❌ 文件格式错误', '只支持GIF、MP4、WebM格式的动画文件');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 检查文件大小（5MB）\n" +
                                "    if (file.size > 5 * 1024 * 1024) {\n" +
                                "        showErrorModal('❌ 文件过大', '动画文件大小不能超过5MB');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行上传\n" +
                                "    \n" +
                                "    const formData = new FormData();\n" +
                                "    formData.append('file', file);\n" +
                                "    \n" +
                                "    fetch('/api?action=upload_animation_file&api_key=" + adminKey + "', {\n" +
                                "        method: 'POST',\n" +
                                "        body: formData\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 上传成功', '动画文件上传成功！\\n\\n动画URL已自动填入，预览已更新');\n" +
                                "            // 更新URL输入框\n" +
                                "            document.getElementById('customAnimationUrl').value = data.file_url;\n" +
                                "            // 自动切换到自定义动画风格\n" +
                                "            document.getElementById('animationStyle').value = 'custom';\n" +
                                "            // 显示自定义动画组\n" +
                                "            document.getElementById('customAnimationGroup').style.display = 'block';\n"
                                +
                                "            // 显示预览（添加时间戳避免缓存）\n" +
                                "            const previewUrl = data.file_url + '?t=' + new Date().getTime();\n" +
                                "            previewCustomAnimation(previewUrl);\n" +
                                "            // 清空文件选择\n" +
                                "            fileInput.value = '';\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 上传失败', '上传失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function previewCustomAnimation(url) {\n" +
                                "    const previewDiv = document.getElementById('animationPreview');\n" +
                                "    const previewContent = document.getElementById('previewContent');\n" +
                                "    \n" +
                                "    if (!url) {\n" +
                                "        hideAnimationPreview();\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 根据文件扩展名判断类型\n" +
                                "    const extension = url.split('.').pop().toLowerCase();\n" +
                                "    \n" +
                                "    if (extension === 'gif') {\n" +
                                "        previewContent.innerHTML = '<img src=\"' + url + '\" style=\"max-width: 200px; max-height: 150px; border-radius: 4px;\" alt=\"动画预览\">';\n"
                                +
                                "    } else if (extension === 'mp4' || extension === 'webm') {\n" +
                                "        previewContent.innerHTML = '<video autoplay loop muted style=\"max-width: 200px; max-height: 150px; border-radius: 4px;\"><source src=\"' + url + '\" type=\"video/' + extension + '\">您的浏览器不支持视频播放</video>';\n"
                                +
                                "    } else {\n" +
                                "        previewContent.innerHTML = '<p style=\"color: #666;\">无法预览此格式的文件</p>';\n" +
                                "    }\n" +
                                "    \n" +
                                "    previewDiv.style.display = 'block';\n" +
                                "}\n" +
                                "\n" +
                                "function hideAnimationPreview() {\n" +
                                "    const previewDiv = document.getElementById('animationPreview');\n" +
                                "    previewDiv.style.display = 'none';\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成事件监听器
         */
        private String generateEventListeners() {
                return "// 监听自定义动画URL输入框变化\n" +
                                "document.addEventListener('DOMContentLoaded', function() {\n" +
                                "    const customUrlInput = document.getElementById('customAnimationUrl');\n" +
                                "    if (customUrlInput) {\n" +
                                "        customUrlInput.addEventListener('input', function() {\n" +
                                "            const url = this.value.trim();\n" +
                                "            if (url) {\n" +
                                "                previewCustomAnimation(url);\n" +
                                "            } else {\n" +
                                "                hideAnimationPreview();\n" +
                                "            }\n" +
                                "        });\n" +
                                "    }\n" +
                                "});\n" +
                                "\n";
        }

        /**
         * 生成首页设置标签页
         */
        private String generateHomepageTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"homepage-tab\" class=\"tab-pane active\">\n");
                html.append("                        <div class=\"content-grid\">\n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🏠 首页基本设置</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"homepageTitle\">首页主标题:</label>\n");
                html.append("                                    <input type=\"text\" id=\"homepageTitle\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.homepage.title", webServer.getTitle()))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入首页主标题\">\n");
                html.append("                                    <small>显示在首页顶部的主标题，支持emoji表情</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"homepageSubtitle\">首页副标题:</label>\n");
                html.append("                                    <input type=\"text\" id=\"homepageSubtitle\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.homepage.subtitle", "欢迎来到游戏娱乐中心"))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入首页副标题\">\n");
                html.append("                                    <small>显示在主标题下方的副标题</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"homepageDescription\">首页描述:</label>\n");
                html.append("                                    <textarea id=\"homepageDescription\" class=\"form-input\" rows=\"4\" \n");
                html.append("                                              placeholder=\"输入首页描述内容，每行一个特色功能\">")
                                .append(plugin.getConfig().getString("website.homepage.description",
                                                "🎯 参与精彩抽奖活动，赢取丰厚奖品\n🏆 查看排行榜，与其他玩家一较高下\n🛒 使用积分购买心仪的游戏道具"))
                                .append("</textarea>\n");
                html.append("                                    <small>首页的功能描述，每行一个特色功能，支持emoji表情</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🖼️ 首页背景设置</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"homepageBackgroundImage\">背景图片URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"homepageBackgroundImage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.homepage.background-image", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入图片URL，留空使用默认渐变背景\">\n");
                html.append("                                    <small>首页的背景图片，建议使用高质量图片以获得最佳效果</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"homepageBackgroundImageFile\" accept=\".jpg,.jpeg,.png,.gif\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadBackgroundImage('homepage')\" class=\"btn btn-secondary\">上传文件</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF格式，文件大小不超过10MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"homepageBackgroundOpacity\">背景透明度:</label>\n");
                html.append("                                    <input type=\"range\" id=\"homepageBackgroundOpacity\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.homepage.background-opacity", 30))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"80\" step=\"10\" oninput=\"updateHomepageOpacityDisplay(this.value)\">\n");
                html.append("                                    <div class=\"opacity-display\">\n");
                html.append("                                        <span>透明度: <strong id=\"homepageOpacityValue\">")
                                .append(plugin.getConfig().getInt("website.homepage.background-opacity", 30))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调整首页背景图片的透明度，数值越小背景图越明显，0为完全透明</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🎮 导航卡片设置</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"showLotteryCard\" ")
                                .append(plugin.getConfig().getBoolean("website.homepage.show-lottery-card", true)
                                                ? "checked"
                                                : "")
                                .append("> 显示抽奖系统卡片\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>在首页显示抽奖系统的导航卡片</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"showLeaderboardCard\" ")
                                .append(plugin.getConfig().getBoolean("website.homepage.show-leaderboard-card", true)
                                                ? "checked"
                                                : "")
                                .append("> 显示排行榜卡片\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>在首页显示排行榜的导航卡片</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"showShopCard\" ")
                                .append(plugin.getConfig().getBoolean("website.homepage.show-shop-card", true)
                                                ? "checked"
                                                : "")
                                .append("> 显示积分商店卡片\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>在首页显示积分商店的导航卡片</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"showPunishmentCard\" ")
                                .append(plugin.getConfig().getBoolean("litebans.display.show-punishment-card", true)
                                                ? "checked"
                                                : "")
                                .append("> 显示处罚记录卡片\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>在首页显示处罚记录的导航卡片</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>📊 系统状态区域</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"showSystemStatus\" ")
                                .append(plugin.getConfig().getBoolean("website.homepage.show-system-status", true)
                                                ? "checked"
                                                : "")
                                .append("> 显示系统状态区域\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>在首页底部显示系统状态和当前时间</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>⚙️ 操作</h3>\n");
                html.append("                                <div class=\"button-group vertical\">\n");
                html.append("                                    <button onclick=\"saveHomepageSettings()\" class=\"btn btn-primary\">💾 保存首页设置</button>\n");
                html.append("                                    <button onclick=\"resetHomepageSettings()\" class=\"btn btn-secondary\">🔄 重置为默认</button>\n");
                html.append("                                    <button onclick=\"previewHomepage()\" class=\"btn btn-info\">👁️ 预览首页</button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成背景设置标签页
         */
        private String generateBackgroundTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"background-tab\" class=\"tab-pane\">\n");
                html.append("                        <div class=\"content-grid\">\n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🎮 抽奖页面背景</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"backgroundImage\">背景图片URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"backgroundImage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.background-image", "")).append("\" \n");
                html.append("                                           placeholder=\"输入图片URL，留空使用默认渐变背景\">\n");
                html.append("                                    <small>支持JPG、PNG、GIF等格式，包括GIF动图。建议使用高质量图片以获得最佳效果</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"backgroundImageFile\" accept=\".jpg,.jpeg,.png,.gif\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadBackgroundImage('lottery')\" class=\"btn btn-secondary\">上传文件</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF格式，文件大小不超过10MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"backgroundOpacity\">背景透明度:</label>\n");
                html.append("                                    <input type=\"range\" id=\"backgroundOpacity\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.background-opacity", 30))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"80\" step=\"10\" oninput=\"updateBackgroundOpacityDisplay(this.value)\">\n");
                html.append("                                    <div class=\"opacity-display\">\n");
                html.append("                                        <span>透明度: <strong id=\"backgroundOpacityValue\">")
                                .append(plugin.getConfig().getInt("website.background-opacity", 30))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调整背景图片的透明度，数值越小背景图越明显，0为完全透明</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🔐 管理员登录页面设置</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"loginBackgroundImage\">背景图片URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"loginBackgroundImage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("web-server.login-background-image", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入图片URL，留空使用默认渐变背景\">\n");
                html.append("                                    <small>支持JPG、PNG、GIF等格式，包括GIF动图。建议使用高质量图片以获得最佳效果</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"loginBackgroundFile\" accept=\".jpg,.jpeg,.png,.gif\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadBackgroundImage('login')\" class=\"btn btn-secondary\">上传文件</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF格式，文件大小不超过10MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"loginLogoUrl\">登录页面Logo URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"loginLogoUrl\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("web-server.login-logo-url", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入Logo图片URL，留空使用默认锁图标\">\n");
                html.append("                                    <small>登录页面顶部显示的Logo图片，建议使用正方形图片，推荐尺寸：128x128像素</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地Logo文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"loginLogoFile\" accept=\".jpg,.jpeg,.png,.gif,.svg\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadLoginLogo()\" class=\"btn btn-secondary\">上传Logo</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF、SVG格式，文件大小不超过2MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"loginBackgroundOpacity\">背景透明度:</label>\n");
                html.append("                                    <input type=\"range\" id=\"loginBackgroundOpacity\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("web-server.login-background-opacity", 30))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"80\" step=\"10\" oninput=\"updateLoginOpacityDisplay(this.value)\">\n");
                html.append("                                    <div class=\"opacity-display\">\n");
                html.append("                                        <span>透明度: <strong id=\"loginOpacityValue\">")
                                .append(plugin.getConfig().getInt("web-server.login-background-opacity", 30))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调整背景图片的透明度，数值越小背景图越明显，0为完全透明</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🏆 排行榜页面背景</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"leaderboardBackgroundImage\">背景图片URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"leaderboardBackgroundImage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.leaderboard-background-image", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入图片URL，留空使用默认渐变背景\">\n");
                html.append("                                    <small>支持JPG、PNG、GIF等格式，包括GIF动图。建议使用高质量图片以获得最佳效果</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"leaderboardBackgroundImageFile\" accept=\".jpg,.jpeg,.png,.gif\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadBackgroundImage('leaderboard')\" class=\"btn btn-secondary\">上传文件</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF格式，文件大小不超过10MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"leaderboardBackgroundOpacity\">背景透明度:</label>\n");
                html.append("                                    <input type=\"range\" id=\"leaderboardBackgroundOpacity\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.leaderboard-background-opacity", 30))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"80\" step=\"10\" oninput=\"updateLeaderboardOpacityDisplay(this.value)\">\n");
                html.append("                                    <div class=\"opacity-display\">\n");
                html.append("                                        <span>透明度: <strong id=\"leaderboardOpacityValue\">")
                                .append(plugin.getConfig().getInt("website.leaderboard-background-opacity", 30))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调整背景图片的透明度，数值越小背景图越明显，0为完全透明</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>⚙️ 操作</h3>\n");
                html.append("                                <div class=\"button-group vertical\">\n");
                html.append("                                    <button onclick=\"saveBackgroundSettings()\" class=\"btn btn-primary\">💾 保存背景设置</button>\n");
                html.append("                                    <button onclick=\"resetBackgroundSettings()\" class=\"btn btn-secondary\">🔄 重置为默认</button>\n");
                html.append("                                    <button onclick=\"previewBackgrounds()\" class=\"btn btn-info\">👁️ 预览效果</button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成封禁设置标签页
         */
        private String generatePunishmentTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"punishment-tab\" class=\"tab-pane\">\n");
                html.append("                        <div class=\"content-grid\">\n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>📋 处罚记录页面背景</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"punishmentBackgroundImage\">背景图片URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"punishmentBackgroundImage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.punishment-background-image", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入图片URL，留空使用默认渐变背景\">\n");
                html.append("                                    <small>处罚记录页面的背景图片，建议使用与主题相符的图片</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"punishmentBackgroundImageFile\" accept=\".jpg,.jpeg,.png,.gif\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadBackgroundImage('punishment')\" class=\"btn btn-secondary\">上传文件</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF格式，文件大小不超过10MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"punishmentBackgroundOpacity\">背景透明度:</label>\n");
                html.append("                                    <input type=\"range\" id=\"punishmentBackgroundOpacity\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.punishment-background-opacity", 30))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"80\" step=\"10\" oninput=\"updatePunishmentOpacityDisplay(this.value)\">\n");
                html.append("                                    <div class=\"opacity-display\">\n");
                html.append("                                        <span>透明度: <strong id=\"punishmentOpacityValue\">")
                                .append(plugin.getConfig().getInt("website.punishment-background-opacity", 30))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调整背景图片的透明度，数值越小背景图越明显，0为完全透明</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🖼️ Logo设置</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"adminLogoUrl\">管理员Logo URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"adminLogoUrl\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("web-server.admin-logo-url", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入管理员Logo图片URL，留空使用默认Logo\">\n");
                html.append("                                    <small>管理员控制台专用Logo，建议使用正方形图片，推荐尺寸：32x32像素</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地Logo文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"adminLogoFile\" accept=\".jpg,.jpeg,.png,.gif,.svg\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadLogo('admin')\" class=\"btn btn-secondary\">上传管理员Logo</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF、SVG格式，文件大小不超过2MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"userLogoUrl\">普通用户Logo URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"userLogoUrl\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("web-server.user-logo-url", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入普通用户Logo图片URL，留空使用默认Logo\">\n");
                html.append("                                    <small>普通用户页面专用Logo，建议使用正方形图片，推荐尺寸：32x32像素</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地Logo文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"userLogoFile\" accept=\".jpg,.jpeg,.png,.gif,.svg\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadLogo('user')\" class=\"btn btn-secondary\">上传普通Logo</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF、SVG格式，文件大小不超过2MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>⚙️ 操作</h3>\n");
                html.append("                                <div class=\"button-group vertical\">\n");
                html.append("                                    <button onclick=\"savePunishmentSettings()\" class=\"btn btn-primary\">💾 保存封禁设置</button>\n");
                html.append("                                    <button onclick=\"resetPunishmentSettings()\" class=\"btn btn-secondary\">🔄 重置为默认</button>\n");
                html.append("                                    <button onclick=\"previewPunishmentPages()\" class=\"btn btn-info\">👁️ 预览效果</button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成动画设置标签页
         */
        private String generateAnimationTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"animation-tab\" class=\"tab-pane\">\n");
                html.append("                        <div class=\"content-grid\">\n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🎬 开箱动画设置</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"enableOpeningAnimation\" onchange=\"toggleOpeningAnimation()\" ")
                                .append(plugin.getConfig().getBoolean("website.opening-animation.enabled", true)
                                                ? "checked"
                                                : "")
                                .append("> 启用开箱动画效果\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>开启后，抽奖时会显示精美的开箱动画</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"animationDuration\">动画持续时间 (秒):</label>\n");
                html.append("                                    <input type=\"number\" id=\"animationDuration\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getDouble("website.opening-animation.duration", 3.0))
                                .append("\" \n");
                html.append("                                           min=\"1\" max=\"10\" step=\"0.5\" placeholder=\"3.0\">\n");
                html.append("                                    <small>建议设置为2-5秒，过长会影响用户体验</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"animationStyle\">动画风格:</label>\n");
                html.append("                                    <select id=\"animationStyle\" class=\"form-input\" onchange=\"toggleCustomAnimation()\">\n");

                String currentStyle = plugin.getConfig().getString("website.opening-animation.style", "classic");
                String[] styles = { "classic", "modern", "sparkle", "rotate", "luxury", "cosmic", "custom" };
                String[] styleNames = { "经典宝箱", "现代卡片", "闪光特效", "旋转展示", "豪华保险库", "宇宙传送门", "自定义动画" };

                for (int i = 0; i < styles.length; i++) {
                        html.append("                                        <option value=\"").append(styles[i])
                                        .append("\"")
                                        .append(styles[i].equals(currentStyle) ? " selected" : "")
                                        .append(">").append(styleNames[i]).append("</option>\n");
                }

                html.append("                                    </select>\n");
                html.append("                                    <small>选择预设动画风格或上传自定义动画。自定义动画支持透明背景效果。</small>\n");
                html.append("                                </div>\n");
                html.append("                                \n");
                html.append("                                <div class=\"form-group\" id=\"customAnimationGroup\" style=\"display: ")
                                .append("custom".equals(currentStyle) ? "block" : "none").append(";\">\n");
                html.append("                                    <label for=\"customAnimationUrl\">自定义动画文件URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"customAnimationUrl\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.opening-animation.custom-url", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入动画文件URL，支持GIF、MP4、WebM格式\">\n");
                html.append("                                    <small>支持GIF动图、MP4、WebM视频格式，建议文件大小不超过5MB</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地动画文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"customAnimationFile\" accept=\".gif,.mp4,.webm\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadAnimationFile()\" class=\"btn btn-secondary\">上传动画</button>\n");
                html.append("                                        <small>支持GIF、MP4、WebM格式，文件大小不超过5MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                    <div class=\"animation-preview\" id=\"animationPreview\" style=\"margin-top: 15px; display: none;\">\n");
                html.append("                                        <label>动画预览:</label>\n");
                html.append("                                        <div class=\"preview-container\" style=\"border: 1px solid #ddd; border-radius: 8px; padding: 10px; background: #f8f9fa; text-align: center;\">\n");
                html.append("                                            <div id=\"previewContent\">暂无预览</div>\n");
                html.append("                                        </div>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>⚙️ 操作</h3>\n");
                html.append("                                <div class=\"button-group vertical\">\n");
                html.append("                                    <button onclick=\"testAnimation()\" class=\"btn btn-success\">🎬 测试动画</button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成音效设置标签页
         */
        private String generateSoundTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"sound-tab\" class=\"tab-pane\">\n");
                html.append("                        <div class=\"content-grid two-columns\">\n");

                // 左侧：开箱音效卡片
                html.append("                            <div class=\"content-card sound-card\">\n");
                html.append("                                <h3>🔊 开箱音效</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"enableOpeningSoundEffects\" onchange=\"toggleOpeningSoundEffects()\" ")
                                .append(plugin.getConfig().getBoolean("website.sound-effects.opening-enabled", true)
                                                ? "checked"
                                                : "")
                                .append("> 启用开箱音效\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>开启后，抽奖开始时会播放音效</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"openingSoundVolume\">开箱音效音量 (%):</label>\n");
                html.append("                                    <input type=\"range\" id=\"openingSoundVolume\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.sound-effects.opening-volume", 50))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"100\" step=\"5\" oninput=\"updateOpeningVolumeDisplay(this.value)\">\n");
                html.append("                                    <div class=\"volume-display\">\n");
                html.append("                                        <span>音量: <strong id=\"openingVolumeValue\">")
                                .append(plugin.getConfig().getInt("website.sound-effects.opening-volume", 50))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调节开箱音效播放音量，0为静音</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"openingSoundUrl\">开箱音效URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"openingSoundUrl\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.sound-effects.opening-sound", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入音效文件URL，留空使用默认音效\">\n");
                html.append("                                    <small>抽奖开始时播放的音效</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>上传本地音效文件:</label>\n");
                html.append("                                    <input type=\"file\" id=\"openingSoundFile\" accept=\".mp3,.wav,.ogg\" class=\"file-input\">\n");
                html.append("                                    <div class=\"button-group\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadSoundFile('opening')\" class=\"btn btn-secondary\">📁 上传音效</button>\n");
                html.append("                                        <button type=\"button\" onclick=\"testOpeningSound()\" class=\"btn btn-info\">🔊 测试音效</button>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>支持MP3、WAV、OGG格式，文件大小不超过2MB</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");

                // 右侧：中奖音效卡片
                html.append("                            <div class=\"content-card sound-card\">\n");
                html.append("                                <h3>🎉 中奖音效</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"enableWinningSoundEffects\" onchange=\"toggleWinningSoundEffects()\" ")
                                .append(plugin.getConfig().getBoolean("website.sound-effects.winning-enabled", true)
                                                ? "checked"
                                                : "")
                                .append("> 启用中奖音效\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>开启后，中奖时会播放庆祝音效</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"winningSoundVolume\">中奖音效音量 (%):</label>\n");
                html.append("                                    <input type=\"range\" id=\"winningSoundVolume\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.sound-effects.winning-volume", 50))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"100\" step=\"5\" oninput=\"updateWinningVolumeDisplay(this.value)\">\n");
                html.append("                                    <div class=\"volume-display\">\n");
                html.append("                                        <span>音量: <strong id=\"winningVolumeValue\">")
                                .append(plugin.getConfig().getInt("website.sound-effects.winning-volume", 50))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调节中奖音效播放音量，0为静音</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"winningSoundUrl\">中奖音效URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"winningSoundUrl\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.sound-effects.winning-sound", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入音效文件URL，留空使用默认音效\">\n");
                html.append("                                    <small>中奖时播放的庆祝音效</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>上传本地音效文件:</label>\n");
                html.append("                                    <input type=\"file\" id=\"winningSoundFile\" accept=\".mp3,.wav,.ogg\" class=\"file-input\">\n");
                html.append("                                    <div class=\"button-group\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadSoundFile('winning')\" class=\"btn btn-secondary\">📁 上传音效</button>\n");
                html.append("                                        <button type=\"button\" onclick=\"testWinningSound()\" class=\"btn btn-success\">🎉 测试音效</button>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>支持MP3、WAV、OGG格式，文件大小不超过2MB</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成积分商店标签页
         */
        private String generateShopTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"shop-tab\" class=\"tab-pane\">\n");
                html.append("                        <div class=\"content-grid\">\n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🛒 积分商店界面</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"shopBackgroundImage\">商店背景图片URL:</label>\n");
                html.append("                                    <input type=\"url\" id=\"shopBackgroundImage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.shop-background-image", ""))
                                .append("\" \n");
                html.append("                                           placeholder=\"输入图片URL，留空使用默认渐变背景\">\n");
                html.append("                                    <small>积分商店页面的背景图片，建议使用高质量图片</small>\n");
                html.append("                                    <div class=\"file-upload-section\">\n");
                html.append("                                        <label>或者上传本地文件:</label>\n");
                html.append("                                        <input type=\"file\" id=\"shopBackgroundImageFile\" accept=\".jpg,.jpeg,.png,.gif\" class=\"file-input\">\n");
                html.append("                                        <button type=\"button\" onclick=\"uploadBackgroundImage('shop')\" class=\"btn btn-secondary\">上传文件</button>\n");
                html.append("                                        <small>支持JPG、PNG、GIF格式，文件大小不超过10MB</small>\n");
                html.append("                                    </div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"shopBackgroundOpacity\">背景透明度:</label>\n");
                html.append("                                    <input type=\"range\" id=\"shopBackgroundOpacity\" class=\"form-range\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.shop-background-opacity", 30))
                                .append("\" \n");
                html.append("                                           min=\"0\" max=\"80\" step=\"10\" oninput=\"updateShopOpacityDisplay(this.value)\">\n");
                html.append("                                    <div class=\"opacity-display\">\n");
                html.append("                                        <span>透明度: <strong id=\"shopOpacityValue\">")
                                .append(plugin.getConfig().getInt("website.shop-background-opacity", 30))
                                .append("%</strong></span>\n");
                html.append("                                    </div>\n");
                html.append("                                    <small>调整背景图片的透明度，数值越小背景图越明显，0为完全透明</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"shopTitle\">商店标题:</label>\n");
                html.append("                                    <input type=\"text\" id=\"shopTitle\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.shop-title", "积分商店")).append("\" \n");
                html.append("                                           placeholder=\"积分商店\">\n");
                html.append("                                    <small>显示在积分商店页面顶部的标题</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"shopDescription\">商店描述:</label>\n");
                html.append("                                    <textarea id=\"shopDescription\" class=\"form-input\" rows=\"3\" \n");
                html.append("                                              placeholder=\"使用积分购买各种游戏道具和特权\">")
                                .append(plugin.getConfig().getString("website.shop-description", "使用积分购买各种游戏道具和特权"))
                                .append("</textarea>\n");
                html.append("                                    <small>显示在积分商店页面的描述文字</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🎨 商店样式</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"shopLayout\">商店布局:</label>\n");
                html.append("                                    <select id=\"shopLayout\" class=\"form-input\">\n");

                String currentLayout = plugin.getConfig().getString("website.shop-layout", "grid");
                String[] layouts = { "grid", "list", "card" };
                String[] layoutNames = { "网格布局", "列表布局", "卡片布局" };

                for (int i = 0; i < layouts.length; i++) {
                        html.append("                                        <option value=\"").append(layouts[i])
                                        .append("\"")
                                        .append(layouts[i].equals(currentLayout) ? " selected" : "")
                                        .append(">").append(layoutNames[i]).append("</option>\n");
                }

                html.append("                                    </select>\n");
                html.append("                                    <small>选择商品的显示布局方式</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"itemsPerPage\">每页显示商品数:</label>\n");
                html.append("                                    <input type=\"number\" id=\"itemsPerPage\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getInt("website.shop-items-per-page", 12)).append("\" \n");
                html.append("                                           min=\"6\" max=\"50\" step=\"1\">\n");
                html.append("                                    <small>每页显示的商品数量，建议6-24个</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"enableShopAnimations\" ")
                                .append(plugin.getConfig().getBoolean("website.shop-animations", true) ? "checked" : "")
                                .append("> 启用商店动画效果\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>商品卡片的悬停和点击动画效果</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>⚙️ 操作</h3>\n");
                html.append("                                <div class=\"button-group vertical\">\n");
                html.append("                                    <button onclick=\"saveShopSettings()\" class=\"btn btn-primary\">💾 保存商店设置</button>\n");
                html.append("                                    <button onclick=\"resetShopSettings()\" class=\"btn btn-secondary\">🔄 重置为默认</button>\n");
                html.append("                                    <button onclick=\"previewShop()\" class=\"btn btn-info\">👁️ 预览商店</button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成高级设置标签页
         */
        private String generateAdvancedTab() {
                StringBuilder html = new StringBuilder();

                html.append("                    <div id=\"advanced-tab\" class=\"tab-pane\">\n");
                html.append("                        <div class=\"content-grid\">\n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🌙 管理员控制台主题</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"adminThemeMode\">主题模式:</label>\n");
                html.append("                                    <select id=\"adminThemeMode\" class=\"form-input\" onchange=\"previewAdminTheme()\">\n");
                html.append("                                        <option value=\"light\">☀️ 浅色模式</option>\n");
                html.append("                                        <option value=\"dark\">🌙 深色模式</option>\n");
                html.append("                                        <option value=\"auto\">🔄 自动切换</option>\n");
                html.append("                                    </select>\n");
                html.append("                                    <small>选择管理员控制台的默认主题模式</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"enableDarkMode\" onchange=\"toggleDarkModeOption()\" checked>\n");
                html.append("                                        启用夜间模式功能\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>允许在管理员控制台中使用夜间模式</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"allowThemeSwitch\" onchange=\"toggleThemeSwitchOption()\" checked>\n");
                html.append("                                        允许用户切换主题\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>在界面上显示主题切换按钮</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🎨 用户界面主题</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"interfaceTheme\">主题风格:</label>\n");
                html.append("                                    <select id=\"interfaceTheme\" class=\"form-input\" onchange=\"previewTheme()\">\n");

                String currentTheme = plugin.getConfig().getString("website.interface.theme", "default");
                String[] themes = { "default", "dark", "light", "blue", "purple", "green" };
                String[] themeNames = { "默认主题", "深色主题", "浅色主题", "蓝色主题", "紫色主题", "绿色主题" };

                for (int i = 0; i < themes.length; i++) {
                        html.append("                                        <option value=\"").append(themes[i])
                                        .append("\"")
                                        .append(themes[i].equals(currentTheme) ? " selected" : "")
                                        .append(">").append(themeNames[i]).append("</option>\n");
                }

                html.append("                                    </select>\n");
                html.append("                                    <small>选择网站整体的颜色主题风格</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"primaryColor\">主色调:</label>\n");
                html.append("                                    <input type=\"color\" id=\"primaryColor\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.interface.primary-color", "#667eea"))
                                .append("\" \n");
                html.append("                                           onchange=\"updatePrimaryColor()\">\n");
                html.append("                                    <small>自定义网站的主要颜色</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"accentColor\">强调色:</label>\n");
                html.append("                                    <input type=\"color\" id=\"accentColor\" class=\"form-input\" \n");
                html.append("                                           value=\"")
                                .append(plugin.getConfig().getString("website.interface.accent-color", "#764ba2"))
                                .append("\" \n");
                html.append("                                           onchange=\"updateAccentColor()\">\n");
                html.append("                                    <small>用于按钮和链接的强调颜色</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>🎬 页面动画</h3>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label>\n");
                html.append("                                        <input type=\"checkbox\" id=\"enablePageAnimations\" onchange=\"togglePageAnimations()\" ")
                                .append(plugin.getConfig().getBoolean("website.interface.enable-animations", true)
                                                ? "checked"
                                                : "")
                                .append("> 启用页面动画效果\n");
                html.append("                                    </label>\n");
                html.append("                                    <small>开启后，页面切换和元素加载会有动画效果</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"pageLoadAnimation\">页面加载动画:</label>\n");
                html.append("                                    <select id=\"pageLoadAnimation\" class=\"form-input\" onchange=\"previewLoadAnimation()\">\n");

                String currentAnimation = plugin.getConfig().getString("website.interface.load-animation", "fade");
                String[] animations = { "fade", "slide", "zoom", "bounce", "matrix", "portal", "glitch", "hologram",
                                "quantum", "cyberpunk", "neon", "none" };
                String[] animationNames = { "淡入效果", "滑动效果", "缩放效果", "弹跳效果", "黑客帝国", "传送门", "故障风格", "全息投影", "量子传输",
                                "赛博朋克", "霓虹灯", "无动画" };

                for (int i = 0; i < animations.length; i++) {
                        html.append("                                        <option value=\"").append(animations[i])
                                        .append("\"")
                                        .append(animations[i].equals(currentAnimation) ? " selected" : "")
                                        .append(">").append(animationNames[i]).append("</option>\n");
                }

                html.append("                                    </select>\n");
                html.append("                                    <small>页面切换时的动画效果</small>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"form-group\">\n");
                html.append("                                    <label for=\"animationSpeed\">动画速度:</label>\n");
                html.append("                                    <select id=\"animationSpeed\" class=\"form-input\">\n");

                String currentSpeed = plugin.getConfig().getString("website.interface.animation-speed", "normal");
                String[] speeds = { "slow", "normal", "fast" };
                String[] speedNames = { "慢速 (0.5s)", "正常 (0.3s)", "快速 (0.15s)" };

                for (int i = 0; i < speeds.length; i++) {
                        html.append("                                        <option value=\"").append(speeds[i])
                                        .append("\"")
                                        .append(speeds[i].equals(currentSpeed) ? " selected" : "")
                                        .append(">").append(speedNames[i]).append("</option>\n");
                }

                html.append("                                    </select>\n");
                html.append("                                    <small>控制动画播放的速度</small>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <div class=\"content-card\">\n");
                html.append("                                <h3>💾 操作</h3>\n");
                html.append("                                <div class=\"button-group vertical\">\n");
                html.append("                                    <button onclick=\"saveAdvancedSettings()\" class=\"btn btn-primary\">💾 保存高级设置</button>\n");
                html.append("                                    <button onclick=\"resetAdvancedSettings()\" class=\"btn btn-secondary\">🔄 重置为默认</button>\n");
                html.append("                                    <button onclick=\"previewAdvancedSettings()\" class=\"btn btn-info\">👁️ 预览效果</button>\n");
                html.append("                                    <button onclick=\"saveInterfaceSettings()\" class=\"btn btn-success\">💾 保存所有设置</button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");

                return html.toString();
        }

        /**
         * 生成标签页切换功能
         */
        private String generateTabSwitchFunction() {
                return "// 标签页切换功能\n" +
                                "function switchInterfaceTab(tabName) {\n" +
                                "    // 隐藏所有标签页\n" +
                                "    const allTabs = document.querySelectorAll('.tab-pane');\n" +
                                "    allTabs.forEach(tab => {\n" +
                                "        tab.classList.remove('active');\n" +
                                "    });\n" +
                                "    \n" +
                                "    // 移除所有按钮的激活状态\n" +
                                "    const allBtns = document.querySelectorAll('.tab-btn');\n" +
                                "    allBtns.forEach(btn => {\n" +
                                "        btn.classList.remove('active');\n" +
                                "    });\n" +
                                "    \n" +
                                "    // 显示选中的标签页\n" +
                                "    const targetTab = document.getElementById(tabName + '-tab');\n" +
                                "    if (targetTab) {\n" +
                                "        targetTab.classList.add('active');\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 激活对应的按钮\n" +
                                "    const targetBtn = event.target;\n" +
                                "    if (targetBtn) {\n" +
                                "        targetBtn.classList.add('active');\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 如果是首页标签页，初始化表单值\n" +
                                "    if (tabName === 'homepage') {\n" +
                                "        initializeHomepageForm();\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function initializeHomepageForm() {\n" +
                                "    // 这个函数在页面加载时会被调用，确保表单值正确显示\n" +
                                "    console.log('首页设置表单已初始化');\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成高级设置相关的JavaScript函数
         */
        private String generateAdvancedSettingsFunctions() {
                return "// 管理员控制台主题相关函数\n" +
                                "function saveAdminThemeSettings() {\n" +
                                "    const adminThemeMode = document.getElementById('adminThemeMode').value;\n" +
                                "    const enableDarkMode = document.getElementById('enableDarkMode').checked;\n" +
                                "    const allowThemeSwitch = document.getElementById('allowThemeSwitch').checked;\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行保存\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_admin_theme_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            admin_theme_mode: adminThemeMode,\n" +
                                "            enable_dark_mode: enableDarkMode,\n" +
                                "            allow_theme_switch: allowThemeSwitch\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', data.message);\n" +
                                "            // 应用新主题\n" +
                                "            applyAdminTheme(adminThemeMode);\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function previewAdminTheme() {\n" +
                                "    const theme = document.getElementById('adminThemeMode').value;\n" +
                                "    applyAdminTheme(theme);\n" +
                                "    showResult('🌙 主题预览：' + (theme === 'light' ? '浅色模式' : theme === 'dark' ? '深色模式' : '自动切换') + ' 已应用', 'info');\n"
                                +
                                "}\n" +
                                "\n" +
                                "function toggleDarkModeOption() {\n" +
                                "    const enabled = document.getElementById('enableDarkMode').checked;\n" +
                                "    showResult(enabled ? '✅ 夜间模式功能已启用' : '❌ 夜间模式功能已禁用', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function toggleThemeSwitchOption() {\n" +
                                "    const enabled = document.getElementById('allowThemeSwitch').checked;\n" +
                                "    showResult(enabled ? '✅ 用户可以切换主题' : '❌ 用户无法切换主题', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function applyAdminTheme(theme) {\n" +
                                "    const body = document.body;\n" +
                                "    \n" +
                                "    // 移除现有主题类\n" +
                                "    body.classList.remove('theme-light', 'theme-dark', 'theme-auto');\n" +
                                "    \n" +
                                "    // 应用新主题\n" +
                                "    if (theme === 'auto') {\n" +
                                "        // 自动模式：根据系统时间或用户偏好\n" +
                                "        const hour = new Date().getHours();\n" +
                                "        const isDarkTime = hour < 6 || hour >= 18;\n" +
                                "        body.classList.add(isDarkTime ? 'theme-dark' : 'theme-light');\n" +
                                "    } else {\n" +
                                "        body.classList.add('theme-' + theme);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 保存到本地存储\n" +
                                "    localStorage.setItem('adminTheme', theme);\n" +
                                "}\n" +
                                "\n" +
                                "// 高级设置相关函数\n" +
                                "function saveAdvancedSettings() {\n" +
                                "    const interfaceTheme = document.getElementById('interfaceTheme').value;\n" +
                                "    const primaryColor = document.getElementById('primaryColor').value;\n" +
                                "    const accentColor = document.getElementById('accentColor').value;\n" +
                                "    const enablePageAnimations = document.getElementById('enablePageAnimations').checked;\n"
                                +
                                "    const pageLoadAnimation = document.getElementById('pageLoadAnimation').value;\n" +
                                "    const animationSpeed = document.getElementById('animationSpeed').value;\n" +
                                "    \n" +
                                "    // 获取管理员主题设置\n" +
                                "    const adminThemeMode = document.getElementById('adminThemeMode').value;\n" +
                                "    const enableDarkMode = document.getElementById('enableDarkMode').checked;\n" +
                                "    const allowThemeSwitch = document.getElementById('allowThemeSwitch').checked;\n" +
                                "    \n" +
                                "    // 不显示加载提示，直接进行保存\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_advanced_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            interface_theme: interfaceTheme,\n" +
                                "            primary_color: primaryColor,\n" +
                                "            accent_color: accentColor,\n" +
                                "            enable_page_animations: enablePageAnimations,\n" +
                                "            page_load_animation: pageLoadAnimation,\n" +
                                "            animation_speed: animationSpeed,\n" +
                                "            admin_theme_mode: adminThemeMode,\n" +
                                "            enable_dark_mode: enableDarkMode,\n" +
                                "            allow_theme_switch: allowThemeSwitch\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', '高级设置已成功保存！\\n\\n界面主题和动画配置已更新');\n" +
                                "            // 应用新的管理员主题\n" +
                                "            applyAdminTheme(adminThemeMode);\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function resetAdvancedSettings() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🔄 重置高级设置',\n" +
                                "        '确定要重置高级设置为默认值吗？\\n\\n这将恢复默认的主题、颜色和动画设置',\n" +
                                "        function() {\n" +
                                "            document.getElementById('interfaceTheme').value = 'default';\n" +
                                "            document.getElementById('primaryColor').value = '#667eea';\n" +
                                "            document.getElementById('accentColor').value = '#764ba2';\n" +
                                "            document.getElementById('enablePageAnimations').checked = true;\n" +
                                "            document.getElementById('pageLoadAnimation').value = 'fade';\n" +
                                "            document.getElementById('animationSpeed').value = 'normal';\n" +
                                "            showSuccessModal('✅ 重置成功', '高级设置已重置为默认值\\n请点击保存按钮应用更改');\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function previewAdvancedSettings() {\n" +
                                "    // 直接跳转到抽奖页面查看高级设置效果\n" +
                                "    window.open('/user', '_blank');\n" +
                                "}\n" +
                                "\n" +
                                "function previewTheme() {\n" +
                                "    const theme = document.getElementById('interfaceTheme').value;\n" +
                                "    showResult('🎨 主题预览：' + theme + ' 主题已选择，保存后生效', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function updatePrimaryColor() {\n" +
                                "    const color = document.getElementById('primaryColor').value;\n" +
                                "    showResult('🎨 主色调已更新为：' + color + '，保存后生效', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function updateAccentColor() {\n" +
                                "    const color = document.getElementById('accentColor').value;\n" +
                                "    showResult('🎨 强调色已更新为：' + color + '，保存后生效', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function togglePageAnimations() {\n" +
                                "    const enabled = document.getElementById('enablePageAnimations').checked;\n" +
                                "    showResult(enabled ? '✅ 页面动画已启用' : '❌ 页面动画已禁用', 'info');\n" +
                                "}\n" +
                                "\n" +
                                "function previewLoadAnimation() {\n" +
                                "    const animation = document.getElementById('pageLoadAnimation').value;\n" +
                                "    showResult('🎬 页面加载动画：' + animation + ' 效果已选择，保存后生效', 'info');\n" +
                                "}\n" +
                                "\n";
        }

        /**
         * 生成积分商店设置相关的JavaScript函数
         */
        private String generateShopSettingsFunctions() {
                return "// 积分商店设置相关函数\n" +
                                "function saveShopSettings() {\n" +
                                "    const shopBackgroundImage = document.getElementById('shopBackgroundImage').value.trim();\n"
                                +
                                "    const shopTitle = document.getElementById('shopTitle').value.trim();\n" +
                                "    const shopDescription = document.getElementById('shopDescription').value.trim();\n"
                                +
                                "    const shopLayout = document.getElementById('shopLayout').value;\n" +
                                "    const itemsPerPage = document.getElementById('itemsPerPage').value;\n" +
                                "    const enableShopAnimations = document.getElementById('enableShopAnimations').checked;\n"
                                +
                                "    \n" +
                                "    // 不显示加载提示，直接进行保存\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'save_shop_settings',\n" +
                                "            api_key: '" + adminKey + "',\n" +
                                "            shop_background_image: shopBackgroundImage,\n" +
                                "            shop_title: shopTitle,\n" +
                                "            shop_description: shopDescription,\n" +
                                "            shop_layout: shopLayout,\n" +
                                "            items_per_page: parseInt(itemsPerPage),\n" +
                                "            enable_shop_animations: enableShopAnimations\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showSuccessModal('✅ 保存成功', '积分商店设置已成功保存！\\n\\n商店配置和显示设置已更新');\n" +
                                "        } else {\n" +
                                "            showErrorModal('❌ 保存失败', '保存失败: ' + data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showErrorModal('❌ 网络错误', '网络错误: ' + error.message);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function resetShopSettings() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🔄 重置积分商店设置',\n" +
                                "        '确定要重置积分商店设置为默认值吗？\\n\\n这将恢复默认的商店标题、描述和布局设置',\n" +
                                "        function() {\n" +
                                "            document.getElementById('shopBackgroundImage').value = '';\n" +
                                "            document.getElementById('shopTitle').value = '积分商店';\n" +
                                "            document.getElementById('shopDescription').value = '使用积分购买各种游戏道具和特权';\n" +
                                "            document.getElementById('shopLayout').value = 'grid';\n" +
                                "            document.getElementById('itemsPerPage').value = '12';\n" +
                                "            document.getElementById('enableShopAnimations').checked = true;\n" +
                                "            showSuccessModal('✅ 重置成功', '积分商店设置已重置为默认值\\n请点击保存按钮应用更改');\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function previewShop() {\n" +
                                "    // 直接跳转到积分商店页面\n" +
                                "    window.open('/points-shop', '_blank');\n" +
                                "}\n" +
                                "\n";
        }
}
