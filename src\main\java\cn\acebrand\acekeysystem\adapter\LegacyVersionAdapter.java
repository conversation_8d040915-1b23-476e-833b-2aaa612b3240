package cn.acebrand.acekeysystem.adapter;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import java.util.Arrays;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

/**
 * 旧版本适配器 (Minecraft 1.12及以下)
 * 使用反射访问NMS类来处理NBT数据和发送标题
 */
public final class LegacyVersionAdapter implements VersionAdapter {

    private final String serverVersion;

    public LegacyVersionAdapter() {
        String packageName = Bukkit.getServer().getClass().getPackage().getName();
        this.serverVersion = packageName.substring(packageName.lastIndexOf('.') + 1);
    }

    @Override
    public String getVersion() {
        return this.serverVersion;
    }

    @Override
    public ItemStack setItemNBT(ItemStack itemStack, String key, String value) {
        try {
            Object nmsItemStack = convertToNMSItemStack(itemStack);
            Object nbtTagCompound = getNBTTagCompound(nmsItemStack);

            Method setStringMethod = nbtTagCompound.getClass().getMethod("setString", String.class, String.class);
            setStringMethod.invoke(nbtTagCompound, key, value);

            Method setTagMethod = nmsItemStack.getClass().getMethod("setTag", nbtTagCompound.getClass());
            setTagMethod.invoke(nmsItemStack, nbtTagCompound);

            Method asBukkitCopyMethod = getCraftBukkitClass("inventory.CraftItemStack")
                    .getMethod("asBukkitCopy", getNMSClass("ItemStack"));
            return (ItemStack) asBukkitCopyMethod.invoke(null, nmsItemStack);
        } catch (Exception e) {
            // 降级处理：使用Lore存储数据
            e.printStackTrace();
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null) {
                itemMeta.setLore(Arrays.asList("§0§0" + key + ":" + value));
                itemStack.setItemMeta(itemMeta);
            }
            return itemStack;
        }
    }

    @Override
    public String getItemNBT(ItemStack itemStack, String key) {
        try {
            Object nmsItemStack = convertToNMSItemStack(itemStack);
            Object nbtTagCompound = getNBTTagCompound(nmsItemStack);

            Method hasKeyMethod = nbtTagCompound.getClass().getMethod("hasKey", String.class);
            boolean hasKey = (Boolean) hasKeyMethod.invoke(nbtTagCompound, key);

            if (hasKey) {
                Method getStringMethod = nbtTagCompound.getClass().getMethod("getString", String.class);
                return (String) getStringMethod.invoke(nbtTagCompound, key);
            }
            return null;
        } catch (Exception e) {
            // 降级处理：从Lore读取数据
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null && itemMeta.hasLore()) {
                for (String lore : itemMeta.getLore()) {
                    if (lore.startsWith("§0§0" + key + ":")) {
                        return lore.substring(lore.indexOf(":") + 1);
                    }
                }
            }
            return null;
        }
    }

    @Override
    public boolean hasItemNBT(ItemStack itemStack, String key) {
        try {
            Object nmsItemStack = convertToNMSItemStack(itemStack);
            Object nbtTagCompound = getNBTTagCompound(nmsItemStack);

            Method hasKeyMethod = nbtTagCompound.getClass().getMethod("hasKey", String.class);
            return (Boolean) hasKeyMethod.invoke(nbtTagCompound, key);
        } catch (Exception e) {
            // 降级处理：检查Lore
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null && itemMeta.hasLore()) {
                for (String lore : itemMeta.getLore()) {
                    if (lore.startsWith("§0§0" + key + ":")) {
                        return true;
                    }
                }
            }
            return false;
        }
    }

    @Override
    public void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            // 创建标题数据包
            Object titleComponent = getNMSClass("IChatBaseComponent").getDeclaredClasses()[0]
                    .getMethod("a", String.class).invoke(null, "{\"text\":\"" + title + "\"}");
            Object subtitleComponent = getNMSClass("IChatBaseComponent").getDeclaredClasses()[0]
                    .getMethod("a", String.class).invoke(null, "{\"text\":\"" + subtitle + "\"}");

            Constructor<?> packetConstructor = getNMSClass("PacketPlayOutTitle").getConstructor(
                    getNMSClass("PacketPlayOutTitle").getDeclaredClasses()[0],
                    getNMSClass("IChatBaseComponent"),
                    Integer.TYPE, Integer.TYPE, Integer.TYPE);

            Object titlePacket = packetConstructor.newInstance(
                    getNMSClass("PacketPlayOutTitle").getDeclaredClasses()[0].getField("TITLE").get(null),
                    titleComponent, fadeIn, stay, fadeOut);
            Object subtitlePacket = packetConstructor.newInstance(
                    getNMSClass("PacketPlayOutTitle").getDeclaredClasses()[0].getField("SUBTITLE").get(null),
                    subtitleComponent, fadeIn, stay, fadeOut);

            sendPacket(player, titlePacket);
            sendPacket(player, subtitlePacket);
        } catch (Exception e) {
            // 降级处理：发送普通消息
            e.printStackTrace();
            player.sendMessage(title);
            player.sendMessage(subtitle);
        }
    }

    /**
     * 获取NMS类
     */
    private Class<?> getNMSClass(String className) throws ClassNotFoundException {
        return Class.forName("net.minecraft.server." + this.serverVersion + "." + className);
    }

    /**
     * 获取CraftBukkit类
     */
    private Class<?> getCraftBukkitClass(String className) throws ClassNotFoundException {
        return Class.forName("org.bukkit.craftbukkit." + this.serverVersion + "." + className);
    }

    /**
     * 将Bukkit物品转换为NMS物品
     */
    private Object convertToNMSItemStack(ItemStack itemStack) throws Exception {
        Method asNMSCopyMethod = getCraftBukkitClass("inventory.CraftItemStack")
                .getMethod("asNMSCopy", ItemStack.class);
        return asNMSCopyMethod.invoke(null, itemStack);
    }

    /**
     * 获取或创建NBTTagCompound
     */
    private Object getNBTTagCompound(Object nmsItemStack) throws Exception {
        Method getTagMethod = nmsItemStack.getClass().getMethod("getTag");
        Object nbtTagCompound = getTagMethod.invoke(nmsItemStack);
        if (nbtTagCompound == null) {
            nbtTagCompound = getNMSClass("NBTTagCompound").newInstance();
        }
        return nbtTagCompound;
    }

    /**
     * 向玩家发送数据包
     */
    private void sendPacket(Player player, Object packet) throws Exception {
        Object entityPlayer = player.getClass().getMethod("getHandle").invoke(player);
        Object playerConnection = entityPlayer.getClass().getField("playerConnection").get(entityPlayer);
        playerConnection.getClass().getMethod("sendPacket", getNMSClass("Packet")).invoke(playerConnection, packet);
    }

    @Override
    public void sendClickableLink(Player player, String url, String displayText) {
        try {
            // 对于旧版本，尝试使用Spigot的BungeeCord API
            Class<?> componentClass = Class.forName("net.md_5.bungee.api.chat.TextComponent");
            Class<?> clickEventClass = Class.forName("net.md_5.bungee.api.chat.ClickEvent");
            Class<?> clickActionClass = Class.forName("net.md_5.bungee.api.chat.ClickEvent$Action");
            Class<?> hoverEventClass = Class.forName("net.md_5.bungee.api.chat.HoverEvent");
            Class<?> hoverActionClass = Class.forName("net.md_5.bungee.api.chat.HoverEvent$Action");

            // 创建TextComponent
            Object textComponent = componentClass.getConstructor(String.class).newInstance(displayText);

            // 创建ClickEvent
            Object openUrlAction = clickActionClass.getField("OPEN_URL").get(null);
            Object clickEvent = clickEventClass.getConstructor(clickActionClass, String.class)
                    .newInstance(openUrlAction, url);

            // 创建HoverEvent (显示提示文本)
            Object showTextAction = hoverActionClass.getField("SHOW_TEXT").get(null);
            Object hoverText = componentClass.getConstructor(String.class).newInstance("§a点击打开抽奖网站\n§7" + url);
            Object hoverEvent = hoverEventClass.getConstructor(hoverActionClass, componentClass)
                    .newInstance(showTextAction, hoverText);

            // 设置点击事件和悬停事件
            Method setClickEventMethod = componentClass.getMethod("setClickEvent", clickEventClass);
            Method setHoverEventMethod = componentClass.getMethod("setHoverEvent", hoverEventClass);
            setClickEventMethod.invoke(textComponent, clickEvent);
            setHoverEventMethod.invoke(textComponent, hoverEvent);

            // 发送消息
            Method spigotMethod = Player.class.getMethod("spigot");
            Object spigot = spigotMethod.invoke(player);
            Method sendMessageMethod = spigot.getClass().getMethod("sendMessage",
                    Class.forName("net.md_5.bungee.api.chat.BaseComponent"));
            sendMessageMethod.invoke(spigot, textComponent);

        } catch (Exception e) {
            // 如果失败，发送普通消息和复制提示
            player.sendMessage(displayText);
            player.sendMessage("§7如果链接无法点击，请手动复制上方链接到浏览器");
        }
    }
}
