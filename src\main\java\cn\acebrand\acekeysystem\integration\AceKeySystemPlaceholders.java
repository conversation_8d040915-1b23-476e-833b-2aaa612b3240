package cn.acebrand.acekeysystem.integration;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.points.PointsShopItem;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.ConfigurationSection;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * AceKeySystem PlaceholderAPI 扩展
 * 提供各种占位符供其他插件使用
 */
public class AceKeySystemPlaceholders extends PlaceholderExpansion {

    private final AceKeySystem plugin;
    private final DecimalFormat numberFormat = new DecimalFormat("#,###");
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public AceKeySystemPlaceholders(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public String getIdentifier() {
        return "acekeysystem";
    }

    @Override
    public String getAuthor() {
        return plugin.getDescription().getAuthors().toString();
    }

    @Override
    public String getVersion() {
        return plugin.getDescription().getVersion();
    }

    @Override
    public boolean persist() {
        return true; // 插件重载时保持注册
    }

    @Override
    public String onRequest(OfflinePlayer player, String params) {
        if (player == null) {
            return "";
        }

        String playerName = player.getName();
        UUID playerUuid = player.getUniqueId();

        // 积分相关占位符
        if (params.equals("points")) {
            return String.valueOf(plugin.getPointsManager().getPlayerPoints(playerName));
        }

        if (params.equals("points_formatted")) {
            int points = plugin.getPointsManager().getPlayerPoints(playerName);
            return numberFormat.format(points);
        }

        // 积分排名
        if (params.equals("points_rank")) {
            return String.valueOf(getPlayerPointsRank(playerName));
        }

        // 总玩家数
        if (params.equals("total_players")) {
            return String.valueOf(plugin.getPointsManager().getAllPlayerPoints().size());
        }

        // 卡密相关占位符
        if (params.equals("total_keys")) {
            return String.valueOf(getTotalKeysCount());
        }

        if (params.equals("used_keys")) {
            return String.valueOf(getUsedKeysCount());
        }

        if (params.equals("unused_keys")) {
            return String.valueOf(getUnusedKeysCount());
        }

        // 玩家使用的卡密数量
        if (params.equals("player_used_keys")) {
            return String.valueOf(getPlayerUsedKeysCount(playerName));
        }

        // 积分商店相关占位符
        if (params.equals("shop_items_count")) {
            return String.valueOf(plugin.getPointsShopManager().getAllItems().size());
        }

        // 玩家购买历史数量
        if (params.equals("player_purchases")) {
            return String.valueOf(getPlayerPurchasesCount(playerUuid));
        }

        // 待处理购买数量
        if (params.equals("pending_purchases")) {
            return String.valueOf(getPendingPurchasesCount(playerName));
        }

        // 绑定状态
        if (params.equals("is_bound")) {
            return plugin.getBindingDataManager().isPlayerBound(playerUuid) ? "是" : "否";
        }

        // 绑定状态（英文）
        if (params.equals("is_bound_en")) {
            return plugin.getBindingDataManager().isPlayerBound(playerUuid) ? "Yes" : "No";
        }

        // 绑定时间
        if (params.equals("bind_time")) {
            long bindTime = plugin.getBindingDataManager().getPlayerBindTime(playerUuid);
            return bindTime > 0 ? dateFormat.format(new Date(bindTime)) : "未绑定";
        }

        // 动态占位符处理
        if (params.startsWith("shop_item_")) {
            return handleShopItemPlaceholder(params, playerUuid);
        }

        if (params.startsWith("top_points_")) {
            return handleTopPointsPlaceholder(params);
        }

        // 系统状态占位符
        if (params.equals("web_server_status")) {
            return plugin.getWebServer() != null && plugin.getWebServer().isRunning() ? "在线" : "离线";
        }

        if (params.equals("web_server_port")) {
            return plugin.getWebServer() != null ? String.valueOf(plugin.getWebServer().getPort()) : "未知";
        }

        return null; // 未知占位符
    }

    /**
     * 处理商店物品相关占位符
     */
    private String handleShopItemPlaceholder(String params, UUID playerUuid) {
        // 格式: shop_item_<itemId>_<property>
        String[] parts = params.split("_", 4);
        if (parts.length < 4) {
            return "";
        }

        String itemId = parts[2];
        String property = parts[3];
        PointsShopItem item = plugin.getPointsShopManager().getItem(itemId);

        if (item == null) {
            return "";
        }

        switch (property) {
            case "name":
                return item.getName();
            case "price":
                return String.valueOf(item.getCost());
            case "price_formatted":
                return numberFormat.format(item.getCost());
            case "stock":
                return item.getStock() == -1 ? "无限" : String.valueOf(item.getStock());
            case "purchased":
                return String.valueOf(plugin.getPointsShopManager().getPlayerPurchaseCount(playerUuid, itemId));
            case "remaining":
                int remaining = plugin.getPointsShopManager().getRemainingPurchases(playerUuid, itemId);
                return remaining == -1 ? "无限" : String.valueOf(remaining);
            case "available":
                return item.isEnabled() ? "是" : "否";
            default:
                return "";
        }
    }

    /**
     * 处理积分排行榜占位符
     */
    private String handleTopPointsPlaceholder(String params) {
        // 格式: top_points_<rank>_<property>
        String[] parts = params.split("_", 4);
        if (parts.length < 4) {
            return "";
        }

        try {
            int rank = Integer.parseInt(parts[2]);
            String property = parts[3];

            List<Map.Entry<String, Integer>> topPlayers = getTopPlayersByPoints();
            if (rank < 1 || rank > topPlayers.size()) {
                return "";
            }

            Map.Entry<String, Integer> entry = topPlayers.get(rank - 1);
            switch (property) {
                case "name":
                    return entry.getKey();
                case "points":
                    return String.valueOf(entry.getValue());
                case "points_formatted":
                    return numberFormat.format(entry.getValue());
                default:
                    return "";
            }
        } catch (NumberFormatException e) {
            return "";
        }
    }

    /**
     * 获取玩家积分排名
     */
    private int getPlayerPointsRank(String playerName) {
        List<Map.Entry<String, Integer>> topPlayers = getTopPlayersByPoints();
        for (int i = 0; i < topPlayers.size(); i++) {
            if (topPlayers.get(i).getKey().equals(playerName)) {
                return i + 1;
            }
        }
        return -1; // 未找到
    }

    /**
     * 获取积分排行榜
     */
    private List<Map.Entry<String, Integer>> getTopPlayersByPoints() {
        // 获取所有玩家积分并排序
        Map<String, Integer> allPoints = plugin.getPointsManager().getAllPlayerPoints();
        List<Map.Entry<String, Integer>> sortedList = new ArrayList<>(allPoints.entrySet());
        sortedList.sort((a, b) -> Integer.compare(b.getValue(), a.getValue()));

        // 返回前50名
        return sortedList.subList(0, Math.min(50, sortedList.size()));
    }

    /**
     * 获取总卡密数量
     */
    private int getTotalKeysCount() {
        ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
        return keysSection != null ? keysSection.getKeys(false).size() : 0;
    }

    /**
     * 获取已使用卡密数量
     */
    private int getUsedKeysCount() {
        ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
        if (keysSection == null) {
            return 0;
        }

        int count = 0;
        for (String key : keysSection.getKeys(false)) {
            if (keysSection.getBoolean(key + ".used", false)) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取未使用卡密数量
     */
    private int getUnusedKeysCount() {
        return getTotalKeysCount() - getUsedKeysCount();
    }

    /**
     * 获取玩家使用的卡密数量
     */
    private int getPlayerUsedKeysCount(String playerName) {
        ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
        if (keysSection == null) {
            return 0;
        }

        int count = 0;
        for (String key : keysSection.getKeys(false)) {
            if (keysSection.getBoolean(key + ".used", false) &&
                    playerName.equals(keysSection.getString(key + ".used_by"))) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取玩家购买数量
     */
    private int getPlayerPurchasesCount(UUID playerUuid) {
        // 计算玩家所有物品的购买总数
        int totalPurchases = 0;
        for (String itemId : plugin.getPointsShopManager().getAllItems().keySet()) {
            totalPurchases += plugin.getPointsShopManager().getPlayerPurchaseCount(playerUuid, itemId);
        }
        return totalPurchases;
    }

    /**
     * 获取待处理购买数量
     */
    private int getPendingPurchasesCount(String playerName) {
        // 由于没有找到对应的方法，返回0作为默认值
        // 如果需要实现此功能，需要在 PendingPurchaseManager 中添加相应方法
        return 0;
    }
}
