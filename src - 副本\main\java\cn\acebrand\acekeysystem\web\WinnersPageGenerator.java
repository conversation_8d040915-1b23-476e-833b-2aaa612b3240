package cn.acebrand.acekeysystem.web;

/**
 * 中奖记录页面生成器
 * 负责生成中奖记录页面的HTML内容
 */
public class WinnersPageGenerator {
    private final String adminKey;

    public WinnersPageGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成中奖记录页面
     */
    public String generateWinnersPage() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"page-header\">\n");
        html.append("                <h1>🏆 中奖记录</h1>\n");
        html.append("                <p>查看用户中奖历史和统计</p>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        // 操作工具栏
        html.append(generateToolbar());
        
        // 中奖记录列表
        html.append(generateWinnersList());

        return html.toString();
    }

    /**
     * 生成操作工具栏
     */
    private String generateToolbar() {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 操作工具栏 -->\n");
        html.append("            <div class=\"toolbar\">\n");
        html.append("                <div class=\"toolbar-left\">\n");
        html.append("                    <span class=\"winner-count\">总计: <strong id=\"totalWinnersCount\">0</strong> 条中奖记录</span>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"toolbar-right\">\n");
        html.append("                    <div class=\"auto-update-status\">\n");
        html.append("                        <span id=\"winnersAutoUpdateStatus\" class=\"status-indicator active\">🔄 自动检测中</span>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"sort-controls\">\n");
        html.append("                        <label for=\"winnersortOrder\">排序:</label>\n");
        html.append("                        <select id=\"winnersortOrder\" class=\"form-input small\" onchange=\"loadWinners()\">\n");
        html.append("                            <option value=\"desc\">最新在前</option>\n");
        html.append("                            <option value=\"asc\">最旧在前</option>\n");
        html.append("                        </select>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"page-size-controls\">\n");
        html.append("                        <label for=\"winnerPageSize\">每页:</label>\n");
        html.append("                        <select id=\"winnerPageSize\" class=\"form-input small\" onchange=\"loadWinners()\">\n");
        html.append("                            <option value=\"10\">10条</option>\n");
        html.append("                            <option value=\"20\" selected>20条</option>\n");
        html.append("                            <option value=\"50\">50条</option>\n");
        html.append("                            <option value=\"100\">100条</option>\n");
        html.append("                        </select>\n");
        html.append("                    </div>\n");
        html.append("                    <button onclick=\"clearAllWinners()\" class=\"btn btn-danger\">🧹 清空记录</button>\n");
        html.append("                    <button onclick=\"refreshWinners()\" class=\"btn btn-info\">🔄 刷新</button>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        return html.toString();
    }

    /**
     * 生成中奖记录列表
     */
    private String generateWinnersList() {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 中奖记录列表 -->\n");
        html.append("            <div class=\"content-card\">\n");
        html.append("                <div class=\"card-header\">\n");
        html.append("                    <h3>中奖记录列表</h3>\n");
        html.append("                    <div class=\"search-box\">\n");
        html.append("                        <input type=\"text\" id=\"winnerSearchInput\" placeholder=\"搜索用户名或奖品...\" class=\"form-input\" onkeyup=\"filterWinners()\">\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"shop-cards-container winners-container\" id=\"winnersContainer\">\n");
        html.append("                    <div class=\"loading-spinner\">正在加载中奖记录...</div>\n");
        html.append("                </div>\n");
        
        // 分页控件
        html.append("                <!-- 分页控件 -->\n");
        html.append("                <div class=\"pagination-container\" id=\"winnerPaginationContainer\" style=\"display: none;\">\n");
        html.append("                    <div class=\"pagination-info\">\n");
        html.append("                        <span id=\"winnerPaginationInfo\">显示第 1-20 条，共 0 条记录</span>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"pagination-controls\">\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"winnerFirstPageBtn\" onclick=\"goToWinnerPage(1)\" disabled>首页</button>\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"winnerPrevPageBtn\" onclick=\"goToWinnerPage(currentWinnerPage - 1)\" disabled>上一页</button>\n");
        html.append("                        <span class=\"page-numbers\" id=\"winnerPageNumbers\"></span>\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"winnerNextPageBtn\" onclick=\"goToWinnerPage(currentWinnerPage + 1)\" disabled>下一页</button>\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"winnerLastPageBtn\" onclick=\"goToWinnerPage(totalWinnerPages)\" disabled>末页</button>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        
        return html.toString();
    }
}
