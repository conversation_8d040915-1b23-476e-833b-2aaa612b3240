package cn.acebrand.acekeysystem.ban;

import cn.acebrand.acekeysystem.AceKeySystem;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;

/**
 * 封禁记录管理器
 */
public class BanManager {

    private final AceKeySystem plugin;
    private final LiteBansDatabase database;

    public BanManager(AceKeySystem plugin) {
        this.plugin = plugin;
        this.database = new LiteBansDatabase(plugin);
    }

    /**
     * 初始化封禁管理器
     */
    public boolean initialize() {
        if (!database.initialize()) {
            return false;
        }

        // 检查数据表是否存在
        if (!database.checkTablesExist()) {
            plugin.getLogger().warning("LiteBans数据表不存在，封禁记录功能将不可用");
            return false;
        }

        plugin.getLogger().info("封禁记录管理器初始化成功");
        return true;
    }

    /**
     * 获取封禁记录列表（分页）
     */
    public List<BanRecord> getBanRecords(int page, int pageSize) {
        List<BanRecord> records = new ArrayList<>();

        if (!database.isEnabled()) {
            return records;
        }

        String sql = "SELECT b.id, b.uuid, b.ip, b.reason, b.banned_by_uuid, b.banned_by_name, " +
                "       b.removed_by_uuid, b.removed_by_name, b.removed_by_reason, b.removed_by_date, " +
                "       b.time, b.until, b.template, b.server_scope, b.server_origin, " +
                "       b.silent, b.ipban, b.ipban_wildcard, b.active, " +
                "       h.name as player_name " +
                "FROM litebans_bans b " +
                "LEFT JOIN litebans_history h ON b.uuid = h.uuid " +
                "WHERE h.date = ( " +
                "    SELECT MAX(h2.date) " +
                "    FROM litebans_history h2 " +
                "    WHERE h2.uuid = b.uuid " +
                ") OR h.uuid IS NULL " +
                "ORDER BY b.time DESC " +
                "LIMIT ? OFFSET ?";

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {

            stmt.setInt(1, pageSize);
            stmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    BanRecord record = createBanRecordFromResultSet(rs);
                    records.add(record);
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "获取封禁记录失败: " + e.getMessage(), e);
        }

        return records;
    }

    /**
     * 获取封禁记录总数
     */
    public int getTotalBanCount() {
        if (!database.isEnabled()) {
            return 0;
        }

        String sql = "SELECT COUNT(*) FROM litebans_bans";

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "获取封禁记录总数失败: " + e.getMessage(), e);
        }

        return 0;
    }

    /**
     * 根据玩家名称搜索封禁记录
     */
    public List<BanRecord> searchBanRecordsByPlayer(String playerName, int page, int pageSize) {
        List<BanRecord> records = new ArrayList<>();

        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty()) {
            return records;
        }

        String sql = "SELECT b.id, b.uuid, b.ip, b.reason, b.banned_by_uuid, b.banned_by_name, " +
                "       b.removed_by_uuid, b.removed_by_name, b.removed_by_reason, b.removed_by_date, " +
                "       b.time, b.until, b.template, b.server_scope, b.server_origin, " +
                "       b.silent, b.ipban, b.ipban_wildcard, b.active, " +
                "       h.name as player_name " +
                "FROM litebans_bans b " +
                "LEFT JOIN litebans_history h ON b.uuid = h.uuid " +
                "WHERE (h.name LIKE ? OR b.uuid LIKE ?) " +
                "AND (h.date = ( " +
                "    SELECT MAX(h2.date) " +
                "    FROM litebans_history h2 " +
                "    WHERE h2.uuid = b.uuid " +
                ") OR h.uuid IS NULL) " +
                "ORDER BY b.time DESC " +
                "LIMIT ? OFFSET ?";

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {

            String searchPattern = "%" + playerName.trim() + "%";
            stmt.setString(1, searchPattern);
            stmt.setString(2, searchPattern);
            stmt.setInt(3, pageSize);
            stmt.setInt(4, (page - 1) * pageSize);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    BanRecord record = createBanRecordFromResultSet(rs);
                    records.add(record);
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "搜索封禁记录失败: " + e.getMessage(), e);
        }

        return records;
    }

    /**
     * 获取封禁统计信息
     */
    public Map<String, Object> getBanStatistics() {
        Map<String, Object> stats = new HashMap<>();

        if (!database.isEnabled()) {
            return stats;
        }

        try (Connection connection = database.getConnection()) {
            // 总封禁数
            try (PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM litebans_bans");
                    ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("total_bans", rs.getInt(1));
                }
            }

            // 活跃封禁数
            try (PreparedStatement stmt = connection
                    .prepareStatement("SELECT COUNT(*) FROM litebans_bans WHERE active = 1");
                    ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("active_bans", rs.getInt(1));
                }
            }

            // IP封禁数
            try (PreparedStatement stmt = connection
                    .prepareStatement("SELECT COUNT(*) FROM litebans_bans WHERE ipban = 1");
                    ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    stats.put("ip_bans", rs.getInt(1));
                }
            }

            // 今日封禁数
            long todayStart = LocalDateTime.now().toLocalDate().atStartOfDay().toEpochSecond(ZoneOffset.UTC) * 1000;
            try (PreparedStatement stmt = connection
                    .prepareStatement("SELECT COUNT(*) FROM litebans_bans WHERE time >= ?")) {
                stmt.setLong(1, todayStart);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        stats.put("today_bans", rs.getInt(1));
                    }
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "获取封禁统计信息失败: " + e.getMessage(), e);
        }

        return stats;
    }

    /**
     * 从ResultSet创建BanRecord对象
     */
    private BanRecord createBanRecordFromResultSet(ResultSet rs) throws SQLException {
        BanRecord record = new BanRecord();

        record.setId(rs.getLong("id"));
        record.setUuid(rs.getString("uuid"));
        record.setPlayerName(rs.getString("player_name"));
        record.setIp(rs.getString("ip"));
        record.setReason(rs.getString("reason"));
        record.setBannedByUuid(rs.getString("banned_by_uuid"));
        record.setBannedByName(rs.getString("banned_by_name"));
        record.setRemovedByUuid(rs.getString("removed_by_uuid"));
        record.setRemovedByName(rs.getString("removed_by_name"));
        record.setRemovedByReason(rs.getString("removed_by_reason"));

        // 处理时间戳转换 - LiteBans 使用毫秒时间戳
        long timeMs = rs.getLong("time");
        if (timeMs > 0) {
            // 将毫秒时间戳转换为 LocalDateTime
            record.setTime(LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(timeMs),
                ZoneOffset.UTC
            ));
        }

        long untilMs = rs.getLong("until");
        if (untilMs == 0) {
            // 永久处罚，设置一个很远的未来时间表示永久
            record.setUntil(LocalDateTime.of(2999, 12, 31, 23, 59, 59));
        } else if (untilMs > 0) {
            // 将毫秒时间戳转换为 LocalDateTime
            record.setUntil(LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(untilMs),
                ZoneOffset.UTC
            ));
        }

        record.setTemplate(rs.getInt("template"));
        record.setServerScope(rs.getString("server_scope"));
        record.setServerOrigin(rs.getString("server_origin"));
        record.setSilent(rs.getBoolean("silent"));
        record.setIpban(rs.getBoolean("ipban"));
        record.setIpbanWildcard(rs.getBoolean("ipban_wildcard"));
        record.setActive(rs.getBoolean("active"));

        return record;
    }

    /**
     * 检查数据库是否可用
     */
    public boolean isEnabled() {
        return database.isEnabled();
    }

    /**
     * 获取数据库状态
     */
    public String getDatabaseStatus() {
        return database.getPoolStatus();
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        database.shutdown();
    }

    /**
     * 重新加载配置
     */
    public boolean reload() {
        return database.reload();
    }
}
