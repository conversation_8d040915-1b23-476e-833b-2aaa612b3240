package cn.acebrand.acekeysystem.ban;

import cn.acebrand.acekeysystem.AceKeySystem;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.logging.Level;

/**
 * LiteBans数据库连接管理器
 */
public class LiteBansDatabase {

    private final AceKeySystem plugin;
    private HikariDataSource dataSource;
    private boolean enabled;

    public LiteBansDatabase(AceKeySystem plugin) {
        this.plugin = plugin;
        this.enabled = false;
    }

    /**
     * 初始化数据库连接
     */
    public boolean initialize() {
        plugin.getLogger().info("=== 开始初始化LiteBans数据库连接 ===");

        try {
            // 检查是否启用LiteBans功能
            boolean enabled = plugin.getConfig().getBoolean("litebans.enabled", false);
            plugin.getLogger().info("LiteBans功能配置状态: " + (enabled ? "启用" : "禁用"));

            if (!enabled) {
                plugin.getLogger().warning("LiteBans功能在配置中被禁用，跳过数据库初始化");
                return false;
            }

            // 获取数据库配置
            String host = plugin.getConfig().getString("litebans.database.host", "localhost");
            int port = plugin.getConfig().getInt("litebans.database.port", 3306);
            String database = plugin.getConfig().getString("litebans.database.database", "litebans");
            String username = plugin.getConfig().getString("litebans.database.username", "root");
            String password = plugin.getConfig().getString("litebans.database.password", "password");

            plugin.getLogger().info("数据库配置信息:");
            plugin.getLogger().info("  主机: " + host);
            plugin.getLogger().info("  端口: " + port);
            plugin.getLogger().info("  数据库: " + database);
            plugin.getLogger().info("  用户名: " + username);
            plugin.getLogger().info("  密码: " + (password.isEmpty() ? "空" : "已设置"));

            // 构建JDBC URL
            String jdbcUrl = String.format(
                    "*********************************************************************************************************",
                    host, port, database);

            plugin.getLogger().info("JDBC URL: " + jdbcUrl);

            // 配置HikariCP连接池
            HikariConfig config = new HikariConfig();
            config.setJdbcUrl(jdbcUrl);
            config.setUsername(username);
            config.setPassword(password);
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");

            // 连接池设置
            config.setMaximumPoolSize(plugin.getConfig().getInt("litebans.database.pool.maximum-pool-size", 10));
            config.setMinimumIdle(plugin.getConfig().getInt("litebans.database.pool.minimum-idle", 2));
            config.setConnectionTimeout(plugin.getConfig().getLong("litebans.database.pool.connection-timeout", 30000));
            config.setIdleTimeout(plugin.getConfig().getLong("litebans.database.pool.idle-timeout", 600000));
            config.setMaxLifetime(plugin.getConfig().getLong("litebans.database.pool.max-lifetime", 1800000));

            // 连接池名称
            config.setPoolName("LiteBans-Pool");

            // 连接测试查询
            config.setConnectionTestQuery("SELECT 1");

            plugin.getLogger().info("创建HikariCP数据源...");

            // 创建数据源
            this.dataSource = new HikariDataSource(config);

            plugin.getLogger().info("测试数据库连接...");

            // 测试连接
            try (Connection connection = dataSource.getConnection()) {
                plugin.getLogger().info("✓ 成功连接到LiteBans数据库: " + database);
                plugin.getLogger().info("✓ 连接有效性: " + connection.isValid(5));
                this.enabled = true;
                plugin.getLogger().info("=== LiteBans数据库初始化完成 ===");
                return true;
            }

        } catch (Exception e) {
            plugin.getLogger().log(Level.SEVERE, "✗ 连接LiteBans数据库失败: " + e.getMessage(), e);
            plugin.getLogger().severe("=== 数据库连接失败，请检查以下项目 ===");
            plugin.getLogger().severe("1. MySQL服务器是否正在运行");
            plugin.getLogger().severe("2. 数据库名称是否存在: " + plugin.getConfig().getString("litebans.database.database", "litebans"));
            plugin.getLogger().severe("3. 用户名和密码是否正确");
            plugin.getLogger().severe("4. 网络连接是否正常");
            plugin.getLogger().severe("5. MySQL驱动是否正确加载");
            this.enabled = false;
            return false;
        }
    }

    /**
     * 获取数据库连接
     */
    public Connection getConnection() throws SQLException {
        if (!enabled || dataSource == null) {
            throw new SQLException("LiteBans数据库未启用或未初始化");
        }
        return dataSource.getConnection();
    }

    /**
     * 检查数据库是否可用
     */
    public boolean isEnabled() {
        return enabled;
    }

    /**
     * 测试数据库连接
     */
    public boolean testConnection() {
        if (!enabled || dataSource == null) {
            return false;
        }

        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5); // 5秒超时
        } catch (SQLException e) {
            plugin.getLogger().log(Level.WARNING, "LiteBans数据库连接测试失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 关闭数据库连接池
     */
    public void shutdown() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            plugin.getLogger().info("LiteBans数据库连接池已关闭");
        }
        this.enabled = false;
    }

    /**
     * 重新加载数据库配置
     */
    public boolean reload() {
        shutdown();
        return initialize();
    }

    /**
     * 获取连接池状态信息
     */
    public String getPoolStatus() {
        if (!enabled || dataSource == null) {
            return "数据库未启用";
        }

        try {
            return String.format("连接池状态 - 活跃连接: %d, 空闲连接: %d, 总连接: %d, 等待连接: %d",
                    dataSource.getHikariPoolMXBean().getActiveConnections(),
                    dataSource.getHikariPoolMXBean().getIdleConnections(),
                    dataSource.getHikariPoolMXBean().getTotalConnections(),
                    dataSource.getHikariPoolMXBean().getThreadsAwaitingConnection());
        } catch (Exception e) {
            return "无法获取连接池状态: " + e.getMessage();
        }
    }

    /**
     * 检查LiteBans表是否存在
     */
    public boolean checkTablesExist() {
        if (!enabled) {
            return false;
        }

        try (Connection connection = getConnection()) {
            // 检查主要的LiteBans表是否存在
            String[] tables = { "litebans_bans", "litebans_mutes", "litebans_warnings", "litebans_kicks",
                    "litebans_history" };

            for (String table : tables) {
                try (PreparedStatement stmt = connection.prepareStatement("SELECT 1 FROM " + table + " LIMIT 1")) {
                    stmt.executeQuery();
                    plugin.getLogger().info("数据表 " + table + " 检查通过");
                } catch (SQLException e) {
                    plugin.getLogger().log(Level.WARNING, "数据表 " + table + " 检查失败: " + e.getMessage());
                    throw e; // 重新抛出异常以便外层捕获
                }
            }

            plugin.getLogger().info("LiteBans数据表检查通过");
            return true;

        } catch (SQLException e) {
            plugin.getLogger().log(Level.WARNING, "LiteBans数据表检查失败，请确保LiteBans插件已正确安装: " + e.getMessage());
            return false;
        }
    }

    /**
     * 测试数据获取功能
     */
    public void testDataRetrieval() {
        if (!enabled) {
            plugin.getLogger().warning("数据库未启用，跳过数据获取测试");
            return;
        }

        try (Connection connection = getConnection()) {
            plugin.getLogger().info("=== 开始数据库连接和数据获取测试 ===");

            // 测试各个表的记录数量
            String[] tables = {"litebans_bans", "litebans_mutes", "litebans_warnings", "litebans_kicks", "litebans_history"};

            for (String table : tables) {
                try (PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM " + table);
                     ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        int count = rs.getInt(1);
                        plugin.getLogger().info("表 " + table + " 中共有 " + count + " 条记录");
                    }
                } catch (SQLException e) {
                    plugin.getLogger().warning("无法查询表 " + table + ": " + e.getMessage());
                }
            }

            // 测试获取最新的几条封禁记录
            try (PreparedStatement stmt = connection.prepareStatement("SELECT id, uuid, reason, banned_by_name, time, until, active FROM litebans_bans ORDER BY time DESC LIMIT 5");
                 ResultSet rs = stmt.executeQuery()) {
                int count = 0;
                plugin.getLogger().info("=== 最新的封禁记录 ===");
                while (rs.next()) {
                    count++;
                    long timeMs = rs.getLong("time");
                    long untilMs = rs.getLong("until");
                    plugin.getLogger().info("封禁记录 " + count + ":");
                    plugin.getLogger().info("  ID: " + rs.getLong("id"));
                    plugin.getLogger().info("  UUID: " + rs.getString("uuid"));
                    plugin.getLogger().info("  原因: " + rs.getString("reason"));
                    plugin.getLogger().info("  执行者: " + rs.getString("banned_by_name"));
                    plugin.getLogger().info("  时间戳: " + timeMs + " (转换后: " + new java.util.Date(timeMs) + ")");
                    plugin.getLogger().info("  到期戳: " + untilMs + " (转换后: " + (untilMs == 0 ? "永久" : new java.util.Date(untilMs)) + ")");
                    plugin.getLogger().info("  活跃: " + rs.getBoolean("active"));
                    plugin.getLogger().info("  ---");
                }
                if (count == 0) {
                    plugin.getLogger().info("数据库中暂无封禁记录");
                }
            }

            plugin.getLogger().info("=== 数据库测试完成 ===");

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "数据获取测试失败: " + e.getMessage(), e);
        }
    }
}
