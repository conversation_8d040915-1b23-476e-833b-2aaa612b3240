package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentManager;
import cn.acebrand.acekeysystem.punishment.PunishmentRecord;
import com.google.gson.Gson;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员玩家状态检查处理器
 * 用于检查玩家的封禁、禁言等状态
 */
public class AdminPlayerStatusHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final Gson gson;

    public AdminPlayerStatusHandler(AceKeySystem plugin) {
        this.plugin = plugin;
        this.gson = new Gson();
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        if ("GET".equals(method)) {
            handleGetRequest(exchange);
        } else {
            sendErrorResponse(exchange, 405, "Method Not Allowed");
        }
    }

    private void handleGetRequest(HttpExchange exchange) throws IOException {
        try {
            // 解析查询参数
            String query = exchange.getRequestURI().getQuery();
            String playerName = null;

            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2 && "player".equals(keyValue[0])) {
                        playerName = java.net.URLDecoder.decode(keyValue[1], "UTF-8");
                        break;
                    }
                }
            }

            if (playerName == null || playerName.trim().isEmpty()) {
                sendErrorResponse(exchange, 400, "缺少玩家名参数");
                return;
            }

            // 检查玩家状态
            Map<String, Object> status = checkPlayerStatus(playerName);

            // 发送响应
            sendJsonResponse(exchange, 200, status);

        } catch (Exception e) {
            plugin.getLogger().warning("检查玩家状态时发生错误: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "服务器内部错误");
        }
    }

    private Map<String, Object> checkPlayerStatus(String playerName) {
        Map<String, Object> status = new HashMap<>();

        try {
            PunishmentManager punishmentManager = plugin.getPunishmentManager();

            // 检查封禁状态
            boolean isBanned = false;
            boolean isMuted = false;
            LocalDateTime now = LocalDateTime.now();

            // 获取玩家的处罚记录
            List<PunishmentRecord> records = punishmentManager.getPunishmentsByPlayer(playerName, 1, 100);

            for (PunishmentRecord record : records) {
                if (!record.isActive()) {
                    continue; // 跳过已撤销的记录
                }

                // 检查是否过期
                if (record.getUntil() != null && record.getUntil().isBefore(now)) {
                    continue; // 跳过已过期的记录
                }

                switch (record.getType()) {
                    case BAN:
                        isBanned = true;
                        break;
                    case MUTE:
                        isMuted = true;
                        break;
                    default:
                        break;
                }
            }

            status.put("banned", isBanned);
            status.put("muted", isMuted);
            status.put("player", playerName);
            status.put("success", true);

        } catch (Exception e) {
            plugin.getLogger().warning("检查玩家状态失败: " + e.getMessage());
            status.put("banned", false);
            status.put("muted", false);
            status.put("player", playerName);
            status.put("success", false);
            status.put("error", e.getMessage());
        }

        return status;
    }

    private void sendJsonResponse(HttpExchange exchange, int statusCode, Object data) throws IOException {
        String jsonResponse = gson.toJson(data);
        byte[] responseBytes = jsonResponse.getBytes(StandardCharsets.UTF_8);

        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type, X-Requested-With");

        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream outputStream = exchange.getResponseBody()) {
            outputStream.write(responseBytes);
        }
    }

    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", message);

        sendJsonResponse(exchange, statusCode, errorResponse);
    }
}
