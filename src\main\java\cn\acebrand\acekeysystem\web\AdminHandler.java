package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.bukkit.configuration.ConfigurationSection;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.IOException;
import java.net.URI;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员界面处理器 - 重新设计版本
 */
public class AdminHandler implements HttpHandler {

        private final AceKeySystem plugin;
        private final WebServer webServer;
        private final PointsShopPageGenerator pointsShopPageGenerator;
        private final InterfacePageGenerator interfacePageGenerator;
        private final RewardPageGenerator rewardPageGenerator;

        public AdminHandler(AceKeySystem plugin, WebServer webServer) {
                this.plugin = plugin;
                this.webServer = webServer;
                this.pointsShopPageGenerator = new PointsShopPageGenerator(plugin, webServer);
                this.interfacePageGenerator = new InterfacePageGenerator(plugin, webServer, webServer.getAdminKey());
                this.rewardPageGenerator = new RewardPageGenerator(plugin, webServer);
        }

        @Override
        public void handle(HttpExchange exchange) throws IOException {
                String method = exchange.getRequestMethod();
                URI uri = exchange.getRequestURI();
                String query = uri.getQuery();

                try {
                        // 验证管理员权限 - 支持会话验证和API密钥验证
                        if (!isAuthorized(exchange)) {
                                handleUnauthorized(exchange);
                                return;
                        }

                        if ("GET".equals(method)) {
                                handleAdminPage(exchange);
                        } else if ("POST".equals(method)) {
                                handleAdminAction(exchange);
                        } else {
                                sendResponse(exchange, 405, "text/plain", "方法不允许");
                        }

                } catch (Exception e) {
                        plugin.getLogger().severe("处理管理员请求时出错: " + e.getMessage());
                        e.printStackTrace();
                        sendResponse(exchange, 500, "text/html; charset=utf-8", generateErrorPage("服务器内部错误"));
                }
        }

        /**
         * 验证管理员权限 - 支持会话验证和API密钥验证，并根据页面检查相应权限
         */
        private boolean isAuthorized(HttpExchange exchange) {
                // 首先检查会话
                if (isValidSession(exchange)) {
                        // 获取页面类型并检查相应权限
                        String query = exchange.getRequestURI().getQuery();
                        Map<String, String> params = parseQuery(query);
                        String page = params.getOrDefault("page", "dashboard");

                        String sessionId = getSessionFromCookie(exchange);
                        String username = webServer.getAdminLoginHandler().getUsernameFromSession(sessionId);
                        if (username != null) {
                                return hasPagePermission(username, page);
                        }
                        return false;
                }

                // 然后检查URL中的API密钥（向后兼容，API密钥拥有所有权限）
                String query = exchange.getRequestURI().getQuery();
                Map<String, String> params = parseQuery(query);
                String providedKey = params.get("key");
                return isValidAdminKey(providedKey);
        }

        /**
         * 验证会话
         */
        private boolean isValidSession(HttpExchange exchange) {
                try {
                        // 获取Cookie中的会话ID
                        String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
                        if (cookieHeader == null) {
                                return false;
                        }

                        String sessionId = null;
                        String[] cookies = cookieHeader.split(";");
                        for (String cookie : cookies) {
                                String[] parts = cookie.trim().split("=", 2);
                                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                                        sessionId = parts[1];
                                        break;
                                }
                        }

                        if (sessionId == null) {
                                return false;
                        }

                        // 验证会话是否有效
                        return webServer.getAdminLoginHandler().isValidSession(sessionId);
                } catch (Exception e) {
                        plugin.getLogger().warning("验证会话时出错: " + e.getMessage());
                        return false;
                }
        }

        /**
         * 验证管理员密钥
         */
        private boolean isValidAdminKey(String key) {
                return key != null && key.equals(webServer.getAdminKey());
        }

        /**
         * 从Cookie中获取会话ID
         */
        private String getSessionFromCookie(HttpExchange exchange) {
                String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
                if (cookieHeader != null) {
                        String[] cookies = cookieHeader.split(";");
                        for (String cookie : cookies) {
                                String[] parts = cookie.trim().split("=", 2);
                                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                                        return parts[1];
                                }
                        }
                }
                return null;
        }

        /**
         * 检查用户是否有访问指定页面的权限
         */
        private boolean hasPagePermission(String username, String page) {
                try {
                        AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);

                        // 超级管理员拥有所有权限
                        if (isSuperAdmin(username, accountHandler)) {
                                return true;
                        }

                        // 根据页面检查相应权限
                        switch (page) {
                                case "accounts":
                                        return false; // 账号管理只有超级管理员可以访问
                                case "online-users":
                                        return accountHandler.hasSystemSettingsPermission(username); // 在线用户管理需要系统设置权限
                                case "settings":
                                        return accountHandler.hasSystemSettingsPermission(username);
                                case "interface":
                                        return accountHandler.hasInterfaceSettingsPermission(username);
                                case "keys":
                                        return accountHandler.hasKeyManagementPermission(username);
                                case "rewards":
                                        return accountHandler.hasRewardManagementPermission(username);
                                case "points-shop":
                                        return accountHandler.hasPointsShopPermission(username);
                                case "statistics":
                                        return accountHandler.hasStatisticsViewPermission(username);
                                case "winners":
                                        return accountHandler.hasWinnersViewPermission(username);
                                case "dashboard":
                                default:
                                        return true; // 仪表板所有管理员都可以访问
                        }
                } catch (Exception e) {
                        plugin.getLogger().warning("检查页面权限时出错: " + e.getMessage());
                        return false;
                }
        }

        /**
         * 检查用户是否为超级管理员
         */
        private boolean isSuperAdmin(String username, AccountManagementHandler accountHandler) {
                String role = accountHandler.getUserRole(username);
                if ("super_admin".equals(role)) {
                        return true;
                }

                // 如果账号不存在，检查是否为配置文件中的默认管理员
                if (role == null) {
                        String configUsername = plugin.getConfig().getString("admin.username", "admin");
                        return configUsername.equals(username);
                }

                return false;
        }

        /**
         * 处理未授权访问
         */
        private void handleUnauthorized(HttpExchange exchange) throws IOException {
                // 检查是否已登录但权限不足
                String sessionId = getSessionFromCookie(exchange);
                if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
                        // 已登录但权限不足，显示权限不足页面
                        String html = generatePermissionDeniedPage();
                        sendResponse(exchange, 403, "text/html; charset=utf-8", html);
                } else {
                        // 未登录，显示未授权页面
                        String html = generateUnauthorizedPage();
                        sendResponse(exchange, 401, "text/html; charset=utf-8", html);
                }
        }

        /**
         * 生成权限不足页面
         */
        private String generatePermissionDeniedPage() {
                return "<!DOCTYPE html>\n" +
                                "<html lang=\"zh-CN\">\n" +
                                "<head>\n" +
                                "    <meta charset=\"UTF-8\">\n" +
                                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                                "    <title>权限不足 - AceKey系统</title>\n" +
                                "    <style>\n" +
                                "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 0; min-height: 100vh; display: flex; align-items: center; justify-content: center; }\n"
                                +
                                "        .container { background: white; border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; }\n"
                                +
                                "        .icon { font-size: 80px; color: #ff6b6b; margin-bottom: 20px; }\n" +
                                "        h1 { color: #333; margin-bottom: 20px; font-size: 28px; }\n" +
                                "        p { color: #666; margin-bottom: 30px; font-size: 16px; line-height: 1.6; }\n" +
                                "        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; border: none; border-radius: 25px; text-decoration: none; display: inline-block; font-size: 16px; transition: transform 0.3s ease; }\n"
                                +
                                "        .btn:hover { transform: translateY(-2px); }\n" +
                                "    </style>\n" +
                                "</head>\n" +
                                "<body>\n" +
                                "    <div class=\"container\">\n" +
                                "        <div class=\"icon\">🚫</div>\n" +
                                "        <h1>权限不足</h1>\n" +
                                "        <p>抱歉，您没有访问此页面的权限。<br>请联系超级管理员为您分配相应权限。</p>\n" +
                                "        <a href=\"/admin\" class=\"btn\">返回管理控制台</a>\n" +
                                "    </div>\n" +
                                "</body>\n" +
                                "</html>";
        }

        /**
         * 处理管理员页面
         */
        private void handleAdminPage(HttpExchange exchange) throws IOException {
                URI uri = exchange.getRequestURI();
                String query = uri.getQuery();
                Map<String, String> params = parseQuery(query);
                String page = params.getOrDefault("page", "dashboard");

                String html = generateAdminPage(page);
                sendResponse(exchange, 200, "text/html; charset=utf-8", html);
        }

        /**
         * 处理管理员操作
         */
        private void handleAdminAction(HttpExchange exchange) throws IOException {
                sendResponse(exchange, 200, "text/plain", "操作完成");
        }

        /**
         * 解析查询参数
         */
        private Map<String, String> parseQuery(String query) {
                Map<String, String> params = new HashMap<>();
                if (query != null) {
                        String[] pairs = query.split("&");
                        for (String pair : pairs) {
                                String[] keyValue = pair.split("=", 2);
                                if (keyValue.length == 2) {
                                        params.put(keyValue[0], keyValue[1]);
                                }
                        }
                }
                return params;
        }

        /**
         * 发送HTTP响应
         */
        private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String response)
                        throws IOException {
                exchange.getResponseHeaders().set("Content-Type", contentType);
                exchange.sendResponseHeaders(statusCode, response.getBytes("UTF-8").length);
                exchange.getResponseBody().write(response.getBytes("UTF-8"));
                exchange.getResponseBody().close();
        }

        /**
         * 生成管理员页面
         */
        private String generateAdminPage(String page) {
                StringBuilder html = new StringBuilder();

                // 获取统计数据
                ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
                int totalKeys = keysSection != null ? keysSection.getKeys(false).size() : 0;

                html.append("<!DOCTYPE html>\n");
                html.append("<html lang=\"zh-CN\">\n");
                html.append("<head>\n");
                html.append("    <meta charset=\"UTF-8\">\n");
                html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
                html.append("    <title>AceKeySystem 管理员控制台 - ").append(webServer.getTitle()).append("</title>\n");
                html.append("    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css\">\n");
                html.append("    <style>\n");
                html.append(getNewAdminCSS());
                html.append("    </style>\n");
                html.append("</head>\n");
                html.append("<body>\n");
                html.append("    <div class=\"admin-container\">\n");
                html.append("        <!-- 侧边导航栏 -->\n");
                html.append("        <nav class=\"sidebar\">\n");
                html.append("            <div class=\"sidebar-header\">\n");
                html.append("                <h2>🛡️ 管理控制台</h2>\n");
                html.append("            </div>\n");
                html.append("            <ul class=\"nav-menu\">\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=dashboard\" class=\"").append("dashboard".equals(page) ? "active" : "")
                                .append("\">📊 仪表板</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=keys\" class=\"").append("keys".equals(page) ? "active" : "")
                                .append("\">🔑 卡密管理</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=statistics\" class=\"").append("statistics".equals(page) ? "active" : "")
                                .append("\">📈 统计分析</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=rewards\" class=\"").append("rewards".equals(page) ? "active" : "")
                                .append("\">🎁 奖品管理</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=points-shop\" class=\"")
                                .append("points-shop".equals(page) ? "active" : "")
                                .append("\">🛒 积分商店</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=checkin\" class=\"")
                                .append("checkin".equals(page) || "checkin-shop".equals(page) ? "active" : "")
                                .append("\">📅 签到系统管理</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=winners\" class=\"").append("winners".equals(page) ? "active" : "")
                                .append("\">🏆 中奖记录</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=settings\" class=\"").append("settings".equals(page) ? "active" : "")
                                .append("\">⚙️ 系统设置</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=interface\" class=\"").append("interface".equals(page) ? "active" : "")
                                .append("\">🎨 界面设置</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=accounts\" class=\"").append("accounts".equals(page) ? "active" : "")
                                .append("\">👥 账号管理</a></li>\n");
                html.append("                <li><a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=online-users\" class=\"")
                                .append("online-users".equals(page) ? "active" : "")
                                .append("\">🌐 在线用户</a></li>\n");
                html.append("                <li><a href=\"/admin-punishments\" target=\"_blank\">🚫 处罚记录</a></li>\n");
                html.append("                <li class=\"divider\"></li>\n");
                html.append("                <li><a href=\"/user\">👤 用户界面</a></li>\n");
                html.append("                <li><a href=\"/\">🏠 返回首页</a></li>\n");
                html.append("                <li><a href=\"#\" onclick=\"logout()\" class=\"logout-btn\">🚪 登出</a></li>\n");
                html.append("            </ul>\n");
                html.append("            \n");
                html.append("            <!-- 主题切换按钮 -->\n");
                html.append("            <div class=\"theme-switcher\">\n");
                html.append("                <button id=\"themeSwitchBtn\" class=\"theme-btn\" onclick=\"toggleAdminTheme()\" title=\"切换主题\">\n");
                html.append("                    <span class=\"theme-icon\">🌙</span>\n");
                html.append("                </button>\n");
                html.append("            </div>\n");
                html.append("        </nav>\n");
                html.append("        \n");
                html.append("        <!-- 主内容区域 -->\n");
                html.append("        <main class=\"main-content\">\n");

                // 根据页面类型生成不同内容
                switch (page) {
                        case "keys":
                                html.append(generateKeysPage(totalKeys));
                                break;
                        case "statistics":
                                html.append(generateStatisticsPage());
                                break;
                        case "rewards":
                                html.append(rewardPageGenerator.generateRewardPage());
                                break;
                        case "points-shop":
                                html.append(pointsShopPageGenerator.generatePointsShopPage());
                                break;
                        case "checkin":
                        case "checkin-shop":
                                html.append(generateCheckInManagePage(page));
                                break;
                        case "winners":
                                html.append(generateWinnersPage());
                                break;
                        case "settings":
                                html.append(generateSettingsPage());
                                break;
                        case "interface":
                                html.append(interfacePageGenerator.generateInterfacePage());
                                break;
                        case "accounts":
                                AccountManagementPageGenerator accountPageGenerator = new AccountManagementPageGenerator(
                                                webServer.getAdminKey());
                                html.append(accountPageGenerator.generateAccountsPage());
                                break;
                        case "online-users":
                                html.append(generateOnlineUsersPage());
                                break;
                        default: // dashboard
                                html.append(generateDashboardPage(totalKeys));
                                break;
                }

                html.append("        </main>\n");
                html.append("    </div>\n");
                html.append("    \n");
                html.append("    <!-- 结果显示区域 -->\n");
                html.append("    <div id=\"result\" class=\"result-area\" style=\"display: none;\"></div>\n");
                html.append("    \n");

                // 添加图标选择器弹窗
                html.append("    <!-- 图标选择弹窗 -->\n");
                html.append("    <div id=\"iconSelectorModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"modal-content icon-modal\">\n");
                html.append("            <div class=\"modal-header\">\n");
                html.append("                <h3>选择图标</h3>\n");
                html.append("                <span class=\"close\" onclick=\"closeIconSelector()\">&times;</span>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-body\">\n");
                html.append("                <div class=\"icon-categories\">\n");
                html.append("                    <button class=\"category-btn active\" onclick=\"showIconCategory('all')\">全部</button>\n");
                html.append("                    <button class=\"category-btn\" onclick=\"showIconCategory('trophy')\">奖杯</button>\n");
                html.append("                    <button class=\"category-btn\" onclick=\"showIconCategory('gem')\">宝石</button>\n");
                html.append("                    <button class=\"category-btn\" onclick=\"showIconCategory('tool')\">工具</button>\n");
                html.append("                    <button class=\"category-btn\" onclick=\"showIconCategory('food')\">食物</button>\n");
                html.append("                    <button class=\"category-btn\" onclick=\"showIconCategory('other')\">其他</button>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"icon-grid\" id=\"iconGrid\">\n");
                html.append("                    <!-- 图标将通过JavaScript动态生成 -->\n");
                html.append("                </div>\n");
                html.append("                <div class=\"icon-help\">\n");
                html.append("                    <p>💡 点击图标即可选择并插入到输入框</p>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");
                html.append("    \n");

                html.append("    <script>\n");
                html.append(getAdminJS());
                html.append("    </script>\n");
                html.append("</body>\n");
                html.append("</html>");

                return html.toString();
        }

        /**
         * 生成仪表板页面
         */
        private String generateDashboardPage(int totalKeys) {
                DashboardPageGenerator generator = new DashboardPageGenerator(plugin, webServer);
                return generator.generateDashboardPage(totalKeys);
        }

        /**
         * 生成卡密管理页面
         */
        private String generateKeysPage(int totalKeys) {
                KeysPageGenerator generator = new KeysPageGenerator(webServer.getAdminKey());
                return generator.generateKeysPage(totalKeys);
        }

        /**
         * 生成签到系统管理页面（带子导航）
         */
        private String generateCheckInManagePage(String currentPage) {
                StringBuilder html = new StringBuilder();

                // 确定当前子页面
                String subPage = "checkin-shop".equals(currentPage) ? "shop" : "management";

                html.append("<div class=\"admin-section\">\n");
                html.append("    <div class=\"section-header\">\n");
                html.append("        <h2><i class=\"fas fa-calendar-check\"></i> 签到系统管理</h2>\n");
                html.append("        <p>管理签到系统的各项设置和功能</p>\n");
                html.append("    </div>\n");

                // 子导航
                html.append("    <div class=\"sub-navigation\">\n");
                html.append("        <div class=\"nav-tabs\">\n");
                html.append("            <a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=checkin\" class=\"nav-tab ")
                                .append("management".equals(subPage) ? "active" : "")
                                .append("\">\n");
                html.append("                <i class=\"fas fa-cogs\"></i> 签到管理\n");
                html.append("            </a>\n");
                html.append("            <a href=\"/admin?key=").append(webServer.getAdminKey())
                                .append("&page=checkin-shop\" class=\"nav-tab ")
                                .append("shop".equals(subPage) ? "active" : "")
                                .append("\">\n");
                html.append("                <i class=\"fas fa-shopping-cart\"></i> 补签商店设置\n");
                html.append("            </a>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                // 根据子页面显示不同内容
                html.append("    <div class=\"tab-content\">\n");
                if ("shop".equals(subPage)) {
                        html.append(generateCheckInShopContent());
                } else {
                        html.append(generateCheckInManagementContent());
                }
                html.append("    </div>\n");
                html.append("</div>\n");

                return html.toString();
        }

        /**
         * 生成签到管理内容
         */
        private String generateCheckInManagementContent() {
                AdminCheckInPageGenerator generator = new AdminCheckInPageGenerator(webServer.getAdminKey());
                return generator.generateCheckInManagePage();
        }

        /**
         * 生成补签商店设置内容
         */
        private String generateCheckInShopContent() {
                StringBuilder html = new StringBuilder();

                html.append("    <div class=\"section-header\">\n");
                html.append("        <h2><i class=\"fas fa-shopping-cart\"></i> 补签卡商店设置</h2>\n");
                html.append("        <p>配置补签卡的价格、货币类型和购买选项</p>\n");
                html.append("    </div>\n");

                // 价格设置卡片
                html.append("    <div class=\"settings-grid\">\n");
                html.append("        <!-- 金币价格设置 -->\n");
                html.append("        <div class=\"setting-card\">\n");
                html.append("            <div class=\"card-header\">\n");
                html.append("                <i class=\"fas fa-coins\" style=\"color: #f59e0b;\"></i>\n");
                html.append("                <h3>金币补签卡</h3>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"card-content\">\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"goldPrice\">价格 (金币)</label>\n");
                html.append("                    <input type=\"number\" id=\"goldPrice\" value=\"100\" min=\"1\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"goldCurrencyName\">货币名称</label>\n");
                html.append("                    <input type=\"text\" id=\"goldCurrencyName\" value=\"金币\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"goldDescription\">描述</label>\n");
                html.append("                    <input type=\"text\" id=\"goldDescription\" value=\"使用游戏内金币购买\" class=\"form-input\" placeholder=\"例如: 使用游戏内金币购买\">\n");
                html.append("                    <small class=\"form-help\">在补签卡商店中显示的描述文字</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"goldBalanceVariable\">余额变量 (PlaceholderAPI)</label>\n");
                html.append("                    <input type=\"text\" id=\"goldBalanceVariable\" value=\"%vault_eco_balance%\" class=\"form-input\" placeholder=\"例如: %vault_eco_balance%\">\n");
                html.append("                    <small class=\"form-help\">用于显示用户余额的PlaceholderAPI变量</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"goldDeductCommand\">扣除指令</label>\n");
                html.append("                    <input type=\"text\" id=\"goldDeductCommand\" value=\"eco take {player} {amount}\" class=\"form-input\" placeholder=\"例如: eco take {player} {amount}\">\n");
                html.append("                    <small class=\"form-help\">购买时执行的扣除指令，{player}=玩家名，{amount}=金额</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label class=\"checkbox-label\">\n");
                html.append("                        <input type=\"checkbox\" id=\"goldEnabled\" checked>\n");
                html.append("                        <span class=\"checkmark\"></span>\n");
                html.append("                        启用金币购买\n");
                html.append("                    </label>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                html.append("        <!-- 点券价格设置 -->\n");
                html.append("        <div class=\"setting-card\">\n");
                html.append("            <div class=\"card-header\">\n");
                html.append("                <i class=\"fas fa-gem\" style=\"color: #8b5cf6;\"></i>\n");
                html.append("                <h3>点券补签卡</h3>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"card-content\">\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"pointsPrice\">价格 (点券)</label>\n");
                html.append("                    <input type=\"number\" id=\"pointsPrice\" value=\"50\" min=\"1\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"pointsCurrencyName\">货币名称</label>\n");
                html.append("                    <input type=\"text\" id=\"pointsCurrencyName\" value=\"点券\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"pointsDescription\">描述</label>\n");
                html.append("                    <input type=\"text\" id=\"pointsDescription\" value=\"使用点券购买\" class=\"form-input\" placeholder=\"例如: 使用点券购买\">\n");
                html.append("                    <small class=\"form-help\">在补签卡商店中显示的描述文字</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"pointsBalanceVariable\">余额变量 (PlaceholderAPI)</label>\n");
                html.append("                    <input type=\"text\" id=\"pointsBalanceVariable\" value=\"%playerpoints_points%\" class=\"form-input\" placeholder=\"例如: %playerpoints_points%\">\n");
                html.append("                    <small class=\"form-help\">用于显示用户余额的PlaceholderAPI变量</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"pointsDeductCommand\">扣除指令</label>\n");
                html.append("                    <input type=\"text\" id=\"pointsDeductCommand\" value=\"pp take {player} {amount}\" class=\"form-input\" placeholder=\"例如: pp take {player} {amount}\">\n");
                html.append("                    <small class=\"form-help\">购买时执行的扣除指令，{player}=玩家名，{amount}=金额</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label class=\"checkbox-label\">\n");
                html.append("                        <input type=\"checkbox\" id=\"pointsEnabled\" checked>\n");
                html.append("                        <span class=\"checkmark\"></span>\n");
                html.append("                        启用点券购买\n");
                html.append("                    </label>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                html.append("        <!-- 积分价格设置 -->\n");
                html.append("        <div class=\"setting-card\">\n");
                html.append("            <div class=\"card-header\">\n");
                html.append("                <i class=\"fas fa-star\" style=\"color: #10b981;\"></i>\n");
                html.append("                <h3>积分补签卡</h3>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"card-content\">\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"scorePrice\">价格 (积分)</label>\n");
                html.append("                    <input type=\"number\" id=\"scorePrice\" value=\"200\" min=\"1\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"scoreCurrencyName\">货币名称</label>\n");
                html.append("                    <input type=\"text\" id=\"scoreCurrencyName\" value=\"积分\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"scoreDescription\">描述</label>\n");
                html.append("                    <input type=\"text\" id=\"scoreDescription\" value=\"使用积分商店积分购买\" class=\"form-input\" placeholder=\"例如: 使用积分商店积分购买\">\n");
                html.append("                    <small class=\"form-help\">在补签卡商店中显示的描述文字</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"scoreBalanceVariable\">余额变量</label>\n");
                html.append("                    <input type=\"text\" id=\"scoreBalanceVariable\" value=\"internal\" class=\"form-input\" readonly>\n");
                html.append("                    <small class=\"form-help\">积分使用积分商店的积分系统，无需修改此项</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"scoreDeductCommand\">扣除指令</label>\n");
                html.append("                    <input type=\"text\" id=\"scoreDeductCommand\" value=\"internal\" class=\"form-input\" readonly>\n");
                html.append("                    <small class=\"form-help\">积分使用内部系统，无需修改此项</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label class=\"checkbox-label\">\n");
                html.append("                        <input type=\"checkbox\" id=\"scoreEnabled\" checked>\n");
                html.append("                        <span class=\"checkmark\"></span>\n");
                html.append("                        启用积分购买\n");
                html.append("                    </label>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                // 添加自定义补签卡设置
                html.append("        <!-- 自定义补签卡设置 -->\n");
                html.append("        <div class=\"setting-card\">\n");
                html.append("            <div class=\"card-header\">\n");
                html.append("                <i class=\"fas fa-cog\" style=\"color: #6b7280;\"></i>\n");
                html.append("                <h3>自定义补签卡</h3>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"card-content\">\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"customPrice\">价格</label>\n");
                html.append("                    <input type=\"number\" id=\"customPrice\" value=\"150\" min=\"1\" class=\"form-input\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"customCurrencyName\">货币名称</label>\n");
                html.append("                    <input type=\"text\" id=\"customCurrencyName\" value=\"自定义货币\" class=\"form-input\" placeholder=\"例如: 钻石\">\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"customDescription\">描述</label>\n");
                html.append("                    <input type=\"text\" id=\"customDescription\" value=\"使用自定义货币购买\" class=\"form-input\" placeholder=\"例如: 使用钻石购买\">\n");
                html.append("                    <small class=\"form-help\">在补签卡商店中显示的描述文字</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"customBalanceVariable\">余额变量 (PlaceholderAPI)</label>\n");
                html.append("                    <input type=\"text\" id=\"customBalanceVariable\" value=\"%custom_balance%\" class=\"form-input\" placeholder=\"例如: %custom_balance%\">\n");
                html.append("                    <small class=\"form-help\">用于显示用户余额的PlaceholderAPI变量</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label for=\"customDeductCommand\">扣除指令</label>\n");
                html.append("                    <input type=\"text\" id=\"customDeductCommand\" value=\"customcurrency take {player} {amount}\" class=\"form-input\" placeholder=\"例如: customcurrency take {player} {amount}\">\n");
                html.append("                    <small class=\"form-help\">购买时执行的扣除指令，{player}=玩家名，{amount}=金额</small>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"form-group\">\n");
                html.append("                    <label class=\"checkbox-label\">\n");
                html.append("                        <input type=\"checkbox\" id=\"customEnabled\">\n");
                html.append("                        <span class=\"checkmark\"></span>\n");
                html.append("                        启用自定义购买\n");
                html.append("                    </label>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                // 保存按钮
                html.append("    <div class=\"action-buttons\">\n");
                html.append("        <button class=\"btn btn-primary\" onclick=\"saveMakeupCardSettings()\">\n");
                html.append("            <i class=\"fas fa-save\"></i> 保存设置\n");
                html.append("        </button>\n");
                html.append("        <button class=\"btn btn-secondary\" onclick=\"loadMakeupCardSettings()\">\n");
                html.append("            <i class=\"fas fa-refresh\"></i> 重新加载\n");
                html.append("        </button>\n");
                html.append("    </div>\n");

                return html.toString();
        }

        /**
         * 生成中奖记录页面
         */
        private String generateWinnersPage() {
                WinnersPageGenerator generator = new WinnersPageGenerator(webServer.getAdminKey());
                return generator.generateWinnersPage();
        }

        /**
         * 生成系统设置页面
         */
        private String generateSettingsPage() {
                SettingsPageGenerator generator = new SettingsPageGenerator(plugin, webServer);
                return generator.generateSettingsPage();
        }

        /**
         * 生成未授权页面
         */
        private String generateUnauthorizedPage() {
                return "<!DOCTYPE html><html lang=\"zh-CN\"><head><meta charset=\"UTF-8\"><title>访问被拒绝</title>" +
                                "<style>body{font-family:Arial,sans-serif;text-align:center;padding:50px;background:#f5f5f5;}"
                                +
                                ".error{background:var(--bg-card, white);padding:40px;border-radius:10px;box-shadow:0 2px 10px rgba(0,0,0,0.1);display:inline-block;border:1px solid var(--border-color, #e1e5e9);}"
                                +
                                "h1{color:#e74c3c;margin-bottom:20px;}p{color:#666;margin-bottom:30px;}" +
                                "a{color:#3498db;text-decoration:none;}</style></head>" +
                                "<body><div class=\"error\"><h1>🚫 访问被拒绝</h1>" +
                                "<p>您没有权限访问管理员界面。</p>" +
                                "<p>请确保您拥有正确的管理员密钥。</p>" +
                                "<a href=\"/\">返回首页</a></div></body></html>";
        }

        /**
         * 生成错误页面
         */
        private String generateErrorPage(String message) {
                return "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>错误</title></head>" +
                                "<body style=\"font-family: Arial, sans-serif; text-align: center; padding: 50px;\">" +
                                "<h1>发生错误</h1><p>" + message + "</p>" +
                                "<a href=\"/admin?key=" + webServer.getAdminKey() + "\">返回管理员界面</a>" +
                                "</body></html>";
        }

        /**
         * 获取新的管理员界面CSS
         */
        private String getNewAdminCSS() {
                return "/* CSS变量定义 */\n" +
                                ":root {\n" +
                                "  --bg-primary: #f5f7fa;\n" +
                                "  --bg-secondary: #ffffff;\n" +
                                "  --bg-card: #ffffff;\n" +
                                "  --text-primary: #333333;\n" +
                                "  --text-secondary: #666666;\n" +
                                "  --text-muted: #999999;\n" +
                                "  --border-color: #e1e5e9;\n" +
                                "  --shadow-light: rgba(0,0,0,0.1);\n" +
                                "  --shadow-medium: rgba(0,0,0,0.15);\n" +
                                "  --sidebar-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                                "  --sidebar-text: #ffffff;\n" +
                                "  --accent-color: #667eea;\n" +
                                "  --success-color: #4caf50;\n" +
                                "  --warning-color: #ff9800;\n" +
                                "  --error-color: #f44336;\n" +
                                "  --info-color: #2196f3;\n" +
                                "}\n" +
                                "\n" +
                                "/* 深色主题 */\n" +
                                "body.theme-dark {\n" +
                                "  --bg-primary: #1a1a1a;\n" +
                                "  --bg-secondary: #2d2d2d;\n" +
                                "  --bg-card: #2d2d2d;\n" +
                                "  --text-primary: #ffffff;\n" +
                                "  --text-secondary: #cccccc;\n" +
                                "  --text-muted: #999999;\n" +
                                "  --border-color: #404040;\n" +
                                "  --shadow-light: rgba(0,0,0,0.3);\n" +
                                "  --shadow-medium: rgba(0,0,0,0.5);\n" +
                                "  --sidebar-bg: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);\n" +
                                "  --sidebar-text: #ecf0f1;\n" +
                                "}\n" +
                                "\n" +
                                "/* 浅色主题（默认） */\n" +
                                "body.theme-light {\n" +
                                "  --bg-primary: #f5f7fa;\n" +
                                "  --bg-secondary: #ffffff;\n" +
                                "  --bg-card: #ffffff;\n" +
                                "  --text-primary: #333333;\n" +
                                "  --text-secondary: #666666;\n" +
                                "  --text-muted: #999999;\n" +
                                "  --border-color: #e1e5e9;\n" +
                                "  --shadow-light: rgba(0,0,0,0.1);\n" +
                                "  --shadow-medium: rgba(0,0,0,0.15);\n" +
                                "  --sidebar-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                                "  --sidebar-text: #ffffff;\n" +
                                "}\n" +
                                "\n" +
                                "/* 浅色主题下的积分商店卡片渐变紫色样式 */\n" +
                                "body.theme-light .shop-card {\n" +
                                "  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                                "  color: #ffffff;\n" +
                                "  border: 1px solid rgba(255,255,255,0.2);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .item-info,\n" +
                                "body.theme-light .shop-card .item-name,\n" +
                                "body.theme-light .shop-card .item-cost {\n" +
                                "  color: #ffffff;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .card-body {\n" +
                                "  background: rgba(255,255,255,0.15);\n" +
                                "  border: 1px solid rgba(255,255,255,0.2);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .item-description,\n" +
                                "body.theme-light .shop-card .detail-label,\n" +
                                "body.theme-light .shop-card .detail-value {\n" +
                                "  color: rgba(255,255,255,0.9);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .action-btn {\n" +
                                "  background: rgba(255,255,255,0.2);\n" +
                                "  border: 1px solid rgba(255,255,255,0.3);\n" +
                                "  color: #ffffff;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .action-btn:hover {\n" +
                                "  background: rgba(255,255,255,0.3);\n" +
                                "  color: #ffffff;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .action-btn.enable {\n" +
                                "  background: #4caf50 !important;\n" +
                                "  color: white !important;\n" +
                                "  border: 1px solid #4caf50 !important;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .action-btn.enable:hover {\n" +
                                "  background: #45a049 !important;\n" +
                                "  color: white !important;\n" +
                                "  border: 1px solid #45a049 !important;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .status-indicator.status-enabled {\n" +
                                "  background: #4caf50 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #4caf50;\n" +
                                "  font-weight: 700;\n" +
                                "  box-shadow: 0 2px 4px rgba(76,175,80,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .status-indicator.status-disabled {\n" +
                                "  background: #f44336 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #f44336;\n" +
                                "  font-weight: 700;\n" +
                                "  box-shadow: 0 2px 4px rgba(244,67,54,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .status-indicator.status-warning {\n" +
                                "  background: #ff9800 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #ff9800;\n" +
                                "  font-weight: 700;\n" +
                                "  box-shadow: 0 2px 4px rgba(255,152,0,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .shop-card .card-checkbox {\n" +
                                "  accent-color: #ffffff;\n" +
                                "}\n" +
                                "\n" +
                                "/* 浅色主题下的中奖记录卡片样式 */\n" +
                                "body.theme-light .winners-container .shop-card .card-name,\n" +
                                "body.theme-light .winners-container .shop-card .info-label,\n" +
                                "body.theme-light .winners-container .shop-card .info-value {\n" +
                                "  color: #ffffff;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .winners-container .shop-card .info-value.prize-name {\n" +
                                "  color: #ffeb3b;\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题下的状态指示器样式 */\n" +
                                "body.theme-dark .status-indicator.status-enabled {\n" +
                                "  background: #4caf50 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #4caf50;\n" +
                                "  font-weight: 700;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .status-indicator.status-disabled {\n" +
                                "  background: #f44336 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #f44336;\n" +
                                "  font-weight: 700;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .status-indicator.status-warning {\n" +
                                "  background: #ff9800 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #ff9800;\n" +
                                "  font-weight: 700;\n" +
                                "}\n" +
                                "\n" +
                                "/* 浅色主题下的复制按钮样式 */\n" +
                                "body.theme-light .key-card .action-btn.copy {\n" +
                                "  background: rgba(76,175,80,0.2) !important;\n" +
                                "  color: #2e7d32 !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid rgba(76,175,80,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .key-card .action-btn.copy:hover {\n" +
                                "  background: rgba(76,175,80,0.3) !important;\n" +
                                "  color: #1b5e20 !important;\n" +
                                "  transform: translateY(-2px);\n" +
                                "  box-shadow: 0 4px 12px rgba(76,175,80,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题下的复制按钮样式 */\n" +
                                "body.theme-dark .key-card .action-btn.copy {\n" +
                                "  background: rgba(76,175,80,0.3) !important;\n" +
                                "  color: #81c784 !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid rgba(76,175,80,0.5);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .key-card .action-btn.copy:hover {\n" +
                                "  background: rgba(76,175,80,0.4) !important;\n" +
                                "  color: #a5d6a7 !important;\n" +
                                "  transform: translateY(-2px);\n" +
                                "  box-shadow: 0 4px 12px rgba(76,175,80,0.4);\n" +
                                "}\n" +
                                "\n" +
                                "/* 浅色主题下的玩家卡密状态标签样式 */\n" +
                                "body.theme-light .key-card .card-status.status-warning {\n" +
                                "  background: #ff9800 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid #f57c00;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .key-card .card-status.status-danger {\n" +
                                "  background: #4caf50 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid #388e3c;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-light .key-card .card-status.status-secondary {\n" +
                                "  background: #f44336 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid #d32f2f;\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题下的玩家卡密状态标签样式 */\n" +
                                "body.theme-dark .key-card .card-status.status-warning {\n" +
                                "  background: #ff9800 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid #f57c00;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .key-card .card-status.status-danger {\n" +
                                "  background: #4caf50 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid #388e3c;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .key-card .card-status.status-secondary {\n" +
                                "  background: #f44336 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  font-weight: 700;\n" +
                                "  border: 1px solid #d32f2f;\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题下的积分商店状态指示器样式 */\n" +
                                "body.theme-dark .shop-card .status-indicator.status-enabled {\n" +
                                "  background: #4caf50 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #4caf50;\n" +
                                "  font-weight: 700;\n" +
                                "  box-shadow: 0 2px 4px rgba(76,175,80,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .shop-card .status-indicator.status-disabled {\n" +
                                "  background: #f44336 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #f44336;\n" +
                                "  font-weight: 700;\n" +
                                "  box-shadow: 0 2px 4px rgba(244,67,54,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .shop-card .status-indicator.status-warning {\n" +
                                "  background: #ff9800 !important;\n" +
                                "  color: #ffffff !important;\n" +
                                "  border: 1px solid #ff9800;\n" +
                                "  font-weight: 700;\n" +
                                "  box-shadow: 0 2px 4px rgba(255,152,0,0.3);\n" +
                                "}\n" +
                                "\n" +
                                "* { margin: 0; padding: 0; box-sizing: border-box; }\n" +
                                "body { \n" +
                                "  font-family: 'Microsoft YaHei', Arial, sans-serif; \n" +
                                "  background: var(--bg-primary); \n" +
                                "  color: var(--text-primary);\n" +
                                "  transition: background-color 0.3s ease, color 0.3s ease;\n" +
                                "}\n" +
                                ".admin-container { display: flex; min-height: 100vh; }\n" +
                                ".sidebar { \n" +
                                "  width: 250px; \n" +
                                "  background: var(--sidebar-bg); \n" +
                                "  color: var(--sidebar-text); \n" +
                                "  position: fixed; \n" +
                                "  height: 100vh; \n" +
                                "  overflow-y: auto;\n" +
                                "  transition: background 0.3s ease;\n" +
                                "}\n"
                                +
                                ".sidebar-header { padding: 20px; text-align: center; border-bottom: 1px solid rgba(255,255,255,0.1); }\n"
                                +
                                ".sidebar-header h2 { margin: 0; font-size: 1.2em; }\n" +
                                ".nav-menu { list-style: none; padding: 0; }\n" +
                                ".nav-menu li { margin: 0; }\n" +
                                "/* 主题切换按钮样式 */\n" +
                                ".theme-switcher { \n" +
                                "  position: absolute; \n" +
                                "  bottom: 20px; \n" +
                                "  left: 50%; \n" +
                                "  transform: translateX(-50%); \n" +
                                "  z-index: 1000;\n" +
                                "}\n" +
                                ".theme-btn { \n" +
                                "  background: rgba(255,255,255,0.2); \n" +
                                "  border: 1px solid rgba(255,255,255,0.3); \n" +
                                "  border-radius: 50%; \n" +
                                "  width: 45px; \n" +
                                "  height: 45px; \n" +
                                "  display: flex; \n" +
                                "  align-items: center; \n" +
                                "  justify-content: center; \n" +
                                "  cursor: pointer; \n" +
                                "  transition: all 0.3s ease; \n" +
                                "  backdrop-filter: blur(10px);\n" +
                                "}\n" +
                                ".theme-btn:hover { \n" +
                                "  background: rgba(255,255,255,0.3); \n" +
                                "  transform: scale(1.1); \n" +
                                "  box-shadow: 0 4px 15px rgba(0,0,0,0.2);\n" +
                                "}\n" +
                                ".theme-icon { \n" +
                                "  font-size: 20px; \n" +
                                "  transition: transform 0.3s ease;\n" +
                                "}\n" +
                                ".theme-btn:hover .theme-icon { \n" +
                                "  transform: rotate(180deg);\n" +
                                "}\n" +
                                "/* 深色主题下的主题切换按钮 */\n" +
                                "body.theme-dark .theme-btn { \n" +
                                "  background: rgba(0,0,0,0.3); \n" +
                                "  border: 1px solid rgba(255,255,255,0.2);\n" +
                                "}\n" +
                                "body.theme-dark .theme-btn:hover { \n" +
                                "  background: rgba(0,0,0,0.5);\n" +
                                "}\n" +
                                ".nav-menu li.divider { height: 1px; background: rgba(255,255,255,0.1); margin: 10px 0; }\n"
                                +
                                ".nav-menu a { display: block; padding: 15px 20px; color: white; text-decoration: none; transition: all 0.3s; border-left: 3px solid transparent; }\n"
                                +
                                ".nav-menu a:hover { background: rgba(255,255,255,0.1); border-left-color: white; }\n" +
                                ".nav-menu a.active { background: rgba(255,255,255,0.2); border-left-color: white; font-weight: bold; }\n"
                                +
                                ".main-content { flex: 1; margin-left: 250px; padding: 30px; }\n" +
                                ".page-header { margin-bottom: 30px; }\n" +
                                ".page-header h1 { font-size: 2.5em; margin-bottom: 10px; color: var(--text-primary); }\n"
                                +
                                ".page-header p { color: var(--text-secondary); font-size: 1.1em; }\n" +
                                "/* 子导航样式 */\n" +
                                ".sub-navigation { margin-bottom: 30px; }\n" +
                                ".nav-tabs { display: flex; border-bottom: 2px solid var(--border-color); }\n" +
                                ".nav-tab { \n" +
                                "  padding: 15px 25px; \n" +
                                "  text-decoration: none; \n" +
                                "  color: var(--text-secondary); \n" +
                                "  border-bottom: 3px solid transparent; \n" +
                                "  transition: all 0.3s ease; \n" +
                                "  font-weight: 500;\n" +
                                "  display: flex;\n" +
                                "  align-items: center;\n" +
                                "  gap: 8px;\n" +
                                "}\n" +
                                ".nav-tab:hover { \n" +
                                "  color: var(--accent-color); \n" +
                                "  background: rgba(102, 126, 234, 0.05);\n" +
                                "}\n" +
                                ".nav-tab.active { \n" +
                                "  color: var(--accent-color); \n" +
                                "  border-bottom-color: var(--accent-color); \n" +
                                "  background: rgba(102, 126, 234, 0.1);\n" +
                                "  font-weight: 600;\n" +
                                "}\n" +
                                ".tab-content { \n" +
                                "  background: var(--bg-card); \n" +
                                "  border-radius: 12px; \n" +
                                "  padding: 30px; \n" +
                                "  box-shadow: 0 4px 6px var(--shadow-light); \n" +
                                "  border: 1px solid var(--border-color);\n" +
                                "}\n" +
                                "/* 管理页面区域样式 */\n" +
                                ".admin-section { margin-bottom: 30px; }\n" +
                                ".section-header { margin-bottom: 30px; }\n" +
                                ".section-header h2 { \n" +
                                "  font-size: 2em; \n" +
                                "  margin-bottom: 10px; \n" +
                                "  color: var(--text-primary); \n" +
                                "  display: flex; \n" +
                                "  align-items: center; \n" +
                                "  gap: 10px;\n" +
                                "}\n" +
                                ".section-header p { \n" +
                                "  color: var(--text-secondary); \n" +
                                "  font-size: 1.1em; \n" +
                                "  margin: 0;\n" +
                                "}\n" +
                                "/* 设置网格和卡片样式 */\n" +
                                ".settings-grid { \n" +
                                "  display: grid; \n" +
                                "  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); \n" +
                                "  gap: 25px; \n" +
                                "  margin-bottom: 30px;\n" +
                                "}\n" +
                                ".setting-card { \n" +
                                "  background: var(--bg-secondary); \n" +
                                "  border-radius: 12px; \n" +
                                "  padding: 25px; \n" +
                                "  box-shadow: 0 4px 6px var(--shadow-light); \n" +
                                "  border: 1px solid var(--border-color); \n" +
                                "  transition: transform 0.3s ease;\n" +
                                "}\n" +
                                ".setting-card:hover { transform: translateY(-2px); }\n" +
                                ".setting-card .card-header { \n" +
                                "  display: flex; \n" +
                                "  align-items: center; \n" +
                                "  gap: 12px; \n" +
                                "  margin-bottom: 20px; \n" +
                                "  padding-bottom: 15px; \n" +
                                "  border-bottom: 1px solid var(--border-color);\n" +
                                "}\n" +
                                ".setting-card .card-header h3 { \n" +
                                "  margin: 0; \n" +
                                "  color: var(--text-primary); \n" +
                                "  font-size: 1.3em;\n" +
                                "}\n" +
                                ".setting-card .card-header i { font-size: 1.5em; }\n" +
                                ".action-buttons { \n" +
                                "  display: flex; \n" +
                                "  gap: 15px; \n" +
                                "  justify-content: center; \n" +
                                "  margin-top: 30px;\n" +
                                "}\n" +
                                ".stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }\n"
                                +
                                ".stat-card { background: var(--bg-card); padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px var(--shadow-light); display: flex; align-items: center; transition: transform 0.3s; border: 1px solid var(--border-color); }\n"
                                +
                                ".stat-card:hover { transform: translateY(-2px); }\n" +
                                ".stat-icon { font-size: 2.5em; margin-right: 20px; }\n" +
                                ".stat-number { font-size: 2em; font-weight: bold; color: var(--text-primary); }\n" +
                                ".stat-label { color: var(--text-secondary); margin-top: 5px; }\n" +
                                ".quick-actions { margin-top: 40px; }\n" +
                                ".quick-actions h2 { margin-bottom: 20px; color: var(--text-primary); }\n" +
                                ".action-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 20px; }\n"
                                +
                                ".action-card { background: var(--bg-card); padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px var(--shadow-light); transition: transform 0.3s; border: 1px solid var(--border-color); }\n"
                                +
                                ".action-card:hover { transform: translateY(-2px); }\n" +
                                ".action-card h3 { margin-bottom: 10px; color: var(--text-primary); }\n" +
                                ".action-card p { color: var(--text-secondary); margin-bottom: 20px; }\n" +
                                ".btn { display: inline-block; padding: 10px 20px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; transition: background 0.3s; border: none; cursor: pointer; }\n"
                                +
                                ".btn:hover { background: #5a6fd8; }\n" +
                                ".btn-primary { background: #667eea; }\n" +
                                ".btn-secondary { background: #6c757d; }\n" +
                                ".btn-danger { background: #dc3545; }\n" +
                                ".btn-info { background: #17a2b8; }\n" +
                                ".content-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; }\n"
                                +
                                ".content-card { background: var(--bg-card); padding: 25px; border-radius: 12px; box-shadow: 0 4px 6px var(--shadow-light); border: 1px solid var(--border-color); }\n"
                                +
                                ".content-card h3 { margin-bottom: 20px; color: var(--text-primary); }\n" +
                                ".form-group { margin-bottom: 20px; }\n" +
                                ".form-group label { display: block; margin-bottom: 8px; color: var(--text-primary); font-weight: bold; }\n"
                                +
                                ".form-input { width: 100%; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; font-size: 14px; background: var(--bg-secondary); color: var(--text-primary); }\n"
                                +
                                ".form-input:focus { border-color: var(--accent-color); outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }\n"
                                +
                                ".form-help { display: block; margin-top: 5px; font-size: 12px; color: var(--text-muted); font-style: italic; }\n"
                                +
                                ".form-input[readonly] { background: var(--bg-primary); color: var(--text-muted); cursor: not-allowed; }\n"
                                +
                                ".file-upload-section { margin-top: 15px; padding: 15px; background: #f8fafc; border: 1px dashed #cbd5e1; border-radius: 8px; }\n"
                                +
                                ".file-upload-section label { display: block; margin-bottom: 8px; color: #374151; font-weight: 500; }\n"
                                +
                                ".file-input { width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; background: var(--bg-card); margin-bottom: 10px; color: var(--text-primary); }\n"
                                +
                                ".file-input:focus { border-color: #3b82f6; outline: none; box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1); }\n"
                                +
                                ".form-range { width: 100%; height: 6px; border-radius: 3px; background: #e2e8f0; outline: none; margin: 10px 0; }\n"
                                +
                                ".form-range::-webkit-slider-thumb { appearance: none; width: 20px; height: 20px; border-radius: 50%; background: #667eea; cursor: pointer; }\n"
                                +
                                ".form-range::-moz-range-thumb { width: 20px; height: 20px; border-radius: 50%; background: #667eea; cursor: pointer; border: none; }\n"
                                +
                                ".volume-display { display: flex; justify-content: space-between; align-items: center; margin-top: 10px; }\n"
                                +
                                ".button-group { display: flex; gap: 10px; flex-wrap: wrap; }\n" +
                                ".button-group.vertical { flex-direction: column; }\n" +
                                ".stat-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 0; border-bottom: 1px solid var(--border-color); }\n"
                                +
                                ".stat-label { color: var(--text-secondary); }\n" +
                                ".stat-value { font-weight: bold; color: var(--text-primary); }\n" +
                                ".card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }\n"
                                +
                                ".loading { text-align: center; color: var(--text-secondary); padding: 20px; }\n" +
                                ".info-list { }\n" +
                                ".info-item { display: flex; justify-content: space-between; padding: 10px 0; border-bottom: 1px solid var(--border-color); }\n"
                                +
                                ".info-label { color: var(--text-secondary); }\n" +
                                ".info-value { font-weight: bold; color: var(--text-primary); }\n" +
                                ".result-area { position: fixed; top: 20px; right: 20px; background: var(--bg-card); padding: 20px; border-radius: 8px; box-shadow: 0 4px 12px var(--shadow-medium); max-width: 400px; z-index: 1000; border: 1px solid var(--border-color); color: var(--text-primary); }\n"
                                +
                                ".success { color: #27ae60; }\n" +
                                ".error { color: #e74c3c; }\n" +
                                ".info { color: #3498db; }\n" +
                                ".modal { position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); }\n"
                                +
                                ".modal-content { background-color: var(--bg-card); margin: 2% auto; padding: 0; border-radius: 12px; width: 80%; max-width: 600px; max-height: 90vh; box-shadow: 0 4px 20px var(--shadow-medium); display: flex; flex-direction: column; border: 1px solid var(--border-color); }\n"
                                +
                                ".modal-header { padding: 20px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; }\n"
                                +
                                ".modal-header h3 { margin: 0; color: var(--text-primary); }\n" +
                                ".close { color: var(--text-muted); font-size: 28px; font-weight: bold; cursor: pointer; }\n"
                                +
                                ".close:hover { color: var(--text-primary); }\n" +
                                ".modal-body { padding: 20px; flex: 1; overflow-y: auto; background: var(--bg-card); scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".modal-body::-webkit-scrollbar { display: none; }\n" +
                                ".form-actions { text-align: right; padding: 15px 20px; border-top: 1px solid var(--border-color); background: var(--bg-secondary); border-radius: 0 0 12px 12px; flex-shrink: 0; }\n"
                                +
                                ".form-actions button { margin-left: 10px; }\n" +
                                ".reward-list { max-height: 500px; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".reward-list::-webkit-scrollbar { display: none; }\n" +
                                ".reward-item { background: var(--bg-secondary); padding: 20px; margin: 15px 0; border-radius: 8px; border-left: 4px solid var(--accent-color); border: 1px solid var(--border-color); }\n"
                                +
                                ".reward-item h4 { margin: 0 0 10px 0; color: var(--text-primary); }\n" +
                                ".reward-item p { margin: 5px 0; color: var(--text-secondary); }\n" +
                                ".reward-actions { margin-top: 15px; }\n" +
                                ".reward-actions button { margin-right: 10px; }\n" +
                                "textarea.form-input { resize: vertical; min-height: 120px; }\n" +
                                "small { color: var(--text-secondary); font-size: 12px; }\n" +
                                ".input-with-icon { display: flex; align-items: center; gap: 10px; }\n" +
                                ".input-with-icon .form-input { flex: 1; }\n" +
                                ".icon-btn { padding: 10px 15px; background: #667eea; color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 16px; transition: background 0.3s; }\n"
                                +
                                ".icon-btn:hover { background: #5a6fd8; }\n" +
                                ".icon-modal { max-width: 700px; }\n" +
                                ".icon-categories { display: flex; gap: 10px; margin-bottom: 20px; flex-wrap: wrap; }\n"
                                +
                                ".category-btn { padding: 8px 16px; background: var(--bg-secondary); border: 1px solid var(--border-color); border-radius: 6px; cursor: pointer; transition: all 0.3s; color: var(--text-primary); }\n"
                                +
                                ".category-btn:hover { background: var(--bg-card); }\n" +
                                ".category-btn.active { background: #667eea; color: white; border-color: #667eea; }\n" +
                                ".icon-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(50px, 1fr)); gap: 10px; max-height: 300px; overflow-y: auto; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".icon-grid::-webkit-scrollbar { display: none; }\n"
                                +
                                ".icon-item { display: flex; align-items: center; justify-content: center; width: 50px; height: 50px; background: var(--bg-card); border-radius: 6px; cursor: pointer; font-size: 24px; transition: all 0.3s; border: 2px solid transparent; }\n"
                                +
                                ".icon-item:hover { transform: scale(1.1); border-color: #667eea; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }\n"
                                +
                                ".icon-help { margin-top: 15px; text-align: center; }\n" +
                                ".icon-help p { color: var(--text-secondary); font-size: 14px; margin: 0; }\n" +
                                ".toolbar { background: var(--bg-card); padding: 20px; border-radius: 12px; box-shadow: 0 4px 6px var(--shadow-light); margin-bottom: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px; border: 1px solid var(--border-color); }\n"
                                +
                                ".toolbar-left, .toolbar-right { display: flex; align-items: center; gap: 15px; flex-wrap: wrap; }\n"
                                +
                                ".form-group.inline { display: flex; align-items: center; gap: 10px; margin: 0; }\n" +
                                ".form-group.inline label { margin: 0; white-space: nowrap; }\n" +
                                ".form-input.small { width: 140px; min-width: 120px; }\n" +
                                ".key-count { color: var(--text-secondary); font-size: 14px; }\n" +
                                ".search-box { min-width: 200px; }\n" +
                                ".keys-container { max-height: 600px; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".keys-container::-webkit-scrollbar { display: none; }\n" +
                                ".loading-spinner { text-align: center; padding: 40px; color: var(--text-secondary); }\n"
                                +
                                ".key-item { background: var(--bg-secondary); padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid var(--accent-color); display: flex; justify-content: space-between; align-items: center; transition: all 0.3s; border: 1px solid var(--border-color); }\n"
                                +
                                ".key-item:hover { background: var(--bg-card); transform: translateX(2px); }\n" +
                                ".key-item.selected { background: var(--bg-card); border-left-color: var(--accent-color); box-shadow: 0 2px 8px var(--shadow-light); }\n"
                                +
                                ".key-info { flex: 1; }\n" +
                                ".key-code { font-family: monospace; font-size: 16px; font-weight: bold; color: var(--text-primary); margin-bottom: 5px; }\n"
                                +
                                ".key-meta { font-size: 12px; color: var(--text-secondary); }\n" +
                                ".key-actions { display: flex; gap: 10px; align-items: center; }\n" +
                                ".key-checkbox { margin-right: 10px; transform: scale(1.2); }\n" +
                                ".btn.small { padding: 5px 10px; font-size: 12px; }\n" +
                                ".no-keys { text-align: center; padding: 40px; color: var(--text-secondary); }\n" +
                                "/* 卡密状态指示器样式 */\n" +
                                ".status-warning { background: linear-gradient(135deg, #ffc107, #ff8f00); color: white; }\n"
                                +
                                ".status-disabled { background: linear-gradient(135deg, #6c757d, #495057); color: white; }\n"
                                +
                                ".sort-controls, .page-size-controls { display: flex; align-items: center; gap: 5px; }\n"
                                +
                                ".sort-controls label, .page-size-controls label { font-size: 12px; color: var(--text-secondary); }\n"
                                +
                                ".pagination-container { margin-top: 20px; padding: 15px; background: var(--bg-secondary); border-radius: 8px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 10px; border: 1px solid var(--border-color); }\n"
                                +
                                ".pagination-info { color: var(--text-secondary); font-size: 14px; }\n" +
                                ".pagination-controls { display: flex; align-items: center; gap: 5px; }\n" +
                                ".page-numbers { display: flex; gap: 2px; margin: 0 10px; }\n" +
                                ".page-number { padding: 5px 10px; background: var(--bg-card); border: 1px solid var(--border-color); border-radius: 4px; cursor: pointer; font-size: 12px; transition: all 0.3s; color: var(--text-primary); }\n"
                                +
                                ".page-number:hover { background: var(--bg-secondary); }\n" +
                                ".page-number.active { background: #667eea; color: white; border-color: #667eea; }\n" +
                                ".page-number.disabled { background: var(--bg-secondary); color: var(--text-disabled, #ccc); cursor: not-allowed; }\n"
                                +
                                ".winner-count { color: var(--text-secondary); font-size: 14px; }\n" +
                                ".winners-container { max-height: 600px; overflow-y: auto; scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".winners-container::-webkit-scrollbar { display: none; }\n" +
                                ".winner-item { background: var(--bg-secondary); padding: 15px; margin: 10px 0; border-radius: 8px; border-left: 4px solid #28a745; display: flex; justify-content: space-between; align-items: center; transition: all 0.3s; border: 1px solid var(--border-color); }\n"
                                +
                                ".winner-item:hover { background: var(--bg-card); transform: translateX(2px); }\n" +
                                ".winner-info { flex: 1; }\n" +
                                ".winner-username { font-size: 16px; font-weight: bold; color: var(--text-primary); margin-bottom: 5px; }\n"
                                +
                                ".winner-prize { font-size: 14px; color: #e74c3c; font-weight: bold; margin-bottom: 5px; }\n"
                                +
                                ".winner-time { font-size: 12px; color: var(--text-secondary); }\n" +
                                ".no-winners { text-align: center; padding: 40px; color: var(--text-secondary); }\n" +
                                ".log-controls { display: flex; flex-direction: column; gap: 15px; }\n" +
                                ".log-controls-row { display: flex; gap: 15px; align-items: center; flex-wrap: wrap; }\n"
                                +
                                ".control-group { display: flex; align-items: center; gap: 8px; min-width: fit-content; }\n"
                                +
                                ".control-group label { font-size: 12px; color: var(--text-secondary); white-space: nowrap; min-width: fit-content; }\n"
                                +
                                ".search-group { flex: 1; min-width: 350px; }\n" +
                                ".log-container { max-height: 500px; overflow-y: auto; background: #1e1e1e; color: #f8f8f2; font-family: 'Consolas', 'Monaco', monospace; font-size: 13px; padding: 15px; border-radius: 6px; position: relative; }\n"
                                +
                                ".log-line { margin: 2px 0; padding: 2px 0; border-bottom: 1px solid rgba(255,255,255,0.1); }\n"
                                +
                                ".log-line:last-child { border-bottom: none; }\n" +
                                ".log-timestamp { color: #6272a4; }\n" +
                                ".log-level { font-weight: bold; margin: 0 8px; }\n" +
                                ".log-level.INFO { color: #50fa7b; }\n" +
                                ".log-level.WARNING { color: #ffb86c; }\n" +
                                ".log-level.SEVERE { color: #ff5555; }\n" +
                                ".log-message { color: #f8f8f2; }\n" +
                                ".log-line.hidden { display: none; }\n" +
                                ".log-line.highlight { background: rgba(255, 255, 0, 0.2); }\n" +
                                ".search-highlight { background: yellow; color: black; padding: 1px 2px; border-radius: 2px; }\n"
                                +
                                ".log-stats { position: absolute; top: 10px; right: 15px; background: rgba(0,0,0,0.7); color: #f8f8f2; padding: 5px 10px; border-radius: 4px; font-size: 11px; }\n"
                                +
                                ".no-logs { text-align: center; padding: 40px; color: #6272a4; }\n" +
                                ".status-nav { display: flex; gap: 15px; flex-wrap: wrap; padding: 15px 0; }\n" +
                                ".status-btn { padding: 12px 20px; background: var(--bg-secondary); border: 2px solid var(--border-color); border-radius: 8px; cursor: pointer; transition: all 0.3s; font-size: 14px; font-weight: 500; display: flex; align-items: center; gap: 8px; color: var(--text-primary); }\n"
                                +
                                ".status-btn:hover { background: var(--bg-card); border-color: #667eea; transform: translateY(-1px); }\n"
                                +
                                ".status-btn.active { background: #667eea; color: white; border-color: #667eea; box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); }\n"
                                +
                                ".status-btn span { background: rgba(255,255,255,0.2); padding: 2px 8px; border-radius: 12px; font-size: 12px; font-weight: bold; }\n"
                                +
                                ".status-btn.active span { background: rgba(255,255,255,0.3); }\n" +
                                ".key-status { display: inline-block; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold; margin-left: 8px; }\n"
                                +
                                ".key-status.available { background: #d4edda; color: #155724; }\n" +
                                ".key-status.assigned { background: #fff3cd; color: #856404; }\n" +
                                ".key-status.used { background: #f8d7da; color: #721c24; }\n" +
                                ".key-status.expired { background: #e2e3e5; color: #495057; }\n" +
                                "\n" +
                                "/* 统计分析页面样式 */\n" +
                                ".statistics-nav { margin-bottom: 20px; }\n" +
                                ".statistics-nav .nav-tabs { display: flex; gap: 10px; flex-wrap: wrap; }\n" +
                                ".statistics-nav .nav-tab { padding: 12px 20px; background: var(--bg-secondary); border: 2px solid var(--border-color); border-radius: 8px; cursor: pointer; transition: all 0.3s; font-size: 14px; font-weight: 500; color: var(--text-primary); }\n"
                                +
                                ".statistics-nav .nav-tab:hover { background: var(--bg-card); border-color: var(--accent-color); transform: translateY(-1px); }\n"
                                +
                                ".statistics-nav .nav-tab.active { background: var(--accent-color); color: white; border-color: var(--accent-color); box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3); }\n"
                                +
                                ".statistics-tab-content { display: none; }\n" +
                                ".statistics-tab-content.active { display: block; }\n" +
                                ".stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 20px; }\n"
                                +
                                ".stat-card { background: var(--bg-card); padding: 20px; border-radius: 12px; box-shadow: 0 2px 10px var(--shadow-light); border-left: 4px solid var(--accent-color); transition: transform 0.3s; border: 1px solid var(--border-color); }\n"
                                +
                                ".stat-card:hover { transform: translateY(-2px); }\n" +
                                ".stat-icon { font-size: 24px; margin-bottom: 10px; }\n" +
                                ".stat-number { font-size: 28px; font-weight: bold; color: var(--text-primary); margin-bottom: 5px; }\n"
                                +
                                ".stat-label { font-size: 14px; color: var(--text-secondary); }\n" +
                                ".chart-container { padding: 20px; background: var(--bg-card); border-radius: 8px; border: 1px solid var(--border-color); }\n"
                                +
                                ".top-players-list { display: flex; flex-direction: column; gap: 10px; }\n" +
                                ".player-rank-item { display: flex; align-items: center; justify-content: space-between; padding: 15px; background: var(--bg-card); border-radius: 8px; box-shadow: 0 2px 5px var(--shadow-light); border: 1px solid var(--border-color); }\n"
                                +
                                ".player-rank-item .rank { font-size: 18px; font-weight: bold; min-width: 40px; }\n" +
                                ".player-rank-item .player-name { flex: 1; font-weight: 500; margin-left: 15px; color: var(--text-primary); }\n"
                                +
                                ".player-rank-item .usage-count { color: var(--accent-color); font-weight: bold; margin-right: 15px; }\n"
                                +
                                ".search-form { margin-bottom: 20px; }\n" +
                                ".search-form .form-group.inline { display: flex; gap: 10px; align-items: center; }\n" +
                                ".search-form .form-group.inline label { min-width: 80px; font-weight: 500; }\n" +
                                ".search-form .form-input { flex: 1; padding: 10px; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-card); color: var(--text-primary); }\n"
                                +
                                ".search-tips { margin-top: 10px; padding: 10px; background: var(--bg-secondary); border-radius: 6px; color: var(--text-secondary); font-size: 14px; border: 1px solid var(--border-color); }\n"
                                +
                                ".player-info-header { margin-bottom: 20px; padding: 20px; background: var(--bg-card); color: var(--text-primary); border-radius: 12px; border: 1px solid var(--border-color); }\n"
                                +
                                ".player-info-header h4 { margin: 0 0 15px 0; font-size: 20px; }\n" +
                                ".player-stats { display: flex; gap: 20px; flex-wrap: wrap; }\n" +
                                ".player-stats .stat-item { background: var(--bg-secondary); padding: 8px 12px; border-radius: 6px; font-size: 14px; border: 1px solid var(--border-color); }\n"
                                +
                                ".key-section { margin-bottom: 30px; }\n" +
                                ".key-section h5 { margin-bottom: 15px; color: var(--text-primary); font-size: 16px; font-weight: 600; }\n"
                                +
                                ".key-list { display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 20px; max-height: 600px; overflow-y: auto; padding: 10px; }\n"
                                +
                                "/* 玩家卡密卡片特殊样式 */\n" +
                                ".key-card.assigned { border-left: 4px solid #ffc107; }\n" +
                                ".key-card.used { border-left: 4px solid #28a745; }\n" +
                                ".key-card.expired { border-left: 4px solid #dc3545; }\n" +
                                ".key-card .card-header { display: flex; flex-direction: column; align-items: center; justify-content: center; padding: 20px; }\n"
                                +
                                ".key-card .card-title { display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; gap: 8px; }\n"
                                +
                                ".key-card .card-title .card-icon { font-size: 24px; }\n" +
                                ".key-card .card-title .card-name { font-family: 'Courier New', monospace; font-weight: bold; font-size: 18px; color: white; text-align: center; margin: 0; }\n"
                                +
                                ".key-card .card-status { margin-top: 8px; padding: 6px 12px; border-radius: 15px; font-size: 12px; font-weight: 600; }\n"
                                +
                                ".key-card .card-status.status-warning { background: rgba(255,193,7,0.2); color: #856404; }\n"
                                +
                                ".key-card .card-status.status-danger { background: rgba(40,167,69,0.2); color: #155724; }\n"
                                +
                                ".key-card .card-status.status-secondary { background: rgba(220,53,69,0.2); color: #721c24; }\n"
                                +
                                ".key-card .card-content { background: var(--bg-card); margin: 0 16px; border-radius: 12px 12px 0 0; padding: 20px; text-align: left; border: 1px solid var(--border-color); border-bottom: none; }\n"
                                +
                                ".key-card .card-info { }\n" +
                                ".key-card .info-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; padding: 8px 0; }\n"
                                +
                                ".key-card .info-row:last-child { margin-bottom: 0; }\n" +
                                ".key-card .info-label { color: var(--text-secondary); font-size: 14px; font-weight: 600; min-width: 80px; }\n"
                                +
                                ".key-card .info-value { color: var(--text-primary); font-size: 14px; font-weight: 500; text-align: right; flex: 1; }\n"
                                +
                                ".key-card .card-footer { padding: 16px 20px 20px; text-align: center; background: var(--bg-card); margin: 0 16px 16px; border-radius: 0 0 12px 12px; border: 1px solid var(--border-color); border-top: none; }\n"
                                +
                                ".key-card .card-actions { display: flex; justify-content: center; }\n" +
                                ".key-card .action-btn.copy { background: rgba(76,175,80,0.2); color: #4caf50; padding: 12px 24px; border-radius: 10px; font-size: 14px; font-weight: 600; border: none; cursor: pointer; transition: all 0.3s ease; display: inline-flex; align-items: center; justify-content: center; gap: 6px; white-space: nowrap; min-width: 120px; text-decoration: none; }\n"
                                +
                                ".key-card .action-btn.copy:hover { background: rgba(76,175,80,0.3); transform: translateY(-2px); box-shadow: 0 4px 12px rgba(76,175,80,0.3); }\n"
                                +
                                ".key-header { display: flex; justify-content: between; align-items: center; margin-bottom: 10px; }\n"
                                +
                                ".key-code { font-family: 'Courier New', monospace; font-weight: bold; color: var(--text-primary); }\n"
                                +
                                ".key-details { font-size: 14px; color: var(--text-secondary); line-height: 1.5; }\n" +
                                ".never-expire { color: #28a745; font-weight: bold; }\n" +
                                ".system-info-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; }\n"
                                +
                                ".info-item { display: flex; justify-content: space-between; padding: 15px; background: var(--bg-secondary); border-radius: 8px; border: 1px solid var(--border-color); }\n"
                                +
                                ".info-label { font-weight: 500; color: var(--text-secondary); }\n" +
                                ".info-value { font-weight: bold; color: var(--text-primary); }\n" +
                                ".no-results { text-align: center; padding: 40px; color: var(--text-secondary); }\n" +
                                ".no-results h4 { margin-bottom: 10px; color: var(--text-primary); }\n" +
                                ".error-message { text-align: center; padding: 20px; color: #dc3545; background: var(--bg-secondary); border-radius: 6px; border: 1px solid var(--border-color); }\n"
                                +
                                "\n" +
                                "/* 玩家卡密标签页样式 */\n" +
                                ".player-keys-nav { margin: 20px 0; }\n" +
                                ".player-keys-nav .nav-tabs { display: flex; gap: 8px; flex-wrap: wrap; }\n" +
                                ".player-keys-nav .nav-tab { padding: 10px 16px; background: var(--bg-secondary); border: 2px solid var(--border-color); border-radius: 6px; cursor: pointer; transition: all 0.3s; font-size: 13px; font-weight: 500; color: var(--text-primary); }\n"
                                +
                                ".player-keys-nav .nav-tab:hover { background: var(--bg-card); border-color: #28a745; transform: translateY(-1px); }\n"
                                +
                                ".player-keys-nav .nav-tab.active { background: #28a745; color: white; border-color: #28a745; box-shadow: 0 2px 6px rgba(40, 167, 69, 0.3); }\n"
                                +
                                ".player-keys-tab-content { display: none; }\n" +
                                ".player-keys-tab-content.active { display: block; }\n" +
                                ".no-keys { text-align: center; padding: 30px; color: var(--text-secondary); background: var(--bg-secondary); border-radius: 8px; font-style: italic; border: 1px solid var(--border-color); }\n"
                                +
                                "\n" +
                                "/* 文件上传区域样式 */\n" +
                                ".file-upload-section { margin-top: 15px; padding: 20px; background: var(--bg-card); border: 2px dashed var(--border-color); border-radius: 12px; transition: all 0.3s; }\n"
                                +
                                ".file-upload-section:hover { border-color: var(--accent-color); background: var(--bg-secondary); }\n"
                                +
                                ".file-upload-section label { display: block; margin-bottom: 10px; font-weight: 500; color: var(--text-primary); }\n"
                                +
                                ".file-input { width: 100%; padding: 10px; margin-bottom: 10px; border: 1px solid var(--border-color); border-radius: 6px; background: var(--bg-secondary); color: var(--text-primary); }\n"
                                +
                                ".file-input:focus { outline: none; border-color: var(--accent-color); box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2); }\n"
                                +
                                "\n" +
                                "/* 积分商店管理样式 - 现代卡片设计 */\n" +
                                ".select-all-container { padding: 0 10px; }\n" +
                                ".checkbox-label { display: flex; align-items: center; gap: 8px; font-size: 14px; color: var(--text-secondary); cursor: pointer; }\n"
                                +
                                ".checkbox-label input[type=\"checkbox\"] { width: 16px; height: 16px; }\n" +
                                ".shop-cards-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 20px; max-height: 600px; overflow-y: auto; padding: 10px; scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".shop-cards-container::-webkit-scrollbar { display: none; }\n" +
                                ".shop-card { background: var(--bg-card); border-radius: 16px; padding: 0; box-shadow: 0 8px 32px var(--shadow-light); transition: all 0.3s ease; position: relative; overflow: hidden; border: 1px solid var(--border-color); }\n"
                                +
                                ".shop-card:hover { transform: translateY(-4px); box-shadow: 0 12px 40px var(--shadow-medium); }\n"
                                +
                                ".shop-card.selected { box-shadow: 0 0 0 3px var(--accent-color), 0 12px 40px var(--shadow-medium); }\n"
                                +
                                ".shop-card.disabled { background: var(--bg-secondary); opacity: 0.7; }\n"
                                +
                                ".shop-card.disabled:hover { transform: none; }\n" +
                                ".card-header { display: flex; justify-content: space-between; align-items: center; padding: 16px 20px 0; }\n"
                                +
                                ".card-checkbox { width: 18px; height: 18px; accent-color: var(--accent-color); }\n" +
                                ".card-actions { display: flex; gap: 8px; }\n" +
                                ".action-btn { background: var(--bg-secondary); border: 1px solid var(--border-color); border-radius: 8px; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s; font-size: 14px; color: var(--text-primary); }\n"
                                +
                                ".action-btn:hover { background: var(--accent-color); color: white; transform: scale(1.1); }\n"
                                +
                                ".action-btn.enable { background: #4caf50; color: white; border: 1px solid #4caf50; }\n"
                                +
                                ".action-btn.enable:hover { background: #45a049; color: white; border: 1px solid #45a049; }\n"
                                +
                                ".action-btn.disable { color: #f44336; }\n" +
                                ".action-btn.edit { color: #2196f3; }\n" +
                                ".action-btn.delete { color: #ff9800; }\n" +
                                ".card-content { padding: 20px; text-align: center; }\n" +
                                ".item-icon { font-size: 48px; margin-bottom: 12px; display: block; }\n" +
                                ".item-info { color: var(--text-primary); }\n" +
                                ".item-name { font-size: 20px; font-weight: 600; margin: 0 0 8px 0; color: var(--text-primary); }\n"
                                +
                                ".item-cost { font-size: 16px; color: var(--text-secondary); font-weight: 500; }\n" +
                                ".card-body { background: var(--bg-secondary); margin: 0 16px 16px; border-radius: 12px; padding: 16px; border: 1px solid var(--border-color); }\n"
                                +
                                ".item-description { color: var(--text-secondary); font-size: 14px; margin-bottom: 12px; line-height: 1.4; text-align: center; }\n"
                                +
                                ".item-details { }\n" +
                                ".detail-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }\n"
                                +
                                ".detail-row:last-child { margin-bottom: 0; }\n" +
                                ".detail-label { color: var(--text-muted); font-size: 13px; }\n" +
                                ".detail-value { color: var(--text-primary); font-size: 13px; font-weight: 500; }\n" +
                                ".card-footer { padding: 0 20px 20px; text-align: center; }\n" +
                                ".status-indicator { display: inline-block; padding: 6px 16px; border-radius: 20px; font-size: 12px; font-weight: 600; }\n"
                                +
                                ".status-indicator.status-enabled { background: #4caf50; color: white; }\n"
                                +
                                ".status-indicator.status-disabled { background: #f44336; color: white; }\n"
                                +
                                ".no-items { text-align: center; padding: 40px; color: var(--text-secondary); background: var(--bg-secondary); border-radius: 8px; font-style: italic; grid-column: 1 / -1; border: 1px solid var(--border-color); }\n"
                                +
                                ".item-count { color: var(--text-secondary); font-size: 14px; }\n" +
                                "\n" +
                                "/* 自动更新状态指示器 */\n" +
                                ".auto-update-status { margin: 0 15px; }\n" +
                                ".status-indicator { padding: 4px 12px; border-radius: 15px; font-size: 12px; font-weight: 600; transition: all 0.3s ease; }\n"
                                +
                                ".status-indicator.active { background: linear-gradient(45deg, #4CAF50, #45a049); color: white; animation: pulse 2s infinite; }\n"
                                +
                                ".status-indicator.inactive { background: #f44336; color: white; }\n" +
                                "@keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }\n" +
                                "\n" +
                                "/* 仪表板专用样式 */\n" +
                                ".dashboard-stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 25px; margin-bottom: 40px; }\n"
                                +
                                ".dashboard-stat-card { background: var(--bg-card); border-radius: 16px; padding: 24px; box-shadow: 0 8px 25px var(--shadow-light); transition: all 0.3s ease; border: 1px solid var(--border-color); position: relative; overflow: hidden; }\n"
                                +
                                ".dashboard-stat-card:hover { transform: translateY(-4px); box-shadow: 0 12px 35px var(--shadow-medium); }\n"
                                +
                                ".dashboard-stat-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 4px; background: linear-gradient(90deg, #667eea, #764ba2); }\n"
                                +
                                ".stat-header { display: flex; align-items: center; margin-bottom: 16px; }\n" +
                                ".stat-icon { width: 48px; height: 48px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 24px; margin-right: 16px; }\n"
                                +
                                ".stat-icon.total { background: linear-gradient(135deg, #667eea, #764ba2); }\n" +
                                ".stat-icon.available { background: linear-gradient(135deg, #4CAF50, #45a049); }\n" +
                                ".stat-icon.used { background: linear-gradient(135deg, #f44336, #d32f2f); }\n" +
                                ".stat-icon.winners { background: linear-gradient(135deg, #ff9800, #f57c00); }\n" +
                                ".stat-icon.server { background: linear-gradient(135deg, #2196F3, #1976D2); }\n" +
                                ".stat-icon.version { background: linear-gradient(135deg, #9c27b0, #7b1fa2); }\n" +
                                ".stat-title { font-size: 16px; font-weight: 600; color: var(--text-primary); margin: 0; }\n"
                                +
                                ".stat-value { font-size: 36px; font-weight: 700; color: var(--text-primary); margin: 8px 0; line-height: 1; transition: all 0.3s ease; }\n"
                                +
                                ".stat-value.status { font-size: 24px; font-weight: 600; }\n" +
                                ".stat-value.version-text { font-size: 20px; font-family: 'Courier New', monospace; font-weight: 600; }\n"
                                +
                                ".stat-subtitle { font-size: 14px; color: var(--text-secondary); margin: 0; }\n" +
                                "\n" +
                                "/* 仪表板动画效果 */\n" +
                                "@keyframes dashboardPulse { 0%, 100% { transform: scale(1); } 50% { transform: scale(1.05); } }\n"
                                +
                                ".dashboard-stat-card.updated { animation: dashboardPulse 0.6s ease-in-out; }\n" +
                                ".stat-value.updating { transform: scale(1.1); transition: transform 0.2s ease; }\n" +
                                "\n" +
                                "/* 中奖记录卡片样式 */\n" +
                                ".winners-container.shop-cards-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(400px, 1fr)); gap: 25px; max-height: 600px; overflow-y: auto; padding: 20px; scrollbar-width: none; -ms-overflow-style: none; }\n"
                                +
                                ".winners-container.shop-cards-container::-webkit-scrollbar { display: none; }\n" +
                                ".winners-container .shop-card { width: 100%; min-height: 220px; }\n" +
                                ".winners-container .shop-card .card-header { display: flex; justify-content: center; align-items: center; padding: 20px; }\n"
                                +
                                ".winners-container .shop-card .card-title { display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; gap: 8px; }\n"
                                +
                                ".winners-container .shop-card .card-icon { font-size: 36px; }\n" +
                                ".winners-container .shop-card .card-name { font-size: 18px; font-weight: 700; color: var(--text-primary); }\n"
                                +
                                ".winners-container .shop-card .card-content { padding: 25px; text-align: left; }\n" +
                                ".winners-container .shop-card .card-info { }\n" +
                                ".winners-container .shop-card .info-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 8px 0; }\n"
                                +
                                ".winners-container .shop-card .info-row:last-child { margin-bottom: 0; }\n" +
                                ".winners-container .shop-card .info-label { color: var(--text-secondary); font-size: 16px; font-weight: 600; }\n"
                                +
                                ".winners-container .shop-card .info-value { color: var(--text-primary); font-size: 16px; font-weight: 700; }\n"
                                +
                                ".winners-container .shop-card .info-value.prize-name { color: #e74c3c; font-weight: bold; font-size: 18px; }\n"
                                +
                                "\n" +
                                "\n" +
                                "/* 装饰性确认对话框样式 */\n" +
                                ".confirm-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.85); backdrop-filter: blur(15px); display: flex; align-items: center; justify-content: center; z-index: 10000; opacity: 0; transition: all 0.4s ease; }\n"
                                +
                                ".confirm-modal.show { opacity: 1; }\n" +
                                ".confirm-modal-content { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 28px; padding: 0; box-shadow: 0 30px 100px rgba(0,0,0,0.5), 0 0 0 1px rgba(255,255,255,0.15), inset 0 1px 0 rgba(255,255,255,0.2); max-width: 580px; width: 92%; position: relative; overflow: hidden; transform: scale(0.85) translateY(40px); transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1); border: 3px solid rgba(255,255,255,0.12); }\n"
                                +
                                ".confirm-modal.show .confirm-modal-content { transform: scale(1) translateY(0); }\n" +
                                ".confirm-modal-content::before { content: ''; position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: linear-gradient(45deg, transparent, rgba(255,255,255,0.15), transparent); transform: rotate(45deg); animation: shimmer 4s infinite; pointer-events: none; }\n"
                                +
                                ".confirm-modal-content::after { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: radial-gradient(circle at 50% 0%, rgba(255,255,255,0.1) 0%, transparent 50%); pointer-events: none; }\n"
                                +
                                "@keyframes shimmer { 0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); } 50% { transform: translateX(100%) translateY(100%) rotate(45deg); } }\n"
                                +
                                ".confirm-modal-header { background: rgba(255,255,255,0.18); padding: 35px 40px; border-bottom: 1px solid rgba(255,255,255,0.25); position: relative; z-index: 1; }\n"
                                +
                                ".confirm-modal-title { color: white; font-size: 26px; font-weight: 700; margin: 0; text-align: center; text-shadow: 0 4px 8px rgba(0,0,0,0.5); letter-spacing: 0.5px; }\n"
                                +
                                ".confirm-modal-body { padding: 40px; color: rgba(255,255,255,0.96); text-align: center; font-size: 18px; line-height: 1.8; text-shadow: 0 2px 4px rgba(0,0,0,0.4); position: relative; z-index: 1; }\n"
                                +
                                ".confirm-modal-footer { padding: 30px 40px; display: flex; gap: 20px; justify-content: center; background: rgba(0,0,0,0.18); position: relative; z-index: 1; }\n"
                                +
                                ".confirm-btn { padding: 14px 35px; border: none; border-radius: 30px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); min-width: 120px; position: relative; overflow: hidden; }\n"
                                +
                                ".confirm-btn::before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.5s; }\n"
                                +
                                ".confirm-btn:hover::before { left: 100%; }\n"
                                +
                                ".confirm-btn:hover { transform: translateY(-3px); box-shadow: 0 12px 35px rgba(0,0,0,0.3); }\n"
                                +
                                ".confirm-btn.primary { background: linear-gradient(45deg, #ff6b6b, #ee5a24); color: white; box-shadow: 0 6px 20px rgba(238,90,36,0.4); }\n"
                                +
                                ".confirm-btn.primary:hover { box-shadow: 0 12px 35px rgba(238,90,36,0.6); }\n"
                                +
                                ".confirm-btn.secondary { background: rgba(255,255,255,0.15); color: white; border: 2px solid rgba(255,255,255,0.3); backdrop-filter: blur(10px); }\n"
                                +
                                ".confirm-btn.secondary:hover { background: rgba(255,255,255,0.25); border-color: rgba(255,255,255,0.5); }\n"
                                +
                                ".confirm-btn:active { transform: translateY(-1px); }\n" +
                                "/* 顶部工具栏和控件的夜间主题优化 */\n" +
                                "body.theme-dark .toolbar { background: #2d2d2d; border: 1px solid #404040; }\n" +
                                "body.theme-dark .toolbar select, body.theme-dark .toolbar input { background: #2d2d2d; color: #f7fafc; border: 1px solid #4a5568; }\n"
                                +
                                "body.theme-dark .toolbar select:focus, body.theme-dark .toolbar input:focus { background: #2d2d2d; border-color: #667eea; }\n"
                                +
                                "body.theme-dark .toolbar button { background: #4c51bf; color: #ffffff; }\n" +
                                "body.theme-dark .toolbar button:hover { background: #5a67d8; }\n" +
                                "body.theme-dark .toolbar .btn-secondary { background: #2d2d2d; }\n" +
                                "body.theme-dark .toolbar .btn-secondary:hover { background: #5a67d8; }\n" +
                                "/* 分页控件夜间主题 */\n" +
                                "body.theme-dark .pagination { background: #2d2d2d; border: 1px solid #404040; }\n" +
                                "body.theme-dark .pagination select, body.theme-dark .pagination input { background: #2d2d2d; color: #f7fafc; border: 1px solid #4a5568; }\n"
                                +
                                "body.theme-dark .pagination button { background: #4c51bf; color: #ffffff; }\n" +
                                "body.theme-dark .pagination button:hover { background: #5a67d8; }\n" +
                                "body.theme-dark .pagination .page-info { color: #cbd5e0; }\n" +
                                "/* 通用表单控件夜间主题 */\n" +
                                "body.theme-dark select, body.theme-dark input[type='text'], body.theme-dark input[type='number'] { background: #2d2d2d; color: #f7fafc; border: 1px solid #4a5568; }\n"
                                +
                                "body.theme-dark select:focus, body.theme-dark input:focus { background: #2d2d2d; border-color: #667eea; }\n"
                                +
                                "body.theme-dark .btn { background: #4c51bf; color: #ffffff; }\n" +
                                "body.theme-dark .btn:hover { background: #5a67d8; }\n" +
                                "body.theme-dark .btn-secondary { background: #2d2d2d; }\n" +
                                "body.theme-dark .btn-secondary:hover { background: #5a67d8; }\n" +
                                "body.theme-dark .btn-danger { background: #e53e3e; }\n" +
                                "body.theme-dark .btn-danger:hover { background: #c53030; }\n" +
                                "body.theme-dark .btn-success { background: #38a169; }\n" +
                                "body.theme-dark .btn-success:hover { background: #2f855a; }\n" +
                                "/* 弹窗中的输入框边框优化 */\n" +
                                "body.theme-dark .modal-body input[type='text'], body.theme-dark .modal-body input[type='number'], body.theme-dark .modal-body textarea, body.theme-dark .modal-body select { background: #2d2d2d; color: #f7fafc; border: 1px solid #4a5568; }\n"
                                +
                                "body.theme-dark .modal-body input:focus, body.theme-dark .modal-body textarea:focus, body.theme-dark .modal-body select:focus { background: #2d2d2d; border-color: #667eea; color: #ffffff; }\n"
                                +
                                "body.theme-dark .modal-body input::placeholder, body.theme-dark .modal-body textarea::placeholder { color: #a0aec0; }\n"
                                +
                                "/* 复选框夜间主题优化 */\n" +
                                "body.theme-dark .checkbox-label { color: #f7fafc; }\n" +
                                "body.theme-dark .checkbox-label input[type='checkbox'] { background: #2d2d2d; border: 1px solid #4a5568; }\n"
                                +
                                "body.theme-dark .card-checkbox { background: #2d2d2d; border: 1px solid #4a5568; }\n" +
                                "body.theme-dark .select-all-container { background: rgba(45, 45, 45, 0.8); border-radius: 6px; padding: 8px 12px; }\n"
                                +

                                // 签到管理页面样式
                                "/* 签到管理页面样式 */\n" +
                                ".daily-rewards-section {\n" +
                                "    margin: 20px 0;\n" +
                                "}\n" +
                                "\n" +
                                ".section-header {\n" +
                                "    text-align: center;\n" +
                                "    margin-bottom: 30px;\n" +
                                "}\n" +
                                "\n" +
                                ".section-header h2 {\n" +
                                "    color: var(--text-primary);\n" +
                                "    margin-bottom: 10px;\n" +
                                "    font-size: 28px;\n" +
                                "    font-weight: 700;\n" +
                                "}\n" +
                                "\n" +
                                ".section-header p {\n" +
                                "    color: var(--text-secondary);\n" +
                                "    font-size: 16px;\n" +
                                "    margin: 0;\n" +
                                "}\n" +
                                "\n" +
                                ".daily-cards-grid {\n" +
                                "    display: grid;\n" +
                                "    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n" +
                                "    gap: 20px;\n" +
                                "    padding: 20px 0;\n" +
                                "}\n" +
                                "\n" +
                                ".daily-reward-card {\n" +
                                "    background: var(--bg-card);\n" +
                                "    border-radius: 16px;\n" +
                                "    padding: 0;\n" +
                                "    box-shadow: 0 8px 32px var(--shadow-light);\n" +
                                "    transition: all 0.3s ease;\n" +
                                "    border: 2px solid transparent;\n" +
                                "    cursor: pointer;\n" +
                                "    position: relative;\n" +
                                "    overflow: hidden;\n" +
                                "}\n" +
                                "\n" +
                                ".daily-reward-card:hover {\n" +
                                "    transform: translateY(-5px);\n" +
                                "    box-shadow: 0 12px 40px var(--shadow-medium);\n" +
                                "    border-color: var(--accent-color);\n" +
                                "}\n" +
                                "\n" +
                                ".daily-reward-card .card-header {\n" +
                                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                                "    color: white;\n" +
                                "    padding: 20px;\n" +
                                "    text-align: center;\n" +
                                "    position: relative;\n" +
                                "}\n" +
                                "\n" +
                                ".daily-reward-card .card-header::before {\n" +
                                "    content: '';\n" +
                                "    position: absolute;\n" +
                                "    top: 0;\n" +
                                "    left: 0;\n" +
                                "    right: 0;\n" +
                                "    bottom: 0;\n" +
                                "    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n"
                                +
                                "    pointer-events: none;\n" +
                                "}\n" +
                                "\n" +
                                ".day-number {\n" +
                                "    font-size: 36px;\n" +
                                "    font-weight: bold;\n" +
                                "    margin-bottom: 5px;\n" +
                                "    position: relative;\n" +
                                "    z-index: 1;\n" +
                                "}\n" +
                                "\n" +
                                ".day-suffix {\n" +
                                "    font-size: 16px;\n" +
                                "    font-weight: 600;\n" +
                                "    opacity: 0.9;\n" +
                                "    position: relative;\n" +
                                "    z-index: 1;\n" +
                                "}\n" +
                                "\n" +
                                ".daily-reward-card .card-body {\n" +
                                "    padding: 20px;\n" +
                                "    background: var(--bg-card);\n" +
                                "}\n" +
                                "\n" +
                                ".reward-info {\n" +
                                "    text-align: center;\n" +
                                "}\n" +
                                "\n" +
                                ".points-reward {\n" +
                                "    display: flex;\n" +
                                "    align-items: center;\n" +
                                "    justify-content: center;\n" +
                                "    gap: 8px;\n" +
                                "    margin-bottom: 12px;\n" +
                                "    font-size: 16px;\n" +
                                "    font-weight: 600;\n" +
                                "    color: #ff6b35;\n" +
                                "}\n" +
                                "\n" +
                                ".points-value {\n" +
                                "    font-size: 20px;\n" +
                                "    font-weight: bold;\n" +
                                "}\n" +
                                "\n" +
                                ".items-reward {\n" +
                                "    display: flex;\n" +
                                "    align-items: center;\n" +
                                "    justify-content: center;\n" +
                                "    gap: 8px;\n" +
                                "    font-size: 14px;\n" +
                                "    color: var(--text-secondary);\n" +
                                "}\n" +
                                "\n" +
                                ".items-text {\n" +
                                "    font-style: italic;\n" +
                                "}\n" +
                                "\n" +
                                ".daily-reward-card .card-actions {\n" +
                                "    padding: 16px 20px;\n" +
                                "    background: var(--bg-secondary);\n" +
                                "    border-top: 1px solid var(--border-color);\n" +
                                "    text-align: center;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-edit {\n" +
                                "    background: linear-gradient(135deg, #4CAF50, #45a049);\n" +
                                "    color: white;\n" +
                                "    border: none;\n" +
                                "    padding: 10px 20px;\n" +
                                "    border-radius: 8px;\n" +
                                "    font-size: 14px;\n" +
                                "    font-weight: 600;\n" +
                                "    cursor: pointer;\n" +
                                "    transition: all 0.3s ease;\n" +
                                "    display: inline-flex;\n" +
                                "    align-items: center;\n" +
                                "    gap: 6px;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-edit:hover {\n" +
                                "    background: linear-gradient(135deg, #45a049, #3d8b40);\n" +
                                "    transform: translateY(-2px);\n" +
                                "    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题适配 */\n" +
                                "body.theme-dark .daily-reward-card {\n" +
                                "    background: #2d3748;\n" +
                                "    border-color: #4a5568;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .daily-reward-card .card-body {\n" +
                                "    background: #2d3748;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .daily-reward-card .card-actions {\n" +
                                "    background: #4a5568;\n" +
                                "    border-top-color: #718096;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .points-reward {\n" +
                                "    color: #ff8a65;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .items-reward {\n" +
                                "    color: #cbd5e0;\n" +
                                "}\n" +
                                "\n" +

                                // 补签卡商店设置页面样式
                                "/* 补签卡商店设置页面样式 */\n" +
                                ".settings-grid {\n" +
                                "    display: grid;\n" +
                                "    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\n" +
                                "    gap: 20px;\n" +
                                "    margin: 20px 0;\n" +
                                "}\n" +
                                "\n" +
                                ".setting-card {\n" +
                                "    background: var(--bg-card);\n" +
                                "    border-radius: 16px;\n" +
                                "    padding: 0;\n" +
                                "    box-shadow: 0 8px 32px var(--shadow-light);\n" +
                                "    transition: all 0.3s ease;\n" +
                                "    border: 2px solid transparent;\n" +
                                "    overflow: hidden;\n" +
                                "}\n" +
                                "\n" +
                                ".setting-card:hover {\n" +
                                "    transform: translateY(-5px);\n" +
                                "    box-shadow: 0 12px 40px var(--shadow-medium);\n" +
                                "    border-color: var(--accent-color);\n" +
                                "}\n" +
                                "\n" +
                                ".setting-card .card-header {\n" +
                                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                                "    color: white;\n" +
                                "    padding: 20px;\n" +
                                "    display: flex;\n" +
                                "    align-items: center;\n" +
                                "    gap: 12px;\n" +
                                "}\n" +
                                "\n" +
                                ".setting-card .card-header i {\n" +
                                "    font-size: 24px;\n" +
                                "}\n" +
                                "\n" +
                                ".setting-card .card-header h3 {\n" +
                                "    margin: 0;\n" +
                                "    font-size: 18px;\n" +
                                "    font-weight: 600;\n" +
                                "}\n" +
                                "\n" +
                                ".setting-card .card-content {\n" +
                                "    padding: 20px;\n" +
                                "}\n" +
                                "\n" +
                                ".form-group {\n" +
                                "    margin-bottom: 20px;\n" +
                                "}\n" +
                                "\n" +
                                ".form-group label {\n" +
                                "    display: block;\n" +
                                "    margin-bottom: 8px;\n" +
                                "    font-weight: 600;\n" +
                                "    color: var(--text-primary);\n" +
                                "    font-size: 14px;\n" +
                                "}\n" +
                                "\n" +
                                ".form-input {\n" +
                                "    width: 100%;\n" +
                                "    padding: 12px;\n" +
                                "    border: 2px solid var(--border-color);\n" +
                                "    border-radius: 8px;\n" +
                                "    background: var(--bg-secondary);\n" +
                                "    color: var(--text-primary);\n" +
                                "    font-size: 14px;\n" +
                                "    transition: border-color 0.3s;\n" +
                                "    box-sizing: border-box;\n" +
                                "}\n" +
                                "\n" +
                                ".form-input:focus {\n" +
                                "    outline: none;\n" +
                                "    border-color: var(--accent-color);\n" +
                                "}\n" +
                                "\n" +
                                ".checkbox-label {\n" +
                                "    display: flex;\n" +
                                "    align-items: center;\n" +
                                "    gap: 10px;\n" +
                                "    cursor: pointer;\n" +
                                "    font-weight: 500;\n" +
                                "}\n" +
                                "\n" +
                                ".checkbox-label input[type=\"checkbox\"] {\n" +
                                "    width: 18px;\n" +
                                "    height: 18px;\n" +
                                "    accent-color: var(--accent-color);\n" +
                                "}\n" +
                                "\n" +
                                ".action-buttons {\n" +
                                "    display: flex;\n" +
                                "    gap: 15px;\n" +
                                "    justify-content: center;\n" +
                                "    margin-top: 30px;\n" +
                                "}\n" +
                                "\n" +
                                ".btn {\n" +
                                "    padding: 12px 24px;\n" +
                                "    border: none;\n" +
                                "    border-radius: 8px;\n" +
                                "    font-size: 14px;\n" +
                                "    font-weight: 600;\n" +
                                "    cursor: pointer;\n" +
                                "    transition: all 0.3s ease;\n" +
                                "    display: inline-flex;\n" +
                                "    align-items: center;\n" +
                                "    gap: 8px;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-primary {\n" +
                                "    background: linear-gradient(135deg, #4CAF50, #45a049);\n" +
                                "    color: white;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-primary:hover {\n" +
                                "    background: linear-gradient(135deg, #45a049, #3d8b40);\n" +
                                "    transform: translateY(-2px);\n" +
                                "    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\n" +
                                "}\n" +
                                "\n" +
                                ".btn-secondary {\n" +
                                "    background: var(--bg-card);\n" +
                                "    color: var(--text-primary);\n" +
                                "    border: 2px solid var(--border-color);\n" +
                                "}\n" +
                                "\n" +
                                ".btn-secondary:hover {\n" +
                                "    background: var(--bg-secondary);\n" +
                                "    transform: translateY(-2px);\n" +
                                "}\n" +
                                "\n" +

                                // 添加各模块CSS
                                new AccountManagementCSSGenerator().generateAccountManagementCSS() +
                                new DashboardJSGenerator(webServer.getAdminKey()).generateDashboardCSS() +
                                new CommonJSGenerator().generateConfirmModalCSS();
        }

        /**
         * 获取管理员界面JavaScript
         */
        private String getAdminJS() {
                return
                // 通用工具函数
                new CommonJSGenerator().generateCommonJS() +

                // 仪表板功能
                                new DashboardJSGenerator(webServer.getAdminKey()).generateDashboardJS() +

                                // 账号管理功能
                                new AccountManagementJSGenerator(webServer.getAdminKey()).generateAccountManagementJS()
                                +

                                // 卡密管理功能
                                "\n// ==================== 卡密管理功能 ====================\n" +
                                "let allKeys = [];\n" +
                                "let filteredKeys = [];\n" +
                                "let selectedKeys = new Set();\n" +
                                "let currentPage = 1;\n" +
                                "let totalPages = 1;\n" +
                                "let totalKeys = 0;\n" +
                                "let currentStatusFilter = 'all';\n" +
                                "let keyStats = { all: 0, available: 0, assigned: 0, used: 0, expired: 0 };\n" +
                                "let lastDataHash = null;\n" +
                                "let autoUpdateInterval = null;\n" +
                                "let isManualRefresh = false;\n" +
                                "// 中奖记录自动检测变量\n" +
                                "let lastWinnersDataHash = null;\n" +
                                "let winnersAutoUpdateInterval = null;\n" +
                                "let isWinnersManualRefresh = false;\n" +
                                "// 统计分析自动检测变量\n" +
                                "let lastStatisticsDataHash = null;\n" +
                                "let statisticsAutoUpdateInterval = null;\n" +
                                "let isStatisticsManualRefresh = false;\n" +
                                "// 排行榜自动检测变量\n" +
                                "let lastLeaderboardDataHash = null;\n" +
                                "let leaderboardAutoUpdateInterval = null;\n" +
                                "let isLeaderboardManualRefresh = false;\n" +
                                "\n" +
                                "function generateKeys() {\n" +
                                "    const count = document.getElementById('keyCount').value;\n" +
                                "    if (!count || count < 1 || count > 1000) {\n" +
                                "        showResult('请输入1-1000之间的数量！', 'error');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    showResult('正在生成 ' + count + ' 个卡密...', 'info');\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'generate_keys',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            count: parseInt(count)\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showResult('✅ ' + data.message + '<br>生成的卡密数量: ' + data.generated_count, 'success');\n"
                                +
                                "            loadKeys();\n" +
                                "        } else {\n" +
                                "            showResult('❌ ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function loadKeys(page = 1, silent = false) {\n" +
                                "    currentPage = page;\n" +
                                "    const pageSize = document.getElementById('pageSize') ? document.getElementById('pageSize').value : 20;\n"
                                +
                                "    const sortOrder = document.getElementById('sortOrder') ? document.getElementById('sortOrder').value : 'desc';\n"
                                +
                                "    \n" +
                                "    if (!silent && !isManualRefresh) {\n" +
                                "        // 只在非静默模式下显示加载提示\n" +
                                "        // showResult('正在加载卡密...', 'info');\n" +
                                "    }\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_all_keys',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            page: currentPage,\n" +
                                "            page_size: parseInt(pageSize),\n" +
                                "            sort_order: sortOrder\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            // 计算数据哈希值用于检测变化\n" +
                                "            const dataHash = calculateDataHash(data);\n" +
                                "            \n" +
                                "            // 检查数据是否有变化\n" +
                                "            if (silent && lastDataHash && lastDataHash === dataHash) {\n" +
                                "                // 数据没有变化，不更新界面\n" +
                                "                return;\n" +
                                "            }\n" +
                                "            \n" +
                                "            // 数据有变化或首次加载，更新界面\n" +
                                "            lastDataHash = dataHash;\n" +
                                "            allKeys = data.keys || [];\n" +
                                "            totalKeys = data.total_keys || 0;\n" +
                                "            totalPages = data.total_pages || 1;\n" +
                                "            currentPage = data.current_page || 1;\n" +
                                "            keyStats = data.stats || { all: 0, available: 0, assigned: 0, used: 0 };\n"
                                +
                                "            \n" +
                                "            updateStatusCounts();\n" +
                                "            applyStatusFilter();\n" +
                                "            updateKeyCount(totalKeys);\n" +
                                "            updatePagination();\n" +
                                "            \n" +
                                "            // 如果是静默检测到变化，显示提示\n" +
                                "            if (silent && !isManualRefresh) {\n" +
                                "                showResult('🔄 检测到数据更新，已自动刷新', 'info');\n" +
                                "            }\n" +
                                "        } else {\n" +
                                "            if (!silent) {\n" +
                                "                showResult('❌ 加载卡密失败: ' + data.message, 'error');\n" +
                                "            }\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        if (!silent) {\n" +
                                "            showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .finally(() => {\n" +
                                "        isManualRefresh = false;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function displayKeys(keys) {\n" +
                                "    const container = document.getElementById('keysContainer');\n" +
                                "    if (!container) return;\n" +
                                "    \n" +
                                "    if (keys.length === 0) {\n" +
                                "        container.innerHTML = '<div class=\"no-items\">暂无卡密，点击上方按钮生成卡密</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    let html = '';\n" +
                                "    keys.forEach((keyInfo, index) => {\n" +
                                "        const isSelected = selectedKeys.has(keyInfo.key);\n" +
                                "        const itemClasses = ['shop-card'];\n" +
                                "        if (isSelected) itemClasses.push('selected');\n" +
                                "        \n" +
                                "        // 确定状态\n" +
                                "        let status = 'available';\n" +
                                "        let statusText = '未分配';\n" +
                                "        let statusClass = 'status-enabled';\n" +
                                "        if (keyInfo.used) {\n" +
                                "            status = 'used';\n" +
                                "            statusText = '已使用';\n" +
                                "            statusClass = 'status-disabled';\n" +
                                "        } else if (keyInfo.expired) {\n" +
                                "            status = 'expired';\n" +
                                "            statusText = '已失效';\n" +
                                "            statusClass = 'status-disabled';\n" +
                                "        } else if (keyInfo.assigned) {\n" +
                                "            status = 'assigned';\n" +
                                "            statusText = '已分配';\n" +
                                "            statusClass = 'status-warning';\n" +
                                "        }\n" +
                                "        \n" +
                                "        html += '<div class=\"' + itemClasses.join(' ') + '\" data-key=\"' + keyInfo.key + '\">';\n"
                                +
                                "        \n" +
                                "        // 卡片头部\n" +
                                "        html += '  <div class=\"card-header\">';\n" +
                                "        html += '    <input type=\"checkbox\" class=\"card-checkbox\" value=\"' + keyInfo.key + '\" onchange=\"toggleKeySelection(\\'' + keyInfo.key + '\\')\"; ' + (isSelected ? 'checked' : '') + '>';\n"
                                +
                                "        html += '    <div class=\"card-actions\">';\n" +
                                "        html += '      <button class=\"action-btn copy\" onclick=\"copyKey(\\'' + keyInfo.key + '\\')\" title=\"复制卡密\">📋</button>';\n"
                                +
                                "        html += '      <button class=\"action-btn delete\" onclick=\"deleteKey(\\'' + keyInfo.key + '\\')\" title=\"删除卡密\">🗑️</button>';\n"
                                +
                                "        html += '    </div>';\n" +
                                "        html += '  </div>';\n" +
                                "        \n" +
                                "        // 卡片内容\n" +
                                "        html += '  <div class=\"card-content\">';\n" +
                                "        html += '    <div class=\"item-icon\">🔑</div>';\n" +
                                "        html += '    <div class=\"item-info\">';\n" +
                                "        html += '      <h3 class=\"item-name\">' + keyInfo.key + '</h3>';\n" +
                                "        html += '      <div class=\"item-cost\">' + (keyInfo.created_date || '未知') + '</div>';\n"
                                +
                                "        html += '    </div>';\n" +
                                "        html += '  </div>';\n" +
                                "        \n" +
                                "        // 卡片主体\n" +
                                "        html += '  <div class=\"card-body\">';\n" +
                                "        \n" +
                                "        // 消息描述\n" +
                                "        if (keyInfo.message) {\n" +
                                "            html += '    <div class=\"item-description\">' + keyInfo.message + '</div>';\n"
                                +
                                "        }\n" +
                                "        \n" +
                                "        html += '    <div class=\"item-details\">';\n" +
                                "        \n" +
                                "        // 显示分配信息\n" +
                                "        if (keyInfo.assigned_to) {\n" +
                                "            html += '      <div class=\"detail-row\">';\n" +
                                "            html += '        <span class=\"detail-label\">分配给:</span>';\n" +
                                "            html += '        <span class=\"detail-value\">' + keyInfo.assigned_to + '</span>';\n"
                                +
                                "            html += '      </div>';\n" +
                                "            if (keyInfo.assigned_time) {\n" +
                                "                const assignedDate = new Date(keyInfo.assigned_time).toLocaleString();\n"
                                +
                                "                html += '      <div class=\"detail-row\">';\n" +
                                "                html += '        <span class=\"detail-label\">分配时间:</span>';\n" +
                                "                html += '        <span class=\"detail-value\">' + assignedDate + '</span>';\n"
                                +
                                "                html += '      </div>';\n" +
                                "            }\n" +
                                "        }\n" +
                                "        \n" +
                                "        // 显示使用信息\n" +
                                "        if (keyInfo.used && keyInfo.used_by) {\n" +
                                "            html += '      <div class=\"detail-row\">';\n" +
                                "            html += '        <span class=\"detail-label\">使用者:</span>';\n" +
                                "            html += '        <span class=\"detail-value\">' + keyInfo.used_by + '</span>';\n"
                                +
                                "            html += '      </div>';\n" +
                                "            if (keyInfo.used_time) {\n" +
                                "                const usedDate = new Date(keyInfo.used_time).toLocaleString();\n" +
                                "                html += '      <div class=\"detail-row\">';\n" +
                                "                html += '        <span class=\"detail-label\">使用时间:</span>';\n" +
                                "                html += '        <span class=\"detail-value\">' + usedDate + '</span>';\n"
                                +
                                "                html += '      </div>';\n" +
                                "            }\n" +
                                "        }\n" +
                                "        \n" +
                                "        // 显示失效信息\n" +
                                "        if (keyInfo.expired && keyInfo.expired_time) {\n" +
                                "            const expiredDate = new Date(keyInfo.expired_time).toLocaleString();\n" +
                                "            html += '      <div class=\"detail-row\">';\n" +
                                "            html += '        <span class=\"detail-label\">失效时间:</span>';\n" +
                                "            html += '        <span class=\"detail-value\">' + expiredDate + '</span>';\n"
                                +
                                "            html += '      </div>';\n" +
                                "        }\n" +
                                "        \n" +
                                "        html += '    </div>';\n" +
                                "        html += '  </div>';\n" +
                                "        \n" +
                                "        // 卡片底部\n" +
                                "        html += '  <div class=\"card-footer\">';\n" +
                                "        html += '    <div class=\"status-indicator ' + statusClass + '\">' + statusText + '</div>';\n"
                                +
                                "        html += '  </div>';\n" +
                                "        \n" +
                                "        html += '</div>';\n" +
                                "    });\n" +
                                "    \n" +
                                "    container.innerHTML = html;\n" +
                                "    updateSelectionButtons();\n" +
                                "}\n" +
                                "\n" +
                                "function viewKeys() {\n" +
                                "    showResult('正在获取卡密列表...', 'info');\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_all_keys',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "'\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            let html = '<h3>📋 卡密列表 (共 ' + data.total_keys + ' 个)</h3>';\n" +
                                "            if (data.keys && data.keys.length > 0) {\n" +
                                "                html += '<div style=\"max-height: 300px; overflow-y: auto; border: 1px solid var(--border-color, #ddd); padding: 10px; border-radius: 4px; background: var(--bg-secondary, #f8f9fa);\">';\n"
                                +
                                "                data.keys.forEach((keyInfo, index) => {\n" +
                                "                    html += '<div style=\"margin: 5px 0; padding: 8px; background: var(--bg-card, white); border-radius: 3px; border-left: 3px solid #667eea; border: 1px solid var(--border-color, #e1e5e9);\">';\n"
                                +
                                "                    html += '<strong>' + (index + 1) + '.</strong> <code>' + keyInfo.key + '</code>';\n"
                                +
                                "                    if (keyInfo.message) {\n" +
                                "                        html += '<br><small style=\"color: var(--text-secondary, #666);\">消息: ' + keyInfo.message + '</small>';\n"
                                +
                                "                    }\n" +
                                "                    html += '</div>';\n" +
                                "                });\n" +
                                "                html += '</div>';\n" +
                                "            } else {\n" +
                                "                html += '<p style=\"color: var(--text-secondary, #666); text-align: center; padding: 20px;\">暂无卡密</p>';\n"
                                +
                                "            }\n" +
                                "            showResult(html, 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function toggleKeySelection(key) {\n" +
                                "    if (selectedKeys.has(key)) {\n" +
                                "        selectedKeys.delete(key);\n" +
                                "    } else {\n" +
                                "        selectedKeys.add(key);\n" +
                                "    }\n" +
                                "    updateSelectionButtons();\n" +
                                "    // 更新视觉状态\n" +
                                "    const keyItems = document.querySelectorAll('.shop-card');\n" +
                                "    keyItems.forEach(item => {\n" +
                                "        const checkbox = item.querySelector('.card-checkbox');\n" +
                                "        if (checkbox && checkbox.value === key) {\n" +
                                "            item.classList.toggle('selected', selectedKeys.has(key));\n" +
                                "            checkbox.checked = selectedKeys.has(key);\n" +
                                "        }\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function selectAllKeys() {\n" +
                                "    const selectAllBtn = document.getElementById('selectAllBtn');\n" +
                                "    const checkboxes = document.querySelectorAll('.card-checkbox');\n" +
                                "    \n" +
                                "    if (selectAllBtn.textContent === '全选') {\n" +
                                "        checkboxes.forEach(checkbox => {\n" +
                                "            checkbox.checked = true;\n" +
                                "            selectedKeys.add(checkbox.value);\n" +
                                "        });\n" +
                                "        selectAllBtn.textContent = '取消全选';\n" +
                                "    } else {\n" +
                                "        checkboxes.forEach(checkbox => {\n" +
                                "            checkbox.checked = false;\n" +
                                "        });\n" +
                                "        selectedKeys.clear();\n" +
                                "        selectAllBtn.textContent = '全选';\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新卡片选中状态\n" +
                                "    const keyItems = document.querySelectorAll('.shop-card');\n" +
                                "    keyItems.forEach(item => {\n" +
                                "        const checkbox = item.querySelector('.card-checkbox');\n" +
                                "        if (checkbox) {\n" +
                                "            item.classList.toggle('selected', checkbox.checked);\n" +
                                "        }\n" +
                                "    });\n" +
                                "    \n" +
                                "    updateSelectionButtons();\n" +
                                "}\n" +
                                "\n" +
                                "function updateSelectionButtons() {\n" +
                                "    const selectAllBtn = document.getElementById('selectAllBtn');\n" +
                                "    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');\n" +
                                "    const checkboxes = document.querySelectorAll('.card-checkbox');\n" +
                                "    const checkedCount = document.querySelectorAll('.card-checkbox:checked').length;\n"
                                +
                                "    \n" +
                                "    if (selectAllBtn) {\n" +
                                "        selectAllBtn.textContent = checkedCount === checkboxes.length && checkboxes.length > 0 ? '取消全选' : '全选';\n"
                                +
                                "    }\n" +
                                "    \n" +
                                "    if (deleteSelectedBtn) {\n" +
                                "        deleteSelectedBtn.disabled = selectedKeys.size === 0;\n" +
                                "        deleteSelectedBtn.textContent = '🗑️ 删除选中 (' + selectedKeys.size + ')';\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function deleteSelectedKeys() {\n" +
                                "    if (selectedKeys.size === 0) {\n" +
                                "        showResult('请先选择要删除的卡密', 'error');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    showConfirmModal(\n" +
                                "        '🗑️ 确认删除',\n" +
                                "        `确定要删除选中的 <strong>${selectedKeys.size}</strong> 个卡密吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可恢复！</span>`,\n"
                                +
                                "        () => {\n" +
                                "            showResult('正在删除选中的卡密...', 'info');\n" +
                                "            \n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'delete_keys',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "',\n" +
                                "                    keys: Array.from(selectedKeys)\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ 成功删除 ' + data.deleted_count + ' 个卡密', 'success');\n"
                                +
                                "                    selectedKeys.clear();\n" +
                                "                    loadKeys();\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function clearAllKeys() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🗑️ 清空所有卡密',\n" +
                                "        `确定要清空所有卡密吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可恢复！</span>`,\n" +
                                "        () => {\n" +
                                "            showResult('正在清空卡密...', 'info');\n" +
                                "            \n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'clear_keys',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "'\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ ' + data.message + '<br>清空的卡密数量: ' + data.cleared_count, 'success');\n"
                                +
                                "                    selectedKeys.clear();\n" +
                                "                    loadKeys();\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function deleteKey(key) {\n" +
                                "    showConfirmModal(\n" +
                                "        '🗑️ 删除卡密',\n" +
                                "        `确定要删除卡密 <strong>\"${key}\"</strong> 吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可恢复！</span>`,\n"
                                +
                                "        () => {\n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'delete_keys',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "',\n" +
                                "                    keys: [key]\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ 卡密删除成功', 'success');\n" +
                                "                    selectedKeys.delete(key);\n" +
                                "                    loadKeys();\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function copyKey(key) {\n" +
                                "    navigator.clipboard.writeText(key).then(() => {\n" +
                                "        showResult('✅ 卡密已复制到剪贴板: ' + key, 'success');\n" +
                                "    }).catch(err => {\n" +
                                "        showResult('❌ 复制失败，请手动复制: ' + key, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function filterByStatus(status) {\n" +
                                "    currentStatusFilter = status;\n" +
                                "    \n" +
                                "    // 更新按钮状态\n" +
                                "    document.querySelectorAll('.status-btn').forEach(btn => btn.classList.remove('active'));\n"
                                +
                                "    document.getElementById('status' + status.charAt(0).toUpperCase() + status.slice(1)).classList.add('active');\n"
                                +
                                "    \n" +
                                "    applyStatusFilter();\n" +
                                "}\n" +
                                "\n" +
                                "function applyStatusFilter() {\n" +
                                "    let filtered = allKeys;\n" +
                                "    \n" +
                                "    // 按状态过滤\n" +
                                "    if (currentStatusFilter !== 'all') {\n" +
                                "        filtered = allKeys.filter(keyInfo => {\n" +
                                "            if (currentStatusFilter === 'available') {\n" +
                                "                return !keyInfo.used && !keyInfo.assigned && !keyInfo.expired;\n" +
                                "            } else if (currentStatusFilter === 'assigned') {\n" +
                                "                return keyInfo.assigned && !keyInfo.used && !keyInfo.expired;\n" +
                                "            } else if (currentStatusFilter === 'used') {\n" +
                                "                return keyInfo.used;\n" +
                                "            } else if (currentStatusFilter === 'expired') {\n" +
                                "                return keyInfo.expired;\n" +
                                "            }\n" +
                                "            return true;\n" +
                                "        });\n" +
                                "    }\n" +
                                "    \n" +
                                "    filteredKeys = filtered;\n" +
                                "    filterKeys();\n" +
                                "}\n" +
                                "\n" +
                                "function filterKeys() {\n" +
                                "    const searchTerm = document.getElementById('searchInput').value.toLowerCase();\n" +
                                "    let filtered = filteredKeys;\n" +
                                "    \n" +
                                "    if (searchTerm) {\n" +
                                "        filtered = filteredKeys.filter(keyInfo => \n" +
                                "            keyInfo.key.toLowerCase().includes(searchTerm) || \n" +
                                "            (keyInfo.message && keyInfo.message.toLowerCase().includes(searchTerm)) ||\n"
                                +
                                "            (keyInfo.assigned_to && keyInfo.assigned_to.toLowerCase().includes(searchTerm)) ||\n"
                                +
                                "            (keyInfo.used_by && keyInfo.used_by.toLowerCase().includes(searchTerm))\n"
                                +
                                "        );\n" +
                                "    }\n" +
                                "    \n" +
                                "    displayKeys(filtered);\n" +
                                "}\n" +
                                "\n" +
                                "function updateStatusCounts() {\n" +
                                "    document.getElementById('countAll').textContent = keyStats.all || 0;\n" +
                                "    document.getElementById('countAvailable').textContent = keyStats.available || 0;\n"
                                +
                                "    document.getElementById('countAssigned').textContent = keyStats.assigned || 0;\n" +
                                "    document.getElementById('countUsed').textContent = keyStats.used || 0;\n" +
                                "    document.getElementById('countExpired').textContent = keyStats.expired || 0;\n" +
                                "}\n" +
                                "\n" +
                                "function refreshKeys() {\n" +
                                "    isManualRefresh = true;\n" +
                                "    selectedKeys.clear();\n" +
                                "    loadKeys();\n" +
                                "}\n" +
                                "\n" +
                                "// 计算数据哈希值用于检测变化\n" +
                                "function calculateDataHash(data) {\n" +
                                "    const hashData = {\n" +
                                "        total_keys: data.total_keys,\n" +
                                "        total_pages: data.total_pages,\n" +
                                "        stats: data.stats,\n" +
                                "        keys: data.keys ? data.keys.map(key => ({\n" +
                                "            key: key.key,\n" +
                                "            used: key.used,\n" +
                                "            assigned: key.assigned,\n" +
                                "            expired: key.expired,\n" +
                                "            created_time: key.created_time,\n" +
                                "            used_time: key.used_time,\n" +
                                "            assigned_time: key.assigned_time\n" +
                                "        })) : []\n" +
                                "    };\n" +
                                "    return JSON.stringify(hashData);\n" +
                                "}\n" +
                                "\n" +
                                "// 启动自动检测\n" +
                                "function startAutoUpdate() {\n" +
                                "    if (autoUpdateInterval) {\n" +
                                "        clearInterval(autoUpdateInterval);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新状态指示器\n" +
                                "    const statusElement = document.getElementById('autoUpdateStatus');\n" +
                                "    if (statusElement) {\n" +
                                "        statusElement.textContent = '🔄 自动检测中';\n" +
                                "        statusElement.className = 'status-indicator active';\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 每1秒检测一次数据变化\n" +
                                "    autoUpdateInterval = setInterval(() => {\n" +
                                "        if (document.getElementById('keysContainer')) {\n" +
                                "            loadKeys(currentPage, true); // 静默检测\n" +
                                "        } else {\n" +
                                "            stopAutoUpdate(); // 如果不在卡密页面，停止检测\n" +
                                "        }\n" +
                                "    }, 1000);\n" +
                                "}\n" +
                                "\n" +
                                "// 停止自动检测\n" +
                                "function stopAutoUpdate() {\n" +
                                "    if (autoUpdateInterval) {\n" +
                                "        clearInterval(autoUpdateInterval);\n" +
                                "        autoUpdateInterval = null;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新状态指示器\n" +
                                "    const statusElement = document.getElementById('autoUpdateStatus');\n" +
                                "    if (statusElement) {\n" +
                                "        statusElement.textContent = '⏸️ 已暂停';\n" +
                                "        statusElement.className = 'status-indicator inactive';\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 计算中奖记录数据哈希值\n" +
                                "function calculateWinnersDataHash(data) {\n" +
                                "    const hashData = {\n" +
                                "        total_winners: data.total_winners,\n" +
                                "        total_pages: data.total_pages,\n" +
                                "        winners: data.winners ? data.winners.map(winner => ({\n" +
                                "            username: winner.username,\n" +
                                "            prize: winner.prize,\n" +
                                "            date: winner.date,\n" +
                                "            timestamp: winner.timestamp\n" +
                                "        })) : []\n" +
                                "    };\n" +
                                "    return JSON.stringify(hashData);\n" +
                                "}\n" +
                                "\n" +
                                "// 启动中奖记录自动检测\n" +
                                "function startWinnersAutoUpdate() {\n" +
                                "    if (winnersAutoUpdateInterval) {\n" +
                                "        clearInterval(winnersAutoUpdateInterval);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新状态指示器\n" +
                                "    const statusElement = document.getElementById('winnersAutoUpdateStatus');\n" +
                                "    if (statusElement) {\n" +
                                "        statusElement.textContent = '🔄 自动检测中';\n" +
                                "        statusElement.className = 'status-indicator active';\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 每1秒检测一次数据变化\n" +
                                "    winnersAutoUpdateInterval = setInterval(() => {\n" +
                                "        if (document.getElementById('winnersContainer')) {\n" +
                                "            loadWinners(currentWinnerPage, true); // 静默检测\n" +
                                "        } else {\n" +
                                "            stopWinnersAutoUpdate(); // 如果不在中奖记录页面，停止检测\n" +
                                "        }\n" +
                                "    }, 1000);\n" +
                                "}\n" +
                                "\n" +
                                "// 停止中奖记录自动检测\n" +
                                "function stopWinnersAutoUpdate() {\n" +
                                "    if (winnersAutoUpdateInterval) {\n" +
                                "        clearInterval(winnersAutoUpdateInterval);\n" +
                                "        winnersAutoUpdateInterval = null;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新状态指示器\n" +
                                "    const statusElement = document.getElementById('winnersAutoUpdateStatus');\n" +
                                "    if (statusElement) {\n" +
                                "        statusElement.textContent = '⏸️ 已暂停';\n" +
                                "        statusElement.className = 'status-indicator inactive';\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 计算统计数据哈希值\n" +
                                "function calculateStatisticsDataHash(data) {\n" +
                                "    const hashData = {\n" +
                                "        basic: data.statistics ? data.statistics.basic : null,\n" +
                                "        top_players: data.statistics ? data.statistics.top_players : null,\n" +
                                "        system: data.statistics ? data.statistics.system : null,\n" +
                                "        daily_usage: data.statistics ? data.statistics.daily_usage : null\n" +
                                "    };\n" +
                                "    return JSON.stringify(hashData);\n" +
                                "}\n" +
                                "\n" +
                                "// 重写loadStatistics函数以支持智能检测\n" +
                                "function loadStatistics(silent = false) {\n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_statistics',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "'\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            // 计算数据哈希值用于检测变化\n" +
                                "            const dataHash = calculateStatisticsDataHash(data);\n" +
                                "            \n" +
                                "            // 检查数据是否有变化\n" +
                                "            if (silent && lastStatisticsDataHash && lastStatisticsDataHash === dataHash) {\n"
                                +
                                "                // 数据没有变化，不更新界面\n" +
                                "                return;\n" +
                                "            }\n" +
                                "            \n" +
                                "            // 数据有变化或首次加载，更新界面\n" +
                                "            lastStatisticsDataHash = dataHash;\n" +
                                "            displayStatistics(data.statistics);\n" +
                                "            \n" +
                                "            // 如果是静默检测到变化，显示提示\n" +
                                "            if (silent && !isStatisticsManualRefresh) {\n" +
                                "                showResult('🔄 检测到统计数据更新，已自动刷新', 'info');\n" +
                                "            }\n" +
                                "        } else {\n" +
                                "            if (!silent) {\n" +
                                "                showResult('加载统计数据失败: ' + data.message, 'error');\n" +
                                "            }\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        if (!silent) {\n" +
                                "            console.error('Error:', error);\n" +
                                "            showResult('加载统计数据时出错', 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .finally(() => {\n" +
                                "        isStatisticsManualRefresh = false;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 启动统计分析自动检测\n" +
                                "function startStatisticsAutoUpdate() {\n" +
                                "    if (statisticsAutoUpdateInterval) {\n" +
                                "        clearInterval(statisticsAutoUpdateInterval);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 每1秒检测一次数据变化\n" +
                                "    statisticsAutoUpdateInterval = setInterval(() => {\n" +
                                "        if (document.getElementById('statisticsContainer')) {\n" +
                                "            loadStatistics(true); // 静默检测\n" +
                                "        } else {\n" +
                                "            stopStatisticsAutoUpdate(); // 如果不在统计页面，停止检测\n" +
                                "        }\n" +
                                "    }, 1000);\n" +
                                "}\n" +
                                "\n" +
                                "// 停止统计分析自动检测\n" +
                                "function stopStatisticsAutoUpdate() {\n" +
                                "    if (statisticsAutoUpdateInterval) {\n" +
                                "        clearInterval(statisticsAutoUpdateInterval);\n" +
                                "        statisticsAutoUpdateInterval = null;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 计算仪表板数据哈希值\n" +
                                "function calculateDashboardDataHash(data) {\n" +
                                "    const hashData = {\n" +
                                "        keys: data.keys || {},\n" +
                                "        winners: data.winners || 0,\n" +
                                "        server_status: data.server_status || 'unknown'\n" +
                                "    };\n" +
                                "    return JSON.stringify(hashData);\n" +
                                "}\n" +
                                "\n" +
                                "// 加载仪表板数据\n" +
                                "function loadDashboard(silent = false) {\n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_dashboard_stats',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "'\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            // 计算数据哈希值用于检测变化\n" +
                                "            const dataHash = calculateDashboardDataHash(data);\n" +
                                "            \n" +
                                "            // 检查数据是否有变化\n" +
                                "            if (silent && lastDashboardDataHash && lastDashboardDataHash === dataHash) {\n"
                                +
                                "                // 数据没有变化，不更新界面\n" +
                                "                return;\n" +
                                "            }\n" +
                                "            \n" +
                                "            // 数据有变化或首次加载，更新界面\n" +
                                "            lastDashboardDataHash = dataHash;\n" +
                                "            updateDashboardStats(data);\n" +
                                "            \n" +
                                "            // 如果是静默检测到变化，显示提示\n" +
                                "            if (silent && !isDashboardManualRefresh) {\n" +
                                "                showResult('🔄 检测到仪表板数据更新，已自动刷新', 'info');\n" +
                                "            }\n" +
                                "        } else {\n" +
                                "            if (!silent) {\n" +
                                "                console.error('加载仪表板数据失败:', data.message);\n" +
                                "            }\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        if (!silent) {\n" +
                                "            console.error('Error:', error);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .finally(() => {\n" +
                                "        isDashboardManualRefresh = false;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 更新仪表板统计数据\n" +
                                "function updateDashboardStats(data) {\n" +
                                "    if (data.keys) {\n" +
                                "        const totalElement = document.getElementById('totalKeysCount');\n" +
                                "        const availableElement = document.getElementById('availableKeysCount');\n" +
                                "        const usedElement = document.getElementById('usedKeysCount');\n" +
                                "        \n" +
                                "        if (totalElement) {\n" +
                                "            totalElement.textContent = data.keys.total || 0;\n" +
                                "            // 添加数字动画效果\n" +
                                "            totalElement.style.transform = 'scale(1.1)';\n" +
                                "            setTimeout(() => {\n" +
                                "                totalElement.style.transform = 'scale(1)';\n" +
                                "            }, 200);\n" +
                                "        }\n" +
                                "        \n" +
                                "        if (availableElement) {\n" +
                                "            availableElement.textContent = data.keys.available || 0;\n" +
                                "            availableElement.style.transform = 'scale(1.1)';\n" +
                                "            setTimeout(() => {\n" +
                                "                availableElement.style.transform = 'scale(1)';\n" +
                                "            }, 200);\n" +
                                "        }\n" +
                                "        \n" +
                                "        if (usedElement) {\n" +
                                "            usedElement.textContent = data.keys.used || 0;\n" +
                                "            usedElement.style.transform = 'scale(1.1)';\n" +
                                "            setTimeout(() => {\n" +
                                "                usedElement.style.transform = 'scale(1)';\n" +
                                "            }, 200);\n" +
                                "        }\n" +
                                "    }\n" +
                                "    \n" +
                                "    if (data.winners !== undefined) {\n" +
                                "        const winnersElement = document.getElementById('totalWinnersCount');\n" +
                                "        if (winnersElement) {\n" +
                                "            winnersElement.textContent = data.winners;\n" +
                                "            winnersElement.style.transform = 'scale(1.1)';\n" +
                                "            setTimeout(() => {\n" +
                                "                winnersElement.style.transform = 'scale(1)';\n" +
                                "            }, 200);\n" +
                                "        }\n" +
                                "    }\n" +
                                "    \n" +
                                "    if (data.server_status) {\n" +
                                "        const serverElement = document.getElementById('webServerStatus');\n" +
                                "        if (serverElement) {\n" +
                                "            serverElement.textContent = data.server_status;\n" +
                                "            // 根据状态设置颜色\n" +
                                "            if (data.server_status === '运行中') {\n" +
                                "                serverElement.style.color = '#4CAF50';\n" +
                                "            } else {\n" +
                                "                serverElement.style.color = '#f44336';\n" +
                                "            }\n" +
                                "        }\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 启动仪表板自动检测\n" +
                                "function startDashboardAutoUpdate() {\n" +
                                "    if (dashboardAutoUpdateInterval) {\n" +
                                "        clearInterval(dashboardAutoUpdateInterval);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新状态指示器\n" +
                                "    const statusElement = document.getElementById('dashboardAutoUpdateStatus');\n" +
                                "    if (statusElement) {\n" +
                                "        statusElement.textContent = '🔄 自动检测中';\n" +
                                "        statusElement.className = 'status-indicator active';\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 每1秒检测一次数据变化\n" +
                                "    dashboardAutoUpdateInterval = setInterval(() => {\n" +
                                "        if (document.getElementById('dashboardContainer')) {\n" +
                                "            loadDashboard(true); // 静默检测\n" +
                                "        } else {\n" +
                                "            stopDashboardAutoUpdate(); // 如果不在仪表板页面，停止检测\n" +
                                "        }\n" +
                                "    }, 1000);\n" +
                                "}\n" +
                                "\n" +
                                "// 停止仪表板自动检测\n" +
                                "function stopDashboardAutoUpdate() {\n" +
                                "    if (dashboardAutoUpdateInterval) {\n" +
                                "        clearInterval(dashboardAutoUpdateInterval);\n" +
                                "        dashboardAutoUpdateInterval = null;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 更新状态指示器\n" +
                                "    const statusElement = document.getElementById('dashboardAutoUpdateStatus');\n" +
                                "    if (statusElement) {\n" +
                                "        statusElement.textContent = '⏸️ 已暂停';\n" +
                                "        statusElement.className = 'status-indicator inactive';\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function updateKeyCount(count) {\n" +
                                "    const countElement = document.getElementById('totalKeysCount');\n" +
                                "    if (countElement) {\n" +
                                "        countElement.textContent = count;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function updatePagination() {\n" +
                                "    const paginationContainer = document.getElementById('paginationContainer');\n" +
                                "    const paginationInfo = document.getElementById('paginationInfo');\n" +
                                "    const pageNumbers = document.getElementById('pageNumbers');\n" +
                                "    const firstPageBtn = document.getElementById('firstPageBtn');\n" +
                                "    const prevPageBtn = document.getElementById('prevPageBtn');\n" +
                                "    const nextPageBtn = document.getElementById('nextPageBtn');\n" +
                                "    const lastPageBtn = document.getElementById('lastPageBtn');\n" +
                                "    \n" +
                                "    if (!paginationContainer) return;\n" +
                                "    \n" +
                                "    // 显示分页容器（当总数据量超过每页显示数量时才显示）\n" +
                                "    const pageSize = parseInt(document.getElementById('pageSize').value);\n" +
                                "    const shouldShowPagination = totalKeys > pageSize;\n" +
                                "    paginationContainer.style.display = shouldShowPagination ? 'flex' : 'none';\n" +
                                "    \n" +
                                "    if (!shouldShowPagination) return;\n" +
                                "    \n" +
                                "    // 更新分页信息\n" +
                                "    const startIndex = (currentPage - 1) * pageSize + 1;\n" +
                                "    const endIndex = Math.min(currentPage * pageSize, totalKeys);\n" +
                                "    paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${totalKeys} 条记录`;\n"
                                +
                                "    \n" +
                                "    // 更新按钮状态\n" +
                                "    firstPageBtn.disabled = currentPage === 1;\n" +
                                "    prevPageBtn.disabled = currentPage === 1;\n" +
                                "    nextPageBtn.disabled = currentPage === totalPages;\n" +
                                "    lastPageBtn.disabled = currentPage === totalPages;\n" +
                                "    \n" +
                                "    // 生成页码\n" +
                                "    let pageNumbersHtml = '';\n" +
                                "    const maxVisiblePages = 5;\n" +
                                "    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n" +
                                "    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n" +
                                "    \n" +
                                "    if (endPage - startPage + 1 < maxVisiblePages) {\n" +
                                "        startPage = Math.max(1, endPage - maxVisiblePages + 1);\n" +
                                "    }\n" +
                                "    \n" +
                                "    for (let i = startPage; i <= endPage; i++) {\n" +
                                "        const isActive = i === currentPage;\n" +
                                "        pageNumbersHtml += `<span class=\"page-number ${isActive ? 'active' : ''}\" onclick=\"goToPage(${i})\">${i}</span>`;\n"
                                +
                                "    }\n" +
                                "    \n" +
                                "    pageNumbers.innerHTML = pageNumbersHtml;\n" +
                                "}\n" +
                                "\n" +
                                "function goToPage(page) {\n" +
                                "    if (page < 1 || page > totalPages || page === currentPage) return;\n" +
                                "    loadKeys(page);\n" +
                                "}\n" +
                                "\n" +
                                // 添加积分商店JavaScript代码
                                pointsShopPageGenerator.generatePointsShopJavaScript() +
                                "function reloadConfig() {\n" +
                                "    showResult('正在重载配置...', 'info');\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'reload_config',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "'\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showResult('✅ ' + data.message, 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function refreshStats() {\n" +
                                "    showResult('正在刷新统计数据...', 'info');\n" +
                                "    setTimeout(() => { location.reload(); }, 1000);\n" +
                                "}\n" +
                                "\n" +
                                "function toggleWebLogging() {\n" +
                                "    const checkbox = document.getElementById('webLogEnabled');\n" +
                                "    if (!checkbox) return;\n" +
                                "    \n" +
                                "    const enabled = checkbox.checked;\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'toggle_web_logging',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            enabled: enabled\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showResult('✅ ' + data.message, 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ ' + data.message, 'error');\n" +
                                "            // 恢复复选框状态\n" +
                                "            checkbox.checked = !enabled;\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "        // 恢复复选框状态\n" +
                                "        checkbox.checked = !enabled;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function toggleConsoleLogging() {\n" +
                                "    const checkbox = document.getElementById('consoleLogEnabled');\n" +
                                "    if (!checkbox) return;\n" +
                                "    \n" +
                                "    const enabled = checkbox.checked;\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'toggle_console_logging',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            enabled: enabled\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showResult('✅ ' + data.message, 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ ' + data.message, 'error');\n" +
                                "            // 恢复复选框状态\n" +
                                "            checkbox.checked = !enabled;\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "        // 恢复复选框状态\n" +
                                "        checkbox.checked = !enabled;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "let logsVisible = false;\n" +
                                "\n" +
                                "function toggleLogs() {\n" +
                                "    const logsSection = document.getElementById('logsSection');\n" +
                                "    if (!logsSection) return;\n" +
                                "    \n" +
                                "    logsVisible = !logsVisible;\n" +
                                "    logsSection.style.display = logsVisible ? 'block' : 'none';\n" +
                                "    \n" +
                                "    if (logsVisible) {\n" +
                                "        loadLogs();\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function loadLogs() {\n" +
                                "    const logContainer = document.getElementById('logContainer');\n" +
                                "    const logLevel = document.getElementById('logLevel') ? document.getElementById('logLevel').value : 'all';\n"
                                +
                                "    const logLines = document.getElementById('logLines') ? document.getElementById('logLines').value : 100;\n"
                                +
                                "    \n" +
                                "    if (!logContainer) return;\n" +
                                "    \n" +
                                "    logContainer.innerHTML = '<div class=\"loading-spinner\">正在加载日志...</div>';\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_logs',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            lines: parseInt(logLines),\n" +
                                "            level: logLevel\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            allLogs = data.logs || [];\n" +
                                "            applyLogFilters();\n" +
                                "        } else {\n" +
                                "            logContainer.innerHTML = '<div class=\"no-logs\">加载日志失败: ' + data.message + '</div>';\n"
                                +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        logContainer.innerHTML = '<div class=\"no-logs\">网络错误: ' + error.message + '</div>';\n"
                                +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function displayLogs(logs) {\n" +
                                "    const logContainer = document.getElementById('logContainer');\n" +
                                "    if (!logContainer) return;\n" +
                                "    \n" +
                                "    if (logs.length === 0) {\n" +
                                "        const message = currentSearchTerm ? '没有找到匹配的日志记录' : '暂无日志记录';\n" +
                                "        logContainer.innerHTML = '<div class=\"no-logs\">' + message + '</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    let html = '';\n" +
                                "    \n" +
                                "    // 添加统计信息\n" +
                                "    const statsHtml = '<div class=\"log-stats\">';\n" +
                                "    if (currentSearchTerm) {\n" +
                                "        html += statsHtml + '找到 ' + logs.length + ' 条匹配记录 / 总共 ' + allLogs.length + ' 条</div>';\n"
                                +
                                "    } else {\n" +
                                "        html += statsHtml + '显示 ' + logs.length + ' 条日志记录</div>';\n" +
                                "    }\n" +
                                "    \n" +
                                "    logs.forEach(logLine => {\n" +
                                "        let formattedLine = formatLogLine(logLine);\n" +
                                "        \n" +
                                "        // 如果有搜索词，高亮显示\n" +
                                "        if (currentSearchTerm) {\n" +
                                "            const regex = new RegExp('(' + escapeRegex(currentSearchTerm) + ')', 'gi');\n"
                                +
                                "            formattedLine = formattedLine.replace(regex, '<span class=\"search-highlight\">$1</span>');\n"
                                +
                                "        }\n" +
                                "        \n" +
                                "        html += '<div class=\"log-line\">' + formattedLine + '</div>';\n" +
                                "    });\n" +
                                "    \n" +
                                "    logContainer.innerHTML = html;\n" +
                                "    // 滚动到底部显示最新日志\n" +
                                "    logContainer.scrollTop = logContainer.scrollHeight;\n" +
                                "}\n" +
                                "\n" +
                                "function escapeRegex(string) {\n" +
                                "    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n" +
                                "}\n" +
                                "\n" +
                                "function formatLogLine(logLine) {\n" +
                                "    // 解析日志行格式: [时间] [级别] 消息\n" +
                                "    const timeMatch = logLine.match(/\\[(\\d{2}:\\d{2}:\\d{2})\\]/);\n" +
                                "    const levelMatch = logLine.match(/\\[(INFO|WARNING|SEVERE)\\]/);\n" +
                                "    \n" +
                                "    let formattedLine = logLine;\n" +
                                "    \n" +
                                "    if (timeMatch) {\n" +
                                "        formattedLine = formattedLine.replace(timeMatch[0], '<span class=\"log-timestamp\">' + timeMatch[0] + '</span>');\n"
                                +
                                "    }\n" +
                                "    \n" +
                                "    if (levelMatch) {\n" +
                                "        const level = levelMatch[1];\n" +
                                "        formattedLine = formattedLine.replace(levelMatch[0], '<span class=\"log-level ' + level + '\">' + levelMatch[0] + '</span>');\n"
                                +
                                "    }\n" +
                                "    \n" +
                                "    return '<span class=\"log-message\">' + formattedLine + '</span>';\n" +
                                "}\n" +
                                "\n" +
                                "function loadLogsWithCustomLines() {\n" +
                                "    const customLines = document.getElementById('customLogLines').value;\n" +
                                "    if (!customLines || customLines < 1 || customLines > 5000) {\n" +
                                "        showResult('请输入1-5000之间的行数', 'error');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const logContainer = document.getElementById('logContainer');\n" +
                                "    const logLevel = document.getElementById('logLevel') ? document.getElementById('logLevel').value : 'all';\n"
                                +
                                "    \n" +
                                "    if (!logContainer) return;\n" +
                                "    \n" +
                                "    logContainer.innerHTML = '<div class=\"loading-spinner\">正在加载 ' + customLines + ' 行日志...</div>';\n"
                                +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_logs',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            lines: parseInt(customLines),\n" +
                                "            level: logLevel\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            allLogs = data.logs || [];\n" +
                                "            applyLogFilters();\n" +
                                "            showResult('成功加载 ' + allLogs.length + ' 行日志', 'success');\n" +
                                "        } else {\n" +
                                "            logContainer.innerHTML = '<div class=\"no-logs\">加载日志失败: ' + data.message + '</div>';\n"
                                +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        logContainer.innerHTML = '<div class=\"no-logs\">网络错误: ' + error.message + '</div>';\n"
                                +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function applyLogFilters() {\n" +
                                "    const searchTerm = document.getElementById('logSearch') ? document.getElementById('logSearch').value.toLowerCase() : '';\n"
                                +
                                "    currentSearchTerm = searchTerm;\n" +
                                "    \n" +
                                "    if (!searchTerm) {\n" +
                                "        filteredLogs = allLogs;\n" +
                                "    } else {\n" +
                                "        filteredLogs = allLogs.filter(log => log.toLowerCase().includes(searchTerm));\n"
                                +
                                "    }\n" +
                                "    \n" +
                                "    displayLogs(filteredLogs);\n" +
                                "}\n" +
                                "\n" +
                                "function filterLogs() {\n" +
                                "    applyLogFilters();\n" +
                                "}\n" +
                                "\n" +
                                "function clearLogSearch() {\n" +
                                "    const searchInput = document.getElementById('logSearch');\n" +
                                "    if (searchInput) {\n" +
                                "        searchInput.value = '';\n" +
                                "        applyLogFilters();\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function exportLogs() {\n" +
                                "    if (filteredLogs.length === 0) {\n" +
                                "        showResult('没有日志可以导出', 'error');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const logContent = filteredLogs.join('\\n');\n" +
                                "    const blob = new Blob([logContent], { type: 'text/plain' });\n" +
                                "    const url = URL.createObjectURL(blob);\n" +
                                "    \n" +
                                "    const a = document.createElement('a');\n" +
                                "    a.href = url;\n" +
                                "    a.download = 'keygen-logs-' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.txt';\n"
                                +
                                "    document.body.appendChild(a);\n" +
                                "    a.click();\n" +
                                "    document.body.removeChild(a);\n" +
                                "    URL.revokeObjectURL(url);\n" +
                                "    \n" +
                                "    showResult('日志已导出到文件', 'success');\n" +
                                "}\n" +
                                "\n" +
                                "function saveLogsToServer() {\n" +
                                "    showConfirmModal(\n" +
                                "        '💾 保存日志到服务器',\n" +
                                "        '确定要将当前日志保存到插件logs文件夹吗？<br><br>保存后可以在服务器的插件文件夹中查看完整的日志文件。',\n" +
                                "        function() {\n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'save_logs_to_server',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "'\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ ' + data.message, 'success');\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 保存日志失败: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function clearLogs() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🗑️ 清空系统日志',\n" +
                                "        '确定要清空系统日志吗？<br><br><span style=\"color: #ff6b6b; font-weight: bold;\">⚠️ 此操作不可恢复！</span><br><br>清空后将无法查看历史日志记录。',\n"
                                +
                                "        function() {\n" +
                                "            showResult('正在清空日志...', 'info');\n" +
                                "            \n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'clear_logs',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "'\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ ' + data.message, 'success');\n" +
                                "                    allLogs = [];\n" +
                                "                    filteredLogs = [];\n" +
                                "                    currentSearchTerm = '';\n" +
                                "                    const searchInput = document.getElementById('logSearch');\n" +
                                "                    if (searchInput) searchInput.value = '';\n" +
                                "                    loadLogs();\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "let currentRewards = [];\n" +
                                "let editingRewardId = null;\n" +
                                "\n" +
                                "function loadRewards() {\n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_rewards',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "'\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            currentRewards = data.rewards || [];\n" +
                                "            displayRewards();\n" +
                                "        } else {\n" +
                                "            showResult('❌ 加载奖品失败: ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function displayRewards() {\n" +
                                "    const rewardList = document.getElementById('rewardList');\n" +
                                "    if (!rewardList) return;\n" +
                                "    \n" +
                                "    let html = '';\n" +
                                "    \n" +
                                "    if (currentRewards.length === 0) {\n" +
                                "        html = '<p style=\"text-align: center; color: var(--text-secondary, #7f8c8d); padding: 40px;\">暂无奖品配置，点击上方按钮新建奖品</p>';\n"
                                +
                                "    } else {\n" +
                                "        currentRewards.forEach(reward => {\n" +
                                "            html += '<div class=\"reward-item\">';\n" +
                                "            html += '<h4>' + reward.name + '</h4>';\n" +
                                "            html += '<p><strong>概率:</strong> ' + reward.probability + '%</p>';\n" +
                                "            html += '<p><strong>描述:</strong> ' + reward.description + '</p>';\n" +
                                "            html += '<p><strong>指令数量:</strong> ' + (reward.commands ? reward.commands.length : 0) + ' 条</p>';\n"
                                +
                                "            html += '<div class=\"reward-actions\">';\n" +
                                "            html += '<button class=\"btn btn-secondary\" onclick=\"editReward(\\'' + reward.id + '\\')\" >编辑</button>';\n"
                                +
                                "            html += '<button class=\"btn btn-danger\" onclick=\"deleteReward(\\'' + reward.id + '\\')\" >删除</button>';\n"
                                +
                                "            html += '</div>';\n" +
                                "            html += '</div>';\n" +
                                "        });\n" +
                                "    }\n" +
                                "    \n" +
                                "    rewardList.innerHTML = html;\n" +
                                "}\n" +
                                "\n" +
                                "function addNewReward() {\n" +
                                "    editingRewardId = null;\n" +
                                "    document.getElementById('editModalTitle').textContent = '新建奖品';\n" +
                                "    document.getElementById('rewardForm').reset();\n" +
                                "    document.getElementById('editRewardModal').style.display = 'block';\n" +
                                "}\n" +
                                "\n" +
                                "function editReward(rewardId) {\n" +
                                "    const reward = currentRewards.find(r => r.id === rewardId);\n" +
                                "    if (!reward) return;\n" +
                                "    \n" +
                                "    editingRewardId = rewardId;\n" +
                                "    document.getElementById('editModalTitle').textContent = '编辑奖品';\n" +
                                "    document.getElementById('rewardName').value = reward.name;\n" +
                                "    document.getElementById('rewardProbability').value = reward.probability;\n" +
                                "    document.getElementById('rewardDescription').value = reward.description;\n" +
                                "    document.getElementById('rewardCommands').value = reward.commands ? reward.commands.join('\\n') : '';\n"
                                +
                                "    document.getElementById('editRewardModal').style.display = 'block';\n" +
                                "}\n" +
                                "\n" +
                                "function deleteReward(rewardId) {\n" +
                                "    const reward = currentRewards.find(r => r.id === rewardId);\n" +
                                "    if (!reward) return;\n" +
                                "    \n" +
                                "    showConfirmModal(\n" +
                                "        '🗑️ 删除奖品',\n" +
                                "        `确定要删除奖品 <strong>\"${reward.name}\"</strong> 吗？<br><br><span style=\"color: #ff6b6b;\">⚠️ 删除后无法恢复</span><br><br>奖品概率: ${reward.probability}%<br>奖品描述: ${reward.description}`,\n"
                                +
                                "        function() {\n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'delete_reward',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "',\n" +
                                "                    reward_id: rewardId\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ 奖品删除成功', 'success');\n" +
                                "                    loadRewards();\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function closeEditRewardModal() {\n" +
                                "    document.getElementById('editRewardModal').style.display = 'none';\n" +
                                "    editingRewardId = null;\n" +
                                "    document.getElementById('rewardForm').reset();\n" +
                                "}\n" +
                                "\n" +
                                "function saveReward(event) {\n" +
                                "    event.preventDefault();\n" +
                                "    \n" +
                                "    const name = document.getElementById('rewardName').value;\n" +
                                "    const probability = parseFloat(document.getElementById('rewardProbability').value);\n"
                                +
                                "    const description = document.getElementById('rewardDescription').value;\n" +
                                "    const commandsText = document.getElementById('rewardCommands').value;\n" +
                                "    const commands = commandsText.split('\\n').filter(cmd => cmd.trim() !== '');\n" +
                                "    \n" +
                                "    if (!name || !description || commands.length === 0) {\n" +
                                "        showResult('请填写所有必填字段', 'error');\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const rewardData = {\n" +
                                "        action: 'save_reward',\n" +
                                "        api_key: '" + webServer.getAdminKey() + "',\n" +
                                "        reward_id: editingRewardId || 'reward_' + Date.now(),\n" +
                                "        name: name,\n" +
                                "        probability: probability,\n" +
                                "        description: description,\n" +
                                "        commands: commands\n" +
                                "    };\n" +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify(rewardData)\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showResult('✅ 奖品保存成功', 'success');\n" +
                                "            closeEditRewardModal();\n" +
                                "            loadRewards();\n" +
                                "        } else {\n" +
                                "            showResult('❌ ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "let allWinners = [];\n" +
                                "let currentWinnerPage = 1;\n" +
                                "let totalWinnerPages = 1;\n" +
                                "let totalWinners = 0;\n" +
                                "\n" +
                                "let allLogs = [];\n" +
                                "let filteredLogs = [];\n" +
                                "let currentSearchTerm = '';\n" +
                                "\n" +
                                "function loadWinners(page = 1, silent = false) {\n" +
                                "    currentWinnerPage = page;\n" +
                                "    const pageSize = document.getElementById('winnerPageSize') ? document.getElementById('winnerPageSize').value : 20;\n"
                                +
                                "    const sortOrder = document.getElementById('winnersortOrder') ? document.getElementById('winnersortOrder').value : 'desc';\n"
                                +
                                "    \n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_winners',\n" +
                                "            api_key: '" + webServer.getAdminKey() + "',\n" +
                                "            page: currentWinnerPage,\n" +
                                "            page_size: parseInt(pageSize),\n" +
                                "            sort_order: sortOrder\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            // 计算数据哈希值用于检测变化\n" +
                                "            const dataHash = calculateWinnersDataHash(data);\n" +
                                "            \n" +
                                "            // 检查数据是否有变化\n" +
                                "            if (silent && lastWinnersDataHash && lastWinnersDataHash === dataHash) {\n"
                                +
                                "                // 数据没有变化，不更新界面\n" +
                                "                return;\n" +
                                "            }\n" +
                                "            \n" +
                                "            // 数据有变化或首次加载，更新界面\n" +
                                "            lastWinnersDataHash = dataHash;\n" +
                                "            allWinners = data.winners || [];\n" +
                                "            totalWinners = data.total_winners || 0;\n" +
                                "            totalWinnerPages = data.total_pages || 1;\n" +
                                "            currentWinnerPage = data.current_page || 1;\n" +
                                "            \n" +
                                "            displayWinners(allWinners);\n" +
                                "            updateWinnerCount(totalWinners);\n" +
                                "            updateWinnerPagination();\n" +
                                "            \n" +
                                "            // 如果是静默检测到变化，显示提示\n" +
                                "            if (silent && !isWinnersManualRefresh) {\n" +
                                "                showResult('🔄 检测到中奖记录更新，已自动刷新', 'info');\n" +
                                "            }\n" +
                                "        } else {\n" +
                                "            if (!silent) {\n" +
                                "                showResult('❌ 加载中奖记录失败: ' + data.message, 'error');\n" +
                                "            }\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        if (!silent) {\n" +
                                "            showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .finally(() => {\n" +
                                "        isWinnersManualRefresh = false;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function displayWinners(winners) {\n" +
                                "    const container = document.getElementById('winnersContainer');\n" +
                                "    if (!container) return;\n" +
                                "    \n" +
                                "    if (winners.length === 0) {\n" +
                                "        container.innerHTML = '<div class=\"no-winners\">暂无中奖记录</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    let html = '';\n" +
                                "    winners.forEach((winner, index) => {\n" +
                                "        html += '<div class=\"shop-card\">';\n" +
                                "        html += '<div class=\"card-header\">';\n" +
                                "        html += '<div class=\"card-title\">';\n" +
                                "        html += '<div class=\"card-icon\">🏆</div>';\n" +
                                "        html += '<div class=\"card-name\">中奖记录 #' + (index + 1) + '</div>';\n" +
                                "        html += '</div>';\n" +
                                "        html += '</div>';\n" +
                                "        html += '<div class=\"card-content\">';\n" +
                                "        html += '<div class=\"card-info\">';\n" +
                                "        html += '<div class=\"info-row\">';\n" +
                                "        html += '<span class=\"info-label\">👤 获奖用户:</span>';\n" +
                                "        html += '<span class=\"info-value\">' + winner.username + '</span>';\n" +
                                "        html += '</div>';\n" +
                                "        html += '<div class=\"info-row\">';\n" +
                                "        html += '<span class=\"info-label\">🎁 奖品名称:</span>';\n" +
                                "        html += '<span class=\"info-value prize-name\">' + winner.prize + '</span>';\n"
                                +
                                "        html += '</div>';\n" +
                                "        html += '<div class=\"info-row\">';\n" +
                                "        html += '<span class=\"info-label\">🕒 中奖时间:</span>';\n" +
                                "        html += '<span class=\"info-value\">' + (winner.date || '未知时间') + '</span>';\n"
                                +
                                "        html += '</div>';\n" +
                                "        html += '</div>';\n" +
                                "        html += '</div>';\n" +
                                "        html += '</div>';\n" +
                                "    });\n" +
                                "    \n" +
                                "    container.innerHTML = html;\n" +
                                "}\n" +
                                "\n" +
                                "function updateWinnerCount(count) {\n" +
                                "    const countElement = document.getElementById('totalWinnersCount');\n" +
                                "    if (countElement) {\n" +
                                "        countElement.textContent = count;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function updateWinnerPagination() {\n" +
                                "    const paginationContainer = document.getElementById('winnerPaginationContainer');\n"
                                +
                                "    const paginationInfo = document.getElementById('winnerPaginationInfo');\n" +
                                "    const pageNumbers = document.getElementById('winnerPageNumbers');\n" +
                                "    const firstPageBtn = document.getElementById('winnerFirstPageBtn');\n" +
                                "    const prevPageBtn = document.getElementById('winnerPrevPageBtn');\n" +
                                "    const nextPageBtn = document.getElementById('winnerNextPageBtn');\n" +
                                "    const lastPageBtn = document.getElementById('winnerLastPageBtn');\n" +
                                "    \n" +
                                "    if (!paginationContainer) return;\n" +
                                "    \n" +
                                "    // 显示分页容器（当总数据量超过每页显示数量时才显示）\n" +
                                "    const pageSize = parseInt(document.getElementById('winnerPageSize').value);\n" +
                                "    const shouldShowPagination = totalWinners > pageSize;\n" +
                                "    paginationContainer.style.display = shouldShowPagination ? 'flex' : 'none';\n" +
                                "    \n" +
                                "    if (!shouldShowPagination) return;\n" +
                                "    \n" +
                                "    // 更新分页信息\n" +
                                "    const startIndex = (currentWinnerPage - 1) * pageSize + 1;\n" +
                                "    const endIndex = Math.min(currentWinnerPage * pageSize, totalWinners);\n" +
                                "    paginationInfo.textContent = `显示第 ${startIndex}-${endIndex} 条，共 ${totalWinners} 条记录`;\n"
                                +
                                "    \n" +
                                "    // 更新按钮状态\n" +
                                "    firstPageBtn.disabled = currentWinnerPage === 1;\n" +
                                "    prevPageBtn.disabled = currentWinnerPage === 1;\n" +
                                "    nextPageBtn.disabled = currentWinnerPage === totalWinnerPages;\n" +
                                "    lastPageBtn.disabled = currentWinnerPage === totalWinnerPages;\n" +
                                "    \n" +
                                "    // 生成页码\n" +
                                "    let pageNumbersHtml = '';\n" +
                                "    const maxVisiblePages = 5;\n" +
                                "    let startPage = Math.max(1, currentWinnerPage - Math.floor(maxVisiblePages / 2));\n"
                                +
                                "    let endPage = Math.min(totalWinnerPages, startPage + maxVisiblePages - 1);\n" +
                                "    \n" +
                                "    if (endPage - startPage + 1 < maxVisiblePages) {\n" +
                                "        startPage = Math.max(1, endPage - maxVisiblePages + 1);\n" +
                                "    }\n" +
                                "    \n" +
                                "    for (let i = startPage; i <= endPage; i++) {\n" +
                                "        const isActive = i === currentWinnerPage;\n" +
                                "        pageNumbersHtml += `<span class=\"page-number ${isActive ? 'active' : ''}\" onclick=\"goToWinnerPage(${i})\">${i}</span>`;\n"
                                +
                                "    }\n" +
                                "    \n" +
                                "    pageNumbers.innerHTML = pageNumbersHtml;\n" +
                                "}\n" +
                                "\n" +
                                "function goToWinnerPage(page) {\n" +
                                "    if (page < 1 || page > totalWinnerPages || page === currentWinnerPage) return;\n" +
                                "    loadWinners(page);\n" +
                                "}\n" +
                                "\n" +
                                "function filterWinners() {\n" +
                                "    const searchTerm = document.getElementById('winnerSearchInput').value.toLowerCase();\n"
                                +
                                "    if (!searchTerm) {\n" +
                                "        displayWinners(allWinners);\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    const filteredWinners = allWinners.filter(winner => \n" +
                                "        winner.username.toLowerCase().includes(searchTerm) || \n" +
                                "        winner.prize.toLowerCase().includes(searchTerm)\n" +
                                "    );\n" +
                                "    \n" +
                                "    displayWinners(filteredWinners);\n" +
                                "}\n" +
                                "\n" +
                                "function clearAllWinners() {\n" +
                                "    showConfirmModal(\n" +
                                "        '🗑️ 清空所有中奖记录',\n" +
                                "        `确定要清空所有中奖记录吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可恢复！</span>`,\n" +
                                "        () => {\n" +
                                "            showResult('正在清空中奖记录...', 'info');\n" +
                                "            \n" +
                                "            fetch('/api', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: { 'Content-Type': 'application/json' },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    action: 'clear_winners',\n" +
                                "                    api_key: '" + webServer.getAdminKey() + "'\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showResult('✅ ' + data.message + '<br>清空的记录数量: ' + data.cleared_count, 'success');\n"
                                +
                                "                    loadWinners();\n" +
                                "                } else {\n" +
                                "                    showResult('❌ ' + data.message, 'error');\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "function refreshWinners() {\n" +
                                "    isWinnersManualRefresh = true;\n" +
                                "    loadWinners();\n" +
                                "}\n" +
                                "\n" +
                                "// 图标数据\n" +
                                "const iconData = {\n" +
                                "    trophy: ['🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '👑', '🎗️'],\n" +
                                "    gem: ['💎', '💍', '💰', '🪙', '💳', '💵', '💴', '💶', '💷', '🔮', '⭐', '🌟', '✨'],\n"
                                +
                                "    tool: ['🔧', '🔨', '⚒️', '🛠️', '⚔️', '🗡️', '🏹', '🛡️', '🔱', '⚡', '🔥', '❄️'],\n"
                                +
                                "    food: ['🍖', '🍗', '🥩', '🍞', '🥖', '🧀', '🥚', '🍳', '🥓', '🍎', '🍊', '🍌', '🍇', '🍓', '🥝', '🍑', '🍒', '🥥', '🍰', '🎂', '🧁', '🍪', '🍫', '🍬', '🍭'],\n"
                                +
                                "    other: ['🎁', '🎀', '🎊', '🎉', '🎈', '🎯', '🎲', '🃏', '🎪', '🎭', '🎨', '🎵', '🎶', '🎸', '🎺', '🎻', '🥁', '🎤', '🎧', '📱', '💻', '⌚', '📷', '🔍', '🔒', '🗝️', '🚀', '⚓', '🌈', '☀️', '🌙', '⭐', '🌍', '🌎', '🌏']\n"
                                +
                                "};\n" +
                                "\n" +
                                "let currentTargetInput = null;\n" +
                                "\n" +
                                "function showIconSelector(targetInputId) {\n" +
                                "    currentTargetInput = targetInputId || 'rewardName';\n" +
                                "    document.getElementById('iconSelectorModal').style.display = 'block';\n" +
                                "    showIconCategory('all');\n" +
                                "}\n" +
                                "\n" +
                                "function closeIconSelector() {\n" +
                                "    document.getElementById('iconSelectorModal').style.display = 'none';\n" +
                                "}\n" +
                                "\n" +
                                "function showIconCategory(category) {\n" +
                                "    // 更新分类按钮状态\n" +
                                "    document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));\n"
                                +
                                "    event.target.classList.add('active');\n" +
                                "    \n" +
                                "    // 获取要显示的图标\n" +
                                "    let icons = [];\n" +
                                "    if (category === 'all') {\n" +
                                "        icons = Object.values(iconData).flat();\n" +
                                "    } else {\n" +
                                "        icons = iconData[category] || [];\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 生成图标网格\n" +
                                "    const iconGrid = document.getElementById('iconGrid');\n" +
                                "    iconGrid.innerHTML = '';\n" +
                                "    \n" +
                                "    icons.forEach(icon => {\n" +
                                "        const iconItem = document.createElement('div');\n" +
                                "        iconItem.className = 'icon-item';\n" +
                                "        iconItem.textContent = icon;\n" +
                                "        iconItem.title = '点击复制: ' + icon;\n" +
                                "        iconItem.onclick = () => selectIcon(icon);\n" +
                                "        iconGrid.appendChild(iconItem);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "function selectIcon(icon) {\n" +
                                "    // 复制到剪贴板\n" +
                                "    navigator.clipboard.writeText(icon).then(() => {\n" +
                                "        showResult('✅ 图标 ' + icon + ' 已选择', 'success');\n" +
                                "        \n" +
                                "        // 插入到目标输入框\n" +
                                "        const targetInput = document.getElementById(currentTargetInput);\n" +
                                "        if (targetInput) {\n" +
                                "            const currentValue = targetInput.value;\n" +
                                "            const cursorPos = targetInput.selectionStart || currentValue.length;\n" +
                                "            const newValue = currentValue.slice(0, cursorPos) + icon + ' ' + currentValue.slice(cursorPos);\n"
                                +
                                "            targetInput.value = newValue;\n" +
                                "            targetInput.focus();\n" +
                                "            targetInput.setSelectionRange(cursorPos + icon.length + 1, cursorPos + icon.length + 1);\n"
                                +
                                "        }\n" +
                                "        \n" +
                                "        closeIconSelector();\n" +
                                "    }).catch(err => {\n" +
                                "        showResult('❌ 选择失败，请手动输入: ' + icon, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 主题切换相关函数\n" +
                                "function toggleAdminTheme() {\n" +
                                "    const body = document.body;\n" +
                                "    const themeIcon = document.querySelector('.theme-icon');\n" +
                                "    \n" +
                                "    if (body.classList.contains('theme-dark')) {\n" +
                                "        body.classList.remove('theme-dark');\n" +
                                "        body.classList.add('theme-light');\n" +
                                "        themeIcon.textContent = '🌙';\n" +
                                "        localStorage.setItem('adminTheme', 'light');\n" +
                                "    } else {\n" +
                                "        body.classList.remove('theme-light');\n" +
                                "        body.classList.add('theme-dark');\n" +
                                "        themeIcon.textContent = '☀️';\n" +
                                "        localStorage.setItem('adminTheme', 'dark');\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "function initializeAdminTheme() {\n" +
                                "    const savedTheme = localStorage.getItem('adminTheme');\n" +
                                "    const defaultTheme = '"
                                + plugin.getConfig().getString("web-server.admin-theme.default-mode", "light") + "';\n"
                                +
                                "    const body = document.body;\n" +
                                "    const themeIcon = document.querySelector('.theme-icon');\n" +
                                "    \n" +
                                "    let currentTheme = savedTheme || defaultTheme;\n" +
                                "    \n" +
                                "    if (currentTheme === 'auto') {\n" +
                                "        const hour = new Date().getHours();\n" +
                                "        currentTheme = (hour < 6 || hour >= 18) ? 'dark' : 'light';\n" +
                                "    }\n" +
                                "    \n" +
                                "    body.classList.remove('theme-light', 'theme-dark');\n" +
                                "    body.classList.add('theme-' + currentTheme);\n" +
                                "    \n" +
                                "    if (themeIcon) {\n" +
                                "        themeIcon.textContent = currentTheme === 'dark' ? '☀️' : '🌙';\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 页面加载时自动加载内容\n" +
                                "document.addEventListener('DOMContentLoaded', function() {\n" +
                                "    // 初始化主题\n" +
                                "    initializeAdminTheme();\n" +
                                "    \n" +
                                "    if (document.getElementById('rewardContainer')) {\n" +
                                "        loadRewards();\n" +
                                "    }\n" +
                                "    if (document.getElementById('keysContainer')) {\n" +
                                "        loadKeys();\n" +
                                "        startAutoUpdate(); // 启动卡密管理的自动检测\n" +
                                "    }\n" +
                                "    if (document.getElementById('winnersContainer')) {\n" +
                                "        loadWinners();\n" +
                                "        startWinnersAutoUpdate(); // 启动中奖记录的自动检测\n" +
                                "    }\n" +
                                "    if (document.getElementById('statisticsContainer')) {\n" +
                                "        loadStatistics();\n" +
                                "        startStatisticsAutoUpdate(); // 启动统计分析的自动检测\n" +
                                "    }\n" +
                                "    if (document.getElementById('dashboardContainer')) {\n" +
                                "        loadDashboard();\n" +
                                "        startDashboardAutoUpdate(); // 启动仪表板的自动检测\n" +
                                "    }\n" +
                                "    if (document.getElementById('dailyCardsGrid')) {\n" +
                                "        loadDailyRewards(); // 加载签到管理页面\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 如果是签到统计页面，加载统计数据\n" +
                                "    if (document.getElementById('todayCheckIns')) {\n" +
                                "        loadCheckInStats(); // 加载签到统计\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 如果是排行榜页面，加载排行榜\n" +
                                "    if (document.getElementById('leaderboardList')) {\n" +
                                "        loadLeaderboard('consecutive'); // 加载排行榜\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 添加签到管理标签页样式\n" +
                                "    addCheckinTabStyles();\n" +
                                "    \n" +
                                "    // 启动签到数据自动更新\n" +
                                "    startCheckinAutoUpdate();\n" +
                                "    \n" +
                                "    // 启动签到数据自动更新\n" +
                                "    startCheckinAutoUpdate();\n" +
                                "    if (document.getElementById('goldPrice')) {\n" +
                                "        loadMakeupCardSettings(); // 加载补签卡商店设置\n" +
                                "    }\n" +
                                "});\n" +
                                "\n" +
                                "// 页面切换时停止自动检测\n" +
                                "window.addEventListener('beforeunload', function() {\n" +
                                "    stopAutoUpdate();\n" +
                                "    stopWinnersAutoUpdate();\n" +
                                "    stopStatisticsAutoUpdate();\n" +
                                "    stopDashboardAutoUpdate();\n" +
                                "});\n" +
                                "\n" +
                                "// 页面可见性变化时控制自动检测\n" +
                                "document.addEventListener('visibilitychange', function() {\n" +
                                "    if (document.hidden) {\n" +
                                "        // 页面隐藏时停止所有自动检测\n" +
                                "        stopAutoUpdate();\n" +
                                "        stopWinnersAutoUpdate();\n" +
                                "        stopStatisticsAutoUpdate();\n" +
                                "        stopDashboardAutoUpdate();\n" +
                                "    } else {\n" +
                                "        // 页面显示时根据当前页面启动对应的自动检测\n" +
                                "        if (document.getElementById('keysContainer')) {\n" +
                                "            startAutoUpdate();\n" +
                                "        }\n" +
                                "        if (document.getElementById('winnersContainer')) {\n" +
                                "            startWinnersAutoUpdate();\n" +
                                "        }\n" +
                                "        if (document.getElementById('statisticsContainer')) {\n" +
                                "            startStatisticsAutoUpdate();\n" +
                                "        }\n" +
                                "        if (document.getElementById('dashboardContainer')) {\n" +
                                "            startDashboardAutoUpdate();\n" +
                                "        }\n" +
                                "    }\n" +
                                "});\n" +
                                "\n" +
                                // 统计分析相关函数
                                new StatisticsPageGenerator(webServer.getAdminKey()).generateStatisticsJavaScript() +
                                new StatisticsPageGenerator(webServer.getAdminKey()).generatePlayerFunctions() +
                                new StatisticsSearchGenerator(webServer.getAdminKey()).generateSearchFunctions() +
                                new StatisticsSearchGenerator(webServer.getAdminKey()).generateSearchResultsFunctions()
                                +
                                new StatisticsSearchGenerator(webServer.getAdminKey()).generateTabFunctions() +
                                new PlayerKeysDisplayGenerator().generatePlayerKeysTabFunctions() +
                                new PlayerKeysDisplayGenerator().generatePlayerKeysDisplayFunctions() +

                                // 界面和奖品管理
                                interfacePageGenerator.generateInterfaceJavaScript() +
                                rewardPageGenerator.generateRewardJavaScript() +

                                // 签到管理功能
                                "\n// ==================== 签到管理功能 ====================\n" +
                                "let dailyRewards = {};\n" +
                                "\n" +
                                "// 加载每日奖励配置\n" +
                                "function loadDailyRewards() {\n" +
                                "    fetch('/admin/checkin-rewards', {\n" +
                                "        method: 'GET',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            dailyRewards = data.rewards || {};\n" +
                                "            updateDailyRewardCards();\n" +
                                "        } else {\n" +
                                "            console.error('加载签到奖励失败:', data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        console.error('网络错误:', error);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 更新每日奖励卡片显示\n" +
                                "function updateDailyRewardCards() {\n" +
                                "    for (let day = 1; day <= 31; day++) {\n" +
                                "        const rewardElement = document.getElementById('reward-' + day);\n" +
                                "        if (rewardElement) {\n" +
                                "            const reward = dailyRewards[day] || { points: 10, commands: [] };\n" +
                                "            \n" +
                                "            const pointsElement = rewardElement.querySelector('.points-value');\n" +
                                "            const itemsElement = rewardElement.querySelector('.items-text');\n" +
                                "            \n" +
                                "            if (pointsElement) {\n" +
                                "                pointsElement.textContent = reward.points;\n" +
                                "            }\n" +
                                "            \n" +
                                "            if (itemsElement) {\n" +
                                "                if (reward.commands && reward.commands.length > 0) {\n" +
                                "                    itemsElement.textContent = reward.commands.length + ' 个额外奖励';\n" +
                                "                } else {\n" +
                                "                    itemsElement.textContent = '无额外奖励';\n" +
                                "                }\n" +
                                "            }\n" +
                                "        }\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 编辑日期奖励\n" +
                                "function editDayReward(day) {\n" +
                                "    const reward = dailyRewards[day] || { points: 10, commands: [] };\n" +
                                "    \n" +
                                "    // 创建编辑对话框\n" +
                                "    const modal = document.createElement('div');\n" +
                                "    modal.className = 'reward-edit-modal';\n" +
                                "    modal.innerHTML = `\n" +
                                "        <div class=\"modal-overlay\" onclick=\"closeRewardEditModal()\"></div>\n" +
                                "        <div class=\"modal-content\">\n" +
                                "            <div class=\"modal-header\">\n" +
                                "                <h3>编辑 ${day} 号签到奖励</h3>\n" +
                                "                <button class=\"modal-close\" onclick=\"closeRewardEditModal()\">×</button>\n"
                                +
                                "            </div>\n" +
                                "            <div class=\"modal-body\">\n" +
                                "                <div class=\"form-group\">\n" +
                                "                    <label for=\"rewardPoints\">积分奖励:</label>\n" +
                                "                    <input type=\"number\" id=\"rewardPoints\" value=\"${reward.points}\" min=\"0\" max=\"10000\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"form-group\">\n" +
                                "                    <label for=\"rewardCommands\">额外命令 (每行一个):</label>\n" +
                                "                    <textarea id=\"rewardCommands\" rows=\"5\" placeholder=\"例如:\\ngive {player} diamond 1\\neffect give {player} speed 60 1\">${reward.commands ? reward.commands.join('\\n') : ''}</textarea>\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"form-help\">\n" +
                                "                    <p>💡 提示: 使用 {player} 作为玩家名占位符</p>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "            <div class=\"modal-footer\">\n" +
                                "                <button class=\"btn btn-secondary\" onclick=\"closeRewardEditModal()\">取消</button>\n"
                                +
                                "                <button class=\"btn btn-primary\" onclick=\"saveDayReward(${day})\">保存</button>\n"
                                +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(modal);\n" +
                                "    \n" +
                                "    // 添加样式\n" +
                                "    addRewardEditModalStyles();\n" +
                                "    \n" +
                                "    // 显示动画\n" +
                                "    setTimeout(() => {\n" +
                                "        modal.classList.add('show');\n" +
                                "    }, 10);\n" +
                                "}\n" +
                                "\n" +
                                "// 保存日期奖励\n" +
                                "function saveDayReward(day) {\n" +
                                "    const points = parseInt(document.getElementById('rewardPoints').value) || 0;\n" +
                                "    const commandsText = document.getElementById('rewardCommands').value.trim();\n" +
                                "    const commands = commandsText ? commandsText.split('\\n').filter(cmd => cmd.trim()) : [];\n"
                                +
                                "    \n" +
                                "    const rewardData = {\n" +
                                "        day: day,\n" +
                                "        points: points,\n" +
                                "        commands: commands\n" +
                                "    };\n" +
                                "    \n" +
                                "    fetch('/admin/checkin-rewards', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        },\n" +
                                "        body: JSON.stringify(rewardData)\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            dailyRewards[day] = { points: points, commands: commands };\n" +
                                "            updateDailyRewardCards();\n" +
                                "            closeRewardEditModal();\n" +
                                "            showResult('✅ ' + day + ' 号奖励保存成功！', 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ 保存失败: ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 关闭编辑对话框\n" +
                                "function closeRewardEditModal() {\n" +
                                "    const modal = document.querySelector('.reward-edit-modal');\n" +
                                "    if (modal) {\n" +
                                "        modal.classList.remove('show');\n" +
                                "        setTimeout(() => {\n" +
                                "            modal.remove();\n" +
                                "        }, 300);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 添加编辑对话框样式\n" +
                                "function addRewardEditModalStyles() {\n" +
                                "    if (!document.querySelector('#rewardEditModalStyle')) {\n" +
                                "        const style = document.createElement('style');\n" +
                                "        style.id = 'rewardEditModalStyle';\n" +
                                "        style.textContent = `\n" +
                                "            .reward-edit-modal {\n" +
                                "                position: fixed;\n" +
                                "                top: 0;\n" +
                                "                left: 0;\n" +
                                "                width: 100%;\n" +
                                "                height: 100%;\n" +
                                "                z-index: 10000;\n" +
                                "                opacity: 0;\n" +
                                "                transition: opacity 0.3s ease;\n" +
                                "            }\n" +
                                "            .reward-edit-modal.show {\n" +
                                "                opacity: 1;\n" +
                                "            }\n" +
                                "            .modal-overlay {\n" +
                                "                position: absolute;\n" +
                                "                top: 0;\n" +
                                "                left: 0;\n" +
                                "                width: 100%;\n" +
                                "                height: 100%;\n" +
                                "                background: rgba(0, 0, 0, 0.7);\n" +
                                "                backdrop-filter: blur(5px);\n" +
                                "            }\n" +
                                "            .modal-content {\n" +
                                "                position: absolute;\n" +
                                "                top: 50%;\n" +
                                "                left: 50%;\n" +
                                "                transform: translate(-50%, -50%);\n" +
                                "                background: var(--bg-card);\n" +
                                "                border-radius: 16px;\n" +
                                "                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                                "                min-width: 500px;\n" +
                                "                max-width: 90vw;\n" +
                                "                max-height: 90vh;\n" +
                                "                overflow: hidden;\n" +
                                "            }\n" +
                                "            .modal-header {\n" +
                                "                background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                                "                color: white;\n" +
                                "                padding: 20px;\n" +
                                "                display: flex;\n" +
                                "                justify-content: space-between;\n" +
                                "                align-items: center;\n" +
                                "            }\n" +
                                "            .modal-header h3 {\n" +
                                "                margin: 0;\n" +
                                "                font-size: 20px;\n" +
                                "                font-weight: 600;\n" +
                                "            }\n" +
                                "            .modal-close {\n" +
                                "                background: none;\n" +
                                "                border: none;\n" +
                                "                color: white;\n" +
                                "                font-size: 24px;\n" +
                                "                cursor: pointer;\n" +
                                "                padding: 0;\n" +
                                "                width: 30px;\n" +
                                "                height: 30px;\n" +
                                "                border-radius: 50%;\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                justify-content: center;\n" +
                                "                transition: background 0.2s;\n" +
                                "            }\n" +
                                "            .modal-close:hover {\n" +
                                "                background: rgba(255, 255, 255, 0.2);\n" +
                                "            }\n" +
                                "            .modal-body {\n" +
                                "                padding: 30px;\n" +
                                "            }\n" +
                                "            .form-group {\n" +
                                "                margin-bottom: 20px;\n" +
                                "            }\n" +
                                "            .form-group label {\n" +
                                "                display: block;\n" +
                                "                margin-bottom: 8px;\n" +
                                "                font-weight: 600;\n" +
                                "                color: var(--text-primary);\n" +
                                "            }\n" +
                                "            .form-group input,\n" +
                                "            .form-group textarea {\n" +
                                "                width: 100%;\n" +
                                "                padding: 12px;\n" +
                                "                border: 2px solid var(--border-color);\n" +
                                "                border-radius: 8px;\n" +
                                "                background: var(--bg-secondary);\n" +
                                "                color: var(--text-primary);\n" +
                                "                font-size: 14px;\n" +
                                "                transition: border-color 0.3s;\n" +
                                "                box-sizing: border-box;\n" +
                                "            }\n" +
                                "            .form-group input:focus,\n" +
                                "            .form-group textarea:focus {\n" +
                                "                outline: none;\n" +
                                "                border-color: var(--accent-color);\n" +
                                "            }\n" +
                                "            .form-help {\n" +
                                "                background: var(--bg-secondary);\n" +
                                "                padding: 15px;\n" +
                                "                border-radius: 8px;\n" +
                                "                border-left: 4px solid #4CAF50;\n" +
                                "            }\n" +
                                "            .form-help p {\n" +
                                "                margin: 0;\n" +
                                "                color: var(--text-secondary);\n" +
                                "                font-size: 14px;\n" +
                                "            }\n" +
                                "            .modal-footer {\n" +
                                "                padding: 20px 30px;\n" +
                                "                background: var(--bg-secondary);\n" +
                                "                display: flex;\n" +
                                "                gap: 15px;\n" +
                                "                justify-content: flex-end;\n" +
                                "            }\n" +
                                "            .modal-footer .btn {\n" +
                                "                padding: 12px 24px;\n" +
                                "                border: none;\n" +
                                "                border-radius: 8px;\n" +
                                "                font-size: 14px;\n" +
                                "                font-weight: 600;\n" +
                                "                cursor: pointer;\n" +
                                "                transition: all 0.3s;\n" +
                                "            }\n" +
                                "            .modal-footer .btn-secondary {\n" +
                                "                background: var(--bg-card);\n" +
                                "                color: var(--text-primary);\n" +
                                "                border: 2px solid var(--border-color);\n" +
                                "            }\n" +
                                "            .modal-footer .btn-secondary:hover {\n" +
                                "                background: var(--bg-secondary);\n" +
                                "            }\n" +
                                "            .modal-footer .btn-primary {\n" +
                                "                background: linear-gradient(135deg, #4CAF50, #45a049);\n" +
                                "                color: white;\n" +
                                "            }\n" +
                                "            .modal-footer .btn-primary:hover {\n" +
                                "                background: linear-gradient(135deg, #45a049, #3d8b40);\n" +
                                "                transform: translateY(-2px);\n" +
                                "                box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);\n" +
                                "            }\n" +
                                "        `;\n" +
                                "        document.head.appendChild(style);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// ==================== 签到统计功能 ====================\n" +
                                "// 加载签到统计数据\n" +
                                "function loadCheckInStats() {\n" +
                                "    fetch('/admin/checkin-stats/stats', {\n" +
                                "        method: 'GET',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            updateStatsDisplay(data.stats);\n" +
                                "        } else {\n" +
                                "            console.error('加载签到统计失败:', data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        console.error('网络错误:', error);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 更新统计显示\n" +
                                "function updateStatsDisplay(stats) {\n" +
                                "    const todayEl = document.getElementById('todayCheckIns');\n" +
                                "    const weekEl = document.getElementById('weekCheckIns');\n" +
                                "    const monthEl = document.getElementById('monthCheckIns');\n" +
                                "    const maxEl = document.getElementById('maxConsecutive');\n" +
                                "    \n" +
                                "    if (todayEl) todayEl.textContent = stats.today_checkins || 0;\n" +
                                "    if (weekEl) weekEl.textContent = stats.week_checkins || 0;\n" +
                                "    if (monthEl) monthEl.textContent = stats.month_checkins || 0;\n" +
                                "    if (maxEl) maxEl.textContent = stats.max_consecutive || 0;\n" +
                                "}\n" +
                                "\n" +
                                "// 加载排行榜\n" +
                                "function loadLeaderboard(type = 'consecutive') {\n" +
                                "    fetch(`/admin/checkin-stats/leaderboard?type=${type}`, {\n" +
                                "        method: 'GET',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            updateLeaderboardDisplay(data.leaderboard, data.type);\n" +
                                "        } else {\n" +
                                "            console.error('加载排行榜失败:', data.message);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        console.error('网络错误:', error);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 更新排行榜显示\n" +
                                "function updateLeaderboardDisplay(leaderboard, type) {\n" +
                                "    const listEl = document.getElementById('leaderboardList');\n" +
                                "    if (!listEl) return;\n" +
                                "    \n" +
                                "    if (leaderboard.length === 0) {\n" +
                                "        listEl.innerHTML = '<div class=\"no-data\">暂无数据</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    let html = '<div class=\"leaderboard-cards-grid\">';\n" +
                                "    leaderboard.forEach(item => {\n" +
                                "        const value = type === 'total' ? item.total_days : item.consecutive_days;\n" +
                                "        const typeLabel = type === 'total' ? '总签到' : '连续签到';\n" +
                                "        const icon = type === 'total' ? 'fas fa-calendar-check' : 'fas fa-fire';\n" +
                                "        const rankClass = item.rank <= 3 ? 'top-rank rank-' + item.rank : 'normal-rank';\n"
                                +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-card ${rankClass}\">\n" +
                                "                <div class=\"card-header\">\n" +
                                "                    <div class=\"rank-badge\">\n" +
                                "                        <span class=\"rank-number\">#${item.rank}</span>\n" +
                                "                        ${item.rank <= 3 ? '<i class=\"fas fa-crown rank-crown\"></i>' : ''}\n"
                                +
                                "                    </div>\n" +
                                "                    <div class=\"player-avatar\">\n" +
                                "                        <img src=\"https://minotar.net/avatar/${item.player}/40\" alt=\"${item.player}\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='flex';\">\n"
                                +
                                "                        <div class=\"avatar-fallback\" style=\"display:none;\"><i class=\"fas fa-user\"></i></div>\n"
                                +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "                <div class=\"card-body\">\n" +
                                "                    <div class=\"player-name\">${item.player}</div>\n" +
                                "                    <div class=\"stats-info\">\n" +
                                "                        <div class=\"main-stat\">\n" +
                                "                            <i class=\"${icon}\"></i>\n" +
                                "                            <span class=\"stat-value\">${value}</span>\n" +
                                "                            <span class=\"stat-unit\">天</span>\n" +
                                "                        </div>\n" +
                                "                        <div class=\"stat-label\">${typeLabel}</div>\n" +
                                "                    </div>\n" +
                                "                    <div class=\"additional-stats\">\n" +
                                "                        <div class=\"stat-item\">\n" +
                                "                            <i class=\"fas fa-calendar-day\"></i>\n" +
                                "                            <span>连续: ${item.consecutive_days}天</span>\n" +
                                "                        </div>\n" +
                                "                        <div class=\"stat-item\">\n" +
                                "                            <i class=\"fas fa-chart-line\"></i>\n" +
                                "                            <span>总计: ${item.total_days}天</span>\n" +
                                "                        </div>\n" +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "                <div class=\"card-footer\">\n" +
                                "                    <div class=\"last-checkin\">\n" +
                                "                        <i class=\"fas fa-clock\"></i>\n" +
                                "                        <span>最后签到: ${item.last_checkin}</span>\n" +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    });\n" +
                                "    html += '</div>';\n" +
                                "    \n" +
                                "    listEl.innerHTML = html;\n" +
                                "}\n" +
                                "\n" +
                                "// 切换排行榜类型\n" +
                                "function switchLeaderboard(type) {\n" +
                                "    // 更新按钮状态\n" +
                                "    document.querySelectorAll('.leaderboard-tabs .tab-btn').forEach(btn => {\n" +
                                "        btn.classList.remove('active');\n" +
                                "    });\n" +
                                "    event.target.classList.add('active');\n" +
                                "    \n" +
                                "    // 加载对应类型的排行榜\n" +
                                "    loadLeaderboard(type);\n" +
                                "}\n" +
                                "\n" +
                                "// 切换签到管理标签页\n" +
                                "function switchCheckinTab(tabName) {\n" +
                                "    // 更新当前活跃标签页\n" +
                                "    currentActiveTab = tabName;\n" +
                                "    \n" +
                                "    // 隐藏所有标签页内容\n" +
                                "    document.querySelectorAll('.checkin-tab-content').forEach(tab => {\n" +
                                "        tab.classList.remove('active');\n" +
                                "    });\n" +
                                "    \n" +
                                "    // 移除所有导航标签的激活状态\n" +
                                "    document.querySelectorAll('.checkin-nav-tabs .nav-tab').forEach(btn => {\n" +
                                "        btn.classList.remove('active');\n" +
                                "    });\n" +
                                "    \n" +
                                "    // 显示选中的标签页\n" +
                                "    const targetTab = document.getElementById(tabName + '-tab');\n" +
                                "    if (targetTab) {\n" +
                                "        targetTab.classList.add('active');\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 激活对应的导航标签\n" +
                                "    event.target.classList.add('active');\n" +
                                "    \n" +
                                "    // 根据标签页类型加载对应数据\n" +
                                "    switch(tabName) {\n" +
                                "        case 'rewards':\n" +
                                "            if (document.getElementById('dailyCardsGrid')) {\n" +
                                "                loadDailyRewards();\n" +
                                "            }\n" +
                                "            break;\n" +
                                "        case 'statistics':\n" +
                                "            loadCheckInStats();\n" +
                                "            break;\n" +
                                "        case 'leaderboard':\n" +
                                "            loadLeaderboard('consecutive');\n" +
                                "            break;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 添加签到管理标签页样式\n" +
                                "function addCheckinTabStyles() {\n" +
                                "    if (!document.querySelector('#checkinTabStyles')) {\n" +
                                "        const style = document.createElement('style');\n" +
                                "        style.id = 'checkinTabStyles';\n" +
                                "        style.textContent = `\n" +
                                "            .checkin-nav-tabs {\n" +
                                "                display: flex;\n" +
                                "                gap: 8px;\n" +
                                "                margin-bottom: 24px;\n" +
                                "                border-bottom: 2px solid #e2e8f0;\n" +
                                "                padding-bottom: 16px;\n" +
                                "            }\n" +
                                "            .checkin-nav-tabs .nav-tab {\n" +
                                "                background: #f7fafc;\n" +
                                "                border: 2px solid #e2e8f0;\n" +
                                "                color: #4a5568;\n" +
                                "                padding: 12px 20px;\n" +
                                "                border-radius: 8px;\n" +
                                "                font-size: 14px;\n" +
                                "                font-weight: 500;\n" +
                                "                cursor: pointer;\n" +
                                "                transition: all 0.3s ease;\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                gap: 8px;\n" +
                                "            }\n" +
                                "            .checkin-nav-tabs .nav-tab:hover {\n" +
                                "                background: #edf2f7;\n" +
                                "                border-color: #cbd5e0;\n" +
                                "                transform: translateY(-1px);\n" +
                                "            }\n" +
                                "            .checkin-nav-tabs .nav-tab.active {\n" +
                                "                background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                                "                color: white;\n" +
                                "                border-color: #667eea;\n" +
                                "                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n" +
                                "            }\n" +
                                "            .checkin-tab-contents {\n" +
                                "                position: relative;\n" +
                                "            }\n" +
                                "            .checkin-tab-content {\n" +
                                "                display: none;\n" +
                                "                animation: fadeIn 0.3s ease;\n" +
                                "            }\n" +
                                "            .checkin-tab-content.active {\n" +
                                "                display: block;\n" +
                                "            }\n" +
                                "            .page-section-header {\n" +
                                "                margin-bottom: 24px;\n" +
                                "                text-align: center;\n" +
                                "            }\n" +
                                "            .page-section-header h2 {\n" +
                                "                margin: 0 0 8px 0;\n" +
                                "                color: #2d3748;\n" +
                                "                font-size: 24px;\n" +
                                "                font-weight: 600;\n" +
                                "            }\n" +
                                "            .page-section-header p {\n" +
                                "                margin: 0;\n" +
                                "                color: #718096;\n" +
                                "                font-size: 16px;\n" +
                                "            }\n" +
                                "            .refresh-section {\n" +
                                "                text-align: center;\n" +
                                "                margin-top: 32px;\n" +
                                "            }\n" +
                                "            .leaderboard-controls {\n" +
                                "                display: flex;\n" +
                                "                justify-content: space-between;\n" +
                                "                align-items: center;\n" +
                                "                margin-bottom: 24px;\n" +
                                "                flex-wrap: wrap;\n" +
                                "                gap: 16px;\n" +
                                "            }\n" +
                                "            .leaderboard-container {\n" +
                                "                background: white;\n" +
                                "                border-radius: 12px;\n" +
                                "                box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n" +
                                "                overflow: hidden;\n" +
                                "            }\n" +
                                "            .loading-placeholder {\n" +
                                "                text-align: center;\n" +
                                "                padding: 40px;\n" +
                                "                color: #718096;\n" +
                                "                font-size: 16px;\n" +
                                "            }\n" +
                                "            .loading-placeholder i {\n" +
                                "                margin-right: 8px;\n" +
                                "                color: #667eea;\n" +
                                "            }\n" +
                                "            .leaderboard-cards-grid {\n" +
                                "                display: grid;\n" +
                                "                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));\n" +
                                "                gap: 20px;\n" +
                                "                padding: 20px;\n" +
                                "            }\n" +
                                "            .leaderboard-card {\n" +
                                "                background: white;\n" +
                                "                border-radius: 16px;\n" +
                                "                box-shadow: 0 4px 12px rgba(0,0,0,0.1);\n" +
                                "                overflow: hidden;\n" +
                                "                transition: all 0.3s ease;\n" +
                                "                border: 2px solid transparent;\n" +
                                "            }\n" +
                                "            .leaderboard-card:hover {\n" +
                                "                transform: translateY(-4px);\n" +
                                "                box-shadow: 0 8px 25px rgba(0,0,0,0.15);\n" +
                                "            }\n" +
                                "            .leaderboard-card.top-rank {\n" +
                                "                border-color: #ffd700;\n" +
                                "                background: linear-gradient(135deg, #fff9e6, #ffffff);\n" +
                                "            }\n" +
                                "            .leaderboard-card.rank-1 {\n" +
                                "                border-color: #ffd700;\n" +
                                "                background: linear-gradient(135deg, #fff9e6, #ffffff);\n" +
                                "                box-shadow: 0 4px 20px rgba(255, 215, 0, 0.3);\n" +
                                "            }\n" +
                                "            .leaderboard-card.rank-2 {\n" +
                                "                border-color: #c0c0c0;\n" +
                                "                background: linear-gradient(135deg, #f8f9fa, #ffffff);\n" +
                                "                box-shadow: 0 4px 20px rgba(192, 192, 192, 0.3);\n" +
                                "            }\n" +
                                "            .leaderboard-card.rank-3 {\n" +
                                "                border-color: #cd7f32;\n" +
                                "                background: linear-gradient(135deg, #fdf2e9, #ffffff);\n" +
                                "                box-shadow: 0 4px 20px rgba(205, 127, 50, 0.3);\n" +
                                "            }\n" +
                                "            .card-header {\n" +
                                "                background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                                "                color: white;\n" +
                                "                padding: 16px 20px;\n" +
                                "                display: flex;\n" +
                                "                justify-content: space-between;\n" +
                                "                align-items: center;\n" +
                                "            }\n" +
                                "            .rank-badge {\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                gap: 8px;\n" +
                                "            }\n" +
                                "            .rank-number {\n" +
                                "                font-size: 18px;\n" +
                                "                font-weight: bold;\n" +
                                "            }\n" +
                                "            .rank-crown {\n" +
                                "                color: #ffd700;\n" +
                                "                font-size: 16px;\n" +
                                "                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));\n" +
                                "            }\n" +
                                "            .player-avatar {\n" +
                                "                width: 40px;\n" +
                                "                height: 40px;\n" +
                                "                background: rgba(255,255,255,0.2);\n" +
                                "                border-radius: 8px;\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                justify-content: center;\n" +
                                "                font-size: 18px;\n" +
                                "                overflow: hidden;\n" +
                                "                position: relative;\n" +
                                "                flex-shrink: 0;\n" +
                                "            }\n" +
                                "            .player-avatar img {\n" +
                                "                width: 40px;\n" +
                                "                height: 40px;\n" +
                                "                object-fit: cover;\n" +
                                "                border-radius: 8px;\n" +
                                "                display: block;\n" +
                                "            }\n" +
                                "            .avatar-fallback {\n" +
                                "                width: 40px;\n" +
                                "                height: 40px;\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                justify-content: center;\n" +
                                "                background: rgba(255,255,255,0.2);\n" +
                                "                border-radius: 8px;\n" +
                                "                color: white;\n" +
                                "                font-size: 18px;\n" +
                                "                position: absolute;\n" +
                                "                top: 0;\n" +
                                "                left: 0;\n" +
                                "            }\n" +
                                "            .card-body {\n" +
                                "                padding: 20px;\n" +
                                "            }\n" +
                                "            .player-name {\n" +
                                "                font-size: 18px;\n" +
                                "                font-weight: 600;\n" +
                                "                color: #2d3748;\n" +
                                "                margin-bottom: 16px;\n" +
                                "                text-align: center;\n" +
                                "            }\n" +
                                "            .stats-info {\n" +
                                "                text-align: center;\n" +
                                "                margin-bottom: 16px;\n" +
                                "            }\n" +
                                "            .main-stat {\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                justify-content: center;\n" +
                                "                gap: 8px;\n" +
                                "                margin-bottom: 8px;\n" +
                                "            }\n" +
                                "            .main-stat i {\n" +
                                "                color: #667eea;\n" +
                                "                font-size: 20px;\n" +
                                "            }\n" +
                                "            .stat-value {\n" +
                                "                font-size: 24px;\n" +
                                "                font-weight: bold;\n" +
                                "                color: #2d3748;\n" +
                                "            }\n" +
                                "            .stat-unit {\n" +
                                "                font-size: 16px;\n" +
                                "                color: #718096;\n" +
                                "            }\n" +
                                "            .stat-label {\n" +
                                "                font-size: 14px;\n" +
                                "                color: #718096;\n" +
                                "                font-weight: 500;\n" +
                                "            }\n" +
                                "            .additional-stats {\n" +
                                "                display: flex;\n" +
                                "                justify-content: space-between;\n" +
                                "                gap: 12px;\n" +
                                "            }\n" +
                                "            .stat-item {\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                gap: 6px;\n" +
                                "                font-size: 12px;\n" +
                                "                color: #718096;\n" +
                                "                background: #f7fafc;\n" +
                                "                padding: 6px 10px;\n" +
                                "                border-radius: 6px;\n" +
                                "                flex: 1;\n" +
                                "            }\n" +
                                "            .stat-item i {\n" +
                                "                color: #667eea;\n" +
                                "                font-size: 12px;\n" +
                                "            }\n" +
                                "            .card-footer {\n" +
                                "                background: #f7fafc;\n" +
                                "                padding: 12px 20px;\n" +
                                "                border-top: 1px solid #e2e8f0;\n" +
                                "            }\n" +
                                "            .last-checkin {\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                gap: 8px;\n" +
                                "                font-size: 12px;\n" +
                                "                color: #718096;\n" +
                                "                justify-content: center;\n" +
                                "            }\n" +
                                "            .last-checkin i {\n" +
                                "                color: #667eea;\n" +
                                "            }\n" +
                                "        `;\n" +
                                "        document.head.appendChild(style);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// ==================== 签到数据自动更新功能 ====================\n" +
                                "let checkinUpdateInterval = null;\n" +
                                "let currentActiveTab = 'rewards';\n" +
                                "\n" +
                                "// 启动签到数据自动更新\n" +
                                "function startCheckinAutoUpdate() {\n" +
                                "    // 清除现有的定时器\n" +
                                "    if (checkinUpdateInterval) {\n" +
                                "        clearInterval(checkinUpdateInterval);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 设置1秒自动更新\n" +
                                "    checkinUpdateInterval = setInterval(() => {\n" +
                                "        updateCheckinDataIfNeeded();\n" +
                                "    }, 1000); // 1秒更新一次\n" +
                                "    \n" +
                                "    console.log('签到数据自动更新已启动 (1秒间隔)');\n" +
                                "}\n" +
                                "\n" +
                                "// 停止签到数据自动更新\n" +
                                "function stopCheckinAutoUpdate() {\n" +
                                "    if (checkinUpdateInterval) {\n" +
                                "        clearInterval(checkinUpdateInterval);\n" +
                                "        checkinUpdateInterval = null;\n" +
                                "        console.log('签到数据自动更新已停止');\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 根据当前活跃标签页更新数据\n" +
                                "function updateCheckinDataIfNeeded() {\n" +
                                "    // 检查是否在签到管理页面\n" +
                                "    if (!document.querySelector('.checkin-nav-tabs')) {\n" +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 根据当前活跃的标签页更新对应数据\n" +
                                "    switch(currentActiveTab) {\n" +
                                "        case 'statistics':\n" +
                                "            // 静默更新统计数据（不显示加载提示）\n" +
                                "            loadCheckInStatsQuietly();\n" +
                                "            break;\n" +
                                "        case 'leaderboard':\n" +
                                "            // 静默更新排行榜数据\n" +
                                "            const activeLeaderboardType = getActiveLeaderboardType();\n" +
                                "            loadLeaderboardQuietly(activeLeaderboardType);\n" +
                                "            break;\n" +
                                "        case 'rewards':\n" +
                                "            // 奖励配置页面不需要自动更新\n" +
                                "            break;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 获取当前活跃的排行榜类型\n" +
                                "function getActiveLeaderboardType() {\n" +
                                "    const activeBtn = document.querySelector('.leaderboard-tabs .tab-btn.active');\n" +
                                "    if (activeBtn && activeBtn.textContent.includes('总签到')) {\n" +
                                "        return 'total';\n" +
                                "    }\n" +
                                "    return 'consecutive'; // 默认连续签到\n" +
                                "}\n" +
                                "\n" +
                                "// 静默加载签到统计（不显示加载状态）\n" +
                                "function loadCheckInStatsQuietly() {\n" +
                                "    fetch('/admin/checkin-stats/stats', {\n" +
                                "        method: 'GET',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success && data.stats) {\n" +
                                "            updateStatsDisplayQuietly(data.stats);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        // 静默处理错误，不显示错误信息\n" +
                                "        console.log('自动更新统计数据失败:', error);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 静默更新统计显示\n" +
                                "function updateStatsDisplayQuietly(stats) {\n" +
                                "    const elements = {\n" +
                                "        'todayCheckIns': stats.today_checkins,\n" +
                                "        'weekCheckIns': stats.week_checkins,\n" +
                                "        'monthCheckIns': stats.month_checkins,\n" +
                                "        'maxConsecutive': stats.max_consecutive\n" +
                                "    };\n" +
                                "    \n" +
                                "    Object.entries(elements).forEach(([id, value]) => {\n" +
                                "        const element = document.getElementById(id);\n" +
                                "        if (element && element.textContent !== value.toString()) {\n" +
                                "            // 添加更新动画效果\n" +
                                "            element.style.transition = 'all 0.3s ease';\n" +
                                "            element.style.transform = 'scale(1.1)';\n" +
                                "            element.style.color = '#667eea';\n" +
                                "            \n" +
                                "            setTimeout(() => {\n" +
                                "                element.textContent = value;\n" +
                                "                element.style.transform = 'scale(1)';\n" +
                                "                element.style.color = '';\n" +
                                "            }, 150);\n" +
                                "        }\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 静默加载排行榜（不显示加载状态）\n" +
                                "function loadLeaderboardQuietly(type = 'consecutive') {\n" +
                                "    fetch(`/admin/checkin-stats/leaderboard?type=${type}`, {\n" +
                                "        method: 'GET',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success && data.leaderboard) {\n" +
                                "            updateLeaderboardDisplayQuietly(data.leaderboard, type);\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        // 静默处理错误，不显示错误信息\n" +
                                "        console.log('自动更新排行榜失败:', error);\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 静默更新排行榜显示\n" +
                                "function updateLeaderboardDisplayQuietly(leaderboard, type) {\n" +
                                "    const listEl = document.getElementById('leaderboardList');\n" +
                                "    if (!listEl) return;\n" +
                                "    \n" +
                                "    // 检查数据是否有变化\n" +
                                "    const currentCards = listEl.querySelectorAll('.leaderboard-card');\n" +
                                "    if (currentCards.length === leaderboard.length) {\n" +
                                "        let hasChanges = false;\n" +
                                "        leaderboard.forEach((item, index) => {\n" +
                                "            const card = currentCards[index];\n" +
                                "            if (card) {\n" +
                                "                const playerName = card.querySelector('.player-name')?.textContent;\n"
                                +
                                "                const statValue = card.querySelector('.stat-value')?.textContent;\n" +
                                "                const currentValue = type === 'total' ? item.total_days : item.consecutive_days;\n"
                                +
                                "                \n" +
                                "                if (playerName !== item.player || parseInt(statValue) !== currentValue) {\n"
                                +
                                "                    hasChanges = true;\n" +
                                "                }\n" +
                                "            }\n" +
                                "        });\n" +
                                "        \n" +
                                "        if (!hasChanges) {\n" +
                                "            return; // 没有变化，不需要更新\n" +
                                "        }\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 有变化时才更新，添加淡入效果\n" +
                                "    listEl.style.opacity = '0.7';\n" +
                                "    listEl.style.transition = 'opacity 0.3s ease';\n" +
                                "    \n" +
                                "    setTimeout(() => {\n" +
                                "        updateLeaderboardDisplay(leaderboard, type);\n" +
                                "        listEl.style.opacity = '1';\n" +
                                "    }, 150);\n" +
                                "}\n" +
                                "\n" +
                                "// ==================== 补签卡商店设置功能 ====================\n" +
                                "// 保存补签卡设置\n" +
                                "function saveMakeupCardSettings() {\n" +
                                "    const settings = {\n" +
                                "        gold: {\n" +
                                "            price: parseInt(document.getElementById('goldPrice').value) || 100,\n" +
                                "            currency_name: document.getElementById('goldCurrencyName').value || '金币',\n"
                                +
                                "            description: document.getElementById('goldDescription').value || '使用游戏内金币购买',\n"
                                +
                                "            balance_variable: document.getElementById('goldBalanceVariable').value || '%vault_eco_balance%',\n"
                                +
                                "            deduct_command: document.getElementById('goldDeductCommand').value || 'eco take {player} {amount}',\n"
                                +
                                "            enabled: document.getElementById('goldEnabled').checked\n" +
                                "        },\n" +
                                "        points: {\n" +
                                "            price: parseInt(document.getElementById('pointsPrice').value) || 50,\n" +
                                "            currency_name: document.getElementById('pointsCurrencyName').value || '点券',\n"
                                +
                                "            description: document.getElementById('pointsDescription').value || '使用点券购买',\n"
                                +
                                "            balance_variable: document.getElementById('pointsBalanceVariable').value || '%playerpoints_points%',\n"
                                +
                                "            deduct_command: document.getElementById('pointsDeductCommand').value || 'pp take {player} {amount}',\n"
                                +
                                "            enabled: document.getElementById('pointsEnabled').checked\n" +
                                "        },\n" +
                                "        score: {\n" +
                                "            price: parseInt(document.getElementById('scorePrice').value) || 200,\n" +
                                "            currency_name: document.getElementById('scoreCurrencyName').value || '积分',\n"
                                +
                                "            description: document.getElementById('scoreDescription').value || '使用积分商店积分购买',\n"
                                +
                                "            balance_variable: 'internal',\n" +
                                "            deduct_command: 'internal',\n" +
                                "            enabled: document.getElementById('scoreEnabled').checked\n" +
                                "        },\n" +
                                "        custom: {\n" +
                                "            price: parseInt(document.getElementById('customPrice').value) || 150,\n" +
                                "            currency_name: document.getElementById('customCurrencyName').value || '自定义货币',\n"
                                +
                                "            description: document.getElementById('customDescription').value || '使用自定义货币购买',\n"
                                +
                                "            balance_variable: document.getElementById('customBalanceVariable').value || '%custom_balance%',\n"
                                +
                                "            deduct_command: document.getElementById('customDeductCommand').value || 'customcurrency take {player} {amount}',\n"
                                +
                                "            enabled: document.getElementById('customEnabled').checked\n" +
                                "        }\n" +
                                "    };\n" +
                                "    \n" +
                                "    fetch('/admin/makeup-card-settings', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        },\n" +
                                "        body: JSON.stringify(settings)\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            showResult('✅ 补签卡设置保存成功！', 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ 保存失败: ' + data.message, 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 加载补签卡设置\n" +
                                "function loadMakeupCardSettings() {\n" +
                                "    fetch('/admin/makeup-card-settings', {\n" +
                                "        method: 'GET',\n" +
                                "        headers: {\n" +
                                "            'Content-Type': 'application/json'\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success && data.settings) {\n" +
                                "            const settings = data.settings;\n" +
                                "            \n" +
                                "            // 更新金币设置\n" +
                                "            if (settings.gold) {\n" +
                                "                document.getElementById('goldPrice').value = settings.gold.price || 100;\n"
                                +
                                "                document.getElementById('goldCurrencyName').value = settings.gold.currency_name || '金币';\n"
                                +
                                "                document.getElementById('goldDescription').value = settings.gold.description || '使用游戏内金币购买';\n"
                                +
                                "                document.getElementById('goldBalanceVariable').value = settings.gold.balance_variable || '%vault_eco_balance%';\n"
                                +
                                "                document.getElementById('goldDeductCommand').value = settings.gold.deduct_command || 'eco take {player} {amount}';\n"
                                +
                                "                document.getElementById('goldEnabled').checked = settings.gold.enabled !== false;\n"
                                +
                                "            }\n" +
                                "            \n" +
                                "            // 更新点券设置\n" +
                                "            if (settings.points) {\n" +
                                "                document.getElementById('pointsPrice').value = settings.points.price || 50;\n"
                                +
                                "                document.getElementById('pointsCurrencyName').value = settings.points.currency_name || '点券';\n"
                                +
                                "                document.getElementById('pointsDescription').value = settings.points.description || '使用点券购买';\n"
                                +
                                "                document.getElementById('pointsBalanceVariable').value = settings.points.balance_variable || '%playerpoints_points%';\n"
                                +
                                "                document.getElementById('pointsDeductCommand').value = settings.points.deduct_command || 'pp take {player} {amount}';\n"
                                +
                                "                document.getElementById('pointsEnabled').checked = settings.points.enabled !== false;\n"
                                +
                                "            }\n" +
                                "            \n" +
                                "            // 更新积分设置\n" +
                                "            if (settings.score) {\n" +
                                "                document.getElementById('scorePrice').value = settings.score.price || 200;\n"
                                +
                                "                document.getElementById('scoreCurrencyName').value = settings.score.currency_name || '积分';\n"
                                +
                                "                document.getElementById('scoreDescription').value = settings.score.description || '使用积分商店积分购买';\n"
                                +
                                "                document.getElementById('scoreEnabled').checked = settings.score.enabled !== false;\n"
                                +
                                "            }\n" +
                                "            \n" +
                                "            // 更新自定义设置\n" +
                                "            if (settings.custom) {\n" +
                                "                document.getElementById('customPrice').value = settings.custom.price || 150;\n"
                                +
                                "                document.getElementById('customCurrencyName').value = settings.custom.currency_name || '自定义货币';\n"
                                +
                                "                document.getElementById('customDescription').value = settings.custom.description || '使用自定义货币购买';\n"
                                +
                                "                document.getElementById('customBalanceVariable').value = settings.custom.balance_variable || '%custom_balance%';\n"
                                +
                                "                document.getElementById('customDeductCommand').value = settings.custom.deduct_command || 'customcurrency take {player} {amount}';\n"
                                +
                                "                document.getElementById('customEnabled').checked = settings.custom.enabled !== false;\n"
                                +
                                "            }\n" +
                                "            \n" +
                                "            showResult('✅ 设置加载成功！', 'success');\n" +
                                "        } else {\n" +
                                "            showResult('❌ 加载失败: ' + (data.message || '未知错误'), 'error');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        showResult('❌ 网络错误: ' + error.message, 'error');\n" +
                                "    });\n" +
                                "}\n";
        }

        /**
         * 生成统计分析页面
         */
        private String generateStatisticsPage() {
                StatisticsPageGenerator generator = new StatisticsPageGenerator(webServer.getAdminKey());
                return generator.generateStatisticsPage();
        }

        /**
         * 生成在线用户管理页面
         */
        private String generateOnlineUsersPage() {
                return "<div class=\"content-section\">\n" +
                                "    <div class=\"section-header\">\n" +
                                "        <h2>🌐 在线用户管理</h2>\n" +
                                "        <p>管理当前在线的管理员用户</p>\n" +
                                "    </div>\n" +
                                "    \n" +
                                "    <div class=\"online-users-container\">\n" +
                                "        <div class=\"stats-row\">\n" +
                                "            <div class=\"stat-card\">\n" +
                                "                <div class=\"stat-icon\">👥</div>\n" +
                                "                <div class=\"stat-info\">\n" +
                                "                    <div class=\"stat-value\" id=\"totalOnlineUsers\">-</div>\n" +
                                "                    <div class=\"stat-label\">在线用户</div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "            <div class=\"stat-card\">\n" +
                                "                <div class=\"stat-icon\">🕐</div>\n" +
                                "                <div class=\"stat-info\">\n" +
                                "                    <div class=\"stat-value\" id=\"lastUpdateTime\">-</div>\n" +
                                "                    <div class=\"stat-label\">最后更新</div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        </div>\n" +
                                "        \n" +
                                "        <div class=\"users-table-container\">\n" +
                                "            <div class=\"table-header\">\n" +
                                "                <h3>在线用户列表</h3>\n" +
                                "                <button class=\"btn btn-primary\" onclick=\"refreshOnlineUsers()\">🔄 刷新</button>\n"
                                +
                                "            </div>\n" +
                                "            \n" +
                                "            <div class=\"table-wrapper\">\n" +
                                "                <table class=\"data-table\" id=\"onlineUsersTable\">\n" +
                                "                    <thead>\n" +
                                "                        <tr>\n" +
                                "                            <th>用户名</th>\n" +
                                "                            <th>IP地址</th>\n" +
                                "                            <th>登录时间</th>\n" +
                                "                            <th>在线时长</th>\n" +
                                "                            <th>最后活动</th>\n" +
                                "                            <th>状态</th>\n" +
                                "                            <th>操作</th>\n" +
                                "                        </tr>\n" +
                                "                    </thead>\n" +
                                "                    <tbody id=\"onlineUsersTableBody\">\n" +
                                "                        <tr>\n" +
                                "                            <td colspan=\"7\" class=\"loading-cell\">🔄 加载中...</td>\n"
                                +
                                "                        </tr>\n" +
                                "                    </tbody>\n" +
                                "                </table>\n" +
                                "            </div>\n" +
                                "        </div>\n" +
                                "    </div>\n" +
                                "</div>\n" +
                                "\n" +
                                "<style>\n" +
                                ".online-users-container {\n" +
                                "    max-width: 1200px;\n" +
                                "    margin: 0 auto;\n" +
                                "}\n" +
                                "\n" +
                                ".stats-row {\n" +
                                "    display: flex;\n" +
                                "    gap: 20px;\n" +
                                "    margin-bottom: 30px;\n" +
                                "}\n" +
                                "\n" +
                                ".stat-card {\n" +
                                "    background: white;\n" +
                                "    border-radius: 12px;\n" +
                                "    padding: 20px;\n" +
                                "    box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n" +
                                "    display: flex;\n" +
                                "    align-items: center;\n" +
                                "    gap: 15px;\n" +
                                "    flex: 1;\n" +
                                "    transition: transform 0.2s ease;\n" +
                                "}\n" +
                                "\n" +
                                ".stat-card:hover {\n" +
                                "    transform: translateY(-2px);\n" +
                                "}\n" +
                                "\n" +
                                ".stat-icon {\n" +
                                "    font-size: 2.5em;\n" +
                                "    opacity: 0.8;\n" +
                                "}\n" +
                                "\n" +
                                ".stat-value {\n" +
                                "    font-size: 2em;\n" +
                                "    font-weight: bold;\n" +
                                "    color: #2d3748;\n" +
                                "    margin-bottom: 5px;\n" +
                                "}\n" +
                                "\n" +
                                ".stat-label {\n" +
                                "    color: #718096;\n" +
                                "    font-size: 0.9em;\n" +
                                "}\n" +
                                "\n" +
                                ".users-table-container {\n" +
                                "    background: white;\n" +
                                "    border-radius: 12px;\n" +
                                "    box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n" +
                                "    overflow: hidden;\n" +
                                "}\n" +
                                "\n" +
                                ".table-header {\n" +
                                "    padding: 20px;\n" +
                                "    border-bottom: 1px solid #e2e8f0;\n" +
                                "    display: flex;\n" +
                                "    justify-content: space-between;\n" +
                                "    align-items: center;\n" +
                                "}\n" +
                                "\n" +
                                ".table-header h3 {\n" +
                                "    margin: 0;\n" +
                                "    color: #2d3748;\n" +
                                "}\n" +
                                "\n" +
                                ".table-wrapper {\n" +
                                "    overflow-x: auto;\n" +
                                "}\n" +
                                "\n" +
                                ".data-table {\n" +
                                "    width: 100%;\n" +
                                "    border-collapse: collapse;\n" +
                                "}\n" +
                                "\n" +
                                ".data-table th,\n" +
                                ".data-table td {\n" +
                                "    padding: 12px 15px;\n" +
                                "    text-align: left;\n" +
                                "    border-bottom: 1px solid #e2e8f0;\n" +
                                "}\n" +
                                "\n" +
                                ".data-table th {\n" +
                                "    background: #f7fafc;\n" +
                                "    font-weight: 600;\n" +
                                "    color: #4a5568;\n" +
                                "    font-size: 0.9em;\n" +
                                "}\n" +
                                "\n" +
                                ".data-table tbody tr:hover {\n" +
                                "    background: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                ".loading-cell {\n" +
                                "    text-align: center;\n" +
                                "    color: #718096;\n" +
                                "    font-style: italic;\n" +
                                "}\n" +
                                "\n" +
                                ".status-badge {\n" +
                                "    padding: 4px 8px;\n" +
                                "    border-radius: 12px;\n" +
                                "    font-size: 0.8em;\n" +
                                "    font-weight: 500;\n" +
                                "}\n" +
                                "\n" +
                                ".status-current {\n" +
                                "    background: #c6f6d5;\n" +
                                "    color: #22543d;\n" +
                                "}\n" +
                                "\n" +
                                ".status-other {\n" +
                                "    background: #bee3f8;\n" +
                                "    color: #2a4365;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-kick {\n" +
                                "    background: #fed7d7;\n" +
                                "    color: #c53030;\n" +
                                "    border: none;\n" +
                                "    padding: 6px 12px;\n" +
                                "    border-radius: 6px;\n" +
                                "    font-size: 0.8em;\n" +
                                "    cursor: pointer;\n" +
                                "    transition: all 0.2s ease;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-kick:hover {\n" +
                                "    background: #fc8181;\n" +
                                "    color: white;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-kick:disabled {\n" +
                                "    background: #e2e8f0;\n" +
                                "    color: #a0aec0;\n" +
                                "    cursor: not-allowed;\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题 */\n" +
                                "body.theme-dark .stat-card {\n" +
                                "    background: #2d3748;\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .stat-value {\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .users-table-container {\n" +
                                "    background: #2d3748;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .table-header {\n" +
                                "    border-bottom-color: #4a5568;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .table-header h3 {\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .data-table th {\n" +
                                "    background: #4a5568;\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .data-table td {\n" +
                                "    border-bottom-color: #4a5568;\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .data-table tbody tr:hover {\n" +
                                "    background: #4a5568;\n" +
                                "}\n" +
                                "\n" +
                                "/* 自定义确认对话框样式 */\n" +
                                ".custom-confirm-overlay {\n" +
                                "    position: fixed;\n" +
                                "    top: 0;\n" +
                                "    left: 0;\n" +
                                "    width: 100%;\n" +
                                "    height: 100%;\n" +
                                "    background: rgba(0, 0, 0, 0.6);\n" +
                                "    backdrop-filter: blur(4px);\n" +
                                "    display: flex;\n" +
                                "    align-items: center;\n" +
                                "    justify-content: center;\n" +
                                "    z-index: 10000;\n" +
                                "    opacity: 0;\n" +
                                "    transition: all 0.3s ease;\n" +
                                "}\n" +
                                "\n" +
                                ".custom-confirm-overlay.show {\n" +
                                "    opacity: 1;\n" +
                                "}\n" +
                                "\n" +
                                ".custom-confirm-dialog {\n" +
                                "    background: white;\n" +
                                "    border-radius: 16px;\n" +
                                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                                "    min-width: 400px;\n" +
                                "    max-width: 500px;\n" +
                                "    transform: scale(0.8) translateY(-20px);\n" +
                                "    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);\n" +
                                "    overflow: hidden;\n" +
                                "}\n" +
                                "\n" +
                                ".custom-confirm-dialog.show {\n" +
                                "    transform: scale(1) translateY(0);\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-header {\n" +
                                "    padding: 24px 24px 16px;\n" +
                                "    text-align: center;\n" +
                                "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                                "    color: white;\n" +
                                "    position: relative;\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-header::before {\n" +
                                "    content: '';\n" +
                                "    position: absolute;\n" +
                                "    top: 0;\n" +
                                "    left: 0;\n" +
                                "    right: 0;\n" +
                                "    bottom: 0;\n" +
                                "    background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"%23ffffff\" opacity=\"0.15\"/><circle cx=\"10\" cy=\"60\" r=\"0.5\" fill=\"%23ffffff\" opacity=\"0.15\"/><circle cx=\"90\" cy=\"40\" r=\"0.5\" fill=\"%23ffffff\" opacity=\"0.15\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n"
                                +
                                "    pointer-events: none;\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-icon {\n" +
                                "    font-size: 3em;\n" +
                                "    margin-bottom: 8px;\n" +
                                "    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));\n" +
                                "    position: relative;\n" +
                                "    z-index: 1;\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-title {\n" +
                                "    margin: 0;\n" +
                                "    font-size: 1.4em;\n" +
                                "    font-weight: 600;\n" +
                                "    text-shadow: 0 1px 2px rgba(0,0,0,0.1);\n" +
                                "    position: relative;\n" +
                                "    z-index: 1;\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-body {\n" +
                                "    padding: 24px;\n" +
                                "    text-align: center;\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-message {\n" +
                                "    margin: 0;\n" +
                                "    font-size: 1.1em;\n" +
                                "    color: #4a5568;\n" +
                                "    line-height: 1.6;\n" +
                                "}\n" +
                                "\n" +
                                ".confirm-footer {\n" +
                                "    padding: 16px 24px 24px;\n" +
                                "    display: flex;\n" +
                                "    gap: 12px;\n" +
                                "    justify-content: center;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-confirm-cancel,\n" +
                                ".btn-confirm-ok {\n" +
                                "    padding: 12px 24px;\n" +
                                "    border: none;\n" +
                                "    border-radius: 8px;\n" +
                                "    font-size: 1em;\n" +
                                "    font-weight: 500;\n" +
                                "    cursor: pointer;\n" +
                                "    transition: all 0.2s ease;\n" +
                                "    min-width: 100px;\n" +
                                "    position: relative;\n" +
                                "    overflow: hidden;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-confirm-cancel {\n" +
                                "    background: #e2e8f0;\n" +
                                "    color: #4a5568;\n" +
                                "}\n" +
                                "\n" +
                                ".btn-confirm-cancel:hover {\n" +
                                "    background: #cbd5e0;\n" +
                                "    transform: translateY(-1px);\n" +
                                "    box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n" +
                                "}\n" +
                                "\n" +
                                ".btn-confirm-ok {\n" +
                                "    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);\n" +
                                "    color: white;\n" +
                                "    box-shadow: 0 2px 8px rgba(245, 101, 101, 0.3);\n" +
                                "}\n" +
                                "\n" +
                                ".btn-confirm-ok:hover {\n" +
                                "    background: linear-gradient(135deg, #e53e3e 0%, #c53030 100%);\n" +
                                "    transform: translateY(-1px);\n" +
                                "    box-shadow: 0 6px 16px rgba(245, 101, 101, 0.4);\n" +
                                "}\n" +
                                "\n" +
                                ".btn-confirm-cancel:active,\n" +
                                ".btn-confirm-ok:active {\n" +
                                "    transform: translateY(0);\n" +
                                "}\n" +
                                "\n" +
                                "/* 夜间主题适配 */\n" +
                                "body.theme-dark .custom-confirm-dialog {\n" +
                                "    background: #2d3748;\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .confirm-message {\n" +
                                "    color: #e2e8f0;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .btn-confirm-cancel {\n" +
                                "    background: #4a5568;\n" +
                                "    color: #f7fafc;\n" +
                                "}\n" +
                                "\n" +
                                "body.theme-dark .btn-confirm-cancel:hover {\n" +
                                "    background: #718096;\n" +
                                "}\n" +
                                "</style>\n" +
                                "\n" +
                                "<script>\n" +
                                "let onlineUsersUpdateInterval;\n" +
                                "\n" +
                                "// 页面加载时启动\n" +
                                "document.addEventListener('DOMContentLoaded', function() {\n" +
                                "    if (document.getElementById('onlineUsersTable')) {\n" +
                                "        refreshOnlineUsers();\n" +
                                "        // 每30秒自动刷新\n" +
                                "        onlineUsersUpdateInterval = setInterval(refreshOnlineUsers, 30000);\n" +
                                "    }\n" +
                                "});\n" +
                                "\n" +
                                "// 页面卸载时清理定时器\n" +
                                "window.addEventListener('beforeunload', function() {\n" +
                                "    if (onlineUsersUpdateInterval) {\n" +
                                "        clearInterval(onlineUsersUpdateInterval);\n" +
                                "    }\n" +
                                "});\n" +
                                "\n" +
                                "// 刷新在线用户列表\n" +
                                "function refreshOnlineUsers() {\n" +
                                "    fetch('/admin/online-users')\n" +
                                "        .then(response => response.json())\n" +
                                "        .then(data => {\n" +
                                "            if (data.success) {\n" +
                                "                updateOnlineUsersTable(data.onlineUsers);\n" +
                                "                updateStats(data.totalCount, data.currentTime);\n" +
                                "            } else {\n" +
                                "                showOnlineUsersError('获取在线用户失败: ' + data.message);\n" +
                                "            }\n" +
                                "        })\n" +
                                "        .catch(error => {\n" +
                                "            console.error('Error:', error);\n" +
                                "            showOnlineUsersError('网络错误，请稍后重试');\n" +
                                "        });\n" +
                                "}\n" +
                                "\n" +
                                "// 更新在线用户表格\n" +
                                "function updateOnlineUsersTable(users) {\n" +
                                "    const tbody = document.getElementById('onlineUsersTableBody');\n" +
                                "    \n" +
                                "    if (users.length === 0) {\n" +
                                "        tbody.innerHTML = '<tr><td colspan=\"7\" class=\"loading-cell\">暂无在线用户</td></tr>';\n"
                                +
                                "        return;\n" +
                                "    }\n" +
                                "    \n" +
                                "    tbody.innerHTML = users.map(user => {\n" +
                                "        const loginTime = new Date(user.loginTime).toLocaleString();\n" +
                                "        const lastActivity = new Date(user.lastAccessTime).toLocaleString();\n" +
                                "        const onlineDuration = formatDuration(user.onlineDuration);\n" +
                                "        const isCurrentUser = user.isCurrentSession;\n" +
                                "        \n" +
                                "        return `\n" +
                                "            <tr>\n" +
                                "                <td><strong>${escapeHtml(user.username)}</strong></td>\n" +
                                "                <td><code>${escapeHtml(user.clientIP)}</code></td>\n" +
                                "                <td>${loginTime}</td>\n" +
                                "                <td>${onlineDuration}</td>\n" +
                                "                <td>${lastActivity}</td>\n" +
                                "                <td>\n" +
                                "                    <span class=\"status-badge ${isCurrentUser ? 'status-current' : 'status-other'}\">\n"
                                +
                                "                        ${isCurrentUser ? '当前会话' : '其他会话'}\n" +
                                "                    </span>\n" +
                                "                </td>\n" +
                                "                <td>\n" +
                                "                    <button class=\"btn-kick\" \n" +
                                "                            onclick=\"kickUser('${user.sessionId}', '${escapeHtml(user.username)}')\"\n"
                                +
                                "                            ${isCurrentUser ? 'disabled title=\"不能踢出自己\"' : ''}>\n" +
                                "                        ${isCurrentUser ? '🚫 自己' : '👢 踢出'}\n" +
                                "                    </button>\n" +
                                "                </td>\n" +
                                "            </tr>\n" +
                                "        `;\n" +
                                "    }).join('');\n" +
                                "}\n" +
                                "\n" +
                                "// 更新统计信息\n" +
                                "function updateStats(totalCount, currentTime) {\n" +
                                "    document.getElementById('totalOnlineUsers').textContent = totalCount;\n" +
                                "    document.getElementById('lastUpdateTime').textContent = new Date(currentTime).toLocaleTimeString();\n"
                                +
                                "}\n" +
                                "\n" +
                                "// 踢出用户\n" +
                                "function kickUser(sessionId, username) {\n" +
                                "    showCustomConfirm(\n" +
                                "        '踢出用户确认',\n" +
                                "        `确定要踢出用户 \"${username}\" 吗？`,\n" +
                                "        '⚠️',\n" +
                                "        function() {\n" +
                                "            // 确认踢出\n" +
                                "            fetch('/admin/kick-session', {\n" +
                                "                method: 'POST',\n" +
                                "                headers: {\n" +
                                "                    'Content-Type': 'application/json'\n" +
                                "                },\n" +
                                "                body: JSON.stringify({\n" +
                                "                    sessionId: sessionId\n" +
                                "                })\n" +
                                "            })\n" +
                                "            .then(response => response.json())\n" +
                                "            .then(data => {\n" +
                                "                if (data.success) {\n" +
                                "                    showOnlineUsersSuccess(data.message);\n" +
                                "                    // 刷新列表\n" +
                                "                    setTimeout(refreshOnlineUsers, 1000);\n" +
                                "                } else {\n" +
                                "                    showOnlineUsersError(data.message);\n" +
                                "                }\n" +
                                "            })\n" +
                                "            .catch(error => {\n" +
                                "                console.error('Error:', error);\n" +
                                "                showOnlineUsersError('网络错误，请稍后重试');\n" +
                                "            });\n" +
                                "        }\n" +
                                "    );\n" +
                                "}\n" +
                                "\n" +
                                "// 格式化持续时间\n" +
                                "function formatDuration(milliseconds) {\n" +
                                "    const seconds = Math.floor(milliseconds / 1000);\n" +
                                "    const minutes = Math.floor(seconds / 60);\n" +
                                "    const hours = Math.floor(minutes / 60);\n" +
                                "    const days = Math.floor(hours / 24);\n" +
                                "    \n" +
                                "    if (days > 0) {\n" +
                                "        return `${days}天 ${hours % 24}小时`;\n" +
                                "    } else if (hours > 0) {\n" +
                                "        return `${hours}小时 ${minutes % 60}分钟`;\n" +
                                "    } else if (minutes > 0) {\n" +
                                "        return `${minutes}分钟 ${seconds % 60}秒`;\n" +
                                "    } else {\n" +
                                "        return `${seconds}秒`;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// HTML转义\n" +
                                "function escapeHtml(text) {\n" +
                                "    const div = document.createElement('div');\n" +
                                "    div.textContent = text;\n" +
                                "    return div.innerHTML;\n" +
                                "}\n" +
                                "\n" +
                                "// 自定义确认对话框\n" +
                                "function showCustomConfirm(title, message, icon, onConfirm) {\n" +
                                "    // 创建遮罩层\n" +
                                "    const overlay = document.createElement('div');\n" +
                                "    overlay.className = 'custom-confirm-overlay';\n" +
                                "    \n" +
                                "    // 创建对话框\n" +
                                "    const dialog = document.createElement('div');\n" +
                                "    dialog.className = 'custom-confirm-dialog';\n" +
                                "    \n" +
                                "    dialog.innerHTML = `\n" +
                                "        <div class=\"confirm-header\">\n" +
                                "            <div class=\"confirm-icon\">${icon}</div>\n" +
                                "            <h3 class=\"confirm-title\">${title}</h3>\n" +
                                "        </div>\n" +
                                "        <div class=\"confirm-body\">\n" +
                                "            <p class=\"confirm-message\">${message}</p>\n" +
                                "        </div>\n" +
                                "        <div class=\"confirm-footer\">\n" +
                                "            <button class=\"btn-confirm-cancel\">取消</button>\n" +
                                "            <button class=\"btn-confirm-ok\">确定</button>\n" +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    overlay.appendChild(dialog);\n" +
                                "    document.body.appendChild(overlay);\n" +
                                "    \n" +
                                "    // 添加动画\n" +
                                "    setTimeout(() => {\n" +
                                "        overlay.classList.add('show');\n" +
                                "        dialog.classList.add('show');\n" +
                                "    }, 10);\n" +
                                "    \n" +
                                "    // 绑定事件\n" +
                                "    const cancelBtn = dialog.querySelector('.btn-confirm-cancel');\n" +
                                "    const confirmBtn = dialog.querySelector('.btn-confirm-ok');\n" +
                                "    \n" +
                                "    function closeDialog() {\n" +
                                "        overlay.classList.remove('show');\n" +
                                "        dialog.classList.remove('show');\n" +
                                "        setTimeout(() => {\n" +
                                "            document.body.removeChild(overlay);\n" +
                                "        }, 300);\n" +
                                "    }\n" +
                                "    \n" +
                                "    cancelBtn.onclick = closeDialog;\n" +
                                "    overlay.onclick = function(e) {\n" +
                                "        if (e.target === overlay) closeDialog();\n" +
                                "    };\n" +
                                "    \n" +
                                "    confirmBtn.onclick = function() {\n" +
                                "        closeDialog();\n" +
                                "        if (onConfirm) onConfirm();\n" +
                                "    };\n" +
                                "    \n" +
                                "    // ESC键关闭\n" +
                                "    function handleKeydown(e) {\n" +
                                "        if (e.key === 'Escape') {\n" +
                                "            closeDialog();\n" +
                                "            document.removeEventListener('keydown', handleKeydown);\n" +
                                "        }\n" +
                                "    }\n" +
                                "    document.addEventListener('keydown', handleKeydown);\n" +
                                "}\n" +
                                "\n" +
                                "// 在线用户页面专用的消息显示函数\n" +
                                "function showOnlineUsersSuccess(message) {\n" +
                                "    showOnlineUsersMessage(message, 'success');\n" +
                                "}\n" +
                                "\n" +
                                "function showOnlineUsersError(message) {\n" +
                                "    showOnlineUsersMessage(message, 'error');\n" +
                                "}\n" +
                                "\n" +
                                "function showOnlineUsersMessage(message, type) {\n" +
                                "    // 移除现有的消息\n" +
                                "    const existingMsg = document.querySelector('.online-users-message');\n" +
                                "    if (existingMsg) {\n" +
                                "        existingMsg.remove();\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 创建新消息\n" +
                                "    const msgDiv = document.createElement('div');\n" +
                                "    msgDiv.className = `online-users-message ${type}`;\n" +
                                "    msgDiv.innerHTML = `\n" +
                                "        <div class=\"message-content\">\n" +
                                "            <span class=\"message-icon\">${type === 'success' ? '✅' : '❌'}</span>\n" +
                                "            <span class=\"message-text\">${message}</span>\n" +
                                "            <button class=\"message-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n"
                                +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    // 添加样式\n" +
                                "    if (!document.querySelector('#onlineUsersMessageStyle')) {\n" +
                                "        const style = document.createElement('style');\n" +
                                "        style.id = 'onlineUsersMessageStyle';\n" +
                                "        style.textContent = `\n" +
                                "            .online-users-message {\n" +
                                "                position: fixed;\n" +
                                "                top: 20px;\n" +
                                "                right: 20px;\n" +
                                "                z-index: 9999;\n" +
                                "                border-radius: 8px;\n" +
                                "                box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n" +
                                "                animation: slideInRight 0.3s ease;\n" +
                                "                max-width: 400px;\n" +
                                "            }\n" +
                                "            .online-users-message.success {\n" +
                                "                background: #f0fff4;\n" +
                                "                border: 1px solid #9ae6b4;\n" +
                                "                color: #22543d;\n" +
                                "            }\n" +
                                "            .online-users-message.error {\n" +
                                "                background: #fff5f5;\n" +
                                "                border: 1px solid #feb2b2;\n" +
                                "                color: #742a2a;\n" +
                                "            }\n" +
                                "            .message-content {\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                padding: 12px 16px;\n" +
                                "                gap: 8px;\n" +
                                "            }\n" +
                                "            .message-icon {\n" +
                                "                font-size: 1.2em;\n" +
                                "            }\n" +
                                "            .message-text {\n" +
                                "                flex: 1;\n" +
                                "                font-weight: 500;\n" +
                                "            }\n" +
                                "            .message-close {\n" +
                                "                background: none;\n" +
                                "                border: none;\n" +
                                "                font-size: 1.5em;\n" +
                                "                cursor: pointer;\n" +
                                "                opacity: 0.7;\n" +
                                "                transition: opacity 0.2s;\n" +
                                "            }\n" +
                                "            .message-close:hover {\n" +
                                "                opacity: 1;\n" +
                                "            }\n" +
                                "            @keyframes slideInRight {\n" +
                                "                from {\n" +
                                "                    transform: translateX(100%);\n" +
                                "                    opacity: 0;\n" +
                                "                }\n" +
                                "                to {\n" +
                                "                    transform: translateX(0);\n" +
                                "                    opacity: 1;\n" +
                                "                }\n" +
                                "            }\n" +
                                "        `;\n" +
                                "        document.head.appendChild(style);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 插入到页面\n" +
                                "    document.body.appendChild(msgDiv);\n" +
                                "    \n" +
                                "    // 3秒后自动消失\n" +
                                "    setTimeout(() => {\n" +
                                "        if (msgDiv.parentNode) {\n" +
                                "            msgDiv.remove();\n" +
                                "        }\n" +
                                "    }, 3000);\n" +
                                "}\n" +
                                "\n" +
                                "// 会话状态检查（防止被踢出后仍能使用界面）\n" +
                                "function checkSessionStatus() {\n" +
                                "    fetch('/admin/session-check')\n" +
                                "        .then(response => response.json())\n" +
                                "        .then(data => {\n" +
                                "            if (!data.valid) {\n" +
                                "                // 会话已失效，显示提示并重定向\n" +
                                "                if (data.reason === 'kicked') {\n" +
                                "                    showKickedOutMessage();\n" +
                                "                } else {\n" +
                                "                    // 其他原因（过期等）直接重定向\n" +
                                "                    window.location.href = '/admin/login';\n" +
                                "                }\n" +
                                "            }\n" +
                                "        })\n" +
                                "        .catch(error => {\n" +
                                "            // 网络错误，可能是会话问题\n" +
                                "            console.warn('会话检查失败:', error);\n" +
                                "        });\n" +
                                "}\n" +
                                "\n" +
                                "// 启动会话状态检查（所有管理员页面）\n" +
                                "// 每30秒检查一次会话状态，防止被踢出后仍能使用界面\n" +
                                "setInterval(checkSessionStatus, 30000);\n" +
                                "\n" +
                                "// 页面加载时立即检查一次\n" +
                                "setTimeout(checkSessionStatus, 1000);\n" +
                                "\n" +
                                "// 显示被踢出提示\n" +
                                "function showKickedOutMessage() {\n" +
                                "    // 创建遮罩层\n" +
                                "    const overlay = document.createElement('div');\n" +
                                "    overlay.className = 'kicked-out-overlay';\n" +
                                "    \n" +
                                "    // 创建提示框\n" +
                                "    const dialog = document.createElement('div');\n" +
                                "    dialog.className = 'kicked-out-dialog';\n" +
                                "    \n" +
                                "    dialog.innerHTML = `\n" +
                                "        <div class=\"kicked-header\">\n" +
                                "            <div class=\"kicked-icon\">⚠️</div>\n" +
                                "            <h3 class=\"kicked-title\">账号安全提醒</h3>\n" +
                                "        </div>\n" +
                                "        <div class=\"kicked-body\">\n" +
                                "            <p class=\"kicked-message\">您的账号在其他地方登录，为了账号安全，当前会话已被终止。</p>\n" +
                                "            <p class=\"kicked-tip\">如果不是您本人操作，请立即修改密码！</p>\n" +
                                "        </div>\n" +
                                "        <div class=\"kicked-footer\">\n" +
                                "            <button class=\"btn-kicked-ok\" onclick=\"redirectToLogin()\">重新登录</button>\n"
                                +
                                "        </div>\n" +
                                "    `;\n" +
                                "    \n" +
                                "    overlay.appendChild(dialog);\n" +
                                "    document.body.appendChild(overlay);\n" +
                                "    \n" +
                                "    // 添加样式\n" +
                                "    addKickedOutStyles();\n" +
                                "}\n" +
                                "\n" +
                                "// 添加被踢出提示的样式\n" +
                                "function addKickedOutStyles() {\n" +
                                "    if (!document.querySelector('#kickedOutStyle')) {\n" +
                                "        const style = document.createElement('style');\n" +
                                "        style.id = 'kickedOutStyle';\n" +
                                "        style.textContent = `\n" +
                                "            .kicked-out-overlay {\n" +
                                "                position: fixed;\n" +
                                "                top: 0;\n" +
                                "                left: 0;\n" +
                                "                width: 100%;\n" +
                                "                height: 100%;\n" +
                                "                background: rgba(0, 0, 0, 0.8);\n" +
                                "                backdrop-filter: blur(8px);\n" +
                                "                display: flex;\n" +
                                "                align-items: center;\n" +
                                "                justify-content: center;\n" +
                                "                z-index: 99999;\n" +
                                "                animation: fadeIn 0.3s ease;\n" +
                                "            }\n" +
                                "            .kicked-out-dialog {\n" +
                                "                background: white;\n" +
                                "                border-radius: 16px;\n" +
                                "                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);\n" +
                                "                min-width: 450px;\n" +
                                "                max-width: 500px;\n" +
                                "                animation: slideIn 0.3s ease;\n" +
                                "                overflow: hidden;\n" +
                                "            }\n" +
                                "            .kicked-header {\n" +
                                "                padding: 24px 24px 16px;\n" +
                                "                text-align: center;\n" +
                                "                background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\n" +
                                "                color: white;\n" +
                                "            }\n" +
                                "            .kicked-icon {\n" +
                                "                font-size: 3em;\n" +
                                "                margin-bottom: 8px;\n" +
                                "                filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));\n" +
                                "            }\n" +
                                "            .kicked-title {\n" +
                                "                margin: 0;\n" +
                                "                font-size: 1.4em;\n" +
                                "                font-weight: 600;\n" +
                                "                text-shadow: 0 1px 2px rgba(0,0,0,0.1);\n" +
                                "            }\n" +
                                "            .kicked-body {\n" +
                                "                padding: 24px;\n" +
                                "                text-align: center;\n" +
                                "            }\n" +
                                "            .kicked-message {\n" +
                                "                margin: 0 0 16px 0;\n" +
                                "                font-size: 1.1em;\n" +
                                "                color: #4a5568;\n" +
                                "                line-height: 1.6;\n" +
                                "            }\n" +
                                "            .kicked-tip {\n" +
                                "                margin: 0;\n" +
                                "                font-size: 0.95em;\n" +
                                "                color: #e53e3e;\n" +
                                "                font-weight: 500;\n" +
                                "                background: #fed7d7;\n" +
                                "                padding: 12px;\n" +
                                "                border-radius: 8px;\n" +
                                "                border-left: 4px solid #e53e3e;\n" +
                                "            }\n" +
                                "            .kicked-footer {\n" +
                                "                padding: 16px 24px 24px;\n" +
                                "                text-align: center;\n" +
                                "            }\n" +
                                "            .btn-kicked-ok {\n" +
                                "                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                                "                color: white;\n" +
                                "                border: none;\n" +
                                "                padding: 12px 32px;\n" +
                                "                border-radius: 8px;\n" +
                                "                font-size: 1em;\n" +
                                "                font-weight: 500;\n" +
                                "                cursor: pointer;\n" +
                                "                transition: all 0.2s ease;\n" +
                                "                box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);\n" +
                                "            }\n" +
                                "            .btn-kicked-ok:hover {\n" +
                                "                transform: translateY(-1px);\n" +
                                "                box-shadow: 0 6px 16px rgba(102, 126, 234, 0.4);\n" +
                                "            }\n" +
                                "            @keyframes fadeIn {\n" +
                                "                from { opacity: 0; }\n" +
                                "                to { opacity: 1; }\n" +
                                "            }\n" +
                                "            @keyframes slideIn {\n" +
                                "                from {\n" +
                                "                    transform: scale(0.8) translateY(-20px);\n" +
                                "                    opacity: 0;\n" +
                                "                }\n" +
                                "                to {\n" +
                                "                    transform: scale(1) translateY(0);\n" +
                                "                    opacity: 1;\n" +
                                "                }\n" +
                                "            }\n" +
                                "        `;\n" +
                                "        document.head.appendChild(style);\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 重定向到登录页面\n" +
                                "function redirectToLogin() {\n" +
                                "    window.location.href = '/admin/login';\n" +
                                "}\n" +
                                "</script>";
        }

}
