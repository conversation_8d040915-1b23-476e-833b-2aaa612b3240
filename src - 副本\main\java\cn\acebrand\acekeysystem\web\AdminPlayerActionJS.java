package cn.acebrand.acekeysystem.web;

/**
 * 管理员玩家页面操作JavaScript生成器
 * 负责生成模态框控制和表单提交的JavaScript代码
 */
public class AdminPlayerActionJS {

    /**
     * 生成模态框控制和表单提交的JavaScript代码
     */
    public static String generateActionJavaScript() {
        StringBuilder js = new StringBuilder();

        js.append("        // 模态框控制函数\n");
        js.append("        function showBanModal(playerName) {\n");
        js.append("            document.getElementById('banPlayer').value = playerName;\n");
        js.append("            \n");
        js.append("            // 检查玩家是否已被封禁\n");
        js.append("            checkPlayerBanStatus(playerName).then(isBanned => {\n");
        js.append("                const modal = document.getElementById('banModal');\n");
        js.append("                const title = document.getElementById('banModalTitle');\n");
        js.append("                const submitBtn = document.getElementById('banSubmitBtn');\n");
        js.append("                const templateSection = document.getElementById('banTemplateSection');\n");
        js.append("                \n");
        js.append("                if (isBanned) {\n");
        js.append("                    // 玩家已被封禁，显示解封界面\n");
        js.append("                    title.textContent = '🔓 解封玩家';\n");
        js.append("                    submitBtn.textContent = '确认解封';\n");
        js.append("                    submitBtn.className = 'btn btn-success';\n");
        js.append("                    submitBtn.onclick = submitUnban;\n");
        js.append("                    templateSection.style.display = 'none';\n");
        js.append("                } else {\n");
        js.append("                    // 玩家未被封禁，显示封禁界面\n");
        js.append("                    title.textContent = '🚫 封禁玩家';\n");
        js.append("                    submitBtn.textContent = '确认封禁';\n");
        js.append("                    submitBtn.className = 'btn btn-danger';\n");
        js.append("                    submitBtn.onclick = submitBan;\n");
        js.append("                    templateSection.style.display = 'block';\n");
        js.append("                    // 重置为模板模式\n");
        js.append("                    switchBanTemplate('template');\n");
        js.append("                }\n");
        js.append("                \n");
        js.append("                modal.style.display = 'flex';\n");
        js.append("                document.body.style.overflow = 'hidden';\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function closeBanModal() {\n");
        js.append("            document.getElementById('banModal').style.display = 'none';\n");
        js.append("            document.body.style.overflow = 'auto';\n");
        js.append("            document.getElementById('banForm').reset();\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function showMuteModal(playerName) {\n");
        js.append("            document.getElementById('mutePlayer').value = playerName;\n");
        js.append("            document.getElementById('muteModal').style.display = 'flex';\n");
        js.append("            document.body.style.overflow = 'hidden';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function closeMuteModal() {\n");
        js.append("            document.getElementById('muteModal').style.display = 'none';\n");
        js.append("            document.body.style.overflow = 'auto';\n");
        js.append("            document.getElementById('muteForm').reset();\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function showUnmuteConfirm(playerName) {\n");
        js.append("            showUnmuteModal(playerName);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function showUnmuteModal(playerName) {\n");
        js.append("            document.getElementById('unmutePlayer').value = playerName;\n");
        js.append("            document.getElementById('unmuteModal').style.display = 'flex';\n");
        js.append("            document.body.style.overflow = 'hidden';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function closeUnmuteModal() {\n");
        js.append("            document.getElementById('unmuteModal').style.display = 'none';\n");
        js.append("            document.body.style.overflow = 'auto';\n");
        js.append("            document.getElementById('unmuteForm').reset();\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function submitUnmute() {\n");
        js.append("            const form = document.getElementById('unmuteForm');\n");
        js.append("            const formData = new FormData(form);\n");
        js.append("            \n");
        js.append("            const data = {\n");
        js.append("                action: 'unmute',\n");
        js.append("                player: formData.get('player')\n");
        js.append("            };\n");
        js.append("            \n");
        js.append("            submitPunishment(data, '解除禁言', closeUnmuteModal);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function showWarnModal(playerName) {\n");
        js.append("            document.getElementById('warnPlayer').value = playerName;\n");
        js.append("            document.getElementById('warnModal').style.display = 'flex';\n");
        js.append("            document.body.style.overflow = 'hidden';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function closeWarnModal() {\n");
        js.append("            document.getElementById('warnModal').style.display = 'none';\n");
        js.append("            document.body.style.overflow = 'auto';\n");
        js.append("            document.getElementById('warnForm').reset();\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function showKickModal(playerName) {\n");
        js.append("            document.getElementById('kickPlayer').value = playerName;\n");
        js.append("            document.getElementById('kickModal').style.display = 'flex';\n");
        js.append("            document.body.style.overflow = 'hidden';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function closeKickModal() {\n");
        js.append("            document.getElementById('kickModal').style.display = 'none';\n");
        js.append("            document.body.style.overflow = 'auto';\n");
        js.append("            document.getElementById('kickForm').reset();\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 表单提交函数\n");
        js.append("        function submitBan() {\n");
        js.append("            const form = document.getElementById('banForm');\n");
        js.append("            const formData = new FormData(form);\n");
        js.append("            \n");
        js.append("            let reason, duration;\n");
        js.append("            \n");
        js.append("            // 检查是否使用模板\n");
        js.append("            const selectedTemplate = formData.get('template');\n");
        js.append("            if (selectedTemplate) {\n");
        js.append("                // 使用模板\n");
        js.append("                reason = formData.get('templateReason');\n");
        js.append("                duration = formData.get('templateDuration');\n");
        js.append("            } else {\n");
        js.append("                // 使用自定义\n");
        js.append("                reason = formData.get('reason');\n");
        js.append("                duration = formData.get('duration');\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            if (!reason || !reason.trim()) {\n");
        js.append("                showNotification('请选择封禁模板或输入封禁原因', 'warning');\n");
        js.append("                return;\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            const data = {\n");
        js.append("                action: 'ban',\n");
        js.append("                player: formData.get('player'),\n");
        js.append("                reason: reason,\n");
        js.append("                duration: duration,\n");
        js.append("                silent: formData.get('silent') === 'on'\n");
        js.append("            };\n");
        js.append("            \n");
        js.append("            submitPunishment(data, '封禁', closeBanModal);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function submitUnban() {\n");
        js.append("            const form = document.getElementById('banForm');\n");
        js.append("            const formData = new FormData(form);\n");
        js.append("            \n");
        js.append("            const data = {\n");
        js.append("                action: 'unban',\n");
        js.append("                player: formData.get('player')\n");
        js.append("            };\n");
        js.append("            \n");
        js.append("            submitPunishment(data, '解封', closeBanModal);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function submitMute() {\n");
        js.append("            const form = document.getElementById('muteForm');\n");
        js.append("            const formData = new FormData(form);\n");
        js.append("            \n");
        js.append("            let reason, duration;\n");
        js.append("            \n");
        js.append("            // 检查是否使用模板\n");
        js.append("            const selectedTemplate = formData.get('template');\n");
        js.append("            if (selectedTemplate) {\n");
        js.append("                // 使用模板\n");
        js.append("                reason = formData.get('templateReason');\n");
        js.append("                duration = formData.get('templateDuration');\n");
        js.append("            } else {\n");
        js.append("                // 使用自定义\n");
        js.append("                reason = formData.get('reason');\n");
        js.append("                duration = formData.get('duration');\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            if (!reason || !reason.trim()) {\n");
        js.append("                showNotification('请选择禁言模板或输入禁言原因', 'warning');\n");
        js.append("                return;\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            const data = {\n");
        js.append("                action: 'mute',\n");
        js.append("                player: formData.get('player'),\n");
        js.append("                reason: reason,\n");
        js.append("                duration: duration,\n");
        js.append("                silent: formData.get('silent') === 'on'\n");
        js.append("            };\n");
        js.append("            \n");
        js.append("            submitPunishment(data, '禁言', closeMuteModal);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function submitWarn() {\n");
        js.append("            const form = document.getElementById('warnForm');\n");
        js.append("            const formData = new FormData(form);\n");
        js.append("            \n");
        js.append("            let reason;\n");
        js.append("            \n");
        js.append("            // 检查是否使用模板\n");
        js.append("            const selectedTemplate = formData.get('template');\n");
        js.append("            if (selectedTemplate) {\n");
        js.append("                // 使用模板\n");
        js.append("                reason = formData.get('templateReason');\n");
        js.append("            } else {\n");
        js.append("                // 使用自定义\n");
        js.append("                reason = formData.get('reason');\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            if (!reason || !reason.trim()) {\n");
        js.append("                showNotification('请选择警告模板或输入警告原因', 'warning');\n");
        js.append("                return;\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            const data = {\n");
        js.append("                action: 'warn',\n");
        js.append("                player: formData.get('player'),\n");
        js.append("                reason: reason,\n");
        js.append("                silent: formData.get('silent') === 'on'\n");
        js.append("            };\n");
        js.append("            \n");
        js.append("            submitPunishment(data, '警告', closeWarnModal);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        function submitKick() {\n");
        js.append("            const form = document.getElementById('kickForm');\n");
        js.append("            const formData = new FormData(form);\n");
        js.append("            \n");
        js.append("            let reason;\n");
        js.append("            \n");
        js.append("            // 检查是否使用模板\n");
        js.append("            const selectedTemplate = formData.get('template');\n");
        js.append("            if (selectedTemplate) {\n");
        js.append("                // 使用模板\n");
        js.append("                reason = formData.get('templateReason');\n");
        js.append("            } else {\n");
        js.append("                // 使用自定义\n");
        js.append("                reason = formData.get('reason');\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            if (!reason || !reason.trim()) {\n");
        js.append("                showNotification('请选择踢出模板或输入踢出原因', 'warning');\n");
        js.append("                return;\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            const data = {\n");
        js.append("                action: 'kick',\n");
        js.append("                player: formData.get('player'),\n");
        js.append("                reason: reason,\n");
        js.append("                silent: formData.get('silent') === 'on'\n");
        js.append("            };\n");
        js.append("            \n");
        js.append("            submitPunishment(data, '踢出', closeKickModal);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 通用处罚提交函数\n");
        js.append("        function submitPunishment(data, actionName, closeCallback) {\n");
        js.append("            // 显示加载状态\n");
        js.append("            const submitBtn = event.target;\n");
        js.append("            const originalText = submitBtn.textContent;\n");
        js.append("            submitBtn.textContent = '处理中...';\n");
        js.append("            submitBtn.disabled = true;\n");
        js.append("            \n");
        js.append("            fetch('/admin-punishment-action', {\n");
        js.append("                method: 'POST',\n");
        js.append("                headers: {\n");
        js.append("                    'Content-Type': 'application/json',\n");
        js.append("                    'X-Requested-With': 'XMLHttpRequest'\n");
        js.append("                },\n");
        js.append("                body: JSON.stringify(data)\n");
        js.append("            })\n");
        js.append("            .then(response => response.json())\n");
        js.append("            .then(result => {\n");
        js.append("                if (result.success) {\n");
        js.append("                    showNotification(`${actionName}成功！`, 'success');\n");
        js.append("                    closeCallback();\n");
        js.append("                    \n");
        js.append("                    // 使用自动更新机制而不是页面刷新\n");
        js.append("                    setTimeout(() => {\n");
        js.append("                        // 触发数据更新检测\n");
        js.append("                        if (typeof checkForDataUpdates === 'function') {\n");
        js.append("                            checkForDataUpdates();\n");
        js.append("                        }\n");
        js.append("                        \n");
        js.append("                        // 更新所有操作按钮状态\n");
        js.append("                        const playerName = window.location.pathname.split('/').pop();\n");
        js.append("                        if (playerName && typeof updateAllActionButtonsStatus === 'function') {\n");
        js.append("                            updateAllActionButtonsStatus(playerName);\n");
        js.append("                        }\n");
        js.append("                        \n");
        js.append("                        // 强制更新徽章（针对管理员玩家页面）\n");
        js.append("                        if (window.location.pathname.includes('/admin-player/')) {\n");
        js.append("                            forceUpdatePlayerBadges(playerName);\n");
        js.append("                        }\n");
        js.append("                    }, 1000);\n");
        js.append("                } else {\n");
        js.append("                    // 特殊处理踢出玩家不在线的情况\n");
        js.append("                    if (result.code === 'PLAYER_OFFLINE') {\n");
        js.append("                        showNotification(result.message, 'warning');\n");
        js.append("                    } else {\n");
        js.append(
                "                        showNotification(`${actionName}失败：${result.message || '未知错误'}`, 'error');\n");
        js.append("                    }\n");
        js.append("                    closeCallback();\n");
        js.append("                }\n");
        js.append("            })\n");
        js.append("            .catch(error => {\n");
        js.append("                console.error('Error:', error);\n");
        js.append("                showNotification(`${actionName}失败：网络错误`, 'error');\n");
        js.append("                closeCallback();\n");
        js.append("            })\n");
        js.append("            .finally(() => {\n");
        js.append("                // 恢复按钮状态\n");
        js.append("                submitBtn.textContent = originalText;\n");
        js.append("                submitBtn.disabled = false;\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 美观的通知弹窗函数\n");
        js.append("        function showNotification(message, type = 'info') {\n");
        js.append("            // 移除现有的通知\n");
        js.append("            const existingNotification = document.querySelector('.custom-notification');\n");
        js.append("            if (existingNotification) {\n");
        js.append("                existingNotification.remove();\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            // 创建通知元素\n");
        js.append("            const notification = document.createElement('div');\n");
        js.append("            notification.className = `custom-notification notification-${type}`;\n");
        js.append("            \n");
        js.append("            // 根据类型设置图标\n");
        js.append("            let icon = '';\n");
        js.append("            switch(type) {\n");
        js.append("                case 'success': icon = '✅'; break;\n");
        js.append("                case 'error': icon = '❌'; break;\n");
        js.append("                case 'warning': icon = '⚠️'; break;\n");
        js.append("                default: icon = 'ℹ️'; break;\n");
        js.append("            }\n");
        js.append("            \n");
        js.append("            notification.innerHTML = `\n");
        js.append("                <div class=\"notification-content\">\n");
        js.append("                    <div class=\"notification-icon\">${icon}</div>\n");
        js.append("                    <div class=\"notification-message\">${message}</div>\n");
        js.append(
                "                    <button class=\"notification-close\" onclick=\"this.parentElement.parentElement.remove()\">×</button>\n");
        js.append("                </div>\n");
        js.append("            `;\n");
        js.append("            \n");
        js.append("            // 添加到页面\n");
        js.append("            document.body.appendChild(notification);\n");
        js.append("            \n");
        js.append("            // 显示动画\n");
        js.append("            setTimeout(() => {\n");
        js.append("                notification.classList.add('show');\n");
        js.append("            }, 10);\n");
        js.append("            \n");
        js.append("            // 自动隐藏（成功消息3秒，其他5秒）\n");
        js.append("            const autoHideDelay = type === 'success' ? 3000 : 5000;\n");
        js.append("            setTimeout(() => {\n");
        js.append("                if (notification.parentElement) {\n");
        js.append("                    notification.classList.remove('show');\n");
        js.append("                    setTimeout(() => {\n");
        js.append("                        if (notification.parentElement) {\n");
        js.append("                            notification.remove();\n");
        js.append("                        }\n");
        js.append("                    }, 300);\n");
        js.append("                }\n");
        js.append("            }, autoHideDelay);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 模板切换功能\n");
        js.append("        function switchBanTemplate(mode) {\n");
        js.append("            const templateTab = document.querySelector('.template-tab[onclick*=\"template\"]');\n");
        js.append("            const customTab = document.querySelector('.template-tab[onclick*=\"custom\"]');\n");
        js.append("            const templateOptions = document.getElementById('templateOptions');\n");
        js.append("            const customOptions = document.getElementById('customOptions');\n");
        js.append("            \n");
        js.append("            if (mode === 'template') {\n");
        js.append("                templateTab.classList.add('active');\n");
        js.append("                customTab.classList.remove('active');\n");
        js.append("                templateOptions.style.display = 'block';\n");
        js.append("                customOptions.style.display = 'none';\n");
        js.append("            } else {\n");
        js.append("                templateTab.classList.remove('active');\n");
        js.append("                customTab.classList.add('active');\n");
        js.append("                templateOptions.style.display = 'none';\n");
        js.append("                customOptions.style.display = 'block';\n");
        js.append("                // 清除模板选择\n");
        js.append("                clearTemplateSelection();\n");
        js.append("            }\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 选择封禁模板\n");
        js.append("        function selectBanTemplate(templateId, reason, duration) {\n");
        js.append("            // 清除之前的选择\n");
        js.append("            document.querySelectorAll('.template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            \n");
        js.append("            // 选中当前模板\n");
        js.append("            event.target.closest('.template-item').classList.add('selected');\n");
        js.append("            \n");
        js.append("            // 设置隐藏字段\n");
        js.append("            document.getElementById('selectedTemplate').value = templateId;\n");
        js.append("            document.getElementById('templateReason').value = reason;\n");
        js.append("            document.getElementById('templateDuration').value = duration;\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 清除模板选择\n");
        js.append("        function clearTemplateSelection() {\n");
        js.append("            document.querySelectorAll('.template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            document.getElementById('selectedTemplate').value = '';\n");
        js.append("            document.getElementById('templateReason').value = '';\n");
        js.append("            document.getElementById('templateDuration').value = '';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 禁言模板切换功能\n");
        js.append("        function switchMuteTemplate(mode) {\n");
        js.append(
                "            const templateTab = document.querySelector('#muteModal .template-tab[onclick*=\"template\"]');\n");
        js.append(
                "            const customTab = document.querySelector('#muteModal .template-tab[onclick*=\"custom\"]');\n");
        js.append("            const templateOptions = document.getElementById('muteTemplateOptions');\n");
        js.append("            const customOptions = document.getElementById('muteCustomOptions');\n");
        js.append("            \n");
        js.append("            if (mode === 'template') {\n");
        js.append("                templateTab.classList.add('active');\n");
        js.append("                customTab.classList.remove('active');\n");
        js.append("                templateOptions.style.display = 'block';\n");
        js.append("                customOptions.style.display = 'none';\n");
        js.append("            } else {\n");
        js.append("                templateTab.classList.remove('active');\n");
        js.append("                customTab.classList.add('active');\n");
        js.append("                templateOptions.style.display = 'none';\n");
        js.append("                customOptions.style.display = 'block';\n");
        js.append("                clearMuteTemplateSelection();\n");
        js.append("            }\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 选择禁言模板\n");
        js.append("        function selectMuteTemplate(templateId, reason, duration) {\n");
        js.append("            document.querySelectorAll('#muteModal .template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            event.target.closest('.template-item').classList.add('selected');\n");
        js.append("            document.getElementById('muteSelectedTemplate').value = templateId;\n");
        js.append("            document.getElementById('muteTemplateReason').value = reason;\n");
        js.append("            document.getElementById('muteTemplateDuration').value = duration;\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 清除禁言模板选择\n");
        js.append("        function clearMuteTemplateSelection() {\n");
        js.append("            document.querySelectorAll('#muteModal .template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            document.getElementById('muteSelectedTemplate').value = '';\n");
        js.append("            document.getElementById('muteTemplateReason').value = '';\n");
        js.append("            document.getElementById('muteTemplateDuration').value = '';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 警告模板切换功能\n");
        js.append("        function switchWarnTemplate(mode) {\n");
        js.append(
                "            const templateTab = document.querySelector('#warnModal .template-tab[onclick*=\"template\"]');\n");
        js.append(
                "            const customTab = document.querySelector('#warnModal .template-tab[onclick*=\"custom\"]');\n");
        js.append("            const templateOptions = document.getElementById('warnTemplateOptions');\n");
        js.append("            const customOptions = document.getElementById('warnCustomOptions');\n");
        js.append("            \n");
        js.append("            if (mode === 'template') {\n");
        js.append("                templateTab.classList.add('active');\n");
        js.append("                customTab.classList.remove('active');\n");
        js.append("                templateOptions.style.display = 'block';\n");
        js.append("                customOptions.style.display = 'none';\n");
        js.append("            } else {\n");
        js.append("                templateTab.classList.remove('active');\n");
        js.append("                customTab.classList.add('active');\n");
        js.append("                templateOptions.style.display = 'none';\n");
        js.append("                customOptions.style.display = 'block';\n");
        js.append("                clearWarnTemplateSelection();\n");
        js.append("            }\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 选择警告模板\n");
        js.append("        function selectWarnTemplate(templateId, reason) {\n");
        js.append("            document.querySelectorAll('#warnModal .template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            event.target.closest('.template-item').classList.add('selected');\n");
        js.append("            document.getElementById('warnSelectedTemplate').value = templateId;\n");
        js.append("            document.getElementById('warnTemplateReason').value = reason;\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 清除警告模板选择\n");
        js.append("        function clearWarnTemplateSelection() {\n");
        js.append("            document.querySelectorAll('#warnModal .template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            document.getElementById('warnSelectedTemplate').value = '';\n");
        js.append("            document.getElementById('warnTemplateReason').value = '';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 踢出模板切换功能\n");
        js.append("        function switchKickTemplate(mode) {\n");
        js.append(
                "            const templateTab = document.querySelector('#kickModal .template-tab[onclick*=\"template\"]');\n");
        js.append(
                "            const customTab = document.querySelector('#kickModal .template-tab[onclick*=\"custom\"]');\n");
        js.append("            const templateOptions = document.getElementById('kickTemplateOptions');\n");
        js.append("            const customOptions = document.getElementById('kickCustomOptions');\n");
        js.append("            \n");
        js.append("            if (mode === 'template') {\n");
        js.append("                templateTab.classList.add('active');\n");
        js.append("                customTab.classList.remove('active');\n");
        js.append("                templateOptions.style.display = 'block';\n");
        js.append("                customOptions.style.display = 'none';\n");
        js.append("            } else {\n");
        js.append("                templateTab.classList.remove('active');\n");
        js.append("                customTab.classList.add('active');\n");
        js.append("                templateOptions.style.display = 'none';\n");
        js.append("                customOptions.style.display = 'block';\n");
        js.append("                clearKickTemplateSelection();\n");
        js.append("            }\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 选择踢出模板\n");
        js.append("        function selectKickTemplate(templateId, reason) {\n");
        js.append("            document.querySelectorAll('#kickModal .template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            event.target.closest('.template-item').classList.add('selected');\n");
        js.append("            document.getElementById('kickSelectedTemplate').value = templateId;\n");
        js.append("            document.getElementById('kickTemplateReason').value = reason;\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 清除踢出模板选择\n");
        js.append("        function clearKickTemplateSelection() {\n");
        js.append("            document.querySelectorAll('#kickModal .template-item').forEach(item => {\n");
        js.append("                item.classList.remove('selected');\n");
        js.append("            });\n");
        js.append("            document.getElementById('kickSelectedTemplate').value = '';\n");
        js.append("            document.getElementById('kickTemplateReason').value = '';\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 检查玩家封禁状态\n");
        js.append("        function checkPlayerBanStatus(playerName) {\n");
        js.append("            return fetch(`/admin-player-status?player=${encodeURIComponent(playerName)}`, {\n");
        js.append("                method: 'GET',\n");
        js.append("                headers: {\n");
        js.append("                    'X-Requested-With': 'XMLHttpRequest'\n");
        js.append("                }\n");
        js.append("            })\n");
        js.append("            .then(response => response.json())\n");
        js.append("            .then(result => {\n");
        js.append("                return result.banned || false;\n");
        js.append("            })\n");
        js.append("            .catch(error => {\n");
        js.append("                console.error('检查封禁状态失败:', error);\n");
        js.append("                return false;\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 检查玩家禁言状态\n");
        js.append("        function checkPlayerMuteStatus(playerName) {\n");
        js.append("            return fetch(`/admin-player-status?player=${encodeURIComponent(playerName)}`, {\n");
        js.append("                method: 'GET',\n");
        js.append("                headers: {\n");
        js.append("                    'X-Requested-With': 'XMLHttpRequest'\n");
        js.append("                }\n");
        js.append("            })\n");
        js.append("            .then(response => response.json())\n");
        js.append("            .then(result => {\n");
        js.append("                return result.muted || false;\n");
        js.append("            })\n");
        js.append("            .catch(error => {\n");
        js.append("                console.error('检查禁言状态失败:', error);\n");
        js.append("                return false;\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 更新封禁按钮状态\n");
        js.append("        function updateBanButtonStatus(playerName) {\n");
        js.append("            const banBtn = document.querySelector('.ban-btn, .unban-btn');\n");
        js.append("            if (!banBtn) return;\n");
        js.append("            \n");
        js.append("            checkPlayerBanStatus(playerName).then(isBanned => {\n");
        js.append("                const btnText = banBtn.querySelector('.btn-text');\n");
        js.append("                const btnIcon = banBtn.querySelector('.btn-icon');\n");
        js.append("                \n");
        js.append("                if (isBanned) {\n");
        js.append("                    banBtn.className = 'action-btn unban-btn';\n");
        js.append("                    btnIcon.textContent = '🔓';\n");
        js.append("                    btnText.textContent = '解封玩家';\n");
        js.append("                } else {\n");
        js.append("                    banBtn.className = 'action-btn ban-btn';\n");
        js.append("                    btnIcon.textContent = '🚫';\n");
        js.append("                    btnText.textContent = '封禁玩家';\n");
        js.append("                }\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 更新禁言按钮状态\n");
        js.append("        function updateMuteButtonStatus(playerName) {\n");
        js.append("            const muteBtn = document.querySelector('.mute-btn, .unmute-btn');\n");
        js.append("            if (!muteBtn) return;\n");
        js.append("            \n");
        js.append("            checkPlayerMuteStatus(playerName).then(isMuted => {\n");
        js.append("                const btnText = muteBtn.querySelector('.btn-text');\n");
        js.append("                const btnIcon = muteBtn.querySelector('.btn-icon');\n");
        js.append("                \n");
        js.append("                if (isMuted) {\n");
        js.append("                    muteBtn.className = 'action-btn unmute-btn';\n");
        js.append("                    btnIcon.textContent = '🔊';\n");
        js.append("                    btnText.textContent = '解除禁言';\n");
        js.append("                    // 更新点击事件为解除禁言\n");
        js.append("                    muteBtn.onclick = function() { showUnmuteConfirm(playerName); };\n");
        js.append("                } else {\n");
        js.append("                    muteBtn.className = 'action-btn mute-btn';\n");
        js.append("                    btnIcon.textContent = '🔇';\n");
        js.append("                    btnText.textContent = '禁言玩家';\n");
        js.append("                    // 更新点击事件为禁言\n");
        js.append("                    muteBtn.onclick = function() { showMuteModal(playerName); };\n");
        js.append("                }\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 更新所有操作按钮状态\n");
        js.append("        function updateAllActionButtonsStatus(playerName) {\n");
        js.append("            if (!playerName) return;\n");
        js.append("            updateBanButtonStatus(playerName);\n");
        js.append("            updateMuteButtonStatus(playerName);\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 强制更新玩家徽章（用于管理员玩家页面）\n");
        js.append("        function forceUpdatePlayerBadges(playerName) {\n");
        js.append("            if (!playerName) return;\n");
        js.append("            \n");
        js.append("            // 获取当前页面的最新数据\n");
        js.append("            const currentUrl = window.location.href;\n");
        js.append("            \n");
        js.append("            fetch(currentUrl, {\n");
        js.append("                method: 'GET',\n");
        js.append("                headers: {\n");
        js.append("                    'X-Requested-With': 'XMLHttpRequest',\n");
        js.append("                    'Cache-Control': 'no-cache'\n");
        js.append("                }\n");
        js.append("            })\n");
        js.append("            .then(response => response.text())\n");
        js.append("            .then(html => {\n");
        js.append("                const parser = new DOMParser();\n");
        js.append("                const doc = parser.parseFromString(html, 'text/html');\n");
        js.append("                \n");
        js.append("                // 更新所有徽章\n");
        js.append("                const newBadges = doc.querySelectorAll('.badge');\n");
        js.append("                newBadges.forEach(newBadge => {\n");
        js.append("                    const dataType = newBadge.getAttribute('data-type');\n");
        js.append("                    const dataStat = newBadge.getAttribute('data-stat');\n");
        js.append("                    const href = newBadge.getAttribute('href');\n");
        js.append("                    \n");
        js.append("                    // 找到对应的当前徽章\n");
        js.append("                    let currentBadge = null;\n");
        js.append("                    if (dataType && dataStat) {\n");
        js.append(
                "                        currentBadge = document.querySelector(`[data-type='${dataType}'][data-stat='${dataStat}']`);\n");
        js.append("                    } else if (href) {\n");
        js.append("                        currentBadge = document.querySelector(`[href='${href}']`);\n");
        js.append("                    }\n");
        js.append("                    \n");
        js.append("                    if (currentBadge) {\n");
        js.append("                        // 更新徽章内容\n");
        js.append("                        const newCount = newBadge.querySelector('.badge-count');\n");
        js.append("                        const currentCount = currentBadge.querySelector('.badge-count');\n");
        js.append("                        \n");
        js.append("                        if (newCount && currentCount) {\n");
        js.append("                            const newValue = parseInt(newCount.textContent) || 0;\n");
        js.append("                            currentCount.textContent = newValue;\n");
        js.append("                            \n");
        js.append("                            // 根据数量显示或隐藏徽章\n");
        js.append("                            if (newValue > 0) {\n");
        js.append("                                currentBadge.style.display = '';\n");
        js.append("                            } else {\n");
        js.append("                                currentBadge.style.display = 'none';\n");
        js.append("                            }\n");
        js.append("                        }\n");
        js.append("                    }\n");
        js.append("                });\n");
        js.append("                \n");
        js.append("                console.log('玩家徽章已强制更新');\n");
        js.append("            })\n");
        js.append("            .catch(error => {\n");
        js.append("                console.warn('强制更新玩家徽章失败:', error);\n");
        js.append("            });\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 页面加载时更新按钮状态\n");
        js.append("        document.addEventListener('DOMContentLoaded', function() {\n");
        js.append("            const playerName = window.location.pathname.split('/').pop();\n");
        js.append("            if (playerName) {\n");
        js.append("                updateAllActionButtonsStatus(playerName);\n");
        js.append("            }\n");
        js.append("        });\n");
        js.append("        \n");
        js.append("        // 键盘事件处理\n");
        js.append("        document.addEventListener('keydown', function(event) {\n");
        js.append("            if (event.key === 'Escape') {\n");
        js.append("                // 关闭所有打开的模态框\n");
        js.append("                closeBanModal();\n");
        js.append("                closeMuteModal();\n");
        js.append("                closeUnmuteModal();\n");
        js.append("                closeWarnModal();\n");
        js.append("                closeKickModal();\n");
        js.append("            }\n");
        js.append("        });\n");

        return js.toString();
    }

    /**
     * 生成模态框CSS样式
     */
    public static String generateModalStyles() {
        StringBuilder css = new StringBuilder();

        css.append("        /* 模态框样式 */\n");
        css.append("        .action-modal {\n");
        css.append("            position: fixed;\n");
        css.append("            top: 0;\n");
        css.append("            left: 0;\n");
        css.append("            width: 100%;\n");
        css.append("            height: 100%;\n");
        css.append("            display: flex;\n");
        css.append("            align-items: center;\n");
        css.append("            justify-content: center;\n");
        css.append("            z-index: 1000;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-overlay {\n");
        css.append("            position: absolute;\n");
        css.append("            top: 0;\n");
        css.append("            left: 0;\n");
        css.append("            width: 100%;\n");
        css.append("            height: 100%;\n");
        css.append("            background: rgba(0, 0, 0, 0.5);\n");
        css.append("            backdrop-filter: blur(2px);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-content {\n");
        css.append("            position: relative;\n");
        css.append("            background: white;\n");
        css.append("            border-radius: 8px;\n");
        css.append("            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n");
        css.append("            max-width: 500px;\n");
        css.append("            width: 90%;\n");
        css.append("            max-height: 90vh;\n");
        css.append("            overflow-y: auto;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-header {\n");
        css.append("            display: flex;\n");
        css.append("            align-items: center;\n");
        css.append("            justify-content: space-between;\n");
        css.append("            padding: 20px;\n");
        css.append("            border-bottom: 1px solid #dee2e6;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-title {\n");
        css.append("            margin: 0;\n");
        css.append("            font-size: 18px;\n");
        css.append("            font-weight: 600;\n");
        css.append("            color: #495057;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-close {\n");
        css.append("            background: none;\n");
        css.append("            border: none;\n");
        css.append("            font-size: 20px;\n");
        css.append("            cursor: pointer;\n");
        css.append("            color: #6c757d;\n");
        css.append("            padding: 0;\n");
        css.append("            width: 30px;\n");
        css.append("            height: 30px;\n");
        css.append("            display: flex;\n");
        css.append("            align-items: center;\n");
        css.append("            justify-content: center;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-close:hover {\n");
        css.append("            color: #495057;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-body {\n");
        css.append("            padding: 20px;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .modal-footer {\n");
        css.append("            display: flex;\n");
        css.append("            justify-content: flex-end;\n");
        css.append("            gap: 10px;\n");
        css.append("            padding: 20px;\n");
        css.append("            border-top: 1px solid #dee2e6;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        /* 表单样式 */\n");
        css.append("        .form-group {\n");
        css.append("            margin-bottom: 15px;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .form-group label {\n");
        css.append("            display: block;\n");
        css.append("            margin-bottom: 5px;\n");
        css.append("            font-weight: 500;\n");
        css.append("            color: #495057;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .form-group input[type=\"text\"],\n");
        css.append("        .form-group select {\n");
        css.append("            width: 100%;\n");
        css.append("            padding: 8px 12px;\n");
        css.append("            border: 1px solid #ced4da;\n");
        css.append("            border-radius: 4px;\n");
        css.append("            font-size: 14px;\n");
        css.append("            transition: border-color 0.15s ease-in-out;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .form-group input[type=\"text\"]:focus,\n");
        css.append("        .form-group select:focus {\n");
        css.append("            outline: none;\n");
        css.append("            border-color: #80bdff;\n");
        css.append("            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .form-group input[readonly] {\n");
        css.append("            background-color: #e9ecef;\n");
        css.append("            opacity: 1;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .form-group input[type=\"checkbox\"] {\n");
        css.append("            margin-right: 8px;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        /* 按钮样式 */\n");
        css.append("        .btn {\n");
        css.append("            padding: 8px 16px;\n");
        css.append("            border: none;\n");
        css.append("            border-radius: 4px;\n");
        css.append("            font-size: 14px;\n");
        css.append("            font-weight: 500;\n");
        css.append("            cursor: pointer;\n");
        css.append("            transition: all 0.15s ease-in-out;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn-secondary {\n");
        css.append("            background-color: #6c757d;\n");
        css.append("            color: white;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn-secondary:hover {\n");
        css.append("            background-color: #5a6268;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn-danger {\n");
        css.append("            background-color: #dc3545;\n");
        css.append("            color: white;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn-danger:hover {\n");
        css.append("            background-color: #c82333;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn-warning {\n");
        css.append("            background-color: #ffc107;\n");
        css.append("            color: #212529;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn-warning:hover {\n");
        css.append("            background-color: #e0a800;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .btn:disabled {\n");
        css.append("            opacity: 0.6;\n");
        css.append("            cursor: not-allowed;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        /* 通知弹窗样式 */\n");
        css.append("        .custom-notification {\n");
        css.append("            position: fixed;\n");
        css.append("            top: 50%;\n");
        css.append("            left: 50%;\n");
        css.append("            transform: translate(-50%, -50%) scale(0.8);\n");
        css.append("            z-index: 10000;\n");
        css.append("            min-width: 380px;\n");
        css.append("            max-width: 520px;\n");
        css.append("            background: white;\n");
        css.append("            border-radius: 20px;\n");
        css.append(
                "            box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8);\n");
        css.append("            opacity: 0;\n");
        css.append("            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\n");
        css.append("            backdrop-filter: blur(20px);\n");
        css.append("            overflow: hidden;\n");
        css.append("            border: 1px solid rgba(255, 255, 255, 0.2);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .custom-notification::before {\n");
        css.append("            content: '';\n");
        css.append("            position: absolute;\n");
        css.append("            top: 0;\n");
        css.append("            left: 0;\n");
        css.append("            right: 0;\n");
        css.append("            height: 5px;\n");
        css.append("            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);\n");
        css.append("            animation: shimmer 2s ease-in-out infinite;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        @keyframes shimmer {\n");
        css.append("            0%, 100% { opacity: 1; }\n");
        css.append("            50% { opacity: 0.7; }\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .custom-notification.show {\n");
        css.append("            opacity: 1;\n");
        css.append("            transform: translate(-50%, -50%) scale(1);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-content {\n");
        css.append("            display: flex;\n");
        css.append("            align-items: center;\n");
        css.append("            padding: 28px;\n");
        css.append("            gap: 20px;\n");
        css.append("            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);\n");
        css.append("            position: relative;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-content::after {\n");
        css.append("            content: '';\n");
        css.append("            position: absolute;\n");
        css.append("            top: 0;\n");
        css.append("            left: 0;\n");
        css.append("            right: 0;\n");
        css.append("            bottom: 0;\n");
        css.append(
                "            background: radial-gradient(circle at 20% 20%, rgba(120, 119, 198, 0.05) 0%, transparent 50%);\n");
        css.append("            pointer-events: none;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-icon {\n");
        css.append("            font-size: 32px;\n");
        css.append("            flex-shrink: 0;\n");
        css.append("            width: 56px;\n");
        css.append("            height: 56px;\n");
        css.append("            display: flex;\n");
        css.append("            align-items: center;\n");
        css.append("            justify-content: center;\n");
        css.append("            border-radius: 50%;\n");
        css.append("            background: rgba(255, 255, 255, 0.9);\n");
        css.append("            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.8);\n");
        css.append("            position: relative;\n");
        css.append("            z-index: 1;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-message {\n");
        css.append("            flex: 1;\n");
        css.append("            font-size: 18px;\n");
        css.append("            font-weight: 600;\n");
        css.append("            color: #2d3748;\n");
        css.append("            line-height: 1.5;\n");
        css.append("            position: relative;\n");
        css.append("            z-index: 1;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-close {\n");
        css.append("            background: rgba(255, 255, 255, 0.8);\n");
        css.append("            border: 1px solid rgba(0, 0, 0, 0.1);\n");
        css.append("            font-size: 18px;\n");
        css.append("            color: #718096;\n");
        css.append("            cursor: pointer;\n");
        css.append("            padding: 0;\n");
        css.append("            width: 32px;\n");
        css.append("            height: 32px;\n");
        css.append("            display: flex;\n");
        css.append("            align-items: center;\n");
        css.append("            justify-content: center;\n");
        css.append("            border-radius: 50%;\n");
        css.append("            transition: all 0.3s ease;\n");
        css.append("            flex-shrink: 0;\n");
        css.append("            position: relative;\n");
        css.append("            z-index: 1;\n");
        css.append("            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-close:hover {\n");
        css.append("            background: rgba(239, 68, 68, 0.1);\n");
        css.append("            color: #ef4444;\n");
        css.append("            border-color: rgba(239, 68, 68, 0.2);\n");
        css.append("            transform: scale(1.1);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        /* 不同类型的通知样式 */\n");
        css.append("        .notification-success {\n");
        css.append("            border: 2px solid rgba(34, 197, 94, 0.2);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-success::before {\n");
        css.append("            background: linear-gradient(90deg, #10b981 0%, #34d399 50%, #6ee7b7 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-success .notification-content {\n");
        css.append("            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-success .notification-icon {\n");
        css.append("            background: linear-gradient(135deg, #10b981 0%, #34d399 100%);\n");
        css.append("            color: white;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-error {\n");
        css.append("            border: 2px solid rgba(239, 68, 68, 0.2);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-error::before {\n");
        css.append("            background: linear-gradient(90deg, #ef4444 0%, #f87171 50%, #fca5a5 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-error .notification-content {\n");
        css.append("            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-error .notification-icon {\n");
        css.append("            background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);\n");
        css.append("            color: white;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-warning {\n");
        css.append("            border: 2px solid rgba(245, 158, 11, 0.2);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-warning::before {\n");
        css.append("            background: linear-gradient(90deg, #f59e0b 0%, #fbbf24 50%, #fcd34d 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-warning .notification-content {\n");
        css.append("            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-warning .notification-icon {\n");
        css.append("            background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);\n");
        css.append("            color: white;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-info {\n");
        css.append("            border: 2px solid rgba(59, 130, 246, 0.2);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-info::before {\n");
        css.append("            background: linear-gradient(90deg, #3b82f6 0%, #60a5fa 50%, #93c5fd 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-info .notification-content {\n");
        css.append("            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .notification-info .notification-icon {\n");
        css.append("            background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);\n");
        css.append("            color: white;\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        /* 响应式设计 */\n");
        css.append("        @media (max-width: 480px) {\n");
        css.append("            .custom-notification {\n");
        css.append("                min-width: 280px;\n");
        css.append("                max-width: 90vw;\n");
        css.append("                margin: 0 20px;\n");
        css.append("            }\n");
        css.append("            \n");
        css.append("            .notification-content {\n");
        css.append("                padding: 16px;\n");
        css.append("                gap: 12px;\n");
        css.append("            }\n");
        css.append("            \n");
        css.append("            .notification-message {\n");
        css.append("                font-size: 14px;\n");
        css.append("            }\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        /* 夜间模式样式 */\n");
        css.append("        .dark .modal-content {\n");
        css.append("            background: hsl(var(--card));\n");
        css.append("            color: hsl(var(--card-foreground));\n");
        css.append("            border: 1px solid hsl(var(--border));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .modal-header {\n");
        css.append("            border-bottom-color: hsl(var(--border));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .modal-title {\n");
        css.append("            color: hsl(var(--foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .modal-close {\n");
        css.append("            color: hsl(var(--muted-foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .modal-close:hover {\n");
        css.append("            color: hsl(var(--foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .modal-footer {\n");
        css.append("            border-top-color: hsl(var(--border));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .form-group label {\n");
        css.append("            color: hsl(var(--foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .form-group input[type=\"text\"],\n");
        css.append("        .dark .form-group select {\n");
        css.append("            background: hsl(var(--background));\n");
        css.append("            border-color: hsl(var(--border));\n");
        css.append("            color: hsl(var(--foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .form-group input[type=\"text\"]:focus,\n");
        css.append("        .dark .form-group select:focus {\n");
        css.append("            border-color: hsl(var(--ring));\n");
        css.append("            box-shadow: 0 0 0 0.2rem hsl(var(--ring) / 0.25);\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .form-group input[readonly] {\n");
        css.append("            background-color: hsl(var(--muted));\n");
        css.append("            color: hsl(var(--muted-foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .custom-notification {\n");
        css.append("            background: hsl(var(--card));\n");
        css.append("            border-color: hsl(var(--border));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .notification-content {\n");
        css.append("            background: hsl(var(--background));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .notification-message {\n");
        css.append("            color: hsl(var(--foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .notification-close {\n");
        css.append("            background: hsl(var(--muted));\n");
        css.append("            border-color: hsl(var(--border));\n");
        css.append("            color: hsl(var(--muted-foreground));\n");
        css.append("        }\n");
        css.append("        \n");
        css.append("        .dark .notification-close:hover {\n");
        css.append("            background: hsl(var(--destructive) / 0.1);\n");
        css.append("            color: hsl(var(--destructive));\n");
        css.append("            border-color: hsl(var(--destructive) / 0.2);\n");
        css.append("        }\n");

        return css.toString();
    }
}
