package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.ban.BanManager;
import cn.acebrand.acekeysystem.ban.BanRecord;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 封禁页面处理器
 */
public class BanHandler implements HttpHandler {
    
    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final BanManager banManager;
    private final BanPageGenerator pageGenerator;
    
    public BanHandler(AceKeySystem plugin, WebServer webServer, BanManager banManager) {
        this.plugin = plugin;
        this.webServer = webServer;
        this.banManager = banManager;
        this.pageGenerator = new BanPageGenerator(plugin);
    }
    
    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        URI uri = exchange.getRequestURI();
        String path = uri.getPath();
        
        if (webServer.isDebug()) {
            plugin.getLogger().info("封禁页面请求: " + method + " " + path);
        }
        
        try {
            if ("GET".equals(method)) {
                handleGetRequest(exchange);
            } else {
                sendErrorResponse(exchange, 405, "方法不允许");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理封禁页面请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "服务器内部错误");
        }
    }
    
    /**
     * 处理GET请求
     */
    private void handleGetRequest(HttpExchange exchange) throws IOException {
        // 检查封禁功能是否启用
        if (!banManager.isEnabled()) {
            sendErrorResponse(exchange, 503, "封禁记录功能未启用");
            return;
        }
        
        // 解析查询参数
        String query = exchange.getRequestURI().getQuery();
        Map<String, String> params = parseQuery(query);
        
        // 获取分页参数
        int page = 1;
        try {
            String pageParam = params.get("page");
            if (pageParam != null && !pageParam.isEmpty()) {
                page = Math.max(1, Integer.parseInt(pageParam));
            }
        } catch (NumberFormatException e) {
            page = 1;
        }
        
        // 获取搜索参数
        String searchQuery = params.get("search");
        if (searchQuery != null) {
            searchQuery = java.net.URLDecoder.decode(searchQuery, "UTF-8").trim();
            if (searchQuery.isEmpty()) {
                searchQuery = null;
            }
        }
        
        // 获取每页记录数
        int pageSize = plugin.getConfig().getInt("litebans.display.records-per-page", 10);
        pageSize = Math.max(5, Math.min(50, pageSize)); // 限制在5-50之间
        
        try {
            // 获取封禁记录
            List<BanRecord> records;
            int totalRecords;
            
            if (searchQuery != null && !searchQuery.isEmpty()) {
                // 搜索模式
                records = banManager.searchBanRecordsByPlayer(searchQuery, page, pageSize);
                // 注意：这里简化处理，实际应该有专门的搜索计数方法
                totalRecords = records.size() + (page - 1) * pageSize;
                if (records.size() < pageSize) {
                    totalRecords = (page - 1) * pageSize + records.size();
                } else {
                    totalRecords = page * pageSize + 1; // 假设还有更多记录
                }
            } else {
                // 正常分页模式
                records = banManager.getBanRecords(page, pageSize);
                totalRecords = banManager.getTotalBanCount();
            }
            
            // 计算总页数
            int totalPages = (int) Math.ceil((double) totalRecords / pageSize);
            
            // 获取统计信息
            Map<String, Object> statistics = banManager.getBanStatistics();
            
            // 生成页面HTML
            String html = pageGenerator.generateBanPage(records, page, totalPages, statistics, searchQuery);
            
            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);
            
        } catch (Exception e) {
            plugin.getLogger().severe("获取封禁记录时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取封禁记录失败");
        }
    }
    
    /**
     * 解析查询参数
     */
    private Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return params;
    }
    
    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String content)
            throws IOException {
        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");
        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }
    
    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        String html = generateErrorPage(statusCode, message);
        sendResponse(exchange, statusCode, "text/html; charset=utf-8", html);
    }
    
    /**
     * 生成错误页面
     */
    private String generateErrorPage(int statusCode, String message) {
        return "<!DOCTYPE html>" +
                "<html lang=\"zh-CN\">" +
                "<head>" +
                "<meta charset=\"UTF-8\">" +
                "<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">" +
                "<title>错误 " + statusCode + " - " + plugin.getConfig().getString("web-server.title", "服务器管理") + "</title>" +
                "<style>" +
                "body { font-family: 'Microsoft YaHei', sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); " +
                "min-height: 100vh; display: flex; align-items: center; justify-content: center; margin: 0; }" +
                ".error-container { background: rgba(255, 255, 255, 0.95); padding: 40px; border-radius: 15px; " +
                "box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); text-align: center; max-width: 500px; }" +
                ".error-code { font-size: 4em; color: #e74c3c; margin-bottom: 20px; font-weight: bold; }" +
                ".error-message { font-size: 1.2em; color: #333; margin-bottom: 30px; }" +
                ".back-btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; " +
                "text-decoration: none; border-radius: 8px; transition: all 0.3s ease; }" +
                ".back-btn:hover { background: #5a6fd8; transform: translateY(-2px); }" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<div class=\"error-container\">" +
                "<div class=\"error-code\">" + statusCode + "</div>" +
                "<div class=\"error-message\">" + escapeHtml(message) + "</div>" +
                "<a href=\"/\" class=\"back-btn\">返回首页</a>" +
                "</div>" +
                "</body>" +
                "</html>";
    }
    
    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null) return "";
        return text.replace("&", "&amp;")
                  .replace("<", "&lt;")
                  .replace(">", "&gt;")
                  .replace("\"", "&quot;")
                  .replace("'", "&#x27;");
    }
}
