package cn.acebrand.acekeysystem.lottery;

import java.util.List;

/**
 * 抽奖奖励类
 */
public class LotteryReward {
    private final String id;
    private final String name;
    private final String description;
    private final int weight;
    private final double probability;
    private final List<String> commands;

    public LotteryReward(String id, String name, String description, int weight, double probability,
            List<String> commands) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.weight = weight;
        this.probability = probability;
        this.commands = commands;
    }

    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public int getWeight() {
        return weight;
    }

    public double getProbability() {
        return probability;
    }

    public List<String> getCommands() {
        return commands;
    }

    public boolean hasCommands() {
        return commands != null && !commands.isEmpty();
    }
}
