package cn.acebrand.acekeysystem.web;

/**
 * 签到页面生成器
 * 负责生成签到页面的HTML、CSS和JavaScript
 */
public class CheckInPageGenerator {

        /**
         * 生成签到页面HTML
         */
        public String generateCheckInPage() {
                StringBuilder html = new StringBuilder();

                html.append("<!DOCTYPE html>\n");
                html.append("<html lang=\"zh-CN\">\n");
                html.append("<head>\n");
                html.append("    <meta charset=\"UTF-8\">\n");
                html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
                html.append("    <title>每日签到 - AceKeySystem</title>\n");
                html.append("    <link rel=\"stylesheet\" href=\"/checkin/style.css\">\n");
                html.append(
                                "    <link rel=\"stylesheet\" href=\"https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css\">\n");
                html.append("</head>\n");
                html.append("<body>\n");

                // 导航栏
                html.append(generateNavigation());

                // 主要内容
                html.append("    <div class=\"container\">\n");

                // 日期标题（最上面，小一点）
                html.append(generateCompactDateDisplay());

                // 顶部状态区域（绑定和积分并排）
                html.append(generateTopStatusSection());

                // 签到卡片区域
                html.append(generateCalendar());

                // 签到状态区域（底部）
                html.append(generateStatusArea());

                // 奖励说明已集成到左侧布局中

                // 绑定码弹窗
                html.append(generateBindModal());

                // 购买弹窗
                html.append(generatePurchaseModal());

                html.append("    </div>\n");

                // 脚本
                html.append("    <script src=\"/checkin/script.js\"></script>\n");
                html.append("</body>\n");
                html.append("</html>\n");

                return html.toString();
        }

        /**
         * 生成导航栏
         */
        private String generateNavigation() {
                StringBuilder html = new StringBuilder();

                html.append("    <nav class=\"navbar\">\n");
                html.append("        <div class=\"nav-container\">\n");
                html.append("            <div class=\"nav-brand\">\n");
                html.append("                <i class=\"fas fa-key\"></i>\n");
                html.append("                <span>AceKeySystem</span>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"nav-links\">\n");
                html.append("                <a href=\"/\" class=\"nav-link\">\n");
                html.append("                    <i class=\"fas fa-home\"></i> 首页\n");
                html.append("                </a>\n");
                html.append("                <a href=\"/points-shop\" class=\"nav-link\">\n");
                html.append("                    <i class=\"fas fa-shopping-cart\"></i> 积分商店\n");
                html.append("                </a>\n");
                html.append("                <a href=\"/checkin\" class=\"nav-link active\">\n");
                html.append("                    <i class=\"fas fa-calendar-check\"></i> 每日签到\n");
                html.append("                </a>\n");
                html.append("                <a href=\"/leaderboard\" class=\"nav-link\">\n");
                html.append("                    <i class=\"fas fa-trophy\"></i> 排行榜\n");
                html.append("                </a>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </nav>\n");

                return html.toString();
        }

        /**
         * 生成紧凑的日期显示区域
         */
        private String generateCompactDateDisplay() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 紧凑日期显示区域 -->\n");
                html.append("        <div class=\"compact-date-display\">\n");
                html.append("            <div class=\"compact-date\">\n");
                html.append("                <span class=\"compact-month\" id=\"currentMonth\">2024年1月</span>\n");
                html.append("                <span class=\"compact-day\" id=\"currentDay\">1</span>\n");
                html.append("                <span class=\"compact-day-name\" id=\"dayName\">星期一</span>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成日期显示区域（保留原版本）
         */
        private String generateDateDisplay() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 日期显示区域 -->\n");
                html.append("        <div class=\"date-display\">\n");
                html.append("            <div class=\"current-date\">\n");
                html.append("                <div class=\"year-month\" id=\"currentMonth\">2024年1月</div>\n");
                html.append("                <div class=\"current-day\" id=\"currentDay\">1</div>\n");
                html.append("                <div class=\"day-name\" id=\"dayName\">星期一</div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成顶部状态区域（绑定和积分并排）
         */
        private String generateTopStatusSection() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 顶部状态区域 -->\n");
                html.append("        <div class=\"top-status-section\">\n");
                html.append("            <!-- 绑定状态卡片 -->\n");
                html.append("            <div class=\"status-card bind-card\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <div class=\"header-left\">\n");
                html.append("                        <i class=\"fas fa-link\"></i>\n");
                html.append("                        <span>账户绑定</span>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"card-content\" id=\"bindStatus\">\n");
                html.append("                    <div class=\"bind-info\">\n");
                html.append("                        <p>请先绑定您的游戏账户</p>\n");
                html.append(
                                "                        <button class=\"action-btn bind-btn\" onclick=\"generateBindCode()\" aria-label=\"生成账户绑定码\">🎯 生成绑定码</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 货币状态卡片 -->\n");
                html.append(
                                "            <div class=\"status-card currency-card\" id=\"currencySection\" style=\"display: none;\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <div class=\"header-left\">\n");
                html.append("                        <i class=\"fas fa-wallet\"></i>\n");
                html.append("                        <span>我的资产</span>\n");
                html.append("                    </div>\n");
                html.append("                    <button class=\"action-btn refresh-btn\" onclick=\"refreshAllCurrency()\" title=\"刷新资产\" aria-label=\"刷新所有资产数据\">🔄</button>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"card-content\">\n");
                html.append("                    <div class=\"currency-grid\">\n");
                html.append("                        <div class=\"currency-item\">\n");
                html.append("                            <div class=\"currency-icon\">⭐</div>\n");
                html.append("                            <div class=\"currency-info\">\n");
                html.append("                                <span class=\"currency-label\">积分</span>\n");
                html.append("                                <span class=\"currency-value\" id=\"userPoints\">加载中...</span>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"currency-item\">\n");
                html.append("                            <div class=\"currency-icon\">💰</div>\n");
                html.append("                            <div class=\"currency-info\">\n");
                html.append("                                <span class=\"currency-label\">金币</span>\n");
                html.append("                                <span class=\"currency-value\" id=\"userGold\">加载中...</span>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"currency-item\">\n");
                html.append("                            <div class=\"currency-icon\">🎫</div>\n");
                html.append("                            <div class=\"currency-info\">\n");
                html.append("                                <span class=\"currency-label\">点券</span>\n");
                html.append("                                <span class=\"currency-value\" id=\"userTokens\">加载中...</span>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"currency-item\">\n");
                html.append("                            <div class=\"currency-icon\">🎟️</div>\n");
                html.append("                            <div class=\"currency-info\">\n");
                html.append("                                <span class=\"currency-label\">补签卡</span>\n");
                html.append("                                <span class=\"currency-value\" id=\"userMakeupCards\">加载中...</span>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成绑定状态区域（保留原版本）
         */
        private String generateBindSection() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 绑定状态区域 -->\n");
                html.append("        <div class=\"bind-section\">\n");
                html.append("            <div class=\"bind-status\" id=\"bindStatus\">\n");
                html.append("                <div class=\"bind-info\">\n");
                html.append("                    <h3>🔗 账户绑定</h3>\n");
                html.append("                    <p>请先绑定您的游戏账户才能使用签到功能</p>\n");
                html.append(
                                "                    <button class=\"bind-btn\" onclick=\"generateBindCode()\" aria-label=\"生成账户绑定码\">🎯 生成绑定码</button>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");
                html.append("        <!-- 积分显示区域 -->\n");
                html.append("        <div class=\"points-section\" id=\"pointsSection\" style=\"display: none;\">\n");
                html.append("            <div class=\"user-points\">\n");
                html.append("                <span class=\"points-label\">我的积分:</span>\n");
                html.append("                <span class=\"points-value\" id=\"userPoints\">加载中...</span>\n");
                html.append(
                                "                <button class=\"refresh-btn\" onclick=\"refreshPoints()\" title=\"刷新积分\" aria-label=\"刷新积分数据\">🔄</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成绑定码弹窗
         */
        private String generateBindModal() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 绑定码弹窗 -->\n");
                html.append("        <div id=\"bindModal\" class=\"bind-modal\" style=\"display: none;\">\n");
                html.append("            <div class=\"bind-modal-content\">\n");
                html.append("                <div class=\"bind-modal-header\">\n");
                html.append("                    <h2>🔗 账户绑定</h2>\n");
                html.append(
                                "                    <button class=\"bind-close-btn\" onclick=\"closeBindModal()\" aria-label=\"关闭绑定弹窗\">×</button>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"bind-modal-body\">\n");
                html.append("                    <div class=\"bind-step\">\n");
                html.append("                        <h3>步骤 1: 复制绑定码</h3>\n");
                html.append("                        <div class=\"bind-code-container\">\n");
                html.append("                            <label for=\"bindCodeInput\" class=\"sr-only\">绑定码</label>\n");
                html.append(
                                "                            <input type=\"text\" id=\"bindCodeInput\" readonly class=\"bind-code-input\" \n");
                html.append("                                   title=\"绑定码\" placeholder=\"绑定码将在这里显示\" \n");
                html.append("                                   aria-label=\"绑定码，只读\" aria-describedby=\"bindCodeNote\">\n");
                html.append(
                                "                            <button class=\"copy-btn\" onclick=\"copyBindCode(event)\" \n");
                html.append("                                    aria-label=\"复制绑定码到剪贴板\">📋 复制</button>\n");
                html.append("                        </div>\n");
                html.append("                        <p class=\"bind-code-note\" id=\"bindCodeNote\">绑定码有效期：5分钟</p>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"bind-step\">\n");
                html.append("                        <h3>步骤 2: 游戏内绑定</h3>\n");
                html.append("                        <p>在游戏中输入以下命令：</p>\n");
                html.append("                        <div class=\"command-container\">\n");
                html.append("                            <code>/acebind <span id=\"bindCodeDisplay\">XXXXXX</span></code>\n");
                html.append(
                                "                            <button class=\"copy-btn\" onclick=\"copyCommand(event)\" aria-label=\"复制绑定命令到剪贴板\">📋 复制命令</button>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"bind-status-check\">\n");
                html.append("                        <p id=\"bindStatusText\">等待绑定...</p>\n");
                html.append("                        <div class=\"bind-progress\">\n");
                html.append("                            <div class=\"bind-progress-bar\" id=\"bindProgressBar\"></div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成状态区域（已移除状态卡片）
         */
        private String generateStatusArea() {
                StringBuilder html = new StringBuilder();

                // 状态区域已被移除，返回空的隐藏div以保持兼容性
                html.append("        <div id=\"statusArea\" class=\"status-area\" style=\"display: none;\">\n");
                html.append("            <!-- 状态卡片已移除 -->\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成签到日历
         */
        private String generateCalendar() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 签到日历区域 -->\n");
                html.append("        <div id=\"calendarArea\" class=\"calendar-area\">\n");
                html.append("            <div class=\"calendar-layout\">\n");
                html.append("                <!-- 左侧信息区域 -->\n");
                html.append("                <div class=\"left-info-section\">\n");
                html.append("                    <!-- 状态卡片 -->\n");
                html.append("                    <div class=\"left-status-cards\">\n");
                html.append("                        <div class=\"status-card compact\">\n");
                html.append("                            <div class=\"status-icon\">\n");
                html.append("                                <i class=\"fas fa-calendar-day\"></i>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"status-content\">\n");
                html.append("                                <div class=\"status-label\">今日状态</div>\n");
                html.append("                                <div class=\"status-value\" id=\"todayStatus\">-</div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                        \n");
                html.append("                        <div class=\"status-card compact\">\n");
                html.append("                            <div class=\"status-icon\">\n");
                html.append("                                <i class=\"fas fa-fire\"></i>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"status-content\">\n");
                html.append("                                <div class=\"status-label\">连续签到</div>\n");
                html.append("                                <div class=\"status-value\" id=\"consecutiveDays\">0 天</div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                        \n");
                html.append("                        <div class=\"status-card compact\">\n");
                html.append("                            <div class=\"status-icon\">\n");
                html.append("                                <i class=\"fas fa-chart-line\"></i>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"status-content\">\n");
                html.append("                                <div class=\"status-label\">总签到天数</div>\n");
                html.append("                                <div class=\"status-value\" id=\"totalDays\">0 天</div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 奖励说明卡片 -->\n");
                html.append("                    <div class=\"reward-info-card left-side\">\n");
                html.append("                        <div class=\"card-header\">\n");
                html.append("                            <i class=\"fas fa-gift\"></i>\n");
                html.append("                            <span>签到奖励说明</span>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"reward-grid vertical\">\n");
                html.append("                            <div class=\"reward-item\">\n");
                html.append("                                <div class=\"reward-icon daily\">\n");
                html.append("                                    <i class=\"fas fa-calendar-day\"></i>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"reward-content\">\n");
                html.append("                                    <h4>每日奖励</h4>\n");
                html.append("                                    <p>每天签到获得 <strong>10积分</strong></p>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"reward-item\">\n");
                html.append("                                <div class=\"reward-icon weekly\">\n");
                html.append("                                    <i class=\"fas fa-calendar-week\"></i>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"reward-content\">\n");
                html.append("                                    <h4>连续7天</h4>\n");
                html.append("                                    <p>额外获得 <strong>50积分 + 钻石</strong></p>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"reward-item\">\n");
                html.append("                                <div class=\"reward-icon biweekly\">\n");
                html.append("                                    <i class=\"fas fa-fire\"></i>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"reward-content\">\n");
                html.append("                                    <h4>连续15天</h4>\n");
                html.append("                                    <p>额外获得 <strong>100积分 + 豪华奖励</strong></p>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"reward-item\">\n");
                html.append("                                <div class=\"reward-icon monthly\">\n");
                html.append("                                    <i class=\"fas fa-crown\"></i>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"reward-content\">\n");
                html.append("                                    <h4>连续30天</h4>\n");
                html.append("                                    <p>额外获得 <strong>200积分 + 超级奖励</strong></p>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 补签卡购买区域 -->\n");
                html.append("                    <div class=\"makeup-cards-section\">\n");
                html.append("                        <div class=\"section-header\">\n");
                html.append("                            <i class=\"fas fa-shopping-cart\"></i>\n");
                html.append("                            <span>补签卡商店</span>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"makeup-cards-grid\">\n");
                html.append("                            <!-- 金币购买 -->\n");
                html.append("                            <div class=\"makeup-card gold-card\">\n");
                html.append("                                <div class=\"card-icon\">\n");
                html.append("                                    <span class=\"emoji-icon\">💰</span>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"card-content\">\n");
                html.append("                                    <div class=\"card-title\">金币补签</div>\n");
                html.append("                                    <div class=\"card-price\" id=\"goldPrice\">100 金币</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"card-actions\">\n");
                html.append(
                                "                                    <button class=\"buy-btn gold-btn\" onclick=\"buyMakeupCard('gold')\" aria-label=\"购买金币补签卡\">\n");
                html.append("                                        🛒 购买\n");
                html.append("                                    </button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <!-- 点券购买 -->\n");
                html.append("                            <div class=\"makeup-card points-card\">\n");
                html.append("                                <div class=\"card-icon\">\n");
                html.append("                                    <span class=\"emoji-icon\">💎</span>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"card-content\">\n");
                html.append("                                    <div class=\"card-title\">点券补签</div>\n");
                html.append("                                    <div class=\"card-price\" id=\"pointsPrice\">50 点券</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"card-actions\">\n");
                html.append(
                                "                                    <button class=\"buy-btn points-btn\" onclick=\"buyMakeupCard('points')\" aria-label=\"购买点券补签卡\">\n");
                html.append("                                        🛒 购买\n");
                html.append("                                    </button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            \n");
                html.append("                            <!-- 积分购买 -->\n");
                html.append("                            <div class=\"makeup-card score-card\">\n");
                html.append("                                <div class=\"card-icon\">\n");
                html.append("                                    <span class=\"emoji-icon\">⭐</span>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"card-content\">\n");
                html.append("                                    <div class=\"card-title\">积分补签</div>\n");
                html.append("                                    <div class=\"card-price\" id=\"scorePrice\">200 积分</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"card-actions\">\n");
                html.append(
                                "                                    <button class=\"buy-btn score-btn\" onclick=\"buyMakeupCard('score')\" aria-label=\"购买积分补签卡\">\n");
                html.append("                                        🛒 购买\n");
                html.append("                                    </button>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 右侧区域 -->\n");
                html.append("                <div class=\"right-section\">\n");
                html.append("                    <!-- 签到卡片网格 -->\n");
                html.append("                    <div class=\"checkin-cards-container\">\n");
                html.append("                        <div class=\"checkin-cards-grid\" id=\"checkinCardsGrid\">\n");
                html.append("                            <!-- 签到卡片将通过JavaScript动态生成 -->\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成购买弹窗
         */
        private String generatePurchaseModal() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 购买弹窗 -->\n");
                html.append(
                                "        <div id=\"purchaseModal\" class=\"purchase-modal\" style=\"display: none;\" onclick=\"closePurchaseModal()\">\n");
                html.append("            <div class=\"purchase-modal-content\" onclick=\"event.stopPropagation()\">\n");
                html.append("                <div class=\"purchase-modal-header\">\n");
                html.append("                    <h2 id=\"purchaseTitle\">🛒 购买补签卡</h2>\n");
                html.append(
                                "                    <button class=\"purchase-close-btn\" onclick=\"closePurchaseModal()\">×</button>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"purchase-modal-body\">\n");
                html.append("                    <div class=\"purchase-item-info\">\n");
                html.append("                        <div class=\"item-icon\" id=\"purchaseIcon\">\n");
                html.append("                            <i class=\"fas fa-coins\"></i>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"item-details\">\n");
                html.append("                            <h3 id=\"purchaseItemName\">金币补签卡</h3>\n");
                html.append("                            <p id=\"purchaseItemDesc\">使用游戏内金币购买补签卡</p>\n");
                html.append("                            <div class=\"item-price\">\n");
                html.append("                                <span>单价：</span>\n");
                html.append(
                                "                                <span id=\"purchaseUnitPrice\" class=\"price-value\">100 金币</span>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <div class=\"purchase-quantity\">\n");
                html.append("                        <label for=\"purchaseQuantity\">购买数量：</label>\n");
                html.append("                        <div class=\"quantity-controls\">\n");
                html.append(
                                "                            <button type=\"button\" class=\"quantity-btn\" onclick=\"adjustQuantity(-1)\">-</button>\n");
                html.append(
                                "                            <input type=\"number\" id=\"purchaseQuantity\" value=\"1\" min=\"1\" max=\"99\" onchange=\"updateTotalPrice()\">\n");
                html.append(
                                "                            <button type=\"button\" class=\"quantity-btn\" onclick=\"adjustQuantity(1)\">+</button>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <div class=\"purchase-total\">\n");
                html.append("                        <div class=\"total-price\">\n");
                html.append("                            <span>总价：</span>\n");
                html.append(
                                "                            <span id=\"purchaseTotalPrice\" class=\"price-value\">100 金币</span>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"user-balance\">\n");
                html.append("                            <span>当前余额：</span>\n");
                html.append("                            <span id=\"userBalance\" class=\"balance-value\">-- --</span>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <div class=\"purchase-actions\">\n");
                html.append(
                                "                        <button class=\"btn btn-secondary\" onclick=\"closePurchaseModal()\">取消</button>\n");
                html.append(
                                "                        <button class=\"btn btn-primary\" id=\"confirmPurchaseBtn\" onclick=\"confirmPurchase()\">确认购买</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成奖励说明
         */
        private String generateRewardInfo() {
                StringBuilder html = new StringBuilder();

                html.append("        <!-- 签到说明卡片 -->\n");
                html.append("        <div class=\"reward-info-card\">\n");
                html.append("            <div class=\"card-header\">\n");
                html.append("                <i class=\"fas fa-gift\"></i>\n");
                html.append("                <span>签到奖励说明</span>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"reward-grid\">\n");
                html.append("                <div class=\"reward-item\">\n");
                html.append("                    <div class=\"reward-icon daily\">\n");
                html.append("                        <i class=\"fas fa-calendar-day\"></i>\n");
                html.append("                    </div>\n");
                html.append("                    <h4>每日奖励</h4>\n");
                html.append("                    <p>每天签到获得 <strong>10积分</strong></p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"reward-item\">\n");
                html.append("                    <div class=\"reward-icon weekly\">\n");
                html.append("                        <i class=\"fas fa-calendar-week\"></i>\n");
                html.append("                    </div>\n");
                html.append("                    <h4>连续7天</h4>\n");
                html.append("                    <p>额外获得 <strong>50积分 + 钻石</strong></p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"reward-item\">\n");
                html.append("                    <div class=\"reward-icon biweekly\">\n");
                html.append("                        <i class=\"fas fa-fire\"></i>\n");
                html.append("                    </div>\n");
                html.append("                    <h4>连续15天</h4>\n");
                html.append("                    <p>额外获得 <strong>100积分 + 豪华奖励</strong></p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"reward-item\">\n");
                html.append("                    <div class=\"reward-icon monthly\">\n");
                html.append("                        <i class=\"fas fa-crown\"></i>\n");
                html.append("                    </div>\n");
                html.append("                    <h4>连续30天</h4>\n");
                html.append("                    <p>额外获得 <strong>200积分 + 超级奖励</strong></p>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");

                return html.toString();
        }

        /**
         * 生成CSS样式
         */
        public String generateCSS() {
                StringBuilder css = new StringBuilder();

                // 基础样式
                css.append("/* 签到页面样式 */\n");
                css.append("* {\n");
                css.append("    margin: 0;\n");
                css.append("    padding: 0;\n");
                css.append("    box-sizing: border-box;\n");
                css.append("}\n\n");

                css.append("body {\n");
                css.append("    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n");
                css.append("    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                css.append("    min-height: 100vh;\n");
                css.append("    color: #333;\n");
                css.append("}\n\n");

                // 导航栏样式
                css.append("/* 导航栏 */\n");
                css.append(".navbar {\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);\n");
                css.append("    position: sticky;\n");
                css.append("    top: 0;\n");
                css.append("    z-index: 1000;\n");
                css.append("}\n\n");

                css.append(".nav-container {\n");
                css.append("    max-width: 1200px;\n");
                css.append("    margin: 0 auto;\n");
                css.append("    display: flex;\n");
                css.append("    justify-content: space-between;\n");
                css.append("    align-items: center;\n");
                css.append("    padding: 1rem 2rem;\n");
                css.append("}\n\n");

                css.append(".nav-brand {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    font-size: 1.5rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #667eea;\n");
                css.append("}\n\n");

                css.append(".nav-brand i {\n");
                css.append("    margin-right: 0.5rem;\n");
                css.append("}\n\n");

                css.append(".nav-links {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 1rem;\n");
                css.append("}\n\n");

                css.append(".nav-link {\n");
                css.append("    text-decoration: none;\n");
                css.append("    color: #666;\n");
                css.append("    padding: 0.5rem 1rem;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("}\n\n");

                css.append(".nav-link:hover, .nav-link.active {\n");
                css.append("    background: #667eea;\n");
                css.append("    color: white;\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("}\n\n");

                // 容器样式
                css.append("/* 主容器 */\n");
                css.append(".container {\n");
                css.append("    max-width: 1200px;\n");
                css.append("    margin: 0 auto;\n");
                css.append("    padding: 2rem;\n");
                css.append("}\n\n");

                // 紧凑日期显示样式
                css.append("/* 紧凑日期显示区域 */\n");
                css.append(".compact-date-display {\n");
                css.append("    text-align: center;\n");
                css.append("    margin: 1rem 0;\n");
                css.append("    background: rgba(255, 255, 255, 0.9);\n");
                css.append("    border-radius: 12px;\n");
                css.append("    padding: 1rem;\n");
                css.append("    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n");
                css.append("}\n\n");

                css.append(".compact-date {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    gap: 1rem;\n");
                css.append("}\n\n");

                css.append(".compact-month {\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #667eea;\n");
                css.append("}\n\n");

                css.append(".compact-day {\n");
                css.append("    font-size: 2rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                css.append("    color: white;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    width: 3rem;\n");
                css.append("    height: 3rem;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("}\n\n");

                css.append(".compact-day-name {\n");
                css.append("    font-size: 1rem;\n");
                css.append("    color: #666;\n");
                css.append("}\n\n");

                // 原版日期显示样式（保留）
                css.append("/* 原版日期显示区域 */\n");
                css.append(".date-display {\n");
                css.append("    text-align: center;\n");
                css.append("    margin: 2rem 0;\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    border-radius: 20px;\n");
                css.append("    padding: 2rem;\n");
                css.append("    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n");
                css.append("}\n\n");

                css.append(".current-date {\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 1rem;\n");
                css.append("}\n\n");

                css.append(".year-month {\n");
                css.append("    font-size: 2rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #667eea;\n");
                css.append("}\n\n");

                css.append(".current-day {\n");
                css.append("    font-size: 4rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("    line-height: 1;\n");
                css.append("}\n\n");

                css.append(".day-name {\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    color: #666;\n");
                css.append("}\n\n");

                // 顶部状态区域样式
                css.append("/* 顶部状态区域 */\n");
                css.append(".top-status-section {\n");
                css.append("    display: grid;\n");
                css.append("    grid-template-columns: 1fr 1fr;\n");
                css.append("    gap: 1rem;\n");
                css.append("    margin: 1.5rem 0;\n");
                css.append("    align-items: stretch;\n");
                css.append("}\n\n");

                css.append(".top-status-section .status-card {\n");
                css.append("    height: 100%;\n");
                css.append("    min-height: 140px;\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("}\n\n");

                css.append(".top-status-section .card-header {\n");
                css.append("    height: 2.5rem;\n");
                css.append("    min-height: 2.5rem;\n");
                css.append("    box-sizing: border-box;\n");
                css.append("}\n\n");

                css.append(".top-status-section .card-content {\n");
                css.append("    flex: 1;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: stretch;\n");
                css.append("    justify-content: center;\n");
                css.append("    padding: 0.75rem 1rem 1rem 1rem;\n");
                css.append("}\n\n");

                css.append(".status-card {\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    border-radius: 12px;\n");
                css.append("    padding: 0;\n");
                css.append("    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);\n");
                css.append("    border: 1px solid rgba(255, 255, 255, 0.2);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append(".status-card:hover {\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append(".card-header {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: space-between;\n");
                css.append("    padding: 0.5rem 1rem;\n");
                css.append("    margin-bottom: 0;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("    border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n");
                css.append("    min-height: 2.5rem;\n");
                css.append("}\n\n");

                css.append(".header-left {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    flex: 1;\n");
                css.append("}\n\n");

                css.append(".header-left span {\n");
                css.append("    font-size: 1rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    line-height: 1.2;\n");
                css.append("}\n\n");

                css.append(".card-header i {\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    width: 1.5rem;\n");
                css.append("    height: 1.5rem;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".bind-card .card-header i {\n");
                css.append("    color: #3b82f6;\n");
                css.append("}\n\n");

                css.append(".currency-card .card-header i {\n");
                css.append("    color: #10b981;\n");
                css.append("}\n\n");

                css.append(".card-content {\n");
                css.append("    text-align: center;\n");
                css.append("    padding: 0.75rem 1rem;\n");
                css.append("    flex: 1;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("}\n\n");

                css.append(".bind-info {\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    gap: 0.75rem;\n");
                css.append("    width: 100%;\n");
                css.append("}\n\n");

                css.append(".bind-info p {\n");
                css.append("    color: #666;\n");
                css.append("    margin: 0;\n");
                css.append("    font-size: 0.85rem;\n");
                css.append("    line-height: 1.3;\n");
                css.append("    text-align: center;\n");
                css.append("}\n\n");

                css.append(".action-btn {\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    padding: 0.75rem 1.5rem;\n");
                css.append("    font-size: 0.9rem;\n");
                css.append("    font-weight: 600;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".bind-btn {\n");
                css.append("    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n");
                css.append("    color: white;\n");
                css.append("}\n\n");

                css.append(".bind-btn:hover {\n");
                css.append("    transform: translateY(-1px);\n");
                css.append("    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.4);\n");
                css.append("}\n\n");

                css.append(".currency-grid {\n");
                css.append("    display: grid;\n");
                css.append("    grid-template-columns: repeat(2, 1fr);\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    width: 100%;\n");
                css.append("    height: 100%;\n");
                css.append("}\n\n");

                css.append(".currency-item {\n");
                css.append("    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);\n");
                css.append("    border: 1px solid #e2e8f0;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    padding: 0.875rem 0.75rem;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("    min-height: 48px;\n");
                css.append("}\n\n");

                css.append(".currency-item:hover {\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                css.append("    border-color: #cbd5e1;\n");
                css.append("}\n\n");

                css.append(".currency-item::before {\n");
                css.append("    content: '';\n");
                css.append("    position: absolute;\n");
                css.append("    top: 0;\n");
                css.append("    left: 0;\n");
                css.append("    right: 0;\n");
                css.append("    height: 3px;\n");
                css.append("    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);\n");
                css.append("    opacity: 0;\n");
                css.append("    transition: opacity 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".currency-item:hover::before {\n");
                css.append("    opacity: 1;\n");
                css.append("}\n\n");

                css.append(".currency-icon {\n");
                css.append("    font-size: 1rem;\n");
                css.append("    width: 1.75rem;\n");
                css.append("    height: 1.75rem;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n");
                css.append("    border-radius: 6px;\n");
                css.append("    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".currency-info {\n");
                css.append("    flex: 1;\n");
                css.append("    text-align: left;\n");
                css.append("}\n\n");

                css.append(".currency-label {\n");
                css.append("    display: block;\n");
                css.append("    font-size: 0.7rem;\n");
                css.append("    color: #64748b;\n");
                css.append("    font-weight: 500;\n");
                css.append("    margin-bottom: 0.1rem;\n");
                css.append("    line-height: 1;\n");
                css.append("}\n\n");

                css.append(".currency-value {\n");
                css.append("    display: block;\n");
                css.append("    font-size: 0.85rem;\n");
                css.append("    font-weight: 700;\n");
                css.append("    color: #1e293b;\n");
                css.append("    line-height: 1.1;\n");
                css.append("}\n\n");

                css.append(".currency-actions {\n");
                css.append("    text-align: center;\n");
                css.append("    margin-top: 1rem;\n");
                css.append("    padding-top: 1rem;\n");
                css.append("    border-top: 1px solid #e2e8f0;\n");
                css.append("}\n\n");

                // 不同货币类型的颜色主题
                css.append("/* 货币类型颜色主题 */\n");
                css.append(".currency-item:nth-child(1) {\n");
                css.append("    /* 积分 - 金色主题 */\n");
                css.append("    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n");
                css.append("    border-color: #f59e0b;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(1) .currency-value {\n");
                css.append("    color: #d97706;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(2) {\n");
                css.append("    /* 金币 - 橙色主题 */\n");
                css.append("    background: linear-gradient(135deg, #fed7aa 0%, #fdba74 100%);\n");
                css.append("    border-color: #ea580c;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(2) .currency-value {\n");
                css.append("    color: #c2410c;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(3) {\n");
                css.append("    /* 点券 - 蓝色主题 */\n");
                css.append("    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);\n");
                css.append("    border-color: #3b82f6;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(3) .currency-value {\n");
                css.append("    color: #1d4ed8;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(4) {\n");
                css.append("    /* 补签卡 - 紫色主题 */\n");
                css.append("    background: linear-gradient(135deg, #e9d5ff 0%, #ddd6fe 100%);\n");
                css.append("    border-color: #8b5cf6;\n");
                css.append("}\n\n");

                css.append(".currency-item:nth-child(4) .currency-value {\n");
                css.append("    color: #7c3aed;\n");
                css.append("}\n\n");

                css.append(".action-btn.refresh-btn {\n");
                css.append("    background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                css.append("    color: white;\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 6px;\n");
                css.append("    padding: 0.375rem 0.5rem;\n");
                css.append("    font-size: 0.75rem;\n");
                css.append("    font-weight: 500;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.125rem;\n");
                css.append("    box-shadow: 0 1px 4px rgba(16, 185, 129, 0.2);\n");
                css.append("    min-width: auto;\n");
                css.append("    white-space: nowrap;\n");
                css.append("    height: 2rem;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".action-btn.refresh-btn:hover {\n");
                css.append("    background: linear-gradient(135deg, #059669 0%, #047857 100%);\n");
                css.append("    transform: translateY(-1px);\n");
                css.append("    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);\n");
                css.append("}\n\n");

                css.append(".action-btn.refresh-btn:active {\n");
                css.append("    transform: translateY(0);\n");
                css.append("    box-shadow: 0 2px 6px rgba(16, 185, 129, 0.2);\n");
                css.append("}\n\n");

                // 奖励说明卡片样式
                css.append("/* 奖励说明卡片 */\n");
                css.append(".reward-info-card {\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    border-radius: 15px;\n");
                css.append("    padding: 1.5rem;\n");
                css.append("    margin: 1.5rem 0;\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                css.append("    border: 1px solid rgba(255, 255, 255, 0.2);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".reward-info-card:hover {\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 12px 35px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append(".reward-info-card .card-header {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    margin-bottom: 1.5rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    color: #333;\n");
                css.append("}\n\n");

                css.append(".reward-info-card .card-header i {\n");
                css.append("    color: #f59e0b;\n");
                css.append("    font-size: 1.3rem;\n");
                css.append("}\n\n");

                css.append(".reward-grid {\n");
                css.append("    display: grid;\n");
                css.append("    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n");
                css.append("    gap: 1rem;\n");
                css.append("}\n\n");

                css.append(".reward-item {\n");
                css.append("    text-align: center;\n");
                css.append("    padding: 1rem;\n");
                css.append("    background: rgba(255, 255, 255, 0.5);\n");
                css.append("    border-radius: 10px;\n");
                css.append("    border: 1px solid rgba(0, 0, 0, 0.05);\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".reward-item:hover {\n");
                css.append("    background: rgba(255, 255, 255, 0.8);\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n");
                css.append("}\n\n");

                css.append(".reward-icon {\n");
                css.append("    width: 3rem;\n");
                css.append("    height: 3rem;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    margin: 0 auto 0.75rem;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    color: white;\n");
                css.append("}\n\n");

                css.append(".reward-icon.daily {\n");
                css.append("    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n");
                css.append("}\n\n");

                css.append(".reward-icon.weekly {\n");
                css.append("    background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                css.append("}\n\n");

                css.append(".reward-icon.biweekly {\n");
                css.append("    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n");
                css.append("}\n\n");

                css.append(".reward-icon.monthly {\n");
                css.append("    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n");
                css.append("}\n\n");

                css.append(".reward-item h4 {\n");
                css.append("    font-size: 1rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("    margin-bottom: 0.5rem;\n");
                css.append("}\n\n");

                css.append(".reward-item p {\n");
                css.append("    font-size: 0.85rem;\n");
                css.append("    color: #666;\n");
                css.append("    margin: 0;\n");
                css.append("}\n\n");

                css.append(".reward-item strong {\n");
                css.append("    color: #f59e0b;\n");
                css.append("    font-weight: 600;\n");
                css.append("}\n\n");

                // 左侧奖励说明卡片特殊样式
                css.append("/* 左侧奖励说明卡片 */\n");
                css.append(".reward-info-card.left-side {\n");
                css.append("    margin: 0;\n");
                css.append("}\n\n");

                css.append(".reward-grid.vertical {\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    gap: 0.75rem;\n");
                css.append("}\n\n");

                css.append(".reward-grid.vertical .reward-item {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.75rem;\n");
                css.append("    text-align: left;\n");
                css.append("    padding: 0.75rem;\n");
                css.append("    background: rgba(255, 255, 255, 0.3);\n");
                css.append("}\n\n");

                css.append(".reward-grid.vertical .reward-icon {\n");
                css.append("    width: 2.5rem;\n");
                css.append("    height: 2.5rem;\n");
                css.append("    margin: 0;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".reward-grid.vertical .reward-content {\n");
                css.append("    flex: 1;\n");
                css.append("}\n\n");

                css.append(".reward-grid.vertical .reward-content h4 {\n");
                css.append("    font-size: 0.9rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("    margin: 0 0 0.25rem 0;\n");
                css.append("}\n\n");

                css.append(".reward-grid.vertical .reward-content p {\n");
                css.append("    font-size: 0.8rem;\n");
                css.append("    color: #666;\n");
                css.append("    margin: 0;\n");
                css.append("}\n\n");

                // 右侧区域样式
                css.append("/* 右侧区域 */\n");
                css.append(".right-section {\n");
                css.append("    flex: 1;\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    gap: 1.5rem;\n");
                css.append("}\n\n");

                // 补签卡商店样式
                css.append("/* 补签卡商店 */\n");
                css.append(".makeup-cards-section {\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    border-radius: 15px;\n");
                css.append("    padding: 1.5rem;\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                css.append("    border: 1px solid rgba(255, 255, 255, 0.2);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("}\n\n");

                css.append(".section-header {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    margin-bottom: 1.5rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    color: #333;\n");
                css.append("}\n\n");

                css.append(".section-header i {\n");
                css.append("    color: #667eea;\n");
                css.append("    font-size: 1.3rem;\n");
                css.append("}\n\n");

                css.append(".makeup-cards-grid {\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    gap: 1rem;\n");
                css.append("}\n\n");

                css.append(".makeup-card {\n");
                css.append("    background: rgba(255, 255, 255, 0.8);\n");
                css.append("    border-radius: 12px;\n");
                css.append("    padding: 1rem;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 1rem;\n");
                css.append("    border: 2px solid transparent;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append(".makeup-card:hover {\n");
                css.append("    transform: translateY(-3px);\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append(".makeup-card.gold-card {\n");
                css.append("    border-color: #f59e0b;\n");
                css.append("}\n\n");

                css.append(".makeup-card.gold-card:hover {\n");
                css.append(
                                "    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(217, 119, 6, 0.1) 100%);\n");
                css.append("}\n\n");

                css.append(".makeup-card.points-card {\n");
                css.append("    border-color: #8b5cf6;\n");
                css.append("}\n\n");

                css.append(".makeup-card.points-card:hover {\n");
                css.append(
                                "    background: linear-gradient(135deg, rgba(139, 92, 246, 0.1) 0%, rgba(124, 58, 237, 0.1) 100%);\n");
                css.append("}\n\n");

                css.append(".makeup-card.score-card {\n");
                css.append("    border-color: #10b981;\n");
                css.append("}\n\n");

                css.append(".makeup-card.score-card:hover {\n");
                css.append(
                                "    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);\n");
                css.append("}\n\n");

                css.append(".makeup-card .card-icon {\n");
                css.append("    width: 3rem;\n");
                css.append("    height: 3rem;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    color: white;\n");
                css.append("    position: relative;\n");
                css.append("    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n");
                css.append("}\n\n");

                css.append("/* Emoji图标样式 */\n");
                css.append(".makeup-card .emoji-icon {\n");
                css.append("    font-size: 1.5rem;\n");
                css.append("    line-height: 1;\n");
                css.append("    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\n");
                css.append("}\n\n");

                css.append(".gold-card .card-icon {\n");
                css.append("    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n");
                css.append("}\n\n");

                css.append(".points-card .card-icon {\n");
                css.append("    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n");
                css.append("}\n\n");

                css.append(".score-card .card-icon {\n");
                css.append("    background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                css.append("}\n\n");

                css.append(".makeup-card .card-content {\n");
                css.append("    flex: 1;\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    gap: 0.25rem;\n");
                css.append("}\n\n");

                css.append(".makeup-card .card-actions {\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".makeup-card .card-title {\n");
                css.append("    font-size: 1rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("    margin: 0;\n");
                css.append("}\n\n");

                css.append(".makeup-card .card-price {\n");
                css.append("    font-size: 0.9rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    margin: 0;\n");
                css.append("}\n\n");

                css.append(".gold-card .card-price {\n");
                css.append("    color: #f59e0b;\n");
                css.append("}\n\n");

                css.append(".points-card .card-price {\n");
                css.append("    color: #8b5cf6;\n");
                css.append("}\n\n");

                css.append(".score-card .card-price {\n");
                css.append("    color: #10b981;\n");
                css.append("}\n\n");

                css.append(".buy-btn {\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    padding: 0.75rem 1.25rem;\n");
                css.append("    font-size: 0.9rem;\n");
                css.append("    font-weight: 700;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                css.append("    color: white;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    white-space: nowrap;\n");
                css.append("    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);\n");
                css.append("    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append("/* 购买按钮悬停效果 */\n");

                css.append(".buy-btn:hover {\n");
                css.append("    transform: translateY(-2px) scale(1.02);\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n");
                css.append("}\n\n");

                css.append("/* 购买按钮点击效果 */\n");
                css.append(".buy-btn:active {\n");
                css.append("    transform: translateY(0) scale(0.98);\n");
                css.append("    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);\n");
                css.append("}\n\n");

                css.append(".gold-btn {\n");
                css.append("    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n");
                css.append("    border: 2px solid #fbbf24;\n");
                css.append("}\n\n");

                css.append(".gold-btn:hover {\n");
                css.append("    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);\n");
                css.append("    border-color: #fcd34d;\n");
                css.append("    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);\n");
                css.append("}\n\n");

                css.append(".points-btn {\n");
                css.append("    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);\n");
                css.append("    border: 2px solid #a78bfa;\n");
                css.append("}\n\n");

                css.append(".points-btn:hover {\n");
                css.append("    background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);\n");
                css.append("    border-color: #c4b5fd;\n");
                css.append("    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);\n");
                css.append("}\n\n");

                css.append(".score-btn {\n");
                css.append("    background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                css.append("    border: 2px solid #34d399;\n");
                css.append("}\n\n");

                css.append(".score-btn:hover {\n");
                css.append("    background: linear-gradient(135deg, #34d399 0%, #10b981 100%);\n");
                css.append("    border-color: #6ee7b7;\n");
                css.append("    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);\n");
                css.append("}\n\n");

                css.append("/* 购买按钮脉冲动画 */\n");
                css.append("@keyframes pulse {\n");
                css.append("    0% { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); }\n");
                css.append("    50% { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2), 0 0 0 8px rgba(255, 255, 255, 0.1); }\n");
                css.append("    100% { box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2); }\n");
                css.append("}\n\n");

                css.append(".buy-btn {\n");
                css.append("    animation: pulse 2s infinite;\n");
                css.append("}\n\n");

                css.append(".buy-btn:hover {\n");
                css.append("    animation: none;\n");
                css.append("}\n\n");

                // 响应式设计
                css.append("/* 无障碍性样式 */\n");
                css.append(".sr-only {\n");
                css.append("    position: absolute;\n");
                css.append("    width: 1px;\n");
                css.append("    height: 1px;\n");
                css.append("    padding: 0;\n");
                css.append("    margin: -1px;\n");
                css.append("    overflow: hidden;\n");
                css.append("    clip: rect(0, 0, 0, 0);\n");
                css.append("    white-space: nowrap;\n");
                css.append("    border: 0;\n");
                css.append("}\n\n");

                css.append("/* 响应式设计 */\n");
                css.append("@media (max-width: 768px) {\n");
                css.append("    .top-status-section {\n");
                css.append("        grid-template-columns: 1fr;\n");
                css.append("        gap: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .compact-date {\n");
                css.append("        flex-direction: column;\n");
                css.append("        gap: 0.5rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .calendar-layout {\n");
                css.append("        flex-direction: column;\n");
                css.append("        gap: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .left-info-section {\n");
                css.append("        min-width: auto;\n");
                css.append("        gap: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .left-status-cards {\n");
                css.append("        flex-direction: row;\n");
                css.append("        overflow-x: auto;\n");
                css.append("        gap: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .status-card.compact {\n");
                css.append("        min-width: 160px;\n");
                css.append("        flex-shrink: 0;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    /* 货币网格移动端适配 */\n");
                css.append("    .currency-grid {\n");
                css.append("        grid-template-columns: 1fr;\n");
                css.append("        gap: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .currency-item {\n");
                css.append("        padding: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .currency-icon {\n");
                css.append("        width: 2rem;\n");
                css.append("        height: 2rem;\n");
                css.append("        font-size: 1.2rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .currency-label {\n");
                css.append("        font-size: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .currency-value {\n");
                css.append("        font-size: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .reward-grid {\n");
                css.append("        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n");
                css.append("        gap: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .reward-item {\n");
                css.append("        padding: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .reward-icon {\n");
                css.append("        width: 2.5rem;\n");
                css.append("        height: 2.5rem;\n");
                css.append("        font-size: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .makeup-cards-grid {\n");
                css.append("        grid-template-columns: 1fr;\n");
                css.append("        gap: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .right-section {\n");
                css.append("        gap: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .makeup-card {\n");
                css.append("        padding: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .makeup-card .card-icon {\n");
                css.append("        width: 3rem;\n");
                css.append("        height: 3rem;\n");
                css.append("        font-size: 1.3rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    /* 购买弹窗移动端优化 */\n");
                css.append("    .purchase-modal-content {\n");
                css.append("        width: 95%;\n");
                css.append("        max-width: none;\n");
                css.append("        max-height: 90vh;\n");
                css.append("        margin: 10px;\n");
                css.append("        overflow: hidden;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header {\n");
                css.append("        padding: 1rem 1rem 0.8rem 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-body {\n");
                css.append("        padding: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-item-info {\n");
                css.append("        margin-bottom: 0.8rem;\n");
                css.append("        padding: 0.6rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-quantity {\n");
                css.append("        margin-bottom: 0.8rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-total {\n");
                css.append("        padding: 0.6rem;\n");
                css.append("        margin-bottom: 0.8rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header {\n");
                css.append("        padding: 1.5rem 1.5rem 1rem 1.5rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header h2 {\n");
                css.append("        font-size: 1.4rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-body {\n");
                css.append("        padding: 1.5rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-item-info {\n");
                css.append("        margin-bottom: 1rem;\n");
                css.append("        padding: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .item-icon {\n");
                css.append("        width: 2.5rem;\n");
                css.append("        height: 2.5rem;\n");
                css.append("        font-size: 1.2rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .item-details h3 {\n");
                css.append("        font-size: 1.1rem;\n");
                css.append("        margin-bottom: 0.3rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .item-details p {\n");
                css.append("        font-size: 0.85rem;\n");
                css.append("        margin-bottom: 0.3rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-quantity {\n");
                css.append("        margin-bottom: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-total {\n");
                css.append("        padding: 0.75rem;\n");
                css.append("        margin-bottom: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-actions {\n");
                css.append("        flex-direction: column;\n");
                css.append("        gap: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-actions .btn {\n");
                css.append("        width: 100%;\n");
                css.append("        padding: 0.75rem 1.5rem;\n");
                css.append("        font-size: 0.9rem;\n");
                css.append("    }\n");
                css.append("}\n\n");

                // 小屏幕设备额外优化
                css.append("@media (max-width: 480px) {\n");
                css.append("    .purchase-modal {\n");
                css.append("        padding: 10px;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-content {\n");
                css.append("        width: 100%;\n");
                css.append("        max-height: 95vh;\n");
                css.append("        margin: 5px;\n");
                css.append("        border-radius: 15px;\n");
                css.append("        overflow: hidden;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header {\n");
                css.append("        padding: 0.8rem 0.8rem 0.6rem 0.8rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header h2 {\n");
                css.append("        font-size: 1.3rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-body {\n");
                css.append("        padding: 0.8rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-item-info {\n");
                css.append("        margin-bottom: 0.6rem;\n");
                css.append("        padding: 0.5rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-quantity {\n");
                css.append("        margin-bottom: 0.6rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-total {\n");
                css.append("        padding: 0.5rem;\n");
                css.append("        margin-bottom: 0.6rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header {\n");
                css.append("        padding: 1rem 1rem 0.75rem 1rem;\n");
                css.append("        border-radius: 15px 15px 0 0;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-header h2 {\n");
                css.append("        font-size: 1.2rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-close-btn {\n");
                css.append("        width: 32px;\n");
                css.append("        height: 32px;\n");
                css.append("        font-size: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-modal-body {\n");
                css.append("        padding: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-item-info {\n");
                css.append("        flex-direction: column;\n");
                css.append("        text-align: center;\n");
                css.append("        gap: 0.75rem;\n");
                css.append("        margin-bottom: 0.75rem;\n");
                css.append("        padding: 0.5rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .item-icon {\n");
                css.append("        width: 2rem;\n");
                css.append("        height: 2rem;\n");
                css.append("        font-size: 1rem;\n");
                css.append("        margin: 0 auto;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .item-details h3 {\n");
                css.append("        font-size: 1rem;\n");
                css.append("        margin-bottom: 0.25rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .item-details p {\n");
                css.append("        font-size: 0.8rem;\n");
                css.append("        margin-bottom: 0.25rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-quantity {\n");
                css.append("        margin-bottom: 0.75rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-quantity label {\n");
                css.append("        font-size: 0.9rem;\n");
                css.append("        margin-bottom: 0.4rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .quantity-btn {\n");
                css.append("        width: 2rem;\n");
                css.append("        height: 2rem;\n");
                css.append("        font-size: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    #purchaseQuantity {\n");
                css.append("        width: 3rem;\n");
                css.append("        height: 2rem;\n");
                css.append("        font-size: 1rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-total {\n");
                css.append("        padding: 0.5rem;\n");
                css.append("        margin-bottom: 0.75rem;\n");
                css.append("        font-size: 0.9rem;\n");
                css.append("    }\n");
                css.append("    \n");
                css.append("    .purchase-actions .btn {\n");
                css.append("        padding: 0.6rem 1rem;\n");
                css.append("        font-size: 0.85rem;\n");
                css.append("    }\n");
                css.append("}\n\n");

                // 绑定区域样式（保留原版本）
                css.append("/* 原版绑定区域 */\n");
                css.append(".bind-section {\n");
                css.append("    margin: 1.5rem 0;\n");
                css.append("    background: rgba(30, 41, 59, 0.95);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    border-radius: 12px;\n");
                css.append("    padding: 1rem;\n");
                css.append("    border: 1px solid rgba(255,255,255,0.1);\n");
                css.append("    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);\n");
                css.append("}\n\n");

                css.append(".bind-info {\n");
                css.append("    text-align: center;\n");
                css.append("}\n\n");

                css.append(".bind-info h3 {\n");
                css.append("    color: #ffffff;\n");
                css.append("    font-size: 1.5em;\n");
                css.append("    margin-bottom: 10px;\n");
                css.append("}\n\n");

                css.append(".bind-info p {\n");
                css.append("    color: #94a3b8;\n");
                css.append("    margin-bottom: 20px;\n");
                css.append("}\n\n");

                css.append(".bind-btn {\n");
                css.append("    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 12px;\n");
                css.append("    padding: 15px 30px;\n");
                css.append("    color: white;\n");
                css.append("    font-size: 16px;\n");
                css.append("    font-weight: bold;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".bind-btn:hover {\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4);\n");
                css.append("}\n\n");

                css.append(".bind-actions {\n");
                css.append("    margin-top: 15px;\n");
                css.append("    display: flex;\n");
                css.append("    gap: 10px;\n");
                css.append("    justify-content: center;\n");
                css.append("}\n\n");

                css.append(".unbind-btn {\n");
                css.append("    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n");
                css.append("    color: white;\n");
                css.append("    border: none;\n");
                css.append("    padding: 8px 16px;\n");
                css.append("    border-radius: 6px;\n");
                css.append("    cursor: pointer;\n");
                css.append("    font-size: 14px;\n");
                css.append("    font-weight: 500;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".unbind-btn:hover {\n");
                css.append("    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);\n");
                css.append("    transform: translateY(-1px);\n");
                css.append("}\n\n");

                css.append(".unbind-timer {\n");
                css.append("    color: #f59e0b;\n");
                css.append("    font-size: 0.9em;\n");
                css.append("    margin: 10px 0;\n");
                css.append("}\n\n");

                css.append(".bind-actions.disabled {\n");
                css.append("    opacity: 0.5;\n");
                css.append("    pointer-events: none;\n");
                css.append("}\n\n");

                css.append(".bind-success {\n");
                css.append("    background: rgba(16, 185, 129, 0.1);\n");
                css.append("    border: 2px solid rgba(16, 185, 129, 0.3);\n");
                css.append("}\n\n");

                css.append(".bind-success h3 {\n");
                css.append("    color: #10b981;\n");
                css.append("}\n\n");

                css.append(".bind-success p {\n");
                css.append("    color: #10b981;\n");
                css.append("}\n\n");

                // 积分显示区域样式
                css.append("/* 积分显示区域 */\n");
                css.append(".points-section {\n");
                css.append("    margin: 2rem 0;\n");
                css.append("    background: rgba(30, 41, 59, 0.95);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    border-radius: 15px;\n");
                css.append("    padding: 20px;\n");
                css.append("    border: 1px solid rgba(255,255,255,0.1);\n");
                css.append("}\n\n");

                css.append(".user-points {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    gap: 10px;\n");
                css.append("    background: rgba(15, 23, 42, 0.8);\n");
                css.append("    border-radius: 15px;\n");
                css.append("    padding: 15px;\n");
                css.append("    border: 2px solid rgba(16, 185, 129, 0.3);\n");
                css.append("}\n\n");

                css.append(".points-label {\n");
                css.append("    color: #e2e8f0;\n");
                css.append("    font-size: 1.1em;\n");
                css.append("    font-weight: 500;\n");
                css.append("}\n\n");

                css.append(".points-value {\n");
                css.append("    color: #10b981;\n");
                css.append("    font-size: 1.5em;\n");
                css.append("    font-weight: bold;\n");
                css.append("}\n\n");

                css.append(".refresh-btn {\n");
                css.append("    background: #10b981;\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    width: 35px;\n");
                css.append("    height: 35px;\n");
                css.append("    color: white;\n");
                css.append("    cursor: pointer;\n");
                css.append("    font-size: 16px;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".refresh-btn:hover {\n");
                css.append("    background: #059669;\n");
                css.append("    transform: rotate(180deg);\n");
                css.append("}\n\n");

                // 头部样式
                css.append("/* 页面头部 */\n");
                css.append(".checkin-header {\n");
                css.append("    text-align: center;\n");
                css.append("    margin-bottom: 3rem;\n");
                css.append("    color: white;\n");
                css.append("}\n\n");

                css.append(".checkin-header h1 {\n");
                css.append("    font-size: 3rem;\n");
                css.append("    margin-bottom: 1rem;\n");
                css.append("    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);\n");
                css.append("}\n\n");

                css.append(".checkin-header p {\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    opacity: 0.9;\n");
                css.append("}\n\n");

                // 玩家输入区域
                css.append("/* 玩家输入区域 */\n");
                css.append(".player-input-section {\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    border-radius: 20px;\n");
                css.append("    padding: 2rem;\n");
                css.append("    margin-bottom: 2rem;\n");
                css.append("    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n");
                css.append("}\n\n");

                css.append(".input-group {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 1rem;\n");
                css.append("    align-items: center;\n");
                css.append("    flex-wrap: wrap;\n");
                css.append("}\n\n");

                css.append(".input-wrapper {\n");
                css.append("    position: relative;\n");
                css.append("    flex: 1;\n");
                css.append("    min-width: 300px;\n");
                css.append("}\n\n");

                css.append(".input-icon {\n");
                css.append("    position: absolute;\n");
                css.append("    left: 1rem;\n");
                css.append("    top: 50%;\n");
                css.append("    transform: translateY(-50%);\n");
                css.append("    color: #999;\n");
                css.append("    z-index: 1;\n");
                css.append("}\n\n");

                css.append(".player-input {\n");
                css.append("    width: 100%;\n");
                css.append("    padding: 1rem 1rem 1rem 3rem;\n");
                css.append("    border: 2px solid #e1e5e9;\n");
                css.append("    border-radius: 12px;\n");
                css.append("    font-size: 1rem;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    background: white;\n");
                css.append("}\n\n");

                css.append(".player-input:focus {\n");
                css.append("    outline: none;\n");
                css.append("    border-color: #667eea;\n");
                css.append("    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n");
                css.append("}\n\n");

                // 按钮样式
                css.append("/* 按钮样式 */\n");
                css.append(".btn {\n");
                css.append("    padding: 1rem 2rem;\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 12px;\n");
                css.append("    font-size: 1rem;\n");
                css.append("    font-weight: 600;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    display: inline-flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    text-decoration: none;\n");
                css.append("    white-space: nowrap;\n");
                css.append("}\n\n");

                css.append(".btn-primary {\n");
                css.append("    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                css.append("    color: white;\n");
                css.append("    border: none;\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("    font-weight: 600;\n");
                css.append("    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n");
                css.append("}\n\n");

                css.append(".btn-primary::before {\n");
                css.append("    content: '';\n");
                css.append("    position: absolute;\n");
                css.append("    top: 0;\n");
                css.append("    left: -100%;\n");
                css.append("    width: 100%;\n");
                css.append("    height: 100%;\n");
                css.append("    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n");
                css.append("    transition: left 0.5s;\n");
                css.append("}\n\n");

                css.append(".btn-primary:hover {\n");
                css.append("    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);\n");
                css.append("    transform: translateY(-2px) scale(1.02);\n");
                css.append("    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);\n");
                css.append("}\n\n");

                css.append(".btn-primary:hover::before {\n");
                css.append("    left: 100%;\n");
                css.append("}\n\n");

                css.append(".btn-primary:active {\n");
                css.append("    transform: translateY(-1px) scale(1.01);\n");
                css.append("    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n");
                css.append("}\n\n");

                css.append(".btn-success {\n");
                css.append("    background: #28a745;\n");
                css.append("    color: white;\n");
                css.append("}\n\n");

                css.append(".btn-success:hover {\n");
                css.append("    background: #218838;\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);\n");
                css.append("}\n\n");

                css.append(".btn-large {\n");
                css.append("    padding: 1.5rem 3rem;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("}\n\n");

                // 绑定相关样式
                css.append(".binding-required {\n");
                css.append("    text-align: center;\n");
                css.append("    padding: 40px 20px;\n");
                css.append("    background: linear-gradient(135deg, #ff6b6b, #ee5a24);\n");
                css.append("    color: white;\n");
                css.append("    border-radius: 15px;\n");
                css.append("    margin: 20px 0;\n");
                css.append("}\n\n");

                css.append(".binding-icon {\n");
                css.append("    font-size: 48px;\n");
                css.append("    margin-bottom: 20px;\n");
                css.append("    opacity: 0.9;\n");
                css.append("}\n\n");

                css.append(".binding-required h3 {\n");
                css.append("    margin: 0 0 15px 0;\n");
                css.append("    font-size: 24px;\n");
                css.append("}\n\n");

                css.append(".binding-required p {\n");
                css.append("    margin: 0 0 25px 0;\n");
                css.append("    font-size: 16px;\n");
                css.append("    opacity: 0.9;\n");
                css.append("}\n\n");

                css.append(".binding-actions .btn {\n");
                css.append("    background: rgba(255, 255, 255, 0.2);\n");
                css.append("    border: 2px solid rgba(255, 255, 255, 0.3);\n");
                css.append("    color: white;\n");
                css.append("    padding: 12px 24px;\n");
                css.append("    text-decoration: none;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    font-weight: 600;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".binding-actions .btn:hover {\n");
                css.append("    background: rgba(255, 255, 255, 0.3);\n");
                css.append("    border-color: rgba(255, 255, 255, 0.5);\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("}\n\n");

                css.append(".binding-info {\n");
                css.append("    background: linear-gradient(135deg, #00b894, #00a085);\n");
                css.append("    color: white;\n");
                css.append("    padding: 15px 20px;\n");
                css.append("    border-radius: 10px;\n");
                css.append("    margin: 20px 0;\n");
                css.append("}\n\n");

                css.append(".binding-status {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 10px;\n");
                css.append("    font-weight: 600;\n");
                css.append("}\n\n");

                css.append(".binding-status i {\n");
                css.append("    font-size: 18px;\n");
                css.append("}\n\n");

                // 绑定弹窗样式
                css.append("/* 绑定弹窗 */\n");
                css.append(".bind-modal {\n");
                css.append("    position: fixed;\n");
                css.append("    top: 0;\n");
                css.append("    left: 0;\n");
                css.append("    width: 100%;\n");
                css.append("    height: 100%;\n");
                css.append("    background: rgba(0, 0, 0, 0.8);\n");
                css.append("    z-index: 1000;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("}\n\n");

                css.append(".bind-modal-content {\n");
                css.append("    background: rgba(30, 41, 59, 0.95);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    border-radius: 20px;\n");
                css.append("    padding: 30px;\n");
                css.append("    max-width: 600px;\n");
                css.append("    width: 90%;\n");
                css.append("    border: 1px solid rgba(71, 85, 105, 0.3);\n");
                css.append("}\n\n");

                css.append(".bind-modal-header {\n");
                css.append("    display: flex;\n");
                css.append("    justify-content: space-between;\n");
                css.append("    align-items: center;\n");
                css.append("    margin-bottom: 20px;\n");
                css.append("}\n\n");

                css.append(".bind-modal-header h2 {\n");
                css.append("    color: #ffffff;\n");
                css.append("    font-size: 1.5em;\n");
                css.append("    margin: 0;\n");
                css.append("}\n\n");

                css.append(".bind-close-btn {\n");
                css.append("    background: rgba(239, 68, 68, 0.2);\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    width: 30px;\n");
                css.append("    height: 30px;\n");
                css.append("    color: #ef4444;\n");
                css.append("    cursor: pointer;\n");
                css.append("    font-size: 18px;\n");
                css.append("    transition: all 0.3s;\n");
                css.append("}\n\n");

                css.append(".bind-close-btn:hover {\n");
                css.append("    background: rgba(239, 68, 68, 0.3);\n");
                css.append("    transform: scale(1.1);\n");
                css.append("}\n\n");

                css.append(".bind-step {\n");
                css.append("    margin-bottom: 25px;\n");
                css.append("}\n\n");

                css.append(".bind-step h3 {\n");
                css.append("    color: #ffffff;\n");
                css.append("    font-size: 1.2em;\n");
                css.append("    margin-bottom: 10px;\n");
                css.append("}\n\n");

                css.append(".bind-step p {\n");
                css.append("    color: #94a3b8;\n");
                css.append("    margin-bottom: 10px;\n");
                css.append("}\n\n");

                css.append(".bind-code-container, .command-container {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 10px;\n");
                css.append("    align-items: center;\n");
                css.append("    margin-bottom: 10px;\n");
                css.append("}\n\n");

                css.append(".bind-code-input {\n");
                css.append("    flex: 1;\n");
                css.append("    padding: 12px;\n");
                css.append("    background: rgba(15, 23, 42, 0.8);\n");
                css.append("    border: 2px solid rgba(71, 85, 105, 0.5);\n");
                css.append("    border-radius: 8px;\n");
                css.append("    color: #ffffff;\n");
                css.append("    font-size: 18px;\n");
                css.append("    font-weight: bold;\n");
                css.append("    text-align: center;\n");
                css.append("    letter-spacing: 2px;\n");
                css.append("}\n\n");

                css.append(".command-container code {\n");
                css.append("    flex: 1;\n");
                css.append("    background: rgba(15, 23, 42, 0.8);\n");
                css.append("    border: 2px solid rgba(71, 85, 105, 0.5);\n");
                css.append("    border-radius: 8px;\n");
                css.append("    padding: 12px;\n");
                css.append("    color: #10b981;\n");
                css.append("    font-family: monospace;\n");
                css.append("    font-size: 16px;\n");
                css.append("}\n\n");

                css.append(".copy-btn {\n");
                css.append("    background: #10b981;\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    padding: 10px 15px;\n");
                css.append("    color: white;\n");
                css.append("    font-weight: bold;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s;\n");
                css.append("    white-space: nowrap;\n");
                css.append("}\n\n");

                css.append(".copy-btn:hover {\n");
                css.append("    background: #059669;\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("}\n\n");

                css.append(".bind-code-note {\n");
                css.append("    color: #f59e0b;\n");
                css.append("    font-size: 0.9em;\n");
                css.append("    font-weight: 500;\n");
                css.append("}\n\n");

                css.append(".bind-status-check {\n");
                css.append("    text-align: center;\n");
                css.append("}\n\n");

                css.append(".bind-progress {\n");
                css.append("    width: 100%;\n");
                css.append("    height: 6px;\n");
                css.append("    background: rgba(71, 85, 105, 0.3);\n");
                css.append("    border-radius: 3px;\n");
                css.append("    margin-top: 10px;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append(".bind-progress-bar {\n");
                css.append("    height: 100%;\n");
                css.append("    background: linear-gradient(90deg, #3b82f6, #10b981);\n");
                css.append("    border-radius: 3px;\n");
                css.append("    width: 0%;\n");
                css.append("    transition: width 0.3s ease;\n");
                css.append("    animation: pulse 2s infinite;\n");
                css.append("}\n\n");

                // 日历布局样式
                css.append("/* 日历布局 */\n");
                css.append(".calendar-layout {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 2rem;\n");
                css.append("    align-items: flex-start;\n");
                css.append("}\n\n");

                // 左侧信息区域样式
                css.append("/* 左侧信息区域 */\n");
                css.append(".left-info-section {\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    gap: 1.5rem;\n");
                css.append("    min-width: 280px;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append("/* 左侧状态卡片 */\n");
                css.append(".left-status-cards {\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("    gap: 1rem;\n");
                css.append("}\n\n");

                css.append(".status-card.compact {\n");
                css.append("    background: rgba(255, 255, 255, 0.95);\n");
                css.append("    border-radius: 12px;\n");
                css.append("    padding: 1rem;\n");
                css.append("    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n");
                css.append("    border: 1px solid rgba(255, 255, 255, 0.2);\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.75rem;\n");
                css.append("}\n\n");

                css.append(".status-card.compact:hover {\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append(".status-card.compact .status-icon {\n");
                css.append("    width: 2.5rem;\n");
                css.append("    height: 2.5rem;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    font-size: 1rem;\n");
                css.append("    color: white;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".status-card.compact:nth-child(1) .status-icon {\n");
                css.append("    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);\n");
                css.append("}\n\n");

                css.append(".status-card.compact:nth-child(2) .status-icon {\n");
                css.append("    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n");
                css.append("}\n\n");

                css.append(".status-card.compact:nth-child(3) .status-icon {\n");
                css.append("    background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                css.append("}\n\n");

                css.append(".status-card.compact .status-content {\n");
                css.append("    flex: 1;\n");
                css.append("}\n\n");

                css.append(".status-card.compact .status-label {\n");
                css.append("    font-size: 0.8rem;\n");
                css.append("    color: #666;\n");
                css.append("    margin-bottom: 0.25rem;\n");
                css.append("}\n\n");

                css.append(".status-card.compact .status-value {\n");
                css.append("    font-size: 1rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #333;\n");
                css.append("}\n\n");

                // 签到卡片样式
                css.append("/* 签到卡片容器 */\n");
                css.append(".checkin-cards-container {\n");
                css.append("    flex: 1;\n");
                css.append("}\n\n");

                css.append(".checkin-cards-grid {\n");
                css.append("    display: grid;\n");
                css.append("    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));\n");
                css.append("    gap: 20px;\n");
                css.append("    padding: 20px 0;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card {\n");
                css.append("    background: var(--bg-card);\n");
                css.append("    border-radius: 16px;\n");
                css.append("    padding: 20px;\n");
                css.append("    text-align: center;\n");
                css.append("    box-shadow: 0 8px 32px var(--shadow-light);\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    border: 2px solid transparent;\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card:hover {\n");
                css.append("    transform: translateY(-5px);\n");
                css.append("    box-shadow: 0 12px 40px var(--shadow-medium);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.today {\n");
                css.append("    border-color: #ff6b35;\n");
                css.append("    background: linear-gradient(135deg, #fff, #fff8f5);\n");
                css.append("    cursor: pointer;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.today:hover {\n");
                css.append("    border-color: #e55a2b;\n");
                css.append("    background: linear-gradient(135deg, #fff8f5, #fff0e6);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.checked-in {\n");
                css.append("    border-color: #28a745;\n");
                css.append("    background: linear-gradient(135deg, #f8fff9, #e8f5e8);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.missed {\n");
                css.append("    border-color: #dc3545;\n");
                css.append("    background: linear-gradient(135deg, #fff8f8, #ffeaea);\n");
                css.append("    opacity: 0.7;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.future {\n");
                css.append("    border-color: #6c757d;\n");
                css.append("    background: linear-gradient(135deg, #f8f9fa, #e9ecef);\n");
                css.append("    opacity: 0.8;\n");
                css.append("}\n\n");

                css.append(".day-number {\n");
                css.append("    font-size: 32px;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: var(--text-primary);\n");
                css.append("    margin-bottom: 10px;\n");
                css.append("}\n\n");

                css.append(".day-status {\n");
                css.append("    margin-bottom: 15px;\n");
                css.append("}\n\n");

                css.append(".status-icon {\n");
                css.append("    font-size: 20px;\n");
                css.append("    margin-bottom: 5px;\n");
                css.append("}\n\n");

                css.append(".status-icon.checked {\n");
                css.append("    color: #28a745;\n");
                css.append("}\n\n");

                css.append(".status-icon.today {\n");
                css.append("    color: #ff6b35;\n");
                css.append("}\n\n");

                css.append(".status-icon.missed {\n");
                css.append("    color: #dc3545;\n");
                css.append("}\n\n");

                css.append(".status-icon.future {\n");
                css.append("    color: #6c757d;\n");
                css.append("}\n\n");

                css.append(".status-text {\n");
                css.append("    font-size: 12px;\n");
                css.append("    font-weight: 600;\n");
                css.append("    color: var(--text-secondary);\n");
                css.append("}\n\n");

                css.append(".day-reward {\n");
                css.append("    border-top: 1px solid var(--border-color);\n");
                css.append("    padding-top: 15px;\n");
                css.append("}\n\n");

                css.append(".reward-points {\n");
                css.append("    font-size: 14px;\n");
                css.append("    font-weight: 600;\n");
                css.append("    color: #ff6b35;\n");
                css.append("    margin-bottom: 5px;\n");
                css.append("}\n\n");

                css.append(".reward-items {\n");
                css.append("    font-size: 12px;\n");
                css.append("    color: var(--text-secondary);\n");
                css.append("    font-style: italic;\n");
                css.append("}\n\n");

                css.append(".checkin-hint {\n");
                css.append("    position: absolute;\n");
                css.append("    bottom: 0;\n");
                css.append("    left: 0;\n");
                css.append("    right: 0;\n");
                css.append("    background: linear-gradient(135deg, #ff6b35, #e55a2b);\n");
                css.append("    color: white;\n");
                css.append("    padding: 8px;\n");
                css.append("    font-size: 12px;\n");
                css.append("    font-weight: 600;\n");
                css.append("    border-radius: 0 0 14px 14px;\n");
                css.append("    animation: pulse 2s infinite;\n");
                css.append("}\n\n");

                css.append("@keyframes pulse {\n");
                css.append("    0%, 100% { opacity: 1; }\n");
                css.append("    50% { opacity: 0.7; }\n");
                css.append("}\n\n");

                css.append(".checkin-day-card {\n");
                css.append("    background: white;\n");
                css.append("    border-radius: 15px;\n");
                css.append("    padding: 20px;\n");
                css.append("    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    border: 2px solid transparent;\n");
                css.append("    cursor: pointer;\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card:hover {\n");
                css.append("    transform: translateY(-5px);\n");
                css.append("    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.checked-in {\n");
                css.append("    border-color: #28a745;\n");
                css.append("    background: linear-gradient(135deg, #d4edda, #c3e6cb);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.missed {\n");
                css.append("    border-color: #dc3545;\n");
                css.append("    background: linear-gradient(135deg, #f8d7da, #f1b0b7);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.past-unknown {\n");
                css.append("    border-color: #6c757d;\n");
                css.append("    background: linear-gradient(135deg, #e2e3e5, #d1d3d4);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.today {\n");
                css.append("    border-color: #007bff;\n");
                css.append("    background: linear-gradient(135deg, #cce7ff, #b3d9ff);\n");
                css.append("    cursor: pointer;\n");
                css.append("    animation: pulse 2s infinite;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.future {\n");
                css.append("    opacity: 0.6;\n");
                css.append("    cursor: not-allowed;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.bind-required {\n");
                css.append("    border-color: #f59e0b;\n");
                css.append("    background: linear-gradient(135deg, #fef3c7, #fde68a);\n");
                css.append("    cursor: pointer;\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.bind-required:hover {\n");
                css.append("    transform: translateY(-5px);\n");
                css.append("    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);\n");
                css.append("}\n\n");

                css.append(".checkin-day-card.available {\n");
                css.append("    border-color: #10b981;\n");
                css.append("    background: linear-gradient(135deg, #d1fae5, #a7f3d0);\n");
                css.append("}\n\n");

                css.append(".day-number {\n");
                css.append("    font-size: 24px;\n");
                css.append("    font-weight: bold;\n");
                css.append("    text-align: center;\n");
                css.append("    margin-bottom: 10px;\n");
                css.append("    color: #333;\n");
                css.append("}\n\n");

                css.append(".day-status {\n");
                css.append("    text-align: center;\n");
                css.append("    margin-bottom: 15px;\n");
                css.append("}\n\n");

                css.append(".status-icon {\n");
                css.append("    font-size: 20px;\n");
                css.append("    margin-bottom: 5px;\n");
                css.append("}\n\n");

                css.append(".status-icon.checked {\n");
                css.append("    color: #28a745;\n");
                css.append("}\n\n");

                css.append(".status-icon.missed {\n");
                css.append("    color: #dc3545;\n");
                css.append("}\n\n");

                css.append(".status-icon.past {\n");
                css.append("    color: #6c757d;\n");
                css.append("}\n\n");

                css.append(".status-icon.today {\n");
                css.append("    color: #007bff;\n");
                css.append("}\n\n");

                css.append(".status-icon.future {\n");
                css.append("    color: #6c757d;\n");
                css.append("}\n\n");

                css.append(".status-icon.bind {\n");
                css.append("    color: #f59e0b;\n");
                css.append("}\n\n");

                css.append(".day-reward {\n");
                css.append("    background: rgba(255, 255, 255, 0.8);\n");
                css.append("    border-radius: 8px;\n");
                css.append("    padding: 10px;\n");
                css.append("    text-align: center;\n");
                css.append("    font-size: 14px;\n");
                css.append("}\n\n");

                css.append(".reward-points {\n");
                css.append("    color: #ff6b35;\n");
                css.append("    font-weight: bold;\n");
                css.append("    margin-bottom: 5px;\n");
                css.append("}\n\n");

                css.append(".reward-items {\n");
                css.append("    color: #666;\n");
                css.append("    font-size: 12px;\n");
                css.append("}\n\n");

                css.append(".calendar-description {\n");
                css.append("    text-align: center;\n");
                css.append("    color: #666;\n");
                css.append("    margin: 10px 0;\n");
                css.append("    font-style: italic;\n");
                css.append("}\n\n");

                css.append(".checkin-hint {\n");
                css.append("    position: absolute;\n");
                css.append("    bottom: 5px;\n");
                css.append("    left: 50%;\n");
                css.append("    transform: translateX(-50%);\n");
                css.append("    background: rgba(0, 123, 255, 0.9);\n");
                css.append("    color: white;\n");
                css.append("    padding: 2px 8px;\n");
                css.append("    border-radius: 10px;\n");
                css.append("    font-size: 10px;\n");
                css.append("    font-weight: bold;\n");
                css.append("    animation: blink 1.5s infinite;\n");
                css.append("}\n\n");

                css.append("@keyframes blink {\n");
                css.append("    0%, 50% { opacity: 1; }\n");
                css.append("    51%, 100% { opacity: 0.5; }\n");
                css.append("}\n\n");

                css.append("@keyframes pulse {\n");
                css.append("    0% { transform: scale(1); }\n");
                css.append("    50% { transform: scale(1.05); }\n");
                css.append("    100% { transform: scale(1); }\n");
                css.append("}\n\n");

                // 加载状态样式
                css.append("/* 加载状态卡片 */\n");
                css.append(".loading-card {\n");
                css.append("    border-color: #e5e7eb;\n");
                css.append("    background: linear-gradient(135deg, #f9fafb, #f3f4f6);\n");
                css.append("    opacity: 0.8;\n");
                css.append("}\n\n");

                css.append(".loading-spinner {\n");
                css.append("    font-size: 20px;\n");
                css.append("    animation: spin 1s linear infinite;\n");
                css.append("    margin-bottom: 8px;\n");
                css.append("}\n\n");

                css.append(".loading-text {\n");
                css.append("    font-size: 12px;\n");
                css.append("    color: #6b7280;\n");
                css.append("    font-weight: 500;\n");
                css.append("}\n\n");

                css.append("@keyframes spin {\n");
                css.append("    from { transform: rotate(0deg); }\n");
                css.append("    to { transform: rotate(360deg); }\n");
                css.append("}\n\n");

                // 购买弹窗样式
                css.append("/* 购买弹窗 */\n");
                css.append(".purchase-modal {\n");
                css.append("    position: fixed;\n");
                css.append("    top: 0;\n");
                css.append("    left: 0;\n");
                css.append("    width: 100%;\n");
                css.append("    height: 100%;\n");
                css.append("    background: rgba(0, 0, 0, 0.6);\n");
                css.append("    display: none;\n");
                css.append("    justify-content: center;\n");
                css.append("    align-items: center;\n");
                css.append("    z-index: 999999;\n");
                css.append("    backdrop-filter: blur(8px);\n");
                css.append("    padding: 20px;\n");
                css.append("    box-sizing: border-box;\n");
                css.append("}\n\n");

                css.append(".purchase-modal-content {\n");
                css.append("    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n");
                css.append("    border-radius: 20px;\n");
                css.append("    width: 90%;\n");
                css.append("    max-width: 480px;\n");
                css.append("    max-height: 80vh;\n");
                css.append("    overflow: hidden;\n");
                css.append("    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 255, 255, 0.05);\n");
                css.append("    animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);\n");
                css.append("    position: relative;\n");
                css.append("    transform: translateY(0);\n");
                css.append("    display: flex;\n");
                css.append("    flex-direction: column;\n");
                css.append("}\n\n");

                css.append("/* 弹窗装饰效果 - 移除渐变边框 */\n");
                css.append(".purchase-modal-content::before {\n");
                css.append("    display: none;\n");
                css.append("}\n\n");

                css.append(".purchase-modal-header {\n");
                css.append("    display: flex;\n");
                css.append("    justify-content: space-between;\n");
                css.append("    align-items: center;\n");
                css.append("    padding: 1.5rem 1.5rem 1rem 1.5rem;\n");
                css.append("    border-bottom: 2px solid #f1f5f9;\n");
                css.append("    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                css.append("    border-radius: 20px 20px 0 0;\n");
                css.append("    position: relative;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".purchase-modal-header h2 {\n");
                css.append("    margin: 0;\n");
                css.append("    color: white;\n");
                css.append("    font-size: 1.6rem;\n");
                css.append("    font-weight: 700;\n");
                css.append("    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("}\n\n");

                css.append(".purchase-close-btn {\n");
                css.append("    background: rgba(255, 255, 255, 0.2);\n");
                css.append("    border: 2px solid rgba(255, 255, 255, 0.3);\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    cursor: pointer;\n");
                css.append("    color: white;\n");
                css.append("    padding: 0.5rem;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    width: 36px;\n");
                css.append("    height: 36px;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    backdrop-filter: blur(10px);\n");
                css.append("}\n\n");

                css.append(".purchase-close-btn:hover {\n");
                css.append("    background: rgba(255, 255, 255, 0.3);\n");
                css.append("    border-color: rgba(255, 255, 255, 0.5);\n");
                css.append("    transform: rotate(90deg) scale(1.1);\n");
                css.append("    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append(".purchase-modal-body {\n");
                css.append("    padding: 1.5rem;\n");
                css.append("    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n");
                css.append("    border-radius: 0 0 20px 20px;\n");
                css.append("    flex: 1;\n");
                css.append("    overflow-y: auto;\n");
                css.append("    min-height: 0;\n");
                css.append("}\n\n");

                css.append(".purchase-item-info {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 1rem;\n");
                css.append("    margin-bottom: 1rem;\n");
                css.append("    padding: 0.8rem;\n");
                css.append("    background: #f9fafb;\n");
                css.append("    border-radius: 10px;\n");
                css.append("}\n\n");

                css.append(".item-icon {\n");
                css.append("    width: 3rem;\n");
                css.append("    height: 3rem;\n");
                css.append("    border-radius: 50%;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    justify-content: center;\n");
                css.append("    font-size: 1.5rem;\n");
                css.append("    color: white;\n");
                css.append("    flex-shrink: 0;\n");
                css.append("}\n\n");

                css.append(".item-details {\n");
                css.append("    flex: 1;\n");
                css.append("}\n\n");

                css.append(".item-details h3 {\n");
                css.append("    margin: 0 0 0.5rem 0;\n");
                css.append("    color: #333;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("}\n\n");

                css.append(".item-details p {\n");
                css.append("    margin: 0 0 0.5rem 0;\n");
                css.append("    color: #666;\n");
                css.append("    font-size: 0.9rem;\n");
                css.append("}\n\n");

                css.append(".item-price {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("}\n\n");

                css.append(".price-value {\n");
                css.append("    font-weight: bold;\n");
                css.append("    font-size: 1.1rem;\n");
                css.append("}\n\n");

                css.append(".purchase-quantity {\n");
                css.append("    margin-bottom: 1rem;\n");
                css.append("}\n\n");

                css.append(".purchase-quantity label {\n");
                css.append("    display: block;\n");
                css.append("    margin-bottom: 0.5rem;\n");
                css.append("    font-weight: 600;\n");
                css.append("    color: #333;\n");
                css.append("}\n\n");

                css.append(".quantity-controls {\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.5rem;\n");
                css.append("    justify-content: center;\n");
                css.append("}\n\n");

                css.append(".quantity-btn {\n");
                css.append("    width: 2.5rem;\n");
                css.append("    height: 2.5rem;\n");
                css.append("    border: 2px solid #e5e7eb;\n");
                css.append("    background: white;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    cursor: pointer;\n");
                css.append("    font-size: 1.2rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #666;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("}\n\n");

                css.append(".quantity-btn:hover {\n");
                css.append("    border-color: #667eea;\n");
                css.append("    color: #667eea;\n");
                css.append("    background: #f8faff;\n");
                css.append("}\n\n");

                css.append("#purchaseQuantity {\n");
                css.append("    width: 4rem;\n");
                css.append("    height: 2.5rem;\n");
                css.append("    text-align: center;\n");
                css.append("    border: 2px solid #e5e7eb;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    font-size: 1.1rem;\n");
                css.append("    font-weight: bold;\n");
                css.append("}\n\n");

                css.append("#purchaseQuantity:focus {\n");
                css.append("    outline: none;\n");
                css.append("    border-color: #667eea;\n");
                css.append("}\n\n");

                css.append(".purchase-total {\n");
                css.append("    background: #f9fafb;\n");
                css.append("    border-radius: 10px;\n");
                css.append("    padding: 0.8rem;\n");
                css.append("    margin-bottom: 1rem;\n");
                css.append("}\n\n");

                css.append(".total-price, .user-balance {\n");
                css.append("    display: flex;\n");
                css.append("    justify-content: space-between;\n");
                css.append("    align-items: center;\n");
                css.append("    margin-bottom: 0.5rem;\n");
                css.append("}\n\n");

                css.append(".total-price:last-child, .user-balance:last-child {\n");
                css.append("    margin-bottom: 0;\n");
                css.append("}\n\n");

                css.append(".balance-value {\n");
                css.append("    font-weight: bold;\n");
                css.append("    color: #10b981;\n");
                css.append("}\n\n");

                css.append(".purchase-actions {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 1rem;\n");
                css.append("    justify-content: flex-end;\n");
                css.append("}\n\n");

                css.append(".btn-secondary {\n");
                css.append("    background: #6b7280;\n");
                css.append("    color: white;\n");
                css.append("}\n\n");

                css.append(".btn-secondary:hover {\n");
                css.append("    background: #4b5563;\n");
                css.append("}\n\n");

                css.append("@keyframes modalSlideIn {\n");
                css.append("    from {\n");
                css.append("        opacity: 0;\n");
                css.append("        transform: translateY(-50px) scale(0.9);\n");
                css.append("    }\n");
                css.append("    to {\n");
                css.append("        opacity: 1;\n");
                css.append("        transform: translateY(0) scale(1);\n");
                css.append("    }\n");
                css.append("}\n\n");

                // 补签按钮样式
                css.append("/* 补签按钮 */\n");
                css.append(".makeup-button-container {\n");
                css.append("    position: absolute;\n");
                css.append("    bottom: 0.5rem;\n");
                css.append("    right: 0.5rem;\n");
                css.append("    z-index: 10;\n");
                css.append("}\n\n");

                css.append(".makeup-btn {\n");
                css.append("    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n");
                css.append("    color: white;\n");
                css.append("    border: none;\n");
                css.append("    border-radius: 6px;\n");
                css.append("    padding: 0.4rem 0.8rem;\n");
                css.append("    font-size: 0.75rem;\n");
                css.append("    font-weight: 600;\n");
                css.append("    cursor: pointer;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    display: flex;\n");
                css.append("    align-items: center;\n");
                css.append("    gap: 0.3rem;\n");
                css.append("    box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);\n");
                css.append("}\n\n");

                css.append(".makeup-btn:hover {\n");
                css.append("    background: linear-gradient(135deg, #d97706 0%, #b45309 100%);\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4);\n");
                css.append("}\n\n");

                css.append(".makeup-btn:active {\n");
                css.append("    transform: translateY(0);\n");
                css.append("}\n\n");

                css.append(".makeup-btn i {\n");
                css.append("    font-size: 0.7rem;\n");
                css.append("}\n\n");

                // 高亮动画
                css.append("@keyframes highlight {\n");
                css.append("    0%, 100% {\n");
                css.append("        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.4);\n");
                css.append("        transform: scale(1);\n");
                css.append("    }\n");
                css.append("    50% {\n");
                css.append("        box-shadow: 0 0 0 20px rgba(102, 126, 234, 0);\n");
                css.append("        transform: scale(1.02);\n");
                css.append("    }\n");
                css.append("}\n\n");

                // 购买错误弹窗样式
                css.append("/* 购买错误弹窗 */\n");
                css.append(".purchase-error-container {\n");
                css.append("    text-align: center;\n");
                css.append("    padding: 2rem;\n");
                css.append("    background: linear-gradient(135deg, #fef2f2 0%, #fef7f7 100%);\n");
                css.append("    border-radius: 15px;\n");
                css.append("    border: 2px solid #fecaca;\n");
                css.append("    position: relative;\n");
                css.append("    overflow: hidden;\n");
                css.append("}\n\n");

                css.append(".error-icon-wrapper {\n");
                css.append("    position: relative;\n");
                css.append("    display: inline-block;\n");
                css.append("    margin-bottom: 1.5rem;\n");
                css.append("}\n\n");

                css.append(".error-icon {\n");
                css.append("    font-size: 4rem;\n");
                css.append("    color: #ef4444;\n");
                css.append("    animation: errorShake 0.6s ease-in-out;\n");
                css.append("    position: relative;\n");
                css.append("    z-index: 2;\n");
                css.append("}\n\n");

                css.append(".error-pulse {\n");
                css.append("    position: absolute;\n");
                css.append("    top: 50%;\n");
                css.append("    left: 50%;\n");
                css.append("    width: 80px;\n");
                css.append("    height: 80px;\n");
                css.append("    background: rgba(239, 68, 68, 0.2);\n");
                css.append("    border-radius: 50%;\n");
                css.append("    transform: translate(-50%, -50%);\n");
                css.append("    animation: errorPulse 2s infinite;\n");
                css.append("    z-index: 1;\n");
                css.append("}\n\n");

                css.append(".error-title {\n");
                css.append("    color: #dc2626;\n");
                css.append("    font-size: 1.5rem;\n");
                css.append("    font-weight: 700;\n");
                css.append("    margin: 0 0 1rem 0;\n");
                css.append("    text-shadow: 0 1px 2px rgba(220, 38, 38, 0.1);\n");
                css.append("}\n\n");

                css.append(".error-message {\n");
                css.append("    color: #7f1d1d;\n");
                css.append("    font-size: 1.1rem;\n");
                css.append("    margin: 0 0 2rem 0;\n");
                css.append("    line-height: 1.6;\n");
                css.append("    background: rgba(255, 255, 255, 0.7);\n");
                css.append("    padding: 1rem;\n");
                css.append("    border-radius: 10px;\n");
                css.append("    border-left: 4px solid #ef4444;\n");
                css.append("}\n\n");

                css.append(".error-actions {\n");
                css.append("    display: flex;\n");
                css.append("    gap: 1rem;\n");
                css.append("    justify-content: center;\n");
                css.append("    flex-wrap: wrap;\n");
                css.append("}\n\n");

                css.append(".error-actions .btn {\n");
                css.append("    min-width: 100px;\n");
                css.append("    padding: 0.75rem 1.5rem;\n");
                css.append("    font-weight: 600;\n");
                css.append("    border-radius: 8px;\n");
                css.append("    transition: all 0.3s ease;\n");
                css.append("    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n");
                css.append("}\n\n");

                css.append(".error-actions .btn:hover {\n");
                css.append("    transform: translateY(-2px);\n");
                css.append("    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n");
                css.append("}\n\n");

                css.append("@keyframes errorShake {\n");
                css.append("    0%, 100% { transform: translateX(0); }\n");
                css.append("    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }\n");
                css.append("    20%, 40%, 60%, 80% { transform: translateX(5px); }\n");
                css.append("}\n\n");

                css.append("@keyframes errorPulse {\n");
                css.append("    0% {\n");
                css.append("        transform: translate(-50%, -50%) scale(0.8);\n");
                css.append("        opacity: 0.8;\n");
                css.append("    }\n");
                css.append("    50% {\n");
                css.append("        transform: translate(-50%, -50%) scale(1.2);\n");
                css.append("        opacity: 0.3;\n");
                css.append("    }\n");
                css.append("    100% {\n");
                css.append("        transform: translate(-50%, -50%) scale(0.8);\n");
                css.append("        opacity: 0.8;\n");
                css.append("    }\n");
                css.append("}\n\n");

                return css.toString();
        }

        /**
         * 生成JavaScript代码
         */
        public String generateJS() {
                StringBuilder js = new StringBuilder();

                js.append("// 签到页面JavaScript\n");
                js.append("class CheckInSystem {\n");
                js.append("    constructor() {\n");
                js.append("        this.currentPlayer = null;\n");
                js.append("        this.checkinData = null; // 存储签到数据\n");
                js.append("        this.initializeEventListeners();\n");
                js.append("    }\n\n");

                js.append("    initializeEventListeners() {\n");
                js.append("        // 只为存在的元素添加事件监听器\n");
                js.append("        const playerInput = document.getElementById('playerName');\n");
                js.append("        const checkStatusBtn = document.getElementById('checkStatusBtn');\n\n");

                js.append("        // 玩家名输入事件\n");
                js.append("        if (playerInput) {\n");
                js.append("            playerInput.addEventListener('keypress', (e) => {\n");
                js.append("                if (e.key === 'Enter') {\n");
                js.append("                    this.checkPlayerStatus();\n");
                js.append("                }\n");
                js.append("            });\n");
                js.append("        }\n\n");

                js.append("        // 查看状态按钮\n");
                js.append("        if (checkStatusBtn) {\n");
                js.append("            checkStatusBtn.addEventListener('click', () => {\n");
                js.append("                this.checkPlayerStatus();\n");
                js.append("            });\n");
                js.append("        }\n\n");

                // 签到按钮已移除，签到功能通过卡片实现\n");
                js.append("    }\n\n");

                js.append("    async checkPlayerStatus() {\n");
                js.append("        // 签到页面不需要输入玩家名，直接使用当前绑定的用户名\n");
                js.append("        const playerName = currentUsername;\n");
                js.append("        if (!playerName) {\n");
                js.append("            console.log('没有绑定的用户名，跳过状态检查');\n");
                js.append("            return;\n");
                js.append("        }\n\n");

                js.append("        this.currentPlayer = playerName;\n");
                js.append("        this.showLoading(true);\n\n");

                js.append("        try {\n");
                js.append("            // 首先检查绑定状态\n");
                js.append("            const bindingResponse = await fetch('/checkin/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: {\n");
                js.append("                    'Content-Type': 'application/json'\n");
                js.append("                },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_binding_status',\n");
                js.append("                    player: playerName\n");
                js.append("                })\n");
                js.append("            });\n\n");

                js.append("            const bindingData = await bindingResponse.json();\n");
                js.append("            if (!bindingData.success) {\n");
                js.append("                this.showMessage(bindingData.message || '获取绑定状态失败', 'error');\n");
                js.append("                return;\n");
                js.append("            }\n\n");

                js.append("            // 检查是否已绑定\n");
                js.append("            if (!bindingData.is_bound) {\n");
                js.append("                this.showBindingRequired();\n");
                js.append("                return;\n");
                js.append("            }\n\n");

                js.append("            // 获取签到状态\n");
                js.append("            const response = await fetch('/checkin/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: {\n");
                js.append("                    'Content-Type': 'application/json'\n");
                js.append("                },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_status',\n");
                js.append("                    player: playerName\n");
                js.append("                })\n");
                js.append("            });\n\n");

                js.append("            const data = await response.json();\n");
                js.append("            if (data.success) {\n");
                js.append("                this.updateStatusDisplay(data, bindingData);\n");
                js.append("                this.generateCheckinCards();\n");
                js.append("                this.showStatusArea(true);\n");
                js.append("            } else {\n");
                js.append("                this.showMessage(data.message || '获取状态失败', 'error');\n");
                js.append("            }\n");
                js.append("        } catch (error) {\n");
                js.append("            console.error('Error:', error);\n");
                js.append("            this.showMessage('网络错误，请稍后重试', 'error');\n");
                js.append("        } finally {\n");
                js.append("            this.showLoading(false);\n");
                js.append("        }\n");
                js.append("    }\n\n");

                js.append("    async performCheckIn() {\n");
                js.append("        const playerName = currentUsername || this.currentPlayer;\n");
                js.append("        if (!playerName) {\n");
                js.append("            this.showMessage('请先绑定账户', 'error');\n");
                js.append("            return;\n");
                js.append("        }\n\n");

                js.append("        this.showLoading(true);\n\n");

                js.append("        try {\n");
                js.append("            const response = await fetch('/checkin/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: {\n");
                js.append("                    'Content-Type': 'application/json'\n");
                js.append("                },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'checkin',\n");
                js.append("                    player: playerName\n");
                js.append("                })\n");
                js.append("            });\n\n");

                js.append("            const data = await response.json();\n");
                js.append("            if (data.success) {\n");
                js.append("                this.showMessage(data.message, 'success');\n");
                js.append("                // 重新加载签到数据以更新卡片状态\n");
                js.append("                await this.loadCheckinData();\n");
                js.append("                // 刷新状态显示\n");
                js.append("                await this.checkPlayerStatus();\n");
                js.append("                // 刷新积分显示\n");
                js.append("                if (typeof loadUserPoints === 'function') {\n");
                js.append("                    loadUserPoints();\n");
                js.append("                }\n");
                js.append("            } else {\n");
                js.append("                this.showMessage(data.message || '签到失败', 'error');\n");
                js.append("            }\n");
                js.append("        } catch (error) {\n");
                js.append("            console.error('Error:', error);\n");
                js.append("            this.showMessage('网络错误，请稍后重试', 'error');\n");
                js.append("        } finally {\n");
                js.append("            this.showLoading(false);\n");
                js.append("        }\n");
                js.append("    }\n\n");

                // 辅助方法（更新左侧状态卡片）
                js.append("    updateStatusDisplay(data, bindingData) {\n");
                js.append("        console.log('更新左侧状态显示:', data);\n");
                js.append("        \n");
                js.append("        const todayStatusEl = document.getElementById('todayStatus');\n");
                js.append("        const consecutiveDaysEl = document.getElementById('consecutiveDays');\n");
                js.append("        const totalDaysEl = document.getElementById('totalDays');\n");
                js.append("        \n");
                js.append("        if (todayStatusEl) {\n");
                js.append("            todayStatusEl.textContent = data.has_checked_in ? '已签到' : '未签到';\n");
                js.append("            console.log('今日状态已更新:', data.has_checked_in ? '已签到' : '未签到');\n");
                js.append("        } else {\n");
                js.append("            console.error('找不到todayStatus元素');\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        if (consecutiveDaysEl) {\n");
                js.append("            consecutiveDaysEl.textContent = data.consecutive_days + ' 天';\n");
                js.append("            console.log('连续天数已更新:', data.consecutive_days + ' 天');\n");
                js.append("        } else {\n");
                js.append("            console.error('找不到consecutiveDays元素');\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        if (totalDaysEl) {\n");
                js.append("            totalDaysEl.textContent = data.total_days + ' 天';\n");
                js.append("            console.log('总天数已更新:', data.total_days + ' 天');\n");
                js.append("        } else {\n");
                js.append("            console.error('找不到totalDays元素');\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示绑定信息\n");
                js.append("        this.showBindingInfo(bindingData);\n");
                js.append("    }\n\n");

                js.append("    showBindingRequired() {\n");
                js.append("        const statusArea = document.getElementById('statusArea');\n");
                js.append("        statusArea.innerHTML = `\n");
                js.append("            <div class=\"binding-required\">\n");
                js.append("                <div class=\"binding-icon\">\n");
                js.append("                    <i class=\"fas fa-link\"></i>\n");
                js.append("                </div>\n");
                js.append("                <h3>需要绑定账号</h3>\n");
                js.append("                <p>您还未绑定游戏账号，请先前往积分商店绑定后再使用签到功能。</p>\n");
                js.append("                <div class=\"binding-actions\">\n");
                js.append("                    <a href=\"/points-shop\" class=\"btn btn-primary\">\n");
                js.append("                        <i class=\"fas fa-shopping-cart\"></i> 前往积分商店绑定\n");
                js.append("                    </a>\n");
                js.append("                </div>\n");
                js.append("            </div>\n");
                js.append("        `;\n");
                js.append("        statusArea.style.display = 'block';\n");
                js.append("    }\n\n");

                js.append("    showBindingInfo(bindingData) {\n");
                js.append("        // 在状态区域添加绑定信息\n");
                js.append("        const bindingInfo = document.createElement('div');\n");
                js.append("        bindingInfo.className = 'binding-info';\n");
                js.append("        \n");
                js.append("        const bindTime = new Date(bindingData.bind_time);\n");
                js.append("        const bindDays = bindingData.bind_days || 0;\n");
                js.append("        \n");
                js.append("        bindingInfo.innerHTML = `\n");
                js.append("            <div class=\"binding-status\">\n");
                js.append("                <i class=\"fas fa-check-circle\"></i>\n");
                js.append("                <span>账号已绑定 (${bindDays}天)</span>\n");
                js.append("            </div>\n");
                js.append("        `;\n");
                js.append("        \n");
                js.append("        // 插入到状态卡片后面\n");
                js.append("        const statusCards = document.querySelector('.status-cards');\n");
                js.append("        if (statusCards && !document.querySelector('.binding-info')) {\n");
                js.append("            statusCards.parentNode.insertBefore(bindingInfo, statusCards.nextSibling);\n");
                js.append("        }\n");
                js.append("    }\n\n");

                js.append("    generateCheckinCards() {\n");
                js.append("        const grid = document.getElementById('checkinCardsGrid');\n");
                js.append("        if (!grid) return;\n");
                js.append("        \n");
                js.append("        const currentDate = new Date();\n");
                js.append("        const currentYear = currentDate.getFullYear();\n");
                js.append("        const currentMonth = currentDate.getMonth();\n");
                js.append("        const currentDay = currentDate.getDate();\n");
                js.append("        \n");
                js.append("        // 获取当月天数\n");
                js.append("        const daysInMonth = new Date(currentYear, currentMonth + 1, 0).getDate();\n");
                js.append("        \n");
                js.append("        let cardsHTML = '';\n");
                js.append("        \n");
                js.append("        for (let day = 1; day <= daysInMonth; day++) {\n");
                js.append("            const isToday = day === currentDay;\n");
                js.append("            const isPast = day < currentDay;\n");
                js.append("            const isFuture = day > currentDay;\n");
                js.append("            \n");
                js.append("            // 获取奖励信息\n");
                js.append("            const reward = this.getDayReward(day);\n");
                js.append("            \n");
                js.append("            let cardClass = 'checkin-day-card';\n");
                js.append("            let statusIcon = '';\n");
                js.append("            let statusText = '';\n");
                js.append("            let clickAction = '';\n");
                js.append("            \n");
                js.append("            let hintText = '';\n");
                js.append("            \n");
                js.append("            if (isPast) {\n");
                js.append("                // 如果有用户数据，检查是否已签到\n");
                js.append("                if (currentUsername || this.currentPlayer) {\n");
                js.append("                    const hasCheckedIn = this.hasCheckedInOnDay(day);\n");
                js.append("                    if (hasCheckedIn) {\n");
                js.append("                        cardClass += ' checked-in';\n");
                js.append(
                                "                        statusIcon = '<i class=\"fas fa-check-circle status-icon checked\"></i>';\n");
                js.append("                        statusText = '已签到';\n");
                js.append("                    } else {\n");
                js.append("                        cardClass += ' missed';\n");
                js.append("                        statusIcon = '<i class=\"fas fa-times-circle status-icon missed\"></i>';\n");
                js.append("                        statusText = '已错过';\n");
                js.append("                    }\n");
                js.append("                } else {\n");
                js.append("                    // 没有绑定时显示需要绑定状态\n");
                js.append("                    cardClass += ' bind-required';\n");
                js.append("                    statusIcon = '<i class=\"fas fa-link status-icon bind\"></i>';\n");
                js.append("                    statusText = '需要绑定';\n");
                js.append("                }\n");
                js.append("            } else if (isToday) {\n");
                js.append("                if (currentUsername || this.currentPlayer) {\n");
                js.append("                    // 已绑定，检查今日是否已签到\n");
                js.append("                    const hasCheckedInToday = this.hasCheckedInOnDay(day);\n");
                js.append("                    if (hasCheckedInToday) {\n");
                js.append("                        cardClass += ' checked-in';\n");
                js.append(
                                "                        statusIcon = '<i class=\"fas fa-check-circle status-icon checked\"></i>';\n");
                js.append("                        statusText = '已签到';\n");
                js.append("                    } else {\n");
                js.append("                        cardClass += ' today available';\n");
                js.append("                        statusIcon = '<i class=\"fas fa-star status-icon today\"></i>';\n");
                js.append("                        statusText = '今日签到';\n");
                js.append("                        clickAction = 'onclick=\"checkInTodayFromCard()\"';\n");
                js.append("                        hintText = '点击签到';\n");
                js.append("                    }\n");
                js.append("                } else {\n");
                js.append("                    // 未绑定，显示需要绑定\n");
                js.append("                    cardClass += ' today bind-required';\n");
                js.append("                    statusIcon = '<i class=\"fas fa-link status-icon bind\"></i>';\n");
                js.append("                    statusText = '需要绑定';\n");
                js.append("                    clickAction = 'onclick=\"generateBindCode()\"';\n");
                js.append("                    hintText = '点击绑定';\n");
                js.append("                }\n");
                js.append("            } else {\n");
                js.append("                cardClass += ' future';\n");
                js.append("                statusIcon = '<i class=\"fas fa-clock status-icon future\"></i>';\n");
                js.append("                statusText = '未来';\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 生成补签按钮（仅对已错过的日期显示）\n");
                js.append("            let makeupButton = '';\n");
                js.append("            if (isPast && (currentUsername || this.currentPlayer)) {\n");
                js.append("                const hasCheckedIn = this.hasCheckedInOnDay(day);\n");
                js.append("                if (!hasCheckedIn) {\n");
                js.append("                    makeupButton = `\n");
                js.append("                        <div class=\"makeup-button-container\">\n");
                js.append(
                                "                            <button class=\"makeup-btn\" onclick=\"handleMakeupCheckIn(${day})\" title=\"补签\">\n");
                js.append("                                <i class=\"fas fa-redo\"></i> 补签\n");
                js.append("                            </button>\n");
                js.append("                        </div>\n");
                js.append("                    `;\n");
                js.append("                }\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            cardsHTML += `\n");
                js.append("                <div class=\"${cardClass}\" data-day=\"${day}\" ${clickAction}>\n");
                js.append("                    <div class=\"day-number\">${day}</div>\n");
                js.append("                    <div class=\"day-status\">\n");
                js.append("                        ${statusIcon}\n");
                js.append("                        <div class=\"status-text\">${statusText}</div>\n");
                js.append("                    </div>\n");
                js.append("                    <div class=\"day-reward\">\n");
                js.append("                        <div class=\"reward-points\">💰 ${reward.points} 积分</div>\n");
                js.append(
                                "                        ${reward.items ? `<div class=\"reward-items\">${reward.items}</div>` : ''}\n");
                js.append("                    </div>\n");
                js.append("                    ${hintText ? `<div class=\"checkin-hint\">${hintText}</div>` : ''}\n");
                js.append("                    ${makeupButton}\n");
                js.append("                </div>\n");
                js.append("            `;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        grid.innerHTML = cardsHTML;\n");
                js.append("        \n");
                js.append("        // 更新月份显示\n");
                js.append("        const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', \n");
                js.append("                           '7月', '8月', '9月', '10月', '11月', '12月'];\n");
                js.append("        const monthElement = document.getElementById('currentMonth');\n");
                js.append("        if (monthElement) {\n");
                js.append("            monthElement.textContent = `${currentYear}年${monthNames[currentMonth]}`;\n");
                js.append("        }\n");
                js.append("    }\n\n");

                js.append("    getDayReward(day) {\n");
                js.append("        // 默认奖励配置，实际应该从服务器获取\n");
                js.append("        const basePoints = 10;\n");
                js.append("        const bonusPoints = Math.floor(day / 7) * 5; // 每周额外5积分\n");
                js.append("        \n");
                js.append("        let items = '';\n");
                js.append("        if (day % 7 === 0) {\n");
                js.append("            items = '🎁 周奖励';\n");
                js.append("        } else if (day === 15) {\n");
                js.append("            items = '💎 月中奖励';\n");
                js.append(
                                "        } else if (day === new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).getDate()) {\n");
                js.append("            items = '👑 月末大奖';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        return {\n");
                js.append("            points: basePoints + bonusPoints,\n");
                js.append("            items: items\n");
                js.append("        };\n");
                js.append("    }\n\n");

                js.append("    hasCheckedInOnDay(day) {\n");
                js.append("        // 从实际签到数据判断\n");
                js.append("        if (!this.checkinData || !this.checkinData.checkin_days) {\n");
                js.append("            return false;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        const currentDate = new Date();\n");
                js.append("        const currentYear = currentDate.getFullYear();\n");
                js.append("        const currentMonth = currentDate.getMonth() + 1; // 月份从0开始，需要+1\n");
                js.append(
                                "        const dateKey = `${currentYear}-${currentMonth.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;\n");
                js.append("        \n");
                js.append("        return this.checkinData.checkin_days.includes(dateKey);\n");
                js.append("    }\n\n");

                js.append("    // 获取签到数据\n");
                js.append("    async loadCheckinData() {\n");
                js.append("        if (!this.currentPlayer && !currentUsername) {\n");
                js.append("            console.log('没有当前玩家，跳过加载签到数据');\n");
                js.append("            return;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        const playerName = this.currentPlayer || currentUsername;\n");
                js.append("        console.log('加载签到数据，玩家:', playerName);\n");
                js.append("        \n");
                js.append("        try {\n");
                js.append("            const response = await fetch('/checkin/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_status',\n");
                js.append("                    player: playerName\n");
                js.append("                })\n");
                js.append("            });\n");
                js.append("            \n");
                js.append("            const data = await response.json();\n");
                js.append("            if (data.success) {\n");
                js.append("                this.checkinData = data;\n");
                js.append("                console.log('签到数据加载成功:', data);\n");
                js.append("                // 重新生成卡片以显示正确状态\n");
                js.append("                this.generateCheckinCards();\n");
                js.append("            } else {\n");
                js.append("                console.error('获取签到数据失败:', data.message);\n");
                js.append("                this.checkinData = null;\n");
                js.append("            }\n");
                js.append("        } catch (error) {\n");
                js.append("            console.error('加载签到数据出错:', error);\n");
                js.append("            this.checkinData = null;\n");
                js.append("        }\n");
                js.append("    }\n\n");

                js.append("    showStatusArea(show) {\n");
                js.append("        // 状态区域已移除，此方法保留以保持兼容性\n");
                js.append("        console.log('状态区域已移除，跳过显示');\n");
                js.append("    }\n\n");

                js.append("    updateDateDisplay() {\n");
                js.append("        const now = new Date();\n");
                js.append("        const year = now.getFullYear();\n");
                js.append("        const month = now.getMonth();\n");
                js.append("        const day = now.getDate();\n");
                js.append("        const dayOfWeek = now.getDay();\n");
                js.append("        \n");
                js.append("        const monthNames = ['1月', '2月', '3月', '4月', '5月', '6月', \n");
                js.append("                           '7月', '8月', '9月', '10月', '11月', '12月'];\n");
                js.append("        const dayNames = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];\n");
                js.append("        \n");
                js.append("        const monthElement = document.getElementById('currentMonth');\n");
                js.append("        const dayElement = document.getElementById('currentDay');\n");
                js.append("        const dayNameElement = document.getElementById('dayName');\n");
                js.append("        \n");
                js.append("        if (monthElement) {\n");
                js.append("            monthElement.textContent = `${year}年${monthNames[month]}`;\n");
                js.append("        }\n");
                js.append("        if (dayElement) {\n");
                js.append("            dayElement.textContent = day;\n");
                js.append("        }\n");
                js.append("        if (dayNameElement) {\n");
                js.append("            dayNameElement.textContent = dayNames[dayOfWeek];\n");
                js.append("        }\n");
                js.append("    }\n\n");

                js.append("    showLoading(show) {\n");
                js.append("        const checkStatusBtn = document.getElementById('checkStatusBtn');\n");
                js.append("        \n");
                js.append("        if (show) {\n");
                js.append("            if (checkStatusBtn) {\n");
                js.append("                checkStatusBtn.disabled = true;\n");
                js.append("                checkStatusBtn.innerHTML = '<i class=\"fas fa-spinner fa-spin\"></i> 加载中...';\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            if (checkStatusBtn) {\n");
                js.append("                checkStatusBtn.disabled = false;\n");
                js.append("                checkStatusBtn.innerHTML = '<i class=\"fas fa-search\"></i> 查看状态';\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("    }\n\n");

                js.append("    showMessage(message, type = 'info') {\n");
                js.append("        // 创建消息提示\n");
                js.append("        const messageDiv = document.createElement('div');\n");
                js.append("        messageDiv.className = `message message-${type}`;\n");
                js.append("        messageDiv.innerHTML = `\n");
                js.append("            <i class=\"fas fa-${type === 'success' ? 'check-circle' : \n");
                js.append("                type === 'error' ? 'exclamation-circle' : 'info-circle'}\"></i>\n");
                js.append("            ${message}\n");
                js.append("        `;\n\n");

                js.append("        // 添加样式\n");
                js.append("        messageDiv.style.cssText = `\n");
                js.append("            position: fixed;\n");
                js.append("            top: 20px;\n");
                js.append("            right: 20px;\n");
                js.append("            padding: 1rem 1.5rem;\n");
                js.append("            border-radius: 8px;\n");
                js.append("            color: white;\n");
                js.append("            font-weight: 600;\n");
                js.append("            z-index: 10000;\n");
                js.append("            animation: slideIn 0.3s ease;\n");
                js.append("            background: ${type === 'success' ? '#28a745' : \n");
                js.append("                type === 'error' ? '#dc3545' : '#17a2b8'};\n");
                js.append("            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n");
                js.append("        `;\n\n");

                js.append("        document.body.appendChild(messageDiv);\n\n");

                js.append("        // 3秒后自动移除\n");
                js.append("        setTimeout(() => {\n");
                js.append("            messageDiv.style.animation = 'slideOut 0.3s ease';\n");
                js.append("            setTimeout(() => {\n");
                js.append("                if (messageDiv.parentNode) {\n");
                js.append("                    messageDiv.parentNode.removeChild(messageDiv);\n");
                js.append("                }\n");
                js.append("            }, 300);\n");
                js.append("        }, 3000);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 从卡片点击签到\n");
                js.append("async function checkInTodayFromCard() {\n");
                js.append("    if (window.checkinSystem) {\n");
                js.append("        await window.checkinSystem.performCheckIn();\n");
                js.append("    } else {\n");
                js.append("        await checkInToday();\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 全局签到函数\n");
                js.append("async function checkInToday() {\n");
                js.append("    // 使用当前绑定的用户名\n");
                js.append("    const playerName = currentUsername;\n");
                js.append("    if (!playerName) {\n");
                js.append("        alert('请先绑定账户！');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    try {\n");
                js.append("        // 首先检查绑定状态\n");
                js.append("        const bindingResponse = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: {\n");
                js.append("                'Content-Type': 'application/json'\n");
                js.append("            },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'get_binding_status',\n");
                js.append("                player: playerName\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        const bindingData = await bindingResponse.json();\n");
                js.append("        if (!bindingData.success || !bindingData.is_bound) {\n");
                js.append("            alert('❌ 您还未绑定账号，请先前往积分商店绑定后再签到！');\n");
                js.append("            return;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 执行签到\n");
                js.append("        const response = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: {\n");
                js.append("                'Content-Type': 'application/json'\n");
                js.append("            },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'checkin',\n");
                js.append("                player: playerName\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        const data = await response.json();\n");
                js.append("        if (data.success) {\n");
                js.append("            alert('✅ ' + data.message);\n");
                js.append("            // 重新检查状态以更新界面\n");
                js.append("            if (window.checkinSystem) {\n");
                js.append("                await window.checkinSystem.loadCheckinData();\n");
                js.append("                await window.checkinSystem.checkPlayerStatus();\n");
                js.append("            }\n");
                js.append("            // 刷新积分显示\n");
                js.append("            if (typeof loadUserPoints === 'function') {\n");
                js.append("                loadUserPoints();\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            alert('❌ ' + data.message);\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('Error:', error);\n");
                js.append("        alert('❌ 签到失败: 网络错误');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 从卡片点击签到\n");
                js.append("function checkInTodayFromCard() {\n");
                js.append("    // 检查是否已经查看过状态\n");
                js.append("    if (!window.checkinSystem || !window.checkinSystem.currentPlayer) {\n");
                js.append("        alert('请先输入玩家名并点击\"查看状态\"按钮！');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 调用签到功能\n");
                js.append("    window.checkinSystem.performCheckIn();\n");
                js.append("}\n\n");

                js.append("// 全局变量\n");
                js.append("let currentUsername = '';\n");
                js.append("let userPoints = 0;\n");
                js.append("let bindCheckInterval = null;\n");
                js.append("let progressInterval = null;\n");
                js.append("let currentBindCode = '';\n");
                js.append("let sessionId = '';\n\n");

                js.append("// 购买补签卡功能 - 显示购买弹窗\n");
                js.append("let currentPurchaseType = '';\n");
                js.append("let currentUnitPrice = 0;\n");
                js.append("let currentPriceText = '';\n");
                js.append("let makeupCardPrices = {};\n\n");
                js.append("// 调试函数 - 检查弹窗状态\n");
                js.append("function debugModal() {\n");
                js.append("    const modal = document.getElementById('purchaseModal');\n");
                js.append("    console.log('=== 弹窗调试信息 ===');\n");
                js.append("    console.log('弹窗元素:', modal);\n");
                js.append("    if (modal) {\n");
                js.append("        console.log('弹窗innerHTML长度:', modal.innerHTML.length);\n");
                js.append("        console.log('弹窗样式:', modal.style.cssText);\n");
                js.append("        console.log('弹窗计算样式display:', window.getComputedStyle(modal).display);\n");
                js.append("        console.log('弹窗计算样式visibility:', window.getComputedStyle(modal).visibility);\n");
                js.append("        console.log('弹窗计算样式zIndex:', window.getComputedStyle(modal).zIndex);\n");
                js.append("        console.log('弹窗父元素:', modal.parentElement);\n");
                js.append("        console.log('弹窗位置信息:', modal.getBoundingClientRect());\n");
                js.append("    } else {\n");
                js.append("        console.log('弹窗元素不存在');\n");
                js.append("        console.log('所有ID为purchaseModal的元素:', document.querySelectorAll('#purchaseModal'));\n");
                js.append("        console.log('所有class包含modal的元素:', document.querySelectorAll('[class*=\"modal\"]'));\n");
                js.append("    }\n");
                js.append("    console.log('=== 调试信息结束 ===');\n");
                js.append("}\n\n");

                js.append("async function buyMakeupCard(type) {\n");
                js.append("    console.log('购买补签卡:', type);\n");
                js.append("    console.log('当前用户名:', currentUsername);\n");
                js.append("    \n");
                js.append("    // 首先重置弹窗内容，确保显示的是购买表单而不是成功消息\n");
                js.append("    resetPurchaseModalContent();\n");
                js.append("    \n");
                js.append("    // 调试：检查弹窗状态\n");
                js.append("    debugModal();\n");
                js.append("    \n");
                js.append("    // 使用当前绑定的用户名\n");
                js.append("    if (!currentUsername) {\n");
                js.append("        alert('请先绑定账户！');\n");
                js.append("        console.error('currentUsername为空，无法购买');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 检查价格信息是否已加载\n");
                js.append("    if (!makeupCardPrices || !makeupCardPrices[type]) {\n");
                js.append("        alert('无法获取价格信息，请刷新页面重试');\n");
                js.append("        console.error('价格信息未加载:', makeupCardPrices);\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    currentPurchaseType = type;\n");
                js.append("    \n");
                js.append("    // 从价格数据中获取信息\n");
                js.append("    const priceInfo = makeupCardPrices[type];\n");
                js.append("    currentUnitPrice = priceInfo.price || 0;\n");
                js.append("    currentPriceText = currentUnitPrice + ' ' + (priceInfo.currency_name || '货币');\n");
                js.append("    \n");
                js.append("    console.log('价格信息:', priceInfo);\n");
                js.append("    console.log('单价:', currentUnitPrice, '价格文本:', currentPriceText);\n");
                js.append("    \n");
                js.append("    // 设置弹窗内容\n");
                js.append("    setupPurchaseModal(type);\n");
                js.append("    \n");
                js.append("    // 显示弹窗\n");
                js.append("    const modal = document.getElementById('purchaseModal');\n");
                js.append("    if (modal) {\n");
                js.append("        // 强制设置样式\n");
                js.append("        modal.style.cssText = `\n");
                js.append("            position: fixed !important;\n");
                js.append("            top: 0 !important;\n");
                js.append("            left: 0 !important;\n");
                js.append("            width: 100% !important;\n");
                js.append("            height: 100% !important;\n");
                js.append("            background: rgba(0, 0, 0, 0.6) !important;\n");
                js.append("            display: flex !important;\n");
                js.append("            justify-content: center !important;\n");
                js.append("            align-items: center !important;\n");
                js.append("            z-index: 999999 !important;\n");
                js.append("            backdrop-filter: blur(8px) !important;\n");
                js.append("            padding: 20px !important;\n");
                js.append("            box-sizing: border-box !important;\n");
                js.append("            visibility: visible !important;\n");
                js.append("            opacity: 1 !important;\n");
                js.append("        `;\n");
                js.append("        console.log('购买弹窗已显示');\n");
                js.append("        console.log('弹窗元素:', modal);\n");
                js.append("        console.log('弹窗样式:', modal.style.cssText);\n");
                js.append("        console.log('弹窗计算样式:', window.getComputedStyle(modal).display);\n");
                js.append("    } else {\n");
                js.append("        console.error('找不到购买弹窗元素');\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 加载用户余额\n");
                js.append("    loadUserBalance(type);\n");
                js.append("}\n\n");
                js.append("// 设置购买弹窗内容\n");
                js.append("function setupPurchaseModal(type) {\n");
                js.append("    // 获取价格信息\n");
                js.append("    const priceInfo = makeupCardPrices[type];\n");
                js.append("    if (!priceInfo) {\n");
                js.append("        alert('该支付方式不可用');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    const typeConfig = {\n");
                js.append("        'gold': {\n");
                js.append("            name: '金币补签卡',\n");
                js.append("            desc: '使用游戏内金币购买补签卡',\n");
                js.append("            icon: 'fas fa-coins',\n");
                js.append("            iconBg: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)'\n");
                js.append("        },\n");
                js.append("        'points': {\n");
                js.append("            name: '点券补签卡',\n");
                js.append("            desc: '使用Points插件点券购买补签卡',\n");
                js.append("            icon: 'fas fa-gem',\n");
                js.append("            iconBg: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'\n");
                js.append("        },\n");
                js.append("        'score': {\n");
                js.append("            name: '积分补签卡',\n");
                js.append("            desc: '使用系统积分购买补签卡',\n");
                js.append("            icon: 'fas fa-star',\n");
                js.append("            iconBg: 'linear-gradient(135deg, #10b981 0%, #059669 100%)'\n");
                js.append("        },\n");
                js.append("        'custom': {\n");
                js.append("            name: '自定义补签卡',\n");
                js.append("            desc: '使用自定义货币购买补签卡',\n");
                js.append("            icon: 'fas fa-cog',\n");
                js.append("            iconBg: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)'\n");
                js.append("        }\n");
                js.append("    };\n");
                js.append("    \n");
                js.append("    const config = typeConfig[type] || {\n");
                js.append("        name: priceInfo.currency_name + '补签卡',\n");
                js.append("        desc: '使用' + priceInfo.currency_name + '购买补签卡',\n");
                js.append("        icon: 'fas fa-question',\n");
                js.append("        iconBg: '#6b7280'\n");
                js.append("    };\n");
                js.append("    \n");
                js.append("    // 设置图标\n");
                js.append("    const iconElement = document.getElementById('purchaseIcon');\n");
                js.append("    if (iconElement) {\n");
                js.append("        iconElement.innerHTML = `<i class=\"${config.icon}\"></i>`;\n");
                js.append("        iconElement.style.background = config.iconBg;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 设置商品信息\n");
                js.append("    const nameElement = document.getElementById('purchaseItemName');\n");
                js.append("    if (nameElement) nameElement.textContent = config.name;\n");
                js.append("    \n");
                js.append("    const descElement = document.getElementById('purchaseItemDesc');\n");
                js.append("    if (descElement) descElement.textContent = config.desc;\n");
                js.append("    \n");
                js.append("    const priceElement = document.getElementById('purchaseUnitPrice');\n");
                js.append("    if (priceElement) priceElement.textContent = currentPriceText;\n");
                js.append("    \n");
                js.append("    // 重置数量为1\n");
                js.append("    const quantityElement = document.getElementById('purchaseQuantity');\n");
                js.append("    if (quantityElement) {\n");
                js.append("        quantityElement.value = 1;\n");
                js.append("        updateTotalPrice();\n");
                js.append("    } else {\n");
                js.append("        console.error('找不到purchaseQuantity元素');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 调整购买数量\n");
                js.append("function adjustQuantity(delta) {\n");
                js.append("    const quantityInput = document.getElementById('purchaseQuantity');\n");
                js.append("    if (!quantityInput) {\n");
                js.append("        console.error('找不到purchaseQuantity元素');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    let quantity = parseInt(quantityInput.value) + delta;\n");
                js.append("    quantity = Math.max(1, Math.min(99, quantity));\n");
                js.append("    quantityInput.value = quantity;\n");
                js.append("    updateTotalPrice();\n");
                js.append("}\n\n");

                js.append("// 更新总价\n");
                js.append("function updateTotalPrice() {\n");
                js.append("    const quantityElement = document.getElementById('purchaseQuantity');\n");
                js.append("    const totalPriceElement = document.getElementById('purchaseTotalPrice');\n");
                js.append("    \n");
                js.append("    if (!quantityElement || !totalPriceElement) {\n");
                js.append("        console.error('找不到必要的价格元素');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    const quantity = parseInt(quantityElement.value) || 1;\n");
                js.append("    const totalPrice = currentUnitPrice * quantity;\n");
                js.append("    const unit = currentPriceText.replace(/\\d+/g, '').trim();\n");
                js.append("    totalPriceElement.textContent = totalPrice + ' ' + unit;\n");
                js.append("}\n\n");

                js.append("// 加载用户余额\n");
                js.append("async function loadUserBalance(type) {\n");
                js.append("    console.log('开始加载用户余额, type:', type, 'currentUsername:', currentUsername);\n");
                js.append("    \n");
                js.append("    try {\n");
                js.append("        let balanceText = '-- --';\n");
                js.append("        \n");
                js.append("        // 检查用户是否已绑定\n");
                js.append("        if (!currentUsername) {\n");
                js.append("            console.error('用户未绑定，无法获取余额');\n");
                js.append("            const balanceElement = document.getElementById('userBalance');\n");
                js.append("            if (balanceElement) {\n");
                js.append("                balanceElement.textContent = '请先绑定账户';\n");
                js.append("            }\n");
                js.append("            return;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 获取价格信息\n");
                js.append("        const priceInfo = makeupCardPrices[type];\n");
                js.append("        if (!priceInfo) {\n");
                js.append("            console.error('找不到价格信息:', type);\n");
                js.append("            const balanceElement = document.getElementById('userBalance');\n");
                js.append("            if (balanceElement) {\n");
                js.append("                balanceElement.textContent = '不可用';\n");
                js.append("            }\n");
                js.append("            return;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        const currencyName = priceInfo.currency_name || '未知货币';\n");
                js.append("        const balanceVariable = priceInfo.balance_variable;\n");
                js.append("        \n");
                js.append("        if (balanceVariable === 'internal') {\n");
                js.append("            // 使用积分商店的积分系统，需要从服务器获取\n");
                js.append("            console.log('使用内部积分系统获取余额');\n");
                js.append("            const requestData = {\n");
                js.append("                action: 'get_user_balance',\n");
                js.append("                player: currentUsername,\n");
                js.append("                currency_type: type,\n");
                js.append("                balance_variable: balanceVariable\n");
                js.append("            };\n");
                js.append("            console.log('发送余额请求:', requestData);\n");
                js.append("            \n");
                js.append("            const response = await fetch('/checkin/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify(requestData)\n");
                js.append("            });\n");
                js.append("            \n");
                js.append("            console.log('余额API响应状态:', response.status);\n");
                js.append("            const data = await response.json();\n");
                js.append("            console.log('余额API响应数据:', data);\n");
                js.append("            \n");
                js.append("            if (data.success) {\n");
                js.append("                balanceText = (data.balance || 0) + ' ' + currencyName;\n");
                js.append("                console.log('余额获取成功:', balanceText);\n");
                js.append("            } else {\n");
                js.append("                console.error('余额获取失败:', data.message);\n");
                js.append("                balanceText = '获取失败: ' + (data.message || '未知错误');\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            // 使用PlaceholderAPI变量或从服务器获取\n");
                js.append("            const response = await fetch('/checkin/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_user_balance',\n");
                js.append("                    player: currentUsername,\n");
                js.append("                    currency_type: type,\n");
                js.append("                    balance_variable: balanceVariable\n");
                js.append("                })\n");
                js.append("            });\n");
                js.append("            \n");
                js.append("            const data = await response.json();\n");
                js.append("            if (data.success) {\n");
                js.append("                balanceText = (data.balance || 0) + ' ' + currencyName;\n");
                js.append("            } else {\n");
                js.append("                balanceText = '获取失败';\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        const balanceElement = document.getElementById('userBalance');\n");
                js.append("        if (balanceElement) {\n");
                js.append("            balanceElement.textContent = balanceText;\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('加载余额失败:', error);\n");
                js.append("        const balanceElement = document.getElementById('userBalance');\n");
                js.append("        if (balanceElement) {\n");
                js.append("            balanceElement.textContent = '获取失败';\n");
                js.append("        }\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 关闭购买弹窗\n");
                js.append("function closePurchaseModal() {\n");
                js.append("    console.log('关闭购买弹窗');\n");
                js.append("    const modal = document.getElementById('purchaseModal');\n");
                js.append("    if (modal) {\n");
                js.append("        modal.style.display = 'none';\n");
                js.append("        modal.style.visibility = 'hidden';\n");
                js.append("        modal.style.opacity = '0';\n");
                js.append("        \n");
                js.append("        // 重置弹窗内容为原始购买表单\n");
                js.append("        resetPurchaseModalContent();\n");
                js.append("        \n");
                js.append("        console.log('购买弹窗已隐藏并重置');\n");
                js.append("    } else {\n");
                js.append("        console.error('找不到购买弹窗元素');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 重置购买弹窗内容\n");
                js.append("function resetPurchaseModalContent() {\n");
                js.append("    const modalBody = document.querySelector('.purchase-modal-body');\n");
                js.append("    if (!modalBody) {\n");
                js.append("        console.error('找不到购买弹窗主体元素');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 重置为原始的购买表单HTML\n");
                js.append("    modalBody.innerHTML = `\n");
                js.append("        <div class=\"purchase-item-info\">\n");
                js.append("            <div class=\"item-icon\" id=\"purchaseIcon\">\n");
                js.append("                <i class=\"fas fa-coins\"></i>\n");
                js.append("            </div>\n");
                js.append("            <div class=\"item-details\">\n");
                js.append("                <h3 id=\"purchaseItemName\">金币补签卡</h3>\n");
                js.append("                <p id=\"purchaseItemDesc\">使用游戏内金币购买补签卡</p>\n");
                js.append("                <div class=\"item-price\">\n");
                js.append("                    <span>单价：</span>\n");
                js.append("                    <span id=\"purchaseUnitPrice\" class=\"price-value\">100 金币</span>\n");
                js.append("                </div>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("        \n");
                js.append("        <div class=\"purchase-quantity\">\n");
                js.append("            <label for=\"purchaseQuantity\">购买数量：</label>\n");
                js.append("            <div class=\"quantity-controls\">\n");
                js.append("                <button type=\"button\" class=\"quantity-btn\" onclick=\"adjustQuantity(-1)\">-</button>\n");
                js.append("                <input type=\"number\" id=\"purchaseQuantity\" value=\"1\" min=\"1\" max=\"99\" onchange=\"updateTotalPrice()\">\n");
                js.append("                <button type=\"button\" class=\"quantity-btn\" onclick=\"adjustQuantity(1)\">+</button>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("        \n");
                js.append("        <div class=\"purchase-total\">\n");
                js.append("            <div class=\"total-price\">\n");
                js.append("                <span>总价：</span>\n");
                js.append("                <span id=\"purchaseTotalPrice\" class=\"price-value\">100 金币</span>\n");
                js.append("            </div>\n");
                js.append("            <div class=\"user-balance\">\n");
                js.append("                <span>当前余额：</span>\n");
                js.append("                <span id=\"userBalance\" class=\"balance-value\">-- --</span>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("        \n");
                js.append("        <div class=\"purchase-actions\">\n");
                js.append("            <button class=\"btn btn-secondary\" onclick=\"closePurchaseModal()\">取消</button>\n");
                js.append("            <button class=\"btn btn-primary\" id=\"confirmPurchaseBtn\" onclick=\"confirmPurchase()\">确认购买</button>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    console.log('购买弹窗内容已重置为原始表单');\n");
                js.append("}\n\n");

                js.append("// 显示购买错误消息\n");
                js.append("function showPurchaseError(message) {\n");
                js.append("    const modalBody = document.querySelector('.purchase-modal-body');\n");
                js.append("    if (!modalBody) {\n");
                js.append("        console.error('找不到购买弹窗主体元素');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    modalBody.innerHTML = `\n");
                js.append("        <div class=\"purchase-error-container\">\n");
                js.append("            <div class=\"error-icon-wrapper\">\n");
                js.append("                <div class=\"error-icon\">❌</div>\n");
                js.append("                <div class=\"error-pulse\"></div>\n");
                js.append("            </div>\n");
                js.append("            <h3 class=\"error-title\">购买失败</h3>\n");
                js.append("            <p class=\"error-message\">${message}</p>\n");
                js.append("            <div class=\"error-actions\">\n");
                js.append("                <button class=\"btn btn-secondary\" onclick=\"resetPurchaseModalContent()\">重试</button>\n");
                js.append("                <button class=\"btn btn-primary\" onclick=\"closePurchaseModal()\">确定</button>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("}\n\n");

                js.append("// 确认购买\n");
                js.append("async function confirmPurchase() {\n");
                js.append("    const quantityElement = document.getElementById('purchaseQuantity');\n");
                js.append("    const quantity = quantityElement ? parseInt(quantityElement.value) || 1 : 1;\n");
                js.append("    \n");
                js.append("    // 禁用购买按钮防止重复点击\n");
                js.append("    const confirmBtn = document.getElementById('confirmPurchaseBtn');\n");
                js.append("    if (confirmBtn) {\n");
                js.append("        confirmBtn.disabled = true;\n");
                js.append("        confirmBtn.textContent = '购买中...';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    try {\n");
                js.append("        // 发送购买请求\n");
                js.append("        const response = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'buy_makeup_card',\n");
                js.append("                player: currentUsername,\n");
                js.append("                payment_type: currentPurchaseType,\n");
                js.append("                quantity: quantity\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        const data = await response.json();\n");
                js.append("        if (data.success) {\n");
                js.append("            // 购买成功\n");
                js.append("            showPurchaseSuccess(data.message || '购买成功！', quantity);\n");
                js.append("            \n");
                js.append("            // 关闭弹窗\n");
                js.append("            setTimeout(() => {\n");
                js.append("                closePurchaseModal();\n");
                js.append("            }, 2000);\n");
                js.append("            \n");
                js.append("            // 立即更新补签卡数量和货币显示\n");
                js.append("            updateMakeupCardCount();\n");
                js.append("            loadAllCurrency();\n");
                js.append("            \n");
                js.append("            // 刷新页面数据\n");
                js.append("            if (window.checkinSystem) {\n");
                js.append("                await window.checkinSystem.loadCheckinData();\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            showPurchaseError(data.message || '未知错误');\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('购买失败:', error);\n");
                js.append("        showPurchaseError('购买失败，请稍后重试');\n");
                js.append("    } finally {\n");
                js.append("        // 恢复购买按钮\n");
                js.append("        if (confirmBtn) {\n");
                js.append("            confirmBtn.disabled = false;\n");
                js.append("            confirmBtn.textContent = '确认购买';\n");
                js.append("        }\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 显示购买成功消息\n");
                js.append("function showPurchaseSuccess(message, quantity) {\n");
                js.append("    const modalBody = document.querySelector('.purchase-modal-body');\n");
                js.append("    if (!modalBody) {\n");
                js.append("        console.error('找不到购买弹窗主体元素');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    modalBody.innerHTML = `\n");
                js.append("        <div style=\"text-align: center; padding: 2rem;\">\n");
                js.append("            <div style=\"font-size: 3rem; color: #10b981; margin-bottom: 1rem;\">✅</div>\n");
                js.append("            <h3 style=\"color: #10b981; margin-bottom: 1rem;\">购买成功！</h3>\n");
                js.append("            <p style=\"color: #666; margin-bottom: 1rem;\">${message}</p>\n");
                js.append("            <p style=\"color: #333;\">已获得 <strong>${quantity}</strong> 张补签卡</p>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("}\n\n");

                js.append("// 处理补签操作\n");
                js.append("async function handleMakeupCheckIn(day) {\n");
                js.append("    if (!currentUsername) {\n");
                js.append("        alert('请先绑定账户！');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    try {\n");
                js.append("        // 首先检查用户的补签卡数量\n");
                js.append("        const response = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'get_makeup_cards',\n");
                js.append("                player: currentUsername\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        const data = await response.json();\n");
                js.append("        if (data.success) {\n");
                js.append("            const cardCount = data.card_count || 0;\n");
                js.append("            \n");
                js.append("            if (cardCount > 0) {\n");
                js.append("                // 有补签卡，确认使用\n");
                js.append("                showMakeupConfirmModal(cardCount, day);\n");
                js.append("            } else {\n");
                js.append("                // 没有补签卡，提示购买\n");
                js.append("                showMakeupCardPurchasePrompt(day);\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            alert('获取补签卡信息失败：' + (data.message || '未知错误'));\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('检查补签卡失败:', error);\n");
                js.append("        alert('检查补签卡失败，请稍后重试');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 显示补签确认模态框\n");
                js.append("function showMakeupConfirmModal(cardCount, day) {\n");
                js.append("    const modal = document.createElement('div');\n");
                js.append("    modal.className = 'makeup-confirm-modal';\n");
                js.append("    modal.style.cssText = `\n");
                js.append("        position: fixed;\n");
                js.append("        top: 0;\n");
                js.append("        left: 0;\n");
                js.append("        width: 100%;\n");
                js.append("        height: 100%;\n");
                js.append("        background: rgba(0, 0, 0, 0.6);\n");
                js.append("        display: flex;\n");
                js.append("        justify-content: center;\n");
                js.append("        align-items: center;\n");
                js.append("        z-index: 10001;\n");
                js.append("        backdrop-filter: blur(8px);\n");
                js.append("        opacity: 0;\n");
                js.append("        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    modal.innerHTML = `\n");
                js.append("        <div style=\"\n");
                js.append("            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n");
                js.append("            border-radius: 20px;\n");
                js.append("            padding: 0;\n");
                js.append("            max-width: 420px;\n");
                js.append("            width: 90%;\n");
                js.append("            text-align: center;\n");
                js.append("            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);\n");
                js.append("            border: 1px solid rgba(255, 255, 255, 0.8);\n");
                js.append("            overflow: hidden;\n");
                js.append("            transform: scale(0.8);\n");
                js.append("            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                js.append("        \">\n");
                js.append("            <!-- 装饰性头部 -->\n");
                js.append("            <div style=\"\n");
                js.append("                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                js.append("                padding: 1.5rem;\n");
                js.append("                position: relative;\n");
                js.append("                overflow: hidden;\n");
                js.append("            \">\n");
                js.append("                <div style=\"\n");
                js.append("                    position: absolute;\n");
                js.append("                    top: -50%;\n");
                js.append("                    right: -50%;\n");
                js.append("                    width: 100%;\n");
                js.append("                    height: 100%;\n");
                js.append("                    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);\n");
                js.append("                \"></div>\n");
                js.append("                <div style=\"font-size: 3.5rem; margin-bottom: 0.5rem; position: relative;\">🎫</div>\n");
                js.append("                <h3 style=\"color: white; margin: 0; font-size: 1.3rem; font-weight: 600; position: relative;\">使用补签卡</h3>\n");
                js.append("            </div>\n");
                js.append("            \n");
                js.append("            <!-- 内容区域 -->\n");
                js.append("            <div style=\"padding: 2rem 1.5rem 1.5rem 1.5rem;\">\n");
                js.append("                <div style=\"\n");
                js.append("                    background: linear-gradient(135deg, #e0f2fe 0%, #f3e5f5 100%);\n");
                js.append("                    border-radius: 12px;\n");
                js.append("                    padding: 1rem;\n");
                js.append("                    margin-bottom: 1.5rem;\n");
                js.append("                    border: 1px solid rgba(103, 126, 234, 0.2);\n");
                js.append("                \">\n");
                js.append("                    <p style=\"\n");
                js.append("                        color: #37474f;\n");
                js.append("                        margin: 0;\n");
                js.append("                        font-size: 1rem;\n");
                js.append("                        line-height: 1.5;\n");
                js.append("                        font-weight: 500;\n");
                js.append("                    \">您有 <span style=\"color: #667eea; font-weight: 700;\">${cardCount}</span> 张补签卡</p>\n");
                js.append("                    <p style=\"\n");
                js.append("                        color: #546e7a;\n");
                js.append("                        margin: 0.5rem 0 0 0;\n");
                js.append("                        font-size: 0.9rem;\n");
                js.append("                    \">确定要使用一张补签第 <span style=\"color: #764ba2; font-weight: 600;\">${day}</span> 天吗？</p>\n");
                js.append("                </div>\n");
                js.append("                \n");
                js.append("                <!-- 按钮区域 -->\n");
                js.append("                <div style=\"display: flex; gap: 0.75rem; justify-content: center;\">\n");
                js.append("                    <button onclick=\"closeMakeupConfirmModal()\" style=\"\n");
                js.append("                        background: linear-gradient(135deg, #e0e7ff 0%, #f1f5f9 100%);\n");
                js.append("                        color: #64748b;\n");
                js.append("                        border: 1px solid #cbd5e1;\n");
                js.append("                        border-radius: 10px;\n");
                js.append("                        padding: 0.75rem 1.5rem;\n");
                js.append("                        font-size: 0.9rem;\n");
                js.append("                        font-weight: 600;\n");
                js.append("                        cursor: pointer;\n");
                js.append("                        transition: all 0.2s ease;\n");
                js.append("                        min-width: 80px;\n");
                js.append("                    \" onmouseover=\"this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='none'\">取消</button>\n");
                js.append("                    <button onclick=\"confirmMakeupCheckIn(${day})\" style=\"\n");
                js.append("                        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                js.append("                        color: white;\n");
                js.append("                        border: none;\n");
                js.append("                        border-radius: 10px;\n");
                js.append("                        padding: 0.75rem 1.5rem;\n");
                js.append("                        font-size: 0.9rem;\n");
                js.append("                        font-weight: 600;\n");
                js.append("                        cursor: pointer;\n");
                js.append("                        transition: all 0.2s ease;\n");
                js.append("                        min-width: 80px;\n");
                js.append("                        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n");
                js.append("                    \" onmouseover=\"this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(102, 126, 234, 0.4)'\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(102, 126, 234, 0.3)'\">确定</button>\n");
                js.append("                </div>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    document.body.appendChild(modal);\n");
                js.append("    \n");
                js.append("    // 显示动画\n");
                js.append("    setTimeout(() => {\n");
                js.append("        modal.style.opacity = '1';\n");
                js.append("        const content = modal.querySelector('div');\n");
                js.append("        content.style.transform = 'scale(1)';\n");
                js.append("    }, 10);\n");
                js.append("    \n");
                js.append("    // 点击背景关闭\n");
                js.append("    modal.addEventListener('click', (e) => {\n");
                js.append("        if (e.target === modal) {\n");
                js.append("            closeMakeupConfirmModal();\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    // 存储模态框引用\n");
                js.append("    window.currentMakeupConfirmModal = modal;\n");
                js.append("}\n\n");

                js.append("// 关闭补签确认模态框\n");
                js.append("function closeMakeupConfirmModal() {\n");
                js.append("    const modal = window.currentMakeupConfirmModal;\n");
                js.append("    if (modal) {\n");
                js.append("        modal.style.opacity = '0';\n");
                js.append("        const content = modal.querySelector('div');\n");
                js.append("        content.style.transform = 'scale(0.8)';\n");
                js.append("        setTimeout(() => {\n");
                js.append("            if (document.body.contains(modal)) {\n");
                js.append("                document.body.removeChild(modal);\n");
                js.append("            }\n");
                js.append("            window.currentMakeupConfirmModal = null;\n");
                js.append("        }, 300);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 确认补签\n");
                js.append("function confirmMakeupCheckIn(day) {\n");
                js.append("    closeMakeupConfirmModal();\n");
                js.append("    performMakeupCheckIn(day);\n");
                js.append("}\n\n");

                js.append("// 显示补签卡购买提示\n");
                js.append("function showMakeupCardPurchasePrompt(day) {\n");
                js.append("    const modal = document.createElement('div');\n");
                js.append("    modal.className = 'makeup-prompt-modal';\n");
                js.append("    modal.style.cssText = `\n");
                js.append("        position: fixed;\n");
                js.append("        top: 0;\n");
                js.append("        left: 0;\n");
                js.append("        width: 100%;\n");
                js.append("        height: 100%;\n");
                js.append("        background: rgba(0, 0, 0, 0.5);\n");
                js.append("        display: flex;\n");
                js.append("        justify-content: center;\n");
                js.append("        align-items: center;\n");
                js.append("        z-index: 10001;\n");
                js.append("        backdrop-filter: blur(5px);\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    modal.innerHTML = `\n");
                js.append("        <div style=\"\n");
                js.append("            background: white;\n");
                js.append("            border-radius: 15px;\n");
                js.append("            padding: 2rem;\n");
                js.append("            max-width: 400px;\n");
                js.append("            text-align: center;\n");
                js.append("            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n");
                js.append("        \">\n");
                js.append("            <div style=\"font-size: 3rem; margin-bottom: 1rem;\">🎫</div>\n");
                js.append("            <h3 style=\"color: #333; margin-bottom: 1rem;\">需要补签卡</h3>\n");
                js.append(
                                "            <p style=\"color: #666; margin-bottom: 1.5rem;\">您没有补签卡，无法补签第 ${day} 天。<br>请先购买补签卡。</p>\n");
                js.append("            <div style=\"display: flex; gap: 1rem; justify-content: center;\">\n");
                js.append("                <button onclick=\"closeMakeupPrompt()\" style=\"\n");
                js.append("                    padding: 0.75rem 1.5rem;\n");
                js.append("                    border: none;\n");
                js.append("                    border-radius: 8px;\n");
                js.append("                    background: #6b7280;\n");
                js.append("                    color: white;\n");
                js.append("                    cursor: pointer;\n");
                js.append("                    font-weight: 600;\n");
                js.append("                \">取消</button>\n");
                js.append("                <button onclick=\"goToPurchase()\" style=\"\n");
                js.append("                    padding: 0.75rem 1.5rem;\n");
                js.append("                    border: none;\n");
                js.append("                    border-radius: 8px;\n");
                js.append("                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n");
                js.append("                    color: white;\n");
                js.append("                    cursor: pointer;\n");
                js.append("                    font-weight: 600;\n");
                js.append("                \">去购买</button>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    document.body.appendChild(modal);\n");
                js.append("    \n");
                js.append("    // 点击背景关闭\n");
                js.append("    modal.addEventListener('click', (e) => {\n");
                js.append("        if (e.target === modal) {\n");
                js.append("            closeMakeupPrompt();\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 关闭补签提示\n");
                js.append("function closeMakeupPrompt() {\n");
                js.append("    const modal = document.querySelector('.makeup-prompt-modal');\n");
                js.append("    if (modal) {\n");
                js.append("        modal.remove();\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 前往购买页面\n");
                js.append("function goToPurchase() {\n");
                js.append("    closeMakeupPrompt();\n");
                js.append("    // 滚动到补签卡商店区域\n");
                js.append("    const shopSection = document.querySelector('.makeup-cards-section');\n");
                js.append("    if (shopSection) {\n");
                js.append("        shopSection.scrollIntoView({ behavior: 'smooth', block: 'center' });\n");
                js.append("        // 高亮显示商店区域\n");
                js.append("        shopSection.style.animation = 'highlight 2s ease-in-out';\n");
                js.append("        setTimeout(() => {\n");
                js.append("            shopSection.style.animation = '';\n");
                js.append("        }, 2000);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 执行补签\n");
                js.append("async function performMakeupCheckIn(day) {\n");
                js.append("    try {\n");
                js.append("        const response = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'makeup_checkin',\n");
                js.append("                player: currentUsername,\n");
                js.append("                day: day\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        const data = await response.json();\n");
                js.append("        if (data.success) {\n");
                js.append("            showMakeupSuccessModal(data.message || '补签成功！');\n");
                js.append("            // 刷新页面数据\n");
                js.append("            if (window.checkinSystem) {\n");
                js.append("                await window.checkinSystem.loadCheckinData();\n");
                js.append("                // 检查玩家状态（这会更新状态卡片并生成签到卡片）\n");
                js.append("                window.checkinSystem.checkPlayerStatus();\n");
                js.append("            }\n");
                js.append("            // 更新积分显示\n");
                js.append("            loadUserPoints();\n");
                js.append("        } else {\n");
                js.append("            showMakeupErrorModal(data.message || '补签失败');\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('补签失败:', error);\n");
                js.append("        showMakeupErrorModal('补签失败，请稍后重试');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 加载补签卡价格\n");
                js.append("async function loadMakeupCardPrices() {\n");
                js.append("    try {\n");
                js.append("        console.log('开始加载补签卡价格...');\n");
                js.append("        const response = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: {\n");
                js.append("                'Content-Type': 'application/json'\n");
                js.append("            },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'get_makeup_prices'\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        console.log('API响应状态:', response.status);\n");
                js.append("        const data = await response.json();\n");
                js.append("        console.log('API响应数据:', data);\n");
                js.append("        \n");
                js.append("        if (data.success && data.prices) {\n");
                js.append("            makeupCardPrices = data.prices;\n");
                js.append("            console.log('补签卡价格已加载:', makeupCardPrices);\n");
                js.append("            \n");
                js.append("            // 生成补签卡商店\n");
                js.append("            const shopContainer = document.querySelector('.makeup-cards-grid');\n");
                js.append("            if (shopContainer) {\n");
                js.append("                let shopHTML = '';\n");
                js.append("                \n");
                js.append("                // 定义卡片配置（使用更醒目的彩色图标）\n");
                js.append("                const cardConfigs = {\n");
                js.append("                    gold: {\n");
                js.append("                        icon: '💰',  // 使用彩色emoji图标\n");
                js.append("                        iconClass: 'gold',\n");
                js.append("                        title: '金币补签卡',\n");
                js.append("                        fallbackIcon: 'fas fa-coins'  // 备用图标\n");
                js.append("                    },\n");
                js.append("                    points: {\n");
                js.append("                        icon: '💎',  // 使用彩色emoji图标\n");
                js.append("                        iconClass: 'points',\n");
                js.append("                        title: '点券补签卡',\n");
                js.append("                        fallbackIcon: 'fas fa-gem'  // 备用图标\n");
                js.append("                    },\n");
                js.append("                    score: {\n");
                js.append("                        icon: '⭐',  // 使用彩色emoji图标\n");
                js.append("                        iconClass: 'score',\n");
                js.append("                        title: '积分补签卡',\n");
                js.append("                        fallbackIcon: 'fas fa-star'  // 备用图标\n");
                js.append("                    },\n");
                js.append("                    custom: {\n");
                js.append("                        icon: '⚙️',  // 使用彩色emoji图标\n");
                js.append("                        iconClass: 'custom',\n");
                js.append("                        title: '自定义补签卡',\n");
                js.append("                        fallbackIcon: 'fas fa-cog'  // 备用图标\n");
                js.append("                    }\n");
                js.append("                };\n");
                js.append("                \n");
                js.append("                // 遍历所有可用的支付方式\n");
                js.append("                for (const [type, priceInfo] of Object.entries(data.prices)) {\n");
                js.append("                    const config = cardConfigs[type];\n");
                js.append("                    if (config) {\n");
                js.append("                        shopHTML += `\n");
                js.append("                            <div class=\"makeup-card ${config.iconClass}-card\" data-type=\"${type}\">\n");
                js.append("                                <div class=\"card-icon\">\n");
                js.append("                                    <span class=\"emoji-icon\">${config.icon}</span>\n");
                js.append("                                </div>\n");
                js.append("                                <div class=\"card-content\">\n");
                js.append("                                    <div class=\"card-title\">${priceInfo.currency_name || config.title}</div>\n");
                js.append("                                    <div class=\"card-price\">${priceInfo.price} ${priceInfo.currency_name}</div>\n");
                js.append("                                </div>\n");
                js.append("                                <div class=\"card-actions\">\n");
                js.append("                                    <button class=\"buy-btn ${config.iconClass}-btn\" onclick=\"buyMakeupCard('${type}')\">\n");
                js.append("                                        🛒 购买\n");
                js.append("                                    </button>\n");
                js.append("                                </div>\n");
                js.append("                            </div>\n");
                js.append("                        `;\n");
                js.append("                    }\n");
                js.append("                }\n");
                js.append("                \n");
                js.append("                shopContainer.innerHTML = shopHTML;\n");
                js.append("            } else {\n");
                js.append("                console.error('找不到补签卡商店容器');\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            console.error('加载补签卡价格失败:', data.message || '未知错误');\n");
                js.append("            const shopContainer = document.querySelector('.makeup-cards-grid');\n");
                js.append("            if (shopContainer) {\n");
                js.append(
                                "                shopContainer.innerHTML = '<div style=\"text-align: center; padding: 2rem; color: #666;\">无法加载补签卡价格，请刷新页面重试</div>';\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('加载补签卡价格失败:', error);\n");
                js.append("        const shopContainer = document.querySelector('.makeup-cards-grid');\n");
                js.append("        if (shopContainer) {\n");
                js.append(
                                "            shopContainer.innerHTML = '<div style=\"text-align: center; padding: 2rem; color: #666;\">网络错误，请刷新页面重试</div>';\n");
                js.append("        }\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 初始化系统\n");
                js.append("document.addEventListener('DOMContentLoaded', () => {\n");
                js.append("    // 强制隐藏所有弹窗\n");
                js.append("    hideAllModals();\n");
                js.append("    \n");
                js.append("    sessionId = generateSessionId();\n");
                js.append("    window.checkinSystem = new CheckInSystem();\n");
                js.append("    // 显示日期\n");
                js.append("    window.checkinSystem.updateDateDisplay();\n");
                js.append("    // 显示加载状态的卡片\n");
                js.append("    showLoadingCards();\n");
                js.append("    // 检查绑定状态（这会在完成后生成正确的卡片）\n");
                js.append("    checkBindStatus();\n");
                js.append("    // 加载补签卡价格\n");
                js.append("    loadMakeupCardPrices();\n");
                js.append("});\n\n");

                js.append("// 强制隐藏所有弹窗\n");
                js.append("function hideAllModals() {\n");
                js.append("    console.log('强制隐藏所有弹窗');\n");
                js.append("    \n");
                js.append("    // 隐藏购买弹窗\n");
                js.append("    const purchaseModal = document.getElementById('purchaseModal');\n");
                js.append("    if (purchaseModal) {\n");
                js.append("        purchaseModal.style.display = 'none';\n");
                js.append("        purchaseModal.style.visibility = 'hidden';\n");
                js.append("        console.log('隐藏购买弹窗');\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 隐藏绑定弹窗\n");
                js.append("    const bindModal = document.getElementById('bindModal');\n");
                js.append("    if (bindModal) {\n");
                js.append("        bindModal.style.display = 'none !important';\n");
                js.append("        bindModal.style.visibility = 'hidden';\n");
                js.append("        console.log('隐藏绑定弹窗');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 显示加载状态的卡片\n");
                js.append("function showLoadingCards() {\n");
                js.append("    const grid = document.getElementById('checkinCardsGrid');\n");
                js.append("    if (!grid) return;\n");
                js.append("    \n");
                js.append("    let html = '';\n");
                js.append("    for (let day = 1; day <= 31; day++) {\n");
                js.append("        html += `\n");
                js.append("            <div class=\"checkin-card loading-card\">\n");
                js.append("                <div class=\"card-header\">\n");
                js.append("                    <div class=\"day-number\">${day}</div>\n");
                js.append("                    <div class=\"day-suffix\">日</div>\n");
                js.append("                </div>\n");
                js.append("                <div class=\"card-body\">\n");
                js.append("                    <div class=\"loading-spinner\">⏳</div>\n");
                js.append("                    <div class=\"loading-text\">加载中...</div>\n");
                js.append("                </div>\n");
                js.append("            </div>\n");
                js.append("        `;\n");
                js.append("    }\n");
                js.append("    grid.innerHTML = html;\n");
                js.append("}\n\n");

                js.append("// 生成会话ID\n");
                js.append("function generateSessionId() {\n");
                js.append("    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n");
                js.append("}\n\n");

                js.append("// 检查绑定状态\n");
                js.append("function checkBindStatus() {\n");
                js.append("    console.log('检查绑定状态...');\n");
                js.append("    const savedBinding = localStorage.getItem('player_binding');\n");
                js.append("    console.log('本地绑定信息:', savedBinding);\n");
                js.append("    \n");
                js.append("    if (savedBinding) {\n");
                js.append("        try {\n");
                js.append("            const binding = JSON.parse(savedBinding);\n");
                js.append("            if (binding.username) {\n");
                js.append("                console.log('验证用户绑定状态:', binding.username);\n");
                js.append("                // 直接验证服务器端绑定状态，不检查本地时间\n");
                js.append("                verifyServerBinding(binding.username);\n");
                js.append("                return;\n");
                js.append("            }\n");
                js.append("        } catch (e) {\n");
                js.append("            console.error('解析绑定数据失败:', e);\n");
                js.append("            localStorage.removeItem('player_binding');\n");
                js.append("        }\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    console.log('显示绑定界面');\n");
                js.append("    showBindInterface();\n");
                js.append("}\n\n");

                js.append("// 验证服务器端绑定状态\n");
                js.append("function verifyServerBinding(username) {\n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'verify_binding',\n");
                js.append("            api_key: 'MCTV_KEY_2024_SECURE',\n");
                js.append("            username: username\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        console.log('服务器验证结果:', data);\n");
                js.append("        if (data.success && data.is_bound) {\n");
                js.append("            currentUsername = username;\n");
                js.append("            console.log('设置currentUsername为:', currentUsername);\n");
                js.append("            \n");
                js.append("            // 更新本地存储的绑定信息\n");
                js.append("            if (data.bind_time) {\n");
                js.append("                const bindingInfo = {\n");
                js.append("                    username: username,\n");
                js.append("                    bind_time: data.bind_time,\n");
                js.append("                    player_uuid: data.player_uuid\n");
                js.append("                };\n");
                js.append("                localStorage.setItem('player_binding', JSON.stringify(bindingInfo));\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 确保签到系统正确设置当前玩家\n");
                js.append("            if (window.checkinSystem) {\n");
                js.append("                window.checkinSystem.currentPlayer = username;\n");
                js.append("                console.log('设置当前玩家:', username);\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            showBoundInterface(data);\n");
                js.append("            // 加载签到数据并重新生成卡片\n");
                js.append("            window.checkinSystem.loadCheckinData();\n");
                js.append("            // 检查玩家状态（这会更新状态卡片并生成签到卡片）\n");
                js.append("            window.checkinSystem.checkPlayerStatus();\n");
                js.append("            loadAllCurrency(); // 加载所有货币信息\n");
                js.append("        } else {\n");
                js.append("            console.log('绑定验证失败或未绑定');\n");
                js.append("            localStorage.removeItem('player_binding');\n");
                js.append("            showBindInterface();\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('验证绑定状态失败:', error);\n");
                js.append("        showBindInterface();\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 显示绑定界面\n");
                js.append("function showBindInterface() {\n");
                js.append("    const bindStatus = document.getElementById('bindStatus');\n");
                js.append("    if (!bindStatus) return;\n");
                js.append("    \n");
                js.append("    bindStatus.innerHTML = `\n");
                js.append("        <div class=\"bind-info\">\n");
                js.append("            <p>请先绑定您的游戏账户</p>\n");
                js.append(
                                "            <button class=\"action-btn bind-btn\" onclick=\"generateBindCode()\">🎯 生成绑定码</button>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    // 隐藏积分区域（状态区域已移除）\n");
                js.append("    const pointsSection = document.getElementById('pointsSection');\n");
                js.append("    if (pointsSection) pointsSection.style.display = 'none';\n");
                js.append("    \n");
                js.append("    // 生成显示绑定提示的卡片\n");
                js.append("    if (window.checkinSystem) {\n");
                js.append("        window.checkinSystem.currentPlayer = null;\n");
                js.append("        window.checkinSystem.generateCheckinCards();\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 显示已绑定界面\n");
                js.append("function showBoundInterface(bindingData) {\n");
                js.append("    const bindStatus = document.getElementById('bindStatus');\n");
                js.append("    if (!bindStatus) return;\n");
                js.append("    \n");
                js.append("    // 计算绑定天数\n");
                js.append("    let bindDays = 0;\n");
                js.append("    if (bindingData.bind_time) {\n");
                js.append("        const bindTime = new Date(bindingData.bind_time);\n");
                js.append("        bindDays = Math.floor((Date.now() - bindTime.getTime()) / (1000 * 60 * 60 * 24));\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 更新绑定状态卡片\n");
                js.append("    bindStatus.innerHTML = `\n");
                js.append("        <div class=\"bind-info bind-success\">\n");
                js.append("            <p style=\"color: #10b981; font-weight: bold; margin-bottom: 0.5rem;\">✅ 已绑定</p>\n");
                js.append(
                                "            <p style=\"color: #666; font-size: 0.85rem; margin-bottom: 1rem;\">${bindingData.username} (${bindDays}天)</p>\n");
                js.append("            <div class=\"bind-actions\" id=\"bindActions\">\n");
                js.append(
                                "                <button class=\"action-btn unbind-btn\" onclick=\"unbindAccount()\" style=\"background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; font-size: 0.8rem; padding: 0.5rem 1rem;\">🔓 解绑</button>\n");
                js.append("            </div>\n");
                js.append(
                                "            <div class=\"unbind-timer\" id=\"unbindTimer\" style=\"display: none; color: #f59e0b; font-size: 0.8rem; margin-top: 0.5rem;\"></div>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    // 显示货币区域和状态区域\n");
                js.append("    const currencySection = document.getElementById('currencySection');\n");
                js.append("    const statusArea = document.getElementById('statusArea');\n");
                js.append("    if (currencySection) currencySection.style.display = 'block';\n");
                js.append("    if (statusArea) statusArea.style.display = 'block';\n");
                js.append("    \n");
                js.append("    // 检查解绑时间限制\n");
                js.append("    if (bindingData.bind_time) {\n");
                js.append("        checkUnbindTimeLimit(bindingData.bind_time);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 加载所有货币信息\n");
                js.append("    loadAllCurrency();\n");
                js.append("}\n\n");

                js.append("// 生成绑定码\n");
                js.append("function generateBindCode() {\n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'generate_bind_code',\n");
                js.append("            api_key: 'MCTV_KEY_2024_SECURE',\n");
                js.append("            session_id: sessionId\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            currentBindCode = data.bind_code;\n");
                js.append("            showBindModal(data.bind_code);\n");
                js.append("            startBindCheck();\n");
                js.append("        } else {\n");
                js.append("            alert('生成绑定码失败: ' + data.message);\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('生成绑定码失败:', error);\n");
                js.append("        alert('生成绑定码失败，请稍后重试');\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 显示绑定弹窗\n");
                js.append("function showBindModal(bindCode) {\n");
                js.append("    document.getElementById('bindCodeInput').value = bindCode;\n");
                js.append("    document.getElementById('bindCodeDisplay').textContent = bindCode;\n");
                js.append("    document.getElementById('bindModal').style.display = 'flex';\n");
                js.append("    document.getElementById('bindProgressBar').style.width = '0%';\n");
                js.append("    document.getElementById('bindStatusText').textContent = '等待绑定...';\n");
                js.append("}\n\n");

                js.append("// 关闭绑定弹窗\n");
                js.append("function closeBindModal() {\n");
                js.append("    document.getElementById('bindModal').style.display = 'none';\n");
                js.append("    if (bindCheckInterval) {\n");
                js.append("        clearInterval(bindCheckInterval);\n");
                js.append("        bindCheckInterval = null;\n");
                js.append("    }\n");
                js.append("    if (progressInterval) {\n");
                js.append("        clearInterval(progressInterval);\n");
                js.append("        progressInterval = null;\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 复制绑定码\n");
                js.append("function copyBindCode(event) {\n");
                js.append("    const input = document.getElementById('bindCodeInput');\n");
                js.append("    input.select();\n");
                js.append("    document.execCommand('copy');\n");
                js.append("    \n");
                js.append("    const btn = event.target;\n");
                js.append("    const originalText = btn.textContent;\n");
                js.append("    btn.textContent = '✅ 已复制';\n");
                js.append("    btn.style.background = '#10b981';\n");
                js.append("    \n");
                js.append("    setTimeout(() => {\n");
                js.append("        btn.textContent = originalText;\n");
                js.append("        btn.style.background = '';\n");
                js.append("    }, 2000);\n");
                js.append("}\n\n");

                js.append("// 复制命令\n");
                js.append("function copyCommand(event) {\n");
                js.append("    const command = '/acebind ' + currentBindCode;\n");
                js.append("    navigator.clipboard.writeText(command).then(() => {\n");
                js.append("        const btn = event.target;\n");
                js.append("        const originalText = btn.textContent;\n");
                js.append("        btn.textContent = '✅ 已复制';\n");
                js.append("        btn.style.background = '#10b981';\n");
                js.append("        \n");
                js.append("        setTimeout(() => {\n");
                js.append("            btn.textContent = originalText;\n");
                js.append("            btn.style.background = '';\n");
                js.append("        }, 2000);\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 开始检查绑定状态\n");
                js.append("function startBindCheck() {\n");
                js.append("    let progress = 0;\n");
                js.append("    const maxTime = 5 * 60 * 1000; // 5分钟\n");
                js.append("    const checkInterval = 3000; // 3秒检查一次\n");
                js.append("    const progressStep = (checkInterval / maxTime) * 100;\n");
                js.append("    \n");
                js.append("    // 进度条动画\n");
                js.append("    progressInterval = setInterval(() => {\n");
                js.append("        progress += progressStep;\n");
                js.append("        if (progress >= 100) {\n");
                js.append("            progress = 100;\n");
                js.append("            clearInterval(progressInterval);\n");
                js.append("            document.getElementById('bindStatusText').textContent = '绑定码已过期';\n");
                js.append("        }\n");
                js.append("        document.getElementById('bindProgressBar').style.width = progress + '%';\n");
                js.append("    }, 100);\n");
                js.append("    \n");
                js.append("    // 检查绑定状态\n");
                js.append("    bindCheckInterval = setInterval(() => {\n");
                js.append("        fetch('/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'get_bind_status',\n");
                js.append("                api_key: 'MCTV_KEY_2024_SECURE',\n");
                js.append("                session_id: sessionId\n");
                js.append("            })\n");
                js.append("        })\n");
                js.append("        .then(response => response.json())\n");
                js.append("        .then(data => {\n");
                js.append("            if (data.success && data.is_bound) {\n");
                js.append("                // 绑定成功\n");
                js.append("                clearInterval(bindCheckInterval);\n");
                js.append("                clearInterval(progressInterval);\n");
                js.append("                \n");
                js.append("                document.getElementById('bindStatusText').textContent = '绑定成功！';\n");
                js.append("                document.getElementById('bindProgressBar').style.width = '100%';\n");
                js.append("                document.getElementById('bindProgressBar').style.background = '#10b981';\n");
                js.append("                \n");
                js.append("                // 保存绑定信息到本地存储\n");
                js.append("                const bindingInfo = {\n");
                js.append("                    username: data.username,\n");
                js.append("                    bind_time: Date.now(),\n");
                js.append("                    player_uuid: data.player_uuid\n");
                js.append("                };\n");
                js.append("                localStorage.setItem('player_binding', JSON.stringify(bindingInfo));\n");
                js.append("                \n");
                js.append("                setTimeout(() => {\n");
                js.append("                    closeBindModal();\n");
                js.append("                    currentUsername = data.username;\n");
                js.append("                    showBoundInterface(data);\n");
                js.append("                    window.checkinSystem.currentPlayer = data.username;\n");
                js.append("                    // 加载签到数据并检查玩家状态\n");
                js.append("                    window.checkinSystem.loadCheckinData();\n");
                js.append("                    window.checkinSystem.checkPlayerStatus();\n");
                js.append("                }, 2000);\n");
                js.append("            }\n");
                js.append("        })\n");
                js.append("        .catch(error => {\n");
                js.append("            console.error('检查绑定状态失败:', error);\n");
                js.append("        });\n");
                js.append("    }, checkInterval);\n");
                js.append("}\n\n");

                js.append("// 加载用户积分\n");
                js.append("function loadUserPoints() {\n");
                js.append("    if (!currentUsername) return;\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_user_points',\n");
                js.append("            api_key: 'MCTV_KEY_2024_SECURE',\n");
                js.append("            username: currentUsername\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            userPoints = data.points || 0;\n");
                js.append("            document.getElementById('userPoints').textContent = userPoints.toLocaleString();\n");
                js.append("        } else {\n");
                js.append("            document.getElementById('userPoints').textContent = '获取失败';\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('加载积分失败:', error);\n");
                js.append("        document.getElementById('userPoints').textContent = '加载失败';\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 加载补签卡数量\n");
                js.append("function loadMakeupCards() {\n");
                js.append("    if (!currentUsername) return;\n");
                js.append("    \n");
                js.append("    fetch('/checkin/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_makeup_cards',\n");
                js.append("            player: currentUsername\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            const cardCount = data.card_count || 0;\n");
                js.append("            document.getElementById('userMakeupCards').textContent = cardCount + ' 张';\n");
                js.append("        } else {\n");
                js.append("            document.getElementById('userMakeupCards').textContent = '获取失败';\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('加载补签卡失败:', error);\n");
                js.append("        document.getElementById('userMakeupCards').textContent = '加载失败';\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 加载金币余额\n");
                js.append("function loadGoldBalance() {\n");
                js.append("    if (!currentUsername) return;\n");
                js.append("    \n");
                js.append("    // 先尝试使用购买弹窗中已有的余额获取逻辑\n");
                js.append("    fetch('/checkin/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_user_balance',\n");
                js.append("            player: currentUsername\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        console.log('金币余额API返回:', data);\n");
                js.append("        if (data.success && data.balance !== undefined) {\n");
                js.append("            const balance = data.balance || 0;\n");
                js.append("            document.getElementById('userGold').textContent = Math.floor(balance).toLocaleString();\n");
                js.append("        } else {\n");
                js.append("            // 如果失败，尝试从购买弹窗的余额获取逻辑\n");
                js.append("            document.getElementById('userGold').textContent = '0';\n");
                js.append("            console.log('金币余额获取失败，使用默认值0');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('加载金币失败:', error);\n");
                js.append("        document.getElementById('userGold').textContent = '0';\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 加载点券余额\n");
                js.append("function loadTokensBalance() {\n");
                js.append("    if (!currentUsername) return;\n");
                js.append("    \n");
                js.append("    // 暂时显示0，等待后端实现点券余额获取\n");
                js.append("    document.getElementById('userTokens').textContent = '0';\n");
                js.append("    \n");
                js.append("    // TODO: 实现点券余额获取\n");
                js.append("    // fetch('/checkin/api', {\n");
                js.append("    //     method: 'POST',\n");
                js.append("    //     headers: { 'Content-Type': 'application/json' },\n");
                js.append("    //     body: JSON.stringify({\n");
                js.append("    //         action: 'get_tokens_balance',\n");
                js.append("    //         player: currentUsername\n");
                js.append("    //     })\n");
                js.append("    // })\n");
                js.append("    // .then(response => response.json())\n");
                js.append("    // .then(data => {\n");
                js.append("    //     if (data.success) {\n");
                js.append("    //         const balance = data.balance || 0;\n");
                js.append("    //         document.getElementById('userTokens').textContent = Math.floor(balance).toLocaleString();\n");
                js.append("    //     } else {\n");
                js.append("    //         document.getElementById('userTokens').textContent = '0';\n");
                js.append("    //     }\n");
                js.append("    // })\n");
                js.append("    // .catch(error => {\n");
                js.append("    //     console.error('加载点券失败:', error);\n");
                js.append("    //     document.getElementById('userTokens').textContent = '0';\n");
                js.append("    // });\n");
                js.append("}\n\n");

                js.append("// 加载所有货币信息\n");
                js.append("function loadAllCurrency() {\n");
                js.append("    if (!currentUsername) return;\n");
                js.append("    \n");
                js.append("    // 从补签卡商店设置中获取货币配置并加载余额\n");
                js.append("    loadCurrencyFromConfig();\n");
                js.append("    // 只调用一次补签卡数量更新，避免重复更新\n");
                js.append("    loadMakeupCards();\n");
                js.append("}\n\n");

                js.append("// 从补签卡商店设置加载货币配置和余额\n");
                js.append("function loadCurrencyFromConfig() {\n");
                js.append("    if (!currentUsername) return;\n");
                js.append("    \n");
                js.append("    // 获取补签卡价格配置\n");
                js.append("    fetch('/checkin/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_makeup_prices',\n");
                js.append("            player: currentUsername\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        console.log('获取到补签卡价格配置:', data);\n");
                js.append("        if (data.success && data.prices) {\n");
                js.append("            // 更新货币显示名称\n");
                js.append("            updateCurrencyLabels(data.prices);\n");
                js.append("            // 加载各种货币余额\n");
                js.append("            loadBalancesFromPrices(data.prices);\n");
                js.append("        } else {\n");
                js.append("            console.error('获取补签卡价格配置失败');\n");
                js.append("            // 使用默认方式加载\n");
                js.append("            loadDefaultCurrencies();\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('加载货币配置失败:', error);\n");
                js.append("        // 使用默认方式加载\n");
                js.append("        loadDefaultCurrencies();\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 更新货币标签名称\n");
                js.append("function updateCurrencyLabels(prices) {\n");
                js.append("    try {\n");
                js.append("        // 更新积分标签（始终显示）\n");
                js.append("        if (prices.score) {\n");
                js.append("            const pointsLabel = document.querySelector('#userPoints').parentElement.querySelector('.currency-label');\n");
                js.append("            if (pointsLabel) pointsLabel.textContent = prices.score.currency_name || '积分';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 更新金币标签（始终显示）\n");
                js.append("        if (prices.gold) {\n");
                js.append("            const goldLabel = document.querySelector('#userGold').parentElement.querySelector('.currency-label');\n");
                js.append("            if (goldLabel) goldLabel.textContent = prices.gold.currency_name || '金币';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 更新点券标签（始终显示）\n");
                js.append("        if (prices.points) {\n");
                js.append("            const tokensLabel = document.querySelector('#userTokens').parentElement.querySelector('.currency-label');\n");
                js.append("            if (tokensLabel) tokensLabel.textContent = prices.points.currency_name || '点券';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        console.log('货币标签更新完成');\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('更新货币标签失败:', error);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 根据价格配置加载余额\n");
                js.append("function loadBalancesFromPrices(prices) {\n");
                js.append("    // 更新货币显示名称\n");
                js.append("    updateCurrencyLabels(prices);\n");
                js.append("    \n");
                js.append("    // 始终加载积分余额（不管是否在补签卡商店中启用）\n");
                js.append("    loadUserPoints();\n");
                js.append("    \n");
                js.append("    // 始终尝试加载金币余额\n");
                js.append("    if (prices.gold && prices.gold.balance_variable) {\n");
                js.append("        loadBalanceByVariable('gold', prices.gold.balance_variable, 'userGold');\n");
                js.append("    } else {\n");
                js.append("        // 使用默认的Vault经济系统变量\n");
                js.append("        loadBalanceByVariable('gold', '%vault_eco_balance%', 'userGold');\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 始终尝试加载点券余额\n");
                js.append("    if (prices.points && prices.points.balance_variable) {\n");
                js.append("        loadBalanceByVariable('points', prices.points.balance_variable, 'userTokens');\n");
                js.append("    } else {\n");
                js.append("        // 使用默认的PlayerPoints变量\n");
                js.append("        loadBalanceByVariable('points', '%playerpoints_points%', 'userTokens');\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 使用默认配置加载货币\n");
                js.append("function loadDefaultCurrencies() {\n");
                js.append("    console.log('使用默认配置加载货币');\n");
                js.append("    \n");
                js.append("    // 加载积分\n");
                js.append("    loadUserPoints();\n");
                js.append("    \n");
                js.append("    // 加载金币（使用Vault默认变量）\n");
                js.append("    loadBalanceByVariable('gold', '%vault_eco_balance%', 'userGold');\n");
                js.append("    \n");
                js.append("    // 加载点券（使用PlayerPoints默认变量）\n");
                js.append("    loadBalanceByVariable('points', '%playerpoints_points%', 'userTokens');\n");
                js.append("}\n\n");

                js.append("// 根据变量加载余额\n");
                js.append("function loadBalanceByVariable(type, balanceVariable, elementId) {\n");
                js.append("    if (!currentUsername || !balanceVariable) {\n");
                js.append("        document.getElementById(elementId).textContent = '0';\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    console.log(`加载${type}余额，变量: ${balanceVariable}`);\n");
                js.append("    \n");
                js.append("    fetch('/checkin/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_user_balance',\n");
                js.append("            player: currentUsername,\n");
                js.append("            currency_type: type,\n");
                js.append("            balance_variable: balanceVariable\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        console.log(`${type}余额API返回:`, data);\n");
                js.append("        if (data.success && data.balance !== undefined) {\n");
                js.append("            const balance = data.balance || 0;\n");
                js.append("            document.getElementById(elementId).textContent = Math.floor(balance).toLocaleString();\n");
                js.append("        } else {\n");
                js.append("            document.getElementById(elementId).textContent = '0';\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error(`加载${type}余额失败:`, error);\n");
                js.append("        document.getElementById(elementId).textContent = '0';\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 防抖变量\n");
                js.append("let refreshTimeout = null;\n\n");

                js.append("// 刷新所有货币（带防抖）\n");
                js.append("function refreshAllCurrency() {\n");
                js.append("    if (!currentUsername) {\n");
                js.append("        alert('请先绑定账户');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 防抖：如果在300ms内重复调用，取消之前的调用\n");
                js.append("    if (refreshTimeout) {\n");
                js.append("        clearTimeout(refreshTimeout);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    refreshTimeout = setTimeout(() => {\n");
                js.append("        console.log('执行货币刷新');\n");
                js.append("        loadAllCurrency();\n");
                js.append("        refreshTimeout = null;\n");
                js.append("    }, 100); // 100ms延迟，避免快速连续点击\n");
                js.append("}\n\n");

                js.append("// 刷新积分（保持兼容性）\n");
                js.append("function refreshPoints() {\n");
                js.append("    refreshAllCurrency();\n");
                js.append("}\n\n");

                js.append("// 更新补签卡数量显示\n");
                js.append("async function updateMakeupCardCount() {\n");
                js.append("    if (!currentUsername) {\n");
                js.append("        console.log('用户未绑定，无法更新补签卡数量');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    try {\n");
                js.append("        const response = await fetch('/checkin/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'get_makeup_cards',\n");
                js.append("                player: currentUsername\n");
                js.append("            })\n");
                js.append("        });\n");
                js.append("        \n");
                js.append("        const data = await response.json();\n");
                js.append("        if (data.success) {\n");
                js.append("            const cardCount = data.card_count || 0;\n");
                js.append("            \n");
                js.append("            // 更新补签卡显示（无动画，避免视觉跳动）\n");
                js.append("            const makeupCardElement = document.getElementById('userMakeupCards');\n");
                js.append("            if (makeupCardElement) {\n");
                js.append("                makeupCardElement.textContent = cardCount + ' 张';\n");
                js.append("                console.log('补签卡数量已更新:', cardCount);\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            console.error('获取补签卡数量失败:', data.message);\n");
                js.append("        }\n");
                js.append("    } catch (error) {\n");
                js.append("        console.error('更新补签卡数量失败:', error);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 解除绑定\n");
                js.append("function unbindAccount() {\n");
                js.append("    if (!currentUsername) {\n");
                js.append("        alert('没有绑定的账户');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    if (!confirm('确定要解除绑定吗？解除绑定后需要重新绑定才能使用签到功能。')) {\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'unbind_account',\n");
                js.append("            api_key: 'MCTV_KEY_2024_SECURE',\n");
                js.append("            username: currentUsername\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            // 清除本地存储\n");
                js.append("            localStorage.removeItem('player_binding');\n");
                js.append("            currentUsername = '';\n");
                js.append("            userPoints = 0;\n");
                js.append("            \n");
                js.append("            // 显示解绑成功消息\n");
                js.append("            alert('解绑成功！');\n");
                js.append("            \n");
                js.append("            // 重新显示绑定界面\n");
                js.append("            showBindInterface();\n");
                js.append("            \n");
                js.append("            // 重新生成签到卡片（清除用户数据）\n");
                js.append("            window.checkinSystem.currentPlayer = null;\n");
                js.append("            window.checkinSystem.generateCheckinCards(); // 重新生成卡片显示需要绑定状态\n");
                js.append("        } else {\n");
                js.append("            alert('解绑失败: ' + (data.message || '未知错误'));\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        console.error('解绑失败:', error);\n");
                js.append("        alert('解绑失败，请稍后重试');\n");
                js.append("    });\n");
                js.append("}\n\n");

                js.append("// 检查解绑时间限制\n");
                js.append("function checkUnbindTimeLimit(bindTime) {\n");
                js.append("    const bindDate = new Date(bindTime);\n");
                js.append("    const now = new Date();\n");
                js.append("    const timeDiff = now - bindDate;\n");
                js.append("    const hoursDiff = timeDiff / (1000 * 60 * 60);\n");
                js.append("    \n");
                js.append("    // 绑定后24小时内不能解绑\n");
                js.append("    const lockHours = 24;\n");
                js.append("    \n");
                js.append("    if (hoursDiff < lockHours) {\n");
                js.append("        const remainingHours = Math.ceil(lockHours - hoursDiff);\n");
                js.append("        const bindActions = document.getElementById('bindActions');\n");
                js.append("        const unbindTimer = document.getElementById('unbindTimer');\n");
                js.append("        \n");
                js.append("        if (bindActions) {\n");
                js.append("            bindActions.classList.add('disabled');\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        if (unbindTimer) {\n");
                js.append("            unbindTimer.style.display = 'block';\n");
                js.append("            unbindTimer.textContent = `解绑冷却中，还需等待 ${remainingHours} 小时`;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 设置定时器更新倒计时\n");
                js.append("        const updateTimer = () => {\n");
                js.append("            const currentTime = new Date();\n");
                js.append("            const currentHoursDiff = (currentTime - bindDate) / (1000 * 60 * 60);\n");
                js.append("            \n");
                js.append("            if (currentHoursDiff >= lockHours) {\n");
                js.append("                if (bindActions) {\n");
                js.append("                    bindActions.classList.remove('disabled');\n");
                js.append("                }\n");
                js.append("                if (unbindTimer) {\n");
                js.append("                    unbindTimer.style.display = 'none';\n");
                js.append("                }\n");
                js.append("            } else {\n");
                js.append("                const remainingHours = Math.ceil(lockHours - currentHoursDiff);\n");
                js.append("                if (unbindTimer) {\n");
                js.append("                    unbindTimer.textContent = `解绑冷却中，还需等待 ${remainingHours} 小时`;\n");
                js.append("                }\n");
                js.append("                setTimeout(updateTimer, 60000); // 每分钟更新一次\n");
                js.append("            }\n");
                js.append("        };\n");
                js.append("        \n");
                js.append("        setTimeout(updateTimer, 60000);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("// 添加动画样式\n");
                js.append("const style = document.createElement('style');\n");
                js.append("style.textContent = `\n");
                js.append("    @keyframes slideIn {\n");
                js.append("        from { transform: translateX(100%); opacity: 0; }\n");
                js.append("        to { transform: translateX(0); opacity: 1; }\n");
                js.append("    }\n");
                js.append("    @keyframes slideOut {\n");
                js.append("        from { transform: translateX(0); opacity: 1; }\n");
                js.append("        to { transform: translateX(100%); opacity: 0; }\n");
                js.append("    }\n");
                js.append("    .btn-secondary {\n");
                js.append("        background: #6c757d !important;\n");
                js.append("        cursor: not-allowed !important;\n");
                js.append("    }\n");
                js.append("`;\n");
                js.append("document.head.appendChild(style);\n\n");

                // 添加成功和错误模态框函数
                js.append("// 显示补签成功模态框\n");
                js.append("function showMakeupSuccessModal(message) {\n");
                js.append("    const modal = document.createElement('div');\n");
                js.append("    modal.className = 'makeup-success-modal';\n");
                js.append("    modal.style.cssText = `\n");
                js.append("        position: fixed;\n");
                js.append("        top: 0;\n");
                js.append("        left: 0;\n");
                js.append("        width: 100%;\n");
                js.append("        height: 100%;\n");
                js.append("        background: rgba(0, 0, 0, 0.6);\n");
                js.append("        display: flex;\n");
                js.append("        justify-content: center;\n");
                js.append("        align-items: center;\n");
                js.append("        z-index: 10002;\n");
                js.append("        backdrop-filter: blur(8px);\n");
                js.append("        opacity: 0;\n");
                js.append("        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    modal.innerHTML = `\n");
                js.append("        <div style=\"\n");
                js.append("            background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);\n");
                js.append("            border-radius: 20px;\n");
                js.append("            padding: 0;\n");
                js.append("            max-width: 420px;\n");
                js.append("            width: 90%;\n");
                js.append("            text-align: center;\n");
                js.append("            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);\n");
                js.append("            border: 1px solid rgba(16, 185, 129, 0.2);\n");
                js.append("            overflow: hidden;\n");
                js.append("            transform: scale(0.8) translateY(20px);\n");
                js.append("            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                js.append("        \">\n");
                js.append("            <div style=\"\n");
                js.append("                background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                js.append("                padding: 1.5rem;\n");
                js.append("                position: relative;\n");
                js.append("                overflow: hidden;\n");
                js.append("            \">\n");
                js.append("                <div style=\"\n");
                js.append("                    position: absolute;\n");
                js.append("                    top: -50%;\n");
                js.append("                    right: -50%;\n");
                js.append("                    width: 100%;\n");
                js.append("                    height: 100%;\n");
                js.append("                    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);\n");
                js.append("                \"></div>\n");
                js.append("                <div style=\"\n");
                js.append("                    font-size: 4rem;\n");
                js.append("                    margin-bottom: 0.5rem;\n");
                js.append("                    position: relative;\n");
                js.append("                \">✅</div>\n");
                js.append("                <h3 style=\"\n");
                js.append("                    color: white;\n");
                js.append("                    margin: 0;\n");
                js.append("                    font-size: 1.4rem;\n");
                js.append("                    font-weight: 600;\n");
                js.append("                    position: relative;\n");
                js.append("                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n");
                js.append("                \">补签成功！</h3>\n");
                js.append("            </div>\n");
                js.append("            <div style=\"padding: 2rem 1.5rem 1.5rem 1.5rem;\">\n");
                js.append("                <div style=\"\n");
                js.append("                    background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);\n");
                js.append("                    border-radius: 12px;\n");
                js.append("                    padding: 1.25rem;\n");
                js.append("                    margin-bottom: 1.5rem;\n");
                js.append("                    border: 1px solid rgba(16, 185, 129, 0.2);\n");
                js.append("                \">\n");
                js.append("                    <p style=\"\n");
                js.append("                        color: #065f46;\n");
                js.append("                        margin: 0;\n");
                js.append("                        font-size: 1rem;\n");
                js.append("                        line-height: 1.5;\n");
                js.append("                        font-weight: 500;\n");
                js.append("                    \">${message}</p>\n");
                js.append("                </div>\n");
                js.append("                <button onclick=\"closeMakeupSuccessModal()\" style=\"\n");
                js.append("                    background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                js.append("                    color: white;\n");
                js.append("                    border: none;\n");
                js.append("                    border-radius: 10px;\n");
                js.append("                    padding: 0.75rem 2rem;\n");
                js.append("                    font-size: 1rem;\n");
                js.append("                    font-weight: 600;\n");
                js.append("                    cursor: pointer;\n");
                js.append("                    transition: all 0.2s ease;\n");
                js.append("                    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n");
                js.append("                \" onmouseover=\"this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(16, 185, 129, 0.4)'\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(16, 185, 129, 0.3)'\">确定</button>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    document.body.appendChild(modal);\n");
                js.append("    \n");
                js.append("    setTimeout(() => {\n");
                js.append("        modal.style.opacity = '1';\n");
                js.append("        const content = modal.querySelector('div');\n");
                js.append("        content.style.transform = 'scale(1) translateY(0)';\n");
                js.append("    }, 10);\n");
                js.append("    \n");
                js.append("    modal.addEventListener('click', (e) => {\n");
                js.append("        if (e.target === modal) {\n");
                js.append("            closeMakeupSuccessModal();\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    window.currentMakeupSuccessModal = modal;\n");
                js.append("    \n");
                js.append("    setTimeout(() => {\n");
                js.append("        closeMakeupSuccessModal();\n");
                js.append("    }, 3000);\n");
                js.append("    \n");
                js.append("    setTimeout(() => {\n");
                js.append("        if (window.checkinSystem) {\n");
                js.append("            window.checkinSystem.loadCheckinData();\n");
                js.append("            // 检查玩家状态（这会更新状态卡片并生成签到卡片）\n");
                js.append("            window.checkinSystem.checkPlayerStatus();\n");
                js.append("        }\n");
                js.append("        // 只调用loadAllCurrency()，它会同时更新货币和补签卡数量\n");
                js.append("        loadAllCurrency();\n");
                js.append("    }, 500);\n");
                js.append("}\n\n");

                js.append("function closeMakeupSuccessModal() {\n");
                js.append("    const modal = window.currentMakeupSuccessModal;\n");
                js.append("    if (modal) {\n");
                js.append("        modal.style.opacity = '0';\n");
                js.append("        const content = modal.querySelector('div');\n");
                js.append("        content.style.transform = 'scale(0.8) translateY(20px)';\n");
                js.append("        setTimeout(() => {\n");
                js.append("            if (document.body.contains(modal)) {\n");
                js.append("                document.body.removeChild(modal);\n");
                js.append("            }\n");
                js.append("            window.currentMakeupSuccessModal = null;\n");
                js.append("        }, 300);\n");
                js.append("    }\n");
                js.append("}\n\n");

                js.append("function showMakeupErrorModal(message) {\n");
                js.append("    const modal = document.createElement('div');\n");
                js.append("    modal.className = 'makeup-error-modal';\n");
                js.append("    modal.style.cssText = `\n");
                js.append("        position: fixed;\n");
                js.append("        top: 0;\n");
                js.append("        left: 0;\n");
                js.append("        width: 100%;\n");
                js.append("        height: 100%;\n");
                js.append("        background: rgba(0, 0, 0, 0.6);\n");
                js.append("        display: flex;\n");
                js.append("        justify-content: center;\n");
                js.append("        align-items: center;\n");
                js.append("        z-index: 10002;\n");
                js.append("        backdrop-filter: blur(8px);\n");
                js.append("        opacity: 0;\n");
                js.append("        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    modal.innerHTML = `\n");
                js.append("        <div style=\"\n");
                js.append("            background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);\n");
                js.append("            border-radius: 20px;\n");
                js.append("            padding: 0;\n");
                js.append("            max-width: 420px;\n");
                js.append("            width: 90%;\n");
                js.append("            text-align: center;\n");
                js.append("            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);\n");
                js.append("            border: 1px solid rgba(239, 68, 68, 0.2);\n");
                js.append("            overflow: hidden;\n");
                js.append("            transform: scale(0.8) translateY(20px);\n");
                js.append("            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n");
                js.append("        \">\n");
                js.append("            <div style=\"\n");
                js.append("                background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n");
                js.append("                padding: 1.5rem;\n");
                js.append("                position: relative;\n");
                js.append("                overflow: hidden;\n");
                js.append("            \">\n");
                js.append("                <div style=\"\n");
                js.append("                    position: absolute;\n");
                js.append("                    top: -50%;\n");
                js.append("                    right: -50%;\n");
                js.append("                    width: 100%;\n");
                js.append("                    height: 100%;\n");
                js.append("                    background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%);\n");
                js.append("                \"></div>\n");
                js.append("                <div style=\"\n");
                js.append("                    font-size: 4rem;\n");
                js.append("                    margin-bottom: 0.5rem;\n");
                js.append("                    position: relative;\n");
                js.append("                \">❌</div>\n");
                js.append("                <h3 style=\"\n");
                js.append("                    color: white;\n");
                js.append("                    margin: 0;\n");
                js.append("                    font-size: 1.4rem;\n");
                js.append("                    font-weight: 600;\n");
                js.append("                    position: relative;\n");
                js.append("                    text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n");
                js.append("                \">补签失败</h3>\n");
                js.append("            </div>\n");
                js.append("            <div style=\"padding: 2rem 1.5rem 1.5rem 1.5rem;\">\n");
                js.append("                <div style=\"\n");
                js.append("                    background: linear-gradient(135deg, #fef2f2 0%, #fef2f2 100%);\n");
                js.append("                    border-radius: 12px;\n");
                js.append("                    padding: 1.25rem;\n");
                js.append("                    margin-bottom: 1.5rem;\n");
                js.append("                    border: 1px solid rgba(239, 68, 68, 0.2);\n");
                js.append("                \">\n");
                js.append("                    <p style=\"\n");
                js.append("                        color: #991b1b;\n");
                js.append("                        margin: 0;\n");
                js.append("                        font-size: 1rem;\n");
                js.append("                        line-height: 1.5;\n");
                js.append("                        font-weight: 500;\n");
                js.append("                    \">${message}</p>\n");
                js.append("                </div>\n");
                js.append("                <button onclick=\"closeMakeupErrorModal()\" style=\"\n");
                js.append("                    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n");
                js.append("                    color: white;\n");
                js.append("                    border: none;\n");
                js.append("                    border-radius: 10px;\n");
                js.append("                    padding: 0.75rem 2rem;\n");
                js.append("                    font-size: 1rem;\n");
                js.append("                    font-weight: 600;\n");
                js.append("                    cursor: pointer;\n");
                js.append("                    transition: all 0.2s ease;\n");
                js.append("                    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);\n");
                js.append("                \" onmouseover=\"this.style.transform='translateY(-2px)'; this.style.boxShadow='0 6px 20px rgba(239, 68, 68, 0.4)'\" onmouseout=\"this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 15px rgba(239, 68, 68, 0.3)'\">确定</button>\n");
                js.append("            </div>\n");
                js.append("        </div>\n");
                js.append("    `;\n");
                js.append("    \n");
                js.append("    document.body.appendChild(modal);\n");
                js.append("    \n");
                js.append("    setTimeout(() => {\n");
                js.append("        modal.style.opacity = '1';\n");
                js.append("        const content = modal.querySelector('div');\n");
                js.append("        content.style.transform = 'scale(1) translateY(0)';\n");
                js.append("    }, 10);\n");
                js.append("    \n");
                js.append("    modal.addEventListener('click', (e) => {\n");
                js.append("        if (e.target === modal) {\n");
                js.append("            closeMakeupErrorModal();\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    window.currentMakeupErrorModal = modal;\n");
                js.append("}\n\n");

                js.append("function closeMakeupErrorModal() {\n");
                js.append("    const modal = window.currentMakeupErrorModal;\n");
                js.append("    if (modal) {\n");
                js.append("        modal.style.opacity = '0';\n");
                js.append("        const content = modal.querySelector('div');\n");
                js.append("        content.style.transform = 'scale(0.8) translateY(20px)';\n");
                js.append("        setTimeout(() => {\n");
                js.append("            if (document.body.contains(modal)) {\n");
                js.append("                document.body.removeChild(modal);\n");
                js.append("            }\n");
                js.append("            window.currentMakeupErrorModal = null;\n");
                js.append("        }, 300);\n");
                js.append("    }\n");
                js.append("}\n\n");

                return js.toString();
        }
}
