package cn.acebrand.acekeysystem.task;

import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 待处理奖励清理任务
 * 定期清理过期的待处理奖励
 */
public final class PendingRewardCleanupTask extends BukkitRunnable {

    private final AceKeySystem plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public PendingRewardCleanupTask(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void run() {
        // 检查并清理过期的待处理奖励
        this.plugin.checkExpiredPendingRewards();
    }
}
