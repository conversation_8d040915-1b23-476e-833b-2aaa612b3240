package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;

/**
 * 排行榜页面处理器
 * 为普通玩家提供排行榜查看功能
 */
public class LeaderboardHandler implements HttpHandler {

        private final AceKeySystem plugin;
        private final WebServer webServer;

        public LeaderboardHandler(AceKeySystem plugin, WebServer webServer) {
                this.plugin = plugin;
                this.webServer = webServer;
        }

        @Override
        public void handle(HttpExchange exchange) throws IOException {
                String method = exchange.getRequestMethod();

                if ("GET".equals(method)) {
                        sendLeaderboardPage(exchange);
                } else {
                        sendErrorResponse(exchange, 405, "方法不允许");
                }
        }

        /**
         * 发送排行榜页面
         */
        private void sendLeaderboardPage(HttpExchange exchange) throws IOException {
                String html = generateLeaderboardPage();

                exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
                exchange.sendResponseHeaders(200, html.getBytes(StandardCharsets.UTF_8).length);

                try (OutputStream os = exchange.getResponseBody()) {
                        os.write(html.getBytes(StandardCharsets.UTF_8));
                }
        }

        /**
         * 生成排行榜页面HTML
         */
        private String generateLeaderboardPage() {
                StringBuilder html = new StringBuilder();

                html.append("<!DOCTYPE html>\n");
                html.append("<html lang=\"zh-CN\">\n");
                html.append("<head>\n");
                html.append("    <meta charset=\"UTF-8\">\n");
                html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
                html.append("    <title>🏆 排行榜 - 抽奖系统</title>\n");
                html.append(
                                "    <link rel=\"icon\" href=\"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏆</text></svg>\">\n");

                // CSS样式
                html.append("    <style>\n");
                html.append(getLeaderboardCSS());
                html.append("    </style>\n");
                html.append("</head>\n");
                html.append("<body>\n");

                // 页面内容
                html.append("    <div class=\"leaderboard-page\">\n");
                html.append("        <div class=\"page-header leaderboard-header\">\n");
                html.append("            <h1>🏆 排行榜</h1>\n");
                html.append("            <p>玩家实力排名与荣誉展示</p>\n");
                html.append("            <div class=\"header-actions\">\n");
                html.append("                <button class=\"btn btn-back\" onclick=\"goBack()\">🔙 返回</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");

                // 导航标签页
                html.append("        <div class=\"leaderboard-nav\">\n");
                html.append("            <div class=\"nav-tabs\">\n");
                html.append("                <button class=\"nav-tab active\" onclick=\"showLeaderboardTab('points')\">\n");
                html.append("                    <span class=\"tab-icon\">💰</span>\n");
                html.append("                    <span class=\"tab-text\">积分排行榜</span>\n");
                html.append("                </button>\n");
                html.append("                <button class=\"nav-tab\" onclick=\"showLeaderboardTab('wins')\">\n");
                html.append("                    <span class=\"tab-icon\">🥇</span>\n");
                html.append("                    <span class=\"tab-text\">获奖次数排行</span>\n");
                html.append("                </button>\n");
                html.append("                <button class=\"nav-tab\" onclick=\"showLeaderboardTab('draws')\">\n");
                html.append("                    <span class=\"tab-icon\">🎲</span>\n");
                html.append("                    <span class=\"tab-text\">抽奖次数排行</span>\n");
                html.append("                </button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");

                // 积分排行榜标签页
                html.append("        <div id=\"leaderboard-tab-points\" class=\"leaderboard-tab-content active\">\n");
                html.append("            <div class=\"leaderboard-card\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <h3>💰 积分排行榜</h3>\n");
                html.append("                    <p>根据玩家当前积分数量进行排名</p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"leaderboard-container\" id=\"pointsLeaderboard\">\n");
                html.append("                    <div class=\"loading-spinner\">正在加载积分排行数据...</div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");

                // 获奖次数排行标签页
                html.append("        <div id=\"leaderboard-tab-wins\" class=\"leaderboard-tab-content\">\n");
                html.append("            <div class=\"leaderboard-card\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <h3>🥇 获奖次数排行榜</h3>\n");
                html.append("                    <p>根据玩家获得奖品的次数进行排名</p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"leaderboard-container\" id=\"winsLeaderboard\">\n");
                html.append("                    <div class=\"loading-spinner\">正在加载获奖排行数据...</div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");

                // 抽奖次数排行标签页
                html.append("        <div id=\"leaderboard-tab-draws\" class=\"leaderboard-tab-content\">\n");
                html.append("            <div class=\"leaderboard-card\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <h3>🎲 抽奖次数排行榜</h3>\n");
                html.append("                    <p>根据玩家参与抽奖的次数进行排名</p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"leaderboard-container\" id=\"drawsLeaderboard\">\n");
                html.append("                    <div class=\"loading-spinner\">正在加载抽奖排行数据...</div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                // JavaScript
                html.append("    <script>\n");
                html.append(generateSecurityJS()); // 添加安全防护
                html.append(getLeaderboardJavaScript());
                html.append("    </script>\n");
                html.append("</body>\n");
                html.append("</html>\n");

                return html.toString();
        }

        /**
         * 获取排行榜CSS样式
         */
        private String getLeaderboardCSS() {
                // 获取界面主题配置
                String theme = plugin.getConfig().getString("website.interface.theme", "default");
                String primaryColor = plugin.getConfig().getString("website.interface.primary-color", "#667eea");
                String accentColor = plugin.getConfig().getString("website.interface.accent-color", "#764ba2");
                boolean enableAnimations = plugin.getConfig().getBoolean("website.interface.enable-animations", true);
                String loadAnimation = plugin.getConfig().getString("website.interface.load-animation", "fade");
                String animationSpeed = plugin.getConfig().getString("website.interface.animation-speed", "normal");

                // 获取背景图配置
                String backgroundImage = plugin.getConfig().getString("website.leaderboard-background-image", "");
                String backgroundStyle = getThemeBackgroundStyle(theme, backgroundImage, primaryColor, accentColor);

                return generateThemeCSS(theme, primaryColor, accentColor, enableAnimations, loadAnimation,
                                animationSpeed) +
                                "* { margin: 0; padding: 0; box-sizing: border-box; }\n" +
                                "body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: #f5f5f5; }\n"
                                +
                                ".leaderboard-page { " + backgroundStyle + " min-height: 100vh; padding: 20px; }\n" +
                                ".leaderboard-header { text-align: center; color: white; margin-bottom: 30px; }\n" +
                                ".leaderboard-header h1 { font-size: 2.5em; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }\n"
                                +
                                ".leaderboard-header p { font-size: 1.2em; opacity: 0.9; margin-bottom: 20px; }\n" +
                                ".header-actions { margin-top: 15px; }\n" +
                                ".btn { padding: 10px 20px; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 600; transition: all 0.3s; text-decoration: none; display: inline-block; }\n"
                                +
                                ".btn-back { background: rgba(255,255,255,0.2); color: white; border: 2px solid rgba(255,255,255,0.3); }\n"
                                +
                                ".btn-back:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }\n"
                                +
                                ".leaderboard-nav { margin-bottom: 30px; }\n" +
                                ".leaderboard-nav .nav-tabs { display: flex; gap: 15px; justify-content: center; flex-wrap: wrap; }\n"
                                +
                                ".leaderboard-nav .nav-tab { padding: 15px 25px; background: rgba(255,255,255,0.1); border: 2px solid rgba(255,255,255,0.2); border-radius: 12px; cursor: pointer; transition: all 0.3s; color: white; font-weight: 600; backdrop-filter: blur(10px); }\n"
                                +
                                ".leaderboard-nav .nav-tab:hover { background: rgba(255,255,255,0.2); border-color: rgba(255,255,255,0.4); transform: translateY(-2px); }\n"
                                +
                                ".leaderboard-nav .nav-tab.active { background: rgba(255,255,255,0.3); border-color: rgba(255,255,255,0.6); box-shadow: 0 4px 15px rgba(0,0,0,0.2); }\n"
                                +
                                ".leaderboard-nav .tab-icon { font-size: 1.2em; margin-right: 8px; }\n" +
                                ".leaderboard-nav .tab-text { font-size: 1em; }\n" +
                                ".leaderboard-tab-content { display: none; }\n" +
                                ".leaderboard-tab-content.active { display: block; }\n" +
                                ".leaderboard-card { background: rgba(255,255,255,0.95); border-radius: 15px; padding: 25px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); backdrop-filter: blur(10px); max-width: 1000px; margin: 0 auto; }\n"
                                +
                                ".leaderboard-card .card-header { text-align: center; margin-bottom: 25px; }\n" +
                                ".leaderboard-card .card-header h3 { color: #2c3e50; font-size: 1.8em; margin-bottom: 8px; }\n"
                                +
                                ".leaderboard-card .card-header p { color: #7f8c8d; font-size: 1.1em; }\n" +
                                ".leaderboard-list { display: flex; flex-direction: column; gap: 15px; }\n" +
                                ".leaderboard-item { display: flex; align-items: center; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); transition: all 0.3s; border-left: 4px solid #e9ecef; }\n"
                                +
                                ".leaderboard-item:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.15); }\n"
                                +
                                ".leaderboard-item.first { border-left-color: #ffd700; background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%); }\n"
                                +
                                ".leaderboard-item.second { border-left-color: #c0c0c0; background: linear-gradient(135deg, #f8f8f8 0%, #ffffff 100%); }\n"
                                +
                                ".leaderboard-item.third { border-left-color: #cd7f32; background: linear-gradient(135deg, #fff4e6 0%, #ffffff 100%); }\n"
                                +
                                ".rank-badge { display: flex; flex-direction: column; align-items: center; margin-right: 20px; min-width: 60px; }\n"
                                +
                                ".rank-badge .medal { font-size: 2em; margin-bottom: 5px; }\n" +
                                ".rank-badge .rank-number { font-size: 0.9em; font-weight: bold; color: #666; }\n" +
                                ".player-avatar { margin-right: 20px; }\n" +
                                ".player-avatar img { width: 48px; height: 48px; border-radius: 8px; border: 2px solid #e9ecef; transition: all 0.3s; }\n"
                                +
                                ".leaderboard-item.first .player-avatar img, .leaderboard-item.second .player-avatar img, .leaderboard-item.third .player-avatar img { width: 64px; height: 64px; border-width: 3px; }\n"
                                +
                                ".leaderboard-item.first .player-avatar img { border-color: #ffd700; }\n" +
                                ".leaderboard-item.second .player-avatar img { border-color: #c0c0c0; }\n" +
                                ".leaderboard-item.third .player-avatar img { border-color: #cd7f32; }\n" +
                                ".player-info { flex: 1; }\n" +
                                ".player-info .player-name { font-size: 1.3em; font-weight: bold; color: #2c3e50; margin-bottom: 5px; }\n"
                                +
                                ".leaderboard-item.first .player-name, .leaderboard-item.second .player-name, .leaderboard-item.third .player-name { font-size: 1.5em; }\n"
                                +
                                ".player-stats { display: flex; gap: 15px; flex-wrap: wrap; }\n" +
                                ".player-stats span { padding: 4px 8px; background: #f8f9fa; border-radius: 6px; font-size: 0.9em; color: #495057; }\n"
                                +
                                ".wins-count { background: #d4edda !important; color: #155724 !important; }\n" +
                                ".draws-count { background: #cce7ff !important; color: #004085 !important; }\n" +
                                ".win-rate { background: #fff3cd !important; color: #856404 !important; }\n" +
                                ".points-count { background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%) !important; color: #b8860b !important; font-weight: bold; border: 1px solid #ffd700; }\n"
                                +
                                ".points-rank { background: #e2e3e5 !important; color: #383d41 !important; }\n" +
                                ".loading-spinner { text-align: center; padding: 40px; color: #6c757d; font-size: 1.1em; }\n"
                                +
                                ".no-data { text-align: center; padding: 40px; color: #6c757d; font-size: 1.1em; }\n" +
                                "@media (max-width: 768px) {\n" +
                                "    .leaderboard-page { padding: 10px; }\n" +
                                "    .leaderboard-header h1 { font-size: 2em; }\n" +
                                "    .leaderboard-nav .nav-tab { padding: 12px 20px; }\n" +
                                "    .leaderboard-item { padding: 15px; }\n" +
                                "    .player-stats { gap: 10px; }\n" +
                                "    .player-stats span { font-size: 0.8em; }\n" +
                                "}\n";
        }

        /**
         * 获取排行榜JavaScript
         */
        private String getLeaderboardJavaScript() {
                return "// 返回按钮功能\n" +
                                "function goBack() {\n" +
                                "    // 检查是否有历史记录\n" +
                                "    if (window.history.length > 1) {\n" +
                                "        window.history.back();\n" +
                                "    } else {\n" +
                                "        // 如果没有历史记录，跳转到抽奖页面\n" +
                                "        window.location.href = '/user';\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 排行榜自动检测变量\n" +
                                "let lastLeaderboardDataHash = null;\n" +
                                "let leaderboardAutoUpdateInterval = null;\n" +
                                "let isLeaderboardManualRefresh = false;\n" +
                                "\n" +
                                "// 页面加载完成后自动加载排行榜数据\n" +
                                "document.addEventListener('DOMContentLoaded', function() {\n" +
                                "    loadLeaderboard();\n" +
                                "    startLeaderboardAutoUpdate(); // 启动自动检测\n" +
                                "});\n" +
                                "\n" +
                                "// 页面切换时停止自动检测\n" +
                                "window.addEventListener('beforeunload', function() {\n" +
                                "    stopLeaderboardAutoUpdate();\n" +
                                "});\n" +
                                "\n" +
                                "// 页面可见性变化时控制自动检测\n" +
                                "document.addEventListener('visibilitychange', function() {\n" +
                                "    if (document.hidden) {\n" +
                                "        stopLeaderboardAutoUpdate();\n" +
                                "    } else {\n" +
                                "        startLeaderboardAutoUpdate();\n" +
                                "    }\n" +
                                "});\n" +
                                "\n" +
                                "// 计算排行榜数据哈希值\n" +
                                "function calculateLeaderboardDataHash(data) {\n" +
                                "    const hashData = {\n" +
                                "        points: data.leaderboard ? data.leaderboard.points : [],\n" +
                                "        wins: data.leaderboard ? data.leaderboard.wins : [],\n" +
                                "        draws: data.leaderboard ? data.leaderboard.draws : []\n" +
                                "    };\n" +
                                "    return JSON.stringify(hashData);\n" +
                                "}\n" +
                                "\n" +
                                "// 加载排行榜数据\n" +
                                "function loadLeaderboard(silent = false) {\n" +
                                "    fetch('/api', {\n" +
                                "        method: 'POST',\n" +
                                "        headers: { 'Content-Type': 'application/json' },\n" +
                                "        body: JSON.stringify({\n" +
                                "            action: 'get_public_leaderboard'\n" +
                                "        })\n" +
                                "    })\n" +
                                "    .then(response => response.json())\n" +
                                "    .then(data => {\n" +
                                "        if (data.success) {\n" +
                                "            // 计算数据哈希值用于检测变化\n" +
                                "            const dataHash = calculateLeaderboardDataHash(data);\n" +
                                "            \n" +
                                "            // 检查数据是否有变化\n" +
                                "            if (silent && lastLeaderboardDataHash && lastLeaderboardDataHash === dataHash) {\n"
                                +
                                "                // 数据没有变化，不更新界面\n" +
                                "                return;\n" +
                                "            }\n" +
                                "            \n" +
                                "            // 数据有变化或首次加载，更新界面\n" +
                                "            lastLeaderboardDataHash = dataHash;\n" +
                                "            displayLeaderboard(data.leaderboard);\n" +
                                "            \n" +
                                "            // 如果是静默检测到变化，显示提示\n" +
                                "            if (silent && !isLeaderboardManualRefresh) {\n" +
                                "                showNotification('🔄 检测到排行榜数据更新，已自动刷新');\n" +
                                "            }\n" +
                                "        } else {\n" +
                                "            if (!silent) {\n" +
                                "                showError('加载排行榜数据失败: ' + data.message);\n" +
                                "            }\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .catch(error => {\n" +
                                "        if (!silent) {\n" +
                                "            console.error('Error:', error);\n" +
                                "            showError('加载排行榜数据时出错');\n" +
                                "        }\n" +
                                "    })\n" +
                                "    .finally(() => {\n" +
                                "        isLeaderboardManualRefresh = false;\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 启动排行榜自动检测\n" +
                                "function startLeaderboardAutoUpdate() {\n" +
                                "    if (leaderboardAutoUpdateInterval) {\n" +
                                "        clearInterval(leaderboardAutoUpdateInterval);\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 每1秒检测一次数据变化\n" +
                                "    leaderboardAutoUpdateInterval = setInterval(() => {\n" +
                                "        loadLeaderboard(true); // 静默检测\n" +
                                "    }, 1000);\n" +
                                "}\n" +
                                "\n" +
                                "// 停止排行榜自动检测\n" +
                                "function stopLeaderboardAutoUpdate() {\n" +
                                "    if (leaderboardAutoUpdateInterval) {\n" +
                                "        clearInterval(leaderboardAutoUpdateInterval);\n" +
                                "        leaderboardAutoUpdateInterval = null;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 显示通知消息\n" +
                                "function showNotification(message) {\n" +
                                "    // 创建通知元素\n" +
                                "    const notification = document.createElement('div');\n" +
                                "    notification.className = 'notification success';\n" +
                                "    notification.innerHTML = message;\n" +
                                "    notification.style.cssText = `\n" +
                                "        position: fixed;\n" +
                                "        top: 20px;\n" +
                                "        right: 20px;\n" +
                                "        background: linear-gradient(45deg, #4CAF50, #45a049);\n" +
                                "        color: white;\n" +
                                "        padding: 12px 20px;\n" +
                                "        border-radius: 8px;\n" +
                                "        box-shadow: 0 4px 12px rgba(0,0,0,0.15);\n" +
                                "        z-index: 10000;\n" +
                                "        font-size: 14px;\n" +
                                "        font-weight: 500;\n" +
                                "        animation: slideIn 0.3s ease-out;\n" +
                                "    `;\n" +
                                "    \n" +
                                "    document.body.appendChild(notification);\n" +
                                "    \n" +
                                "    // 3秒后自动移除\n" +
                                "    setTimeout(() => {\n" +
                                "        if (notification.parentNode) {\n" +
                                "            notification.style.animation = 'slideOut 0.3s ease-in';\n" +
                                "            setTimeout(() => {\n" +
                                "                if (notification.parentNode) {\n" +
                                "                    notification.parentNode.removeChild(notification);\n" +
                                "                }\n" +
                                "            }, 300);\n" +
                                "        }\n" +
                                "    }, 3000);\n" +
                                "}\n" +
                                "\n" +
                                "function displayLeaderboard(leaderboard) {\n" +
                                "    // 保存排行榜数据到全局变量\n" +
                                "    window.leaderboardData = leaderboard;\n" +
                                "    // 默认显示积分排行榜\n" +
                                "    showLeaderboardTab('points');\n" +
                                "}\n" +
                                "\n" +
                                "function showError(message) {\n" +
                                "    const containers = ['pointsLeaderboard', 'winsLeaderboard', 'drawsLeaderboard'];\n"
                                +
                                "    containers.forEach(id => {\n" +
                                "        const container = document.getElementById(id);\n" +
                                "        if (container) {\n" +
                                "            container.innerHTML = '<div class=\"no-data\">' + message + '</div>';\n" +
                                "        }\n" +
                                "    });\n" +
                                "}\n" +
                                "\n" +
                                "// 排行榜标签页切换功能\n" +
                                "function showLeaderboardTab(tabName) {\n" +
                                "    // 隐藏所有标签页内容\n" +
                                "    const allTabs = document.querySelectorAll('.leaderboard-tab-content');\n" +
                                "    allTabs.forEach(tab => {\n" +
                                "        tab.classList.remove('active');\n" +
                                "    });\n" +
                                "\n" +
                                "    // 移除所有标签按钮的激活状态\n" +
                                "    const allTabButtons = document.querySelectorAll('.leaderboard-nav .nav-tab');\n" +
                                "    allTabButtons.forEach(button => {\n" +
                                "        button.classList.remove('active');\n" +
                                "    });\n" +
                                "\n" +
                                "    // 显示选中的标签页\n" +
                                "    const selectedTab = document.getElementById('leaderboard-tab-' + tabName);\n" +
                                "    if (selectedTab) {\n" +
                                "        selectedTab.classList.add('active');\n" +
                                "    }\n" +
                                "\n" +
                                "    // 激活对应的标签按钮\n" +
                                "    if (event && event.target) {\n" +
                                "        // 找到最近的nav-tab元素\n" +
                                "        let button = event.target;\n" +
                                "        while (button && !button.classList.contains('nav-tab')) {\n" +
                                "            button = button.parentElement;\n" +
                                "        }\n" +
                                "        if (button) {\n" +
                                "            button.classList.add('active');\n" +
                                "        }\n" +
                                "    } else {\n" +
                                "        // 如果没有event，找到对应的按钮并激活\n" +
                                "        const buttons = document.querySelectorAll('.leaderboard-nav .nav-tab');\n" +
                                "        buttons.forEach((button, index) => {\n" +
                                "            if ((tabName === 'points' && index === 0) ||\n" +
                                "                (tabName === 'wins' && index === 1) ||\n" +
                                "                (tabName === 'draws' && index === 2)) {\n" +
                                "                button.classList.add('active');\n" +
                                "            }\n" +
                                "        });\n" +
                                "    }\n" +
                                "\n" +
                                "    // 根据标签页类型显示对应的排行榜\n" +
                                "    if (window.leaderboardData) {\n" +
                                "        switch(tabName) {\n" +
                                "            case 'points':\n" +
                                "                displayPointsLeaderboard(window.leaderboardData.points || []);\n" +
                                "                break;\n" +
                                "            case 'wins':\n" +
                                "                displayWinsLeaderboard(window.leaderboardData.wins || []);\n" +
                                "                break;\n" +
                                "            case 'draws':\n" +
                                "                displayDrawsLeaderboard(window.leaderboardData.draws || []);\n" +
                                "                break;\n" +
                                "        }\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "// 显示获奖次数排行榜\n" +
                                "function displayWinsLeaderboard(winsData) {\n" +
                                "    const container = document.getElementById('winsLeaderboard');\n" +
                                "    if (!container) return;\n" +
                                "\n" +
                                "    if (!winsData || winsData.length === 0) {\n" +
                                "        container.innerHTML = '<div class=\"no-data\">暂无获奖数据</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "\n" +
                                "    // 按获奖次数排序\n" +
                                "    winsData.sort((a, b) => b.wins - a.wins);\n" +
                                "\n" +
                                "    let html = '<div class=\"leaderboard-list\">';\n" +
                                "    \n" +
                                "    // 前三名特殊显示\n" +
                                "    for (let i = 0; i < Math.min(3, winsData.length); i++) {\n" +
                                "        const player = winsData[i];\n" +
                                "        const rank = i + 1;\n" +
                                "        const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : '🥉';\n" +
                                "        const rankClass = rank === 1 ? 'first' : rank === 2 ? 'second' : 'third';\n" +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-item top-${rank} ${rankClass}\">\n" +
                                "                <div class=\"rank-badge\">\n" +
                                "                    <span class=\"medal\">${medal}</span>\n" +
                                "                    <span class=\"rank-number\">#${rank}</span>\n" +
                                "                </div>\n" +
                                "                <div class=\"player-avatar\">\n" +
                                "                    <img src=\"https://minotar.net/helm/${player.player}/64\" alt=\"${player.player}\" onerror=\"this.src='https://minotar.net/helm/steve/64'\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"player-info\">\n" +
                                "                    <div class=\"player-name\">${player.player}</div>\n" +
                                "                    <div class=\"player-stats\">\n" +
                                "                        <span class=\"wins-count\">${player.wins} 次获奖</span>\n" +
                                "                        <span class=\"win-rate\">${player.total_draws > 0 ? ((player.wins / player.total_draws) * 100).toFixed(1) : '0.0'}% 胜率</span>\n"
                                +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 其余排名正常显示\n" +
                                "    for (let i = 3; i < Math.min(10, winsData.length); i++) {\n" +
                                "        const player = winsData[i];\n" +
                                "        const rank = i + 1;\n" +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-item\">\n" +
                                "                <div class=\"rank-badge\">\n" +
                                "                    <span class=\"rank-number\">#${rank}</span>\n" +
                                "                </div>\n" +
                                "                <div class=\"player-avatar\">\n" +
                                "                    <img src=\"https://minotar.net/helm/${player.player}/48\" alt=\"${player.player}\" onerror=\"this.src='https://minotar.net/helm/steve/48'\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"player-info\">\n" +
                                "                    <div class=\"player-name\">${player.player}</div>\n" +
                                "                    <div class=\"player-stats\">\n" +
                                "                        <span class=\"wins-count\">${player.wins} 次获奖</span>\n" +
                                "                        <span class=\"win-rate\">${player.total_draws > 0 ? ((player.wins / player.total_draws) * 100).toFixed(1) : '0.0'}% 胜率</span>\n"
                                +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    }\n" +
                                "    \n" +
                                "    html += '</div>';\n" +
                                "    container.innerHTML = html;\n" +
                                "}\n" +
                                "\n" +
                                "// 显示抽奖次数排行榜\n" +
                                "function displayDrawsLeaderboard(drawsData) {\n" +
                                "    const container = document.getElementById('drawsLeaderboard');\n" +
                                "    if (!container) return;\n" +
                                "\n" +
                                "    if (!drawsData || drawsData.length === 0) {\n" +
                                "        container.innerHTML = '<div class=\"no-data\">暂无抽奖数据</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "\n" +
                                "    // 按抽奖次数排序\n" +
                                "    drawsData.sort((a, b) => b.draws - a.draws);\n" +
                                "\n" +
                                "    let html = '<div class=\"leaderboard-list\">';\n" +
                                "    \n" +
                                "    // 前三名特殊显示\n" +
                                "    for (let i = 0; i < Math.min(3, drawsData.length); i++) {\n" +
                                "        const player = drawsData[i];\n" +
                                "        const rank = i + 1;\n" +
                                "        const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : '🥉';\n" +
                                "        const rankClass = rank === 1 ? 'first' : rank === 2 ? 'second' : 'third';\n" +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-item top-${rank} ${rankClass}\">\n" +
                                "                <div class=\"rank-badge\">\n" +
                                "                    <span class=\"medal\">${medal}</span>\n" +
                                "                    <span class=\"rank-number\">#${rank}</span>\n" +
                                "                </div>\n" +
                                "                <div class=\"player-avatar\">\n" +
                                "                    <img src=\"https://minotar.net/helm/${player.player}/64\" alt=\"${player.player}\" onerror=\"this.src='https://minotar.net/helm/steve/64'\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"player-info\">\n" +
                                "                    <div class=\"player-name\">${player.player}</div>\n" +
                                "                    <div class=\"player-stats\">\n" +
                                "                        <span class=\"draws-count\">${player.draws} 次抽奖</span>\n" +
                                "                        <span class=\"win-rate\">${player.draws > 0 ? ((player.wins / player.draws) * 100).toFixed(1) : '0.0'}% 胜率</span>\n"
                                +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 其余排名正常显示\n" +
                                "    for (let i = 3; i < Math.min(10, drawsData.length); i++) {\n" +
                                "        const player = drawsData[i];\n" +
                                "        const rank = i + 1;\n" +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-item\">\n" +
                                "                <div class=\"rank-badge\">\n" +
                                "                    <span class=\"rank-number\">#${rank}</span>\n" +
                                "                </div>\n" +
                                "                <div class=\"player-avatar\">\n" +
                                "                    <img src=\"https://minotar.net/helm/${player.player}/48\" alt=\"${player.player}\" onerror=\"this.src='https://minotar.net/helm/steve/48'\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"player-info\">\n" +
                                "                    <div class=\"player-name\">${player.player}</div>\n" +
                                "                    <div class=\"player-stats\">\n" +
                                "                        <span class=\"draws-count\">${player.draws} 次抽奖</span>\n" +
                                "                        <span class=\"win-rate\">${player.draws > 0 ? ((player.wins / player.draws) * 100).toFixed(1) : '0.0'}% 胜率</span>\n"
                                +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    }\n" +
                                "    \n" +
                                "    html += '</div>';\n" +
                                "    container.innerHTML = html;\n" +
                                "}\n" +
                                "\n" +
                                "// 显示积分排行榜\n" +
                                "function displayPointsLeaderboard(pointsData) {\n" +
                                "    const container = document.getElementById('pointsLeaderboard');\n" +
                                "    if (!container) return;\n" +
                                "\n" +
                                "    if (!pointsData || pointsData.length === 0) {\n" +
                                "        container.innerHTML = '<div class=\"no-data\">暂无积分数据</div>';\n" +
                                "        return;\n" +
                                "    }\n" +
                                "\n" +
                                "    // 按积分数量排序\n" +
                                "    pointsData.sort((a, b) => b.points - a.points);\n" +
                                "\n" +
                                "    let html = '<div class=\"leaderboard-list\">';\n" +
                                "    \n" +
                                "    // 前三名特殊显示\n" +
                                "    for (let i = 0; i < Math.min(3, pointsData.length); i++) {\n" +
                                "        const player = pointsData[i];\n" +
                                "        const rank = i + 1;\n" +
                                "        const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : '🥉';\n" +
                                "        const rankClass = rank === 1 ? 'first' : rank === 2 ? 'second' : 'third';\n" +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-item top-${rank} ${rankClass}\">\n" +
                                "                <div class=\"rank-badge\">\n" +
                                "                    <span class=\"medal\">${medal}</span>\n" +
                                "                    <span class=\"rank-number\">#${rank}</span>\n" +
                                "                </div>\n" +
                                "                <div class=\"player-avatar\">\n" +
                                "                    <img src=\"https://minotar.net/helm/${player.player}/64\" alt=\"${player.player}\" onerror=\"this.src='https://minotar.net/helm/steve/64'\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"player-info\">\n" +
                                "                    <div class=\"player-name\">${player.player}</div>\n" +
                                "                    <div class=\"player-stats\">\n" +
                                "                        <span class=\"points-count\">💰 ${player.points.toLocaleString()} 积分</span>\n"
                                +
                                "                        <span class=\"points-rank\">第 ${rank} 名</span>\n" +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    }\n" +
                                "    \n" +
                                "    // 其余排名正常显示\n" +
                                "    for (let i = 3; i < Math.min(10, pointsData.length); i++) {\n" +
                                "        const player = pointsData[i];\n" +
                                "        const rank = i + 1;\n" +
                                "        \n" +
                                "        html += `\n" +
                                "            <div class=\"leaderboard-item\">\n" +
                                "                <div class=\"rank-badge\">\n" +
                                "                    <span class=\"rank-number\">#${rank}</span>\n" +
                                "                </div>\n" +
                                "                <div class=\"player-avatar\">\n" +
                                "                    <img src=\"https://minotar.net/helm/${player.player}/48\" alt=\"${player.player}\" onerror=\"this.src='https://minotar.net/helm/steve/48'\">\n"
                                +
                                "                </div>\n" +
                                "                <div class=\"player-info\">\n" +
                                "                    <div class=\"player-name\">${player.player}</div>\n" +
                                "                    <div class=\"player-stats\">\n" +
                                "                        <span class=\"points-count\">💰 ${player.points.toLocaleString()} 积分</span>\n"
                                +
                                "                        <span class=\"points-rank\">第 ${rank} 名</span>\n" +
                                "                    </div>\n" +
                                "                </div>\n" +
                                "            </div>\n" +
                                "        `;\n" +
                                "    }\n" +
                                "    \n" +
                                "    html += '</div>';\n" +
                                "    container.innerHTML = html;\n" +
                                "}\n";
        }

        /**
         * 发送错误响应
         */
        private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
                String errorHtml = "<!DOCTYPE html><html><head><title>错误</title></head><body><h1>错误 " + statusCode
                                + "</h1><p>"
                                + message + "</p></body></html>";
                exchange.getResponseHeaders().set("Content-Type", "text/html; charset=UTF-8");
                exchange.sendResponseHeaders(statusCode, errorHtml.getBytes(StandardCharsets.UTF_8).length);

                try (OutputStream os = exchange.getResponseBody()) {
                        os.write(errorHtml.getBytes(StandardCharsets.UTF_8));
                }
        }

        /**
         * 获取主题背景样式
         */
        private String getThemeBackgroundStyle(String theme, String backgroundImage, String primaryColor,
                        String accentColor) {
                if (backgroundImage != null && !backgroundImage.trim().isEmpty()) {
                        // 获取透明度设置
                        int opacity = plugin.getConfig().getInt("website.leaderboard-background-opacity", 30);
                        // 将透明度转换为0-1之间的值
                        double opacityValue = opacity / 100.0;

                        // 如果配置了背景图，使用背景图（支持GIF动图）
                        return "background: linear-gradient(rgba(102, 126, 234, " + opacityValue
                                        + "), rgba(118, 75, 162, " + (opacityValue + 0.1) + ")), url('"
                                        + backgroundImage + "'); " +
                                        "background-size: cover; background-position: center; background-attachment: fixed; background-repeat: no-repeat;";
                }

                // 根据主题返回不同的背景样式
                switch (theme) {
                        case "dark":
                                return "background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #404040 100%);";
                        case "light":
                                return "background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);";
                        case "blue":
                                return "background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);";
                        case "purple":
                                return "background: linear-gradient(135deg, #581c87 0%, #8b5cf6 50%, #a78bfa 100%);";
                        case "green":
                                return "background: linear-gradient(135deg, #14532d 0%, #22c55e 50%, #4ade80 100%);";
                        default:
                                return "background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);";
                }
        }

        /**
         * 生成主题CSS
         */
        private String generateThemeCSS(String theme, String primaryColor, String accentColor, boolean enableAnimations,
                        String loadAnimation, String animationSpeed) {
                StringBuilder css = new StringBuilder();

                // CSS变量定义
                css.append(":root {\n");
                css.append("  --primary-color: ").append(primaryColor).append(";\n");
                css.append("  --accent-color: ").append(accentColor).append(";\n");
                css.append("  --animation-duration: ").append(getAnimationDuration(animationSpeed)).append(";\n");
                css.append("}\n\n");

                // 页面加载动画
                if (enableAnimations && !"none".equals(loadAnimation)) {
                        css.append(generateLoadAnimationCSS(loadAnimation));
                }

                // 主题特定样式
                css.append(generateThemeSpecificCSS(theme));

                return css.toString();
        }

        /**
         * 获取动画持续时间
         */
        private String getAnimationDuration(String speed) {
                switch (speed) {
                        case "slow":
                                return "0.8s";
                        case "fast":
                                return "0.3s";
                        default:
                                return "0.5s";
                }
        }

        /**
         * 生成页面加载动画CSS
         */
        private String generateLoadAnimationCSS(String animation) {
                StringBuilder css = new StringBuilder();

                switch (animation) {
                        case "fade":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  from { opacity: 0; }\n");
                                css.append("  to { opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "slide":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  from { transform: translateY(30px); opacity: 0; }\n");
                                css.append("  to { transform: translateY(0); opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "zoom":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  from { transform: scale(0.9); opacity: 0; }\n");
                                css.append("  to { transform: scale(1); opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "bounce":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { transform: translateY(-30px); opacity: 0; }\n");
                                css.append("  50% { transform: translateY(10px); opacity: 0.8; }\n");
                                css.append("  100% { transform: translateY(0); opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "matrix":
                                // 黑客帝国风格矩阵效果
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: scale(0.8) rotateX(90deg); filter: hue-rotate(0deg) brightness(0.5); }\n");
                                css.append(
                                                "  25% { opacity: 0.3; transform: scale(0.9) rotateX(45deg); filter: hue-rotate(90deg) brightness(0.7); }\n");
                                css.append(
                                                "  50% { opacity: 0.6; transform: scale(1.05) rotateX(0deg); filter: hue-rotate(180deg) brightness(1.2); }\n");
                                css.append(
                                                "  75% { opacity: 0.8; transform: scale(1.02) rotateX(-10deg); filter: hue-rotate(270deg) brightness(1.1); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: scale(1) rotateX(0deg); filter: hue-rotate(360deg) brightness(1); }\n");
                                css.append("}\n");
                                css.append(
                                                "body { animation: pageLoad var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94); }\n");
                                css.append("body::before { animation: matrixGlow var(--animation-duration) ease-out; }\n");
                                css.append("@keyframes matrixGlow {\n");
                                css.append("  0% { box-shadow: inset 0 0 100px #00ff00; }\n");
                                css.append("  100% { box-shadow: inset 0 0 0px #00ff00; }\n");
                                css.append("}\n\n");
                                break;
                        case "portal":
                                // 传送门效果
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: scale(0) rotate(0deg); border-radius: 50%; filter: blur(20px); }\n");
                                css.append(
                                                "  25% { opacity: 0.4; transform: scale(0.3) rotate(90deg); border-radius: 30%; filter: blur(15px); }\n");
                                css.append(
                                                "  50% { opacity: 0.7; transform: scale(0.7) rotate(180deg); border-radius: 20%; filter: blur(10px); }\n");
                                css.append(
                                                "  75% { opacity: 0.9; transform: scale(0.95) rotate(270deg); border-radius: 10%; filter: blur(5px); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: scale(1) rotate(360deg); border-radius: 0%; filter: blur(0px); }\n");
                                css.append("}\n");
                                css.append(
                                                "body { animation: pageLoad var(--animation-duration) cubic-bezier(0.68, -0.55, 0.265, 1.55); overflow: hidden; }\n");
                                css.append(
                                                "body::after { content: ''; position: fixed; top: 50%; left: 50%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(102,126,234,0.3) 0%, transparent 70%); transform: translate(-50%, -50%); animation: portalRing var(--animation-duration) ease-out; z-index: -1; }\n");
                                css.append("@keyframes portalRing {\n");
                                css.append("  0% { width: 0px; height: 0px; opacity: 1; }\n");
                                css.append("  50% { width: 400px; height: 400px; opacity: 0.8; }\n");
                                css.append("  100% { width: 800px; height: 800px; opacity: 0; }\n");
                                css.append("}\n\n");
                                break;
                        case "glitch":
                                // 故障风格效果
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: translateX(-100px) skewX(10deg); filter: hue-rotate(0deg); }\n");
                                css.append(
                                                "  10% { opacity: 0.2; transform: translateX(-50px) skewX(-5deg); filter: hue-rotate(45deg); }\n");
                                css.append(
                                                "  20% { opacity: 0.1; transform: translateX(-80px) skewX(15deg); filter: hue-rotate(90deg); }\n");
                                css.append(
                                                "  30% { opacity: 0.4; transform: translateX(-20px) skewX(-10deg); filter: hue-rotate(135deg); }\n");
                                css.append(
                                                "  40% { opacity: 0.2; transform: translateX(-60px) skewX(8deg); filter: hue-rotate(180deg); }\n");
                                css.append(
                                                "  50% { opacity: 0.6; transform: translateX(-10px) skewX(-3deg); filter: hue-rotate(225deg); }\n");
                                css.append(
                                                "  60% { opacity: 0.3; transform: translateX(-30px) skewX(12deg); filter: hue-rotate(270deg); }\n");
                                css.append(
                                                "  70% { opacity: 0.7; transform: translateX(-5px) skewX(-2deg); filter: hue-rotate(315deg); }\n");
                                css.append(
                                                "  80% { opacity: 0.5; transform: translateX(-15px) skewX(5deg); filter: hue-rotate(360deg); }\n");
                                css.append(
                                                "  90% { opacity: 0.9; transform: translateX(-2px) skewX(-1deg); filter: hue-rotate(0deg); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: translateX(0px) skewX(0deg); filter: hue-rotate(0deg); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) steps(10, end); }\n\n");
                                break;
                        case "hologram":
                                // 全息投影效果
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: perspective(1000px) rotateY(90deg) rotateX(30deg); filter: brightness(0.3) contrast(2); }\n");
                                css.append(
                                                "  25% { opacity: 0.3; transform: perspective(1000px) rotateY(45deg) rotateX(15deg); filter: brightness(0.6) contrast(1.8); }\n");
                                css.append(
                                                "  50% { opacity: 0.6; transform: perspective(1000px) rotateY(0deg) rotateX(0deg); filter: brightness(1.2) contrast(1.5); }\n");
                                css.append(
                                                "  75% { opacity: 0.8; transform: perspective(1000px) rotateY(-10deg) rotateX(-5deg); filter: brightness(1.1) contrast(1.2); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: perspective(1000px) rotateY(0deg) rotateX(0deg); filter: brightness(1) contrast(1); }\n");
                                css.append("}\n");
                                css.append(
                                                "body { animation: pageLoad var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94); }\n");
                                css.append("body::before { animation: holoScan var(--animation-duration) ease-out; }\n");
                                css.append("@keyframes holoScan {\n");
                                css.append(
                                                "  0% { background: linear-gradient(90deg, transparent 0%, rgba(0,255,255,0.3) 50%, transparent 100%); transform: translateX(-100%); }\n");
                                css.append(
                                                "  100% { background: linear-gradient(90deg, transparent 0%, rgba(0,255,255,0.3) 50%, transparent 100%); transform: translateX(100%); }\n");
                                css.append("}\n\n");
                                break;
                        case "quantum":
                                // 量子传输效果
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: scale(0.1) rotate(0deg); filter: blur(20px) saturate(0); }\n");
                                css.append(
                                                "  20% { opacity: 0.2; transform: scale(0.3) rotate(72deg); filter: blur(15px) saturate(0.5); }\n");
                                css.append(
                                                "  40% { opacity: 0.4; transform: scale(0.6) rotate(144deg); filter: blur(10px) saturate(1); }\n");
                                css.append(
                                                "  60% { opacity: 0.6; transform: scale(0.8) rotate(216deg); filter: blur(5px) saturate(1.5); }\n");
                                css.append(
                                                "  80% { opacity: 0.8; transform: scale(0.95) rotate(288deg); filter: blur(2px) saturate(1.2); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: scale(1) rotate(360deg); filter: blur(0px) saturate(1); }\n");
                                css.append("}\n");
                                css.append(
                                                "body { animation: pageLoad var(--animation-duration) cubic-bezier(0.68, -0.55, 0.265, 1.55); }\n");
                                css.append(
                                                "body::after { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle at center, rgba(138,43,226,0.1) 0%, transparent 50%), conic-gradient(from 0deg, transparent, rgba(138,43,226,0.2), transparent); animation: quantumField var(--animation-duration) ease-out; z-index: -1; }\n");
                                css.append("@keyframes quantumField {\n");
                                css.append("  0% { opacity: 1; transform: rotate(0deg) scale(2); }\n");
                                css.append("  100% { opacity: 0; transform: rotate(360deg) scale(0.5); }\n");
                                css.append("}\n\n");
                                break;
                        case "cyberpunk":
                                // 赛博朋克风格
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: translateY(50px) skewY(5deg); filter: hue-rotate(0deg) brightness(0.5) contrast(2); }\n");
                                css.append(
                                                "  25% { opacity: 0.3; transform: translateY(30px) skewY(3deg); filter: hue-rotate(90deg) brightness(0.8) contrast(1.8); }\n");
                                css.append(
                                                "  50% { opacity: 0.6; transform: translateY(10px) skewY(1deg); filter: hue-rotate(180deg) brightness(1.2) contrast(1.5); }\n");
                                css.append(
                                                "  75% { opacity: 0.8; transform: translateY(5px) skewY(0.5deg); filter: hue-rotate(270deg) brightness(1.1) contrast(1.2); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: translateY(0px) skewY(0deg); filter: hue-rotate(360deg) brightness(1) contrast(1); }\n");
                                css.append("}\n");
                                css.append(
                                                "body { animation: pageLoad var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94); }\n");
                                css.append("body::before { animation: cyberGrid var(--animation-duration) ease-out; }\n");
                                css.append("@keyframes cyberGrid {\n");
                                css.append(
                                                "  0% { background-image: linear-gradient(rgba(0,255,255,0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255,0,255,0.3) 1px, transparent 1px); background-size: 20px 20px; opacity: 1; }\n");
                                css.append(
                                                "  100% { background-image: linear-gradient(rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,0,255,0.1) 1px, transparent 1px); background-size: 40px 40px; opacity: 0; }\n");
                                css.append("}\n\n");
                                break;
                        case "neon":
                                // 霓虹灯效果
                                css.append("@keyframes pageLoad {\n");
                                css.append(
                                                "  0% { opacity: 0; transform: scale(0.8); filter: brightness(0) drop-shadow(0 0 0px #ff00ff); }\n");
                                css.append(
                                                "  25% { opacity: 0.3; transform: scale(0.9); filter: brightness(0.5) drop-shadow(0 0 10px #ff00ff); }\n");
                                css.append(
                                                "  50% { opacity: 0.6; transform: scale(1.05); filter: brightness(1.2) drop-shadow(0 0 20px #00ffff); }\n");
                                css.append(
                                                "  75% { opacity: 0.8; transform: scale(1.02); filter: brightness(1.1) drop-shadow(0 0 15px #ffff00); }\n");
                                css.append(
                                                "  100% { opacity: 1; transform: scale(1); filter: brightness(1) drop-shadow(0 0 5px rgba(255,255,255,0.5)); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n");
                                css.append(
                                                "body::after { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle at 25% 25%, rgba(255,0,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(0,255,255,0.1) 0%, transparent 50%); animation: neonPulse var(--animation-duration) ease-out; z-index: -1; }\n");
                                css.append("@keyframes neonPulse {\n");
                                css.append("  0% { opacity: 1; }\n");
                                css.append("  50% { opacity: 0.5; }\n");
                                css.append("  100% { opacity: 0; }\n");
                                css.append("}\n\n");
                                break;
                }

                return css.toString();
        }

        /**
         * 生成主题特定CSS
         */
        private String generateThemeSpecificCSS(String theme) {
                StringBuilder css = new StringBuilder();

                switch (theme) {
                        case "dark":
                                css.append(".leaderboard-card { background: rgba(15, 15, 15, 0.95); color: #ffffff; }\n");
                                css.append(".leaderboard-item { background: rgba(40, 40, 40, 0.8); color: #ffffff; }\n");
                                css.append(
                                                ".btn-back { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                break;
                        case "light":
                                css.append(".leaderboard-card { background: rgba(255, 255, 255, 0.98); color: #1a1a1a; }\n");
                                css.append(".leaderboard-item { background: rgba(255, 255, 255, 0.9); color: #1a1a1a; }\n");
                                css.append(
                                                ".btn-back { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                break;
                        default:
                                // 使用默认样式，通过CSS变量应用自定义颜色
                                css.append(
                                                ".btn-back { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                css.append(".leaderboard-nav .nav-tab { border-color: var(--primary-color); }\n");
                                break;
                }

                return css.toString();
        }

        /**
         * 生成安全防护JavaScript
         */
        private String generateSecurityJS() {
                StringBuilder js = new StringBuilder();

                // 添加安全防护样式
                js.append("// 安全防护样式\n");
                js.append("const securityStyle = document.createElement('style');\n");
                js.append("securityStyle.textContent = `\n");
                js.append("    .security-overlay {\n");
                js.append("        position: fixed;\n");
                js.append("        top: 0;\n");
                js.append("        left: 0;\n");
                js.append("        width: 100%;\n");
                js.append("        height: 100%;\n");
                js.append("        background: rgba(0,0,0,0.9);\n");
                js.append("        color: white;\n");
                js.append("        display: none;\n");
                js.append("        z-index: 9999;\n");
                js.append("        justify-content: center;\n");
                js.append("        align-items: center;\n");
                js.append("        font-size: 24px;\n");
                js.append("        text-align: center;\n");
                js.append("        font-family: Arial, sans-serif;\n");
                js.append("    }\n");
                js.append("`;\n");
                js.append("document.head.appendChild(securityStyle);\n");
                js.append("\n");

                // 添加智能安全防护代码
                js.append("(function(){\n");
                js.append("var _0x1a2b=['keydown','F12','preventDefault','contextmenu','selectstart','dragstart'];\n");
                js.append("var _0x5e6f=document.createElement('div');\n");
                js.append("_0x5e6f.className='security-overlay';\n");
                js.append("_0x5e6f.innerHTML='🚫 检测到开发者工具<br>请关闭后重新访问<br><br>为保护系统安全，禁止使用开发者工具';\n");
                js.append("document.body.appendChild(_0x5e6f);\n");
                js.append("var _0xDetected = false;\n");

                // 键盘事件监听 - 只阻止开发者工具快捷键
                js.append("document.addEventListener(_0x1a2b[0],function(e){\n");
                js.append("if(e.key===_0x1a2b[1]||e.keyCode===123||\n");
                js.append("(e.ctrlKey&&e.shiftKey&&(e.keyCode===73||e.keyCode===74))||\n");
                js.append("(e.ctrlKey&&e.keyCode===85)){\n");
                js.append("e[_0x1a2b[2]]();showWarning();\n");
                js.append("}});\n");

                // 右键菜单禁用
                js.append("document.addEventListener(_0x1a2b[3],function(e){e[_0x1a2b[2]]();});\n");

                // 选择和拖拽禁用 - 但不影响正常点击
                js.append("document.addEventListener(_0x1a2b[4],function(e){\n");
                js.append("if(e.target.tagName!=='INPUT'&&e.target.tagName!=='TEXTAREA'&&e.target.tagName!=='BUTTON'){\n");
                js.append("e[_0x1a2b[2]]();\n");
                js.append("}});\n");
                js.append("document.addEventListener(_0x1a2b[5],function(e){e[_0x1a2b[2]]();});\n");

                // 智能窗口大小检测 - 更宽松的检测
                js.append("var _0xLastCheck = Date.now();\n");
                js.append("setInterval(function(){\n");
                js.append("var now = Date.now();\n");
                js.append("if(now - _0xLastCheck > 5000) {\n"); // 5秒检测一次
                js.append("if(window.outerHeight-window.innerHeight>250||window.outerWidth-window.innerWidth>250){\n");
                js.append("if(!_0xDetected) showWarning();\n");
                js.append("}\n");
                js.append("_0xLastCheck = now;\n");
                js.append("}},2000);\n");

                // 智能debugger检测 - 降低频率
                js.append("setInterval(function(){\n");
                js.append("if(_0xDetected) return;\n");
                js.append("var _0x9i0j=new Date().getTime();\n");
                js.append("debugger;\n");
                js.append("if(new Date().getTime()-_0x9i0j>150){\n");
                js.append("showWarning();\n");
                js.append("}},5000);\n"); // 5秒检测一次

                // 显示警告函数
                js.append("function showWarning(){\n");
                js.append("if(_0xDetected) return;\n");
                js.append("_0xDetected = true;\n");
                js.append("_0x5e6f.style.display='flex';\n");
                js.append("setTimeout(function(){\n");
                js.append("_0x5e6f.style.display='none';\n");
                js.append("_0xDetected = false;\n");
                js.append("},3000);\n"); // 3秒后自动隐藏，不刷新页面
                js.append("}\n");

                js.append("})();\n");
                js.append("\n");

                return js.toString();
        }
}
