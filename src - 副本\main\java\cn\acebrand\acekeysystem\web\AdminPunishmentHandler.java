package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentManager;
import cn.acebrand.acekeysystem.punishment.PunishmentRecord;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员处罚记录Web处理器
 * 处理所有类型的处罚记录请求
 */
public class AdminPunishmentHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final PunishmentManager punishmentManager;
    private final AdminPunishmentPageGenerator pageGenerator;

    public AdminPunishmentHandler(AceKeySystem plugin, WebServer webServer, PunishmentManager punishmentManager) {
        this.plugin = plugin;
        this.webServer = webServer;
        this.punishmentManager = punishmentManager;
        this.pageGenerator = new AdminPunishmentPageGenerator(plugin);
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        try {
            // 验证管理员权限
            if (!isAuthorized(exchange)) {
                handleUnauthorized(exchange);
                return;
            }

            String method = exchange.getRequestMethod();
            URI uri = exchange.getRequestURI();
            String path = uri.getPath();

            if ("GET".equals(method)) {
                handleGetRequest(exchange, path, uri.getQuery());
            } else if ("POST".equals(method)) {
                handlePostRequest(exchange, path);
            } else {
                sendErrorResponse(exchange, 405, "方法不被允许");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理处罚记录请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "服务器内部错误");
        }
    }

    /**
     * 处理POST请求
     */
    private void handlePostRequest(HttpExchange exchange, String path) throws IOException {
        try {
            // 读取请求体
            String requestBody = readRequestBody(exchange);
            plugin.getLogger().info("收到POST请求: " + path + ", 请求体: " + requestBody);

            // 解析JSON请求
            org.json.simple.JSONObject request = parseJsonRequest(requestBody);
            if (request == null) {
                sendJsonResponse(exchange, 400, false, "无效的JSON请求");
                return;
            }

            String action = (String) request.get("action");
            if (action == null) {
                sendJsonResponse(exchange, 400, false, "缺少action参数");
                return;
            }

            // 根据action处理不同的请求
            switch (action) {
                case "revoke":
                    handleRevokePunishment(exchange, request);
                    break;
                case "batchRevoke":
                    handleBatchRevokePunishment(exchange, request);
                    break;
                case "checkDataUpdate":
                    handleCheckDataUpdate(exchange, request);
                    break;
                default:
                    sendJsonResponse(exchange, 400, false, "未知的操作: " + action);
                    break;
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理POST请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendJsonResponse(exchange, 500, false, "服务器内部错误");
        }
    }

    /**
     * 处理GET请求
     */
    private void handleGetRequest(HttpExchange exchange, String path, String query) throws IOException {
        // 解析查询参数
        Map<String, String> params = parseQuery(query);

        // 解析路径
        String[] pathParts = path.split("/");

        if (pathParts.length == 2 && "admin-punishments".equals(pathParts[1])) {
            // /admin-punishments - 所有处罚记录
            handleAllPunishments(exchange, params);
        } else if (pathParts.length == 3 && "admin-punishments".equals(pathParts[1])) {
            // /admin-punishments/{type} - 特定类型的处罚记录
            String typeStr = pathParts[2];
            handleSpecificPunishment(exchange, typeStr, params);
        } else if (pathParts.length == 4 && "admin-punishments".equals(pathParts[1]) && isNumeric(pathParts[3])) {
            // /admin-punishments/{type}/{id} - 处罚详细信息页面
            String typeStr = pathParts[2];
            String idStr = pathParts[3];
            handlePunishmentDetail(exchange, typeStr, idStr);
        } else if (pathParts.length == 3 && "admin-player".equals(pathParts[1])) {
            // /admin-player/{playerName} - 管理员玩家专用页面
            String playerName = pathParts[2];
            handlePlayerPage(exchange, playerName, params);
        } else {
            sendErrorResponse(exchange, 404, "页面未找到");
        }
    }

    /**
     * 处理所有处罚记录请求
     */
    private void handleAllPunishments(HttpExchange exchange, Map<String, String> params) throws IOException {
        try {
            // 获取分页参数
            int page = parseInt(params.get("page"), 1);
            int pageSize = plugin.getConfig().getInt("litebans.display.records-per-page", 10);

            // 检查是否有玩家参数（类似 next-litebans 的 ?player=PlayerName）
            String playerParam = params.get("player");
            String staffParam = params.get("staff");
            String searchQuery = params.get("search");
            // 过滤不等于玩家视图，过滤只是在当前页面添加过滤条件
            boolean isPlayerView = false;
            boolean isStaffFilter = staffParam != null && !staffParam.isEmpty();

            // 按照 next-litebans 逻辑，不再将过滤参数转换为搜索查询
            // 而是直接使用过滤参数进行数据库查询

            List<PunishmentRecord> records;
            int totalPages = 1;
            Map<String, Object> statistics;

            plugin.getLogger().info("=== 处理所有处罚记录请求 ===");
            plugin.getLogger().info("页码: " + page + ", 每页: " + pageSize);
            plugin.getLogger().info("搜索查询: " + searchQuery);
            plugin.getLogger().info("玩家参数: " + playerParam);
            plugin.getLogger().info("是否玩家视图: " + isPlayerView);
            plugin.getLogger().info("处罚管理器状态: " + (punishmentManager != null ? "存在" : "null"));
            plugin.getLogger()
                    .info("处罚管理器启用状态: " + (punishmentManager != null ? punishmentManager.isEnabled() : "N/A"));

            // 检查处罚管理器是否可用
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                plugin.getLogger().info("开始获取处罚记录...");

                // 按照 next-litebans 逻辑处理过滤
                if ((playerParam != null && !playerParam.trim().isEmpty())
                        || (staffParam != null && !staffParam.trim().isEmpty())) {
                    plugin.getLogger().info("执行过滤查询 - 玩家: " + playerParam + ", 执行者: " + staffParam);
                    // 获取所有类型的过滤记录
                    List<PunishmentRecord> allFilteredRecords = new java.util.ArrayList<>();
                    for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                        List<PunishmentRecord> typeRecords = punishmentManager.searchPunishmentRecords(type,
                                playerParam, staffParam, 1, pageSize * 10);
                        allFilteredRecords.addAll(typeRecords);
                    }

                    // 按时间排序
                    allFilteredRecords.sort((a, b) -> {
                        if (a.getTime() == null && b.getTime() == null)
                            return 0;
                        if (a.getTime() == null)
                            return 1;
                        if (b.getTime() == null)
                            return -1;
                        return b.getTime().compareTo(a.getTime());
                    });

                    // 分页
                    int start = (page - 1) * pageSize;
                    int end = Math.min(start + pageSize, allFilteredRecords.size());
                    records = (start < allFilteredRecords.size()) ? allFilteredRecords.subList(start, end)
                            : new java.util.ArrayList<>();

                    // 计算总页数
                    totalPages = (int) Math.ceil((double) allFilteredRecords.size() / pageSize);
                    plugin.getLogger().info("过滤结果总数: " + allFilteredRecords.size() + ", 总页数: " + totalPages);
                } else if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                    plugin.getLogger().info("执行搜索查询: " + searchQuery);
                    records = punishmentManager.searchPunishmentRecords(searchQuery, page, pageSize);

                    // 搜索模式下，简化处理总数
                    int totalRecords = records.size() + (page - 1) * pageSize;
                    if (records.size() < pageSize) {
                        totalRecords = (page - 1) * pageSize + records.size();
                    } else {
                        totalRecords = page * pageSize + 1; // 假设还有更多记录
                    }
                    totalPages = (int) Math.ceil((double) totalRecords / pageSize);
                    plugin.getLogger().info("搜索结果总数: " + totalRecords + ", 总页数: " + totalPages);
                } else {
                    plugin.getLogger().info("获取所有记录");
                    records = punishmentManager.getAllPunishmentRecords(page, pageSize);

                    // 正常模式下，计算所有类型的总数
                    int totalRecords = 0;
                    for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                        int typeCount = punishmentManager.getPunishmentCount(type);
                        totalRecords += typeCount;
                        plugin.getLogger().info(type.getDisplayName() + " 记录数: " + typeCount);
                    }
                    totalPages = (int) Math.ceil((double) totalRecords / pageSize);
                    plugin.getLogger().info("总记录数: " + totalRecords + ", 总页数: " + totalPages);
                }

                plugin.getLogger().info("获取到 " + records.size() + " 条记录");

                // 获取统计信息
                statistics = punishmentManager.getPunishmentStatistics();
                plugin.getLogger().info("统计信息: " + statistics.size() + " 项");
            } else {
                // 数据库未连接，显示空记录
                records = new java.util.ArrayList<>();
                statistics = new java.util.HashMap<>();
                plugin.getLogger().warning("处罚管理器未启用，显示空记录页面");
                plugin.getLogger().warning("punishmentManager == null: " + (punishmentManager == null));
                if (punishmentManager != null) {
                    plugin.getLogger().warning("punishmentManager.isEnabled(): " + punishmentManager.isEnabled());
                }
            }

            // 生成页面HTML（支持过滤器）
            String html = pageGenerator.generatePunishmentPage(null, records, page, totalPages, statistics,
                    searchQuery, isPlayerView, playerParam, staffParam);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取所有处罚记录时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取处罚记录失败");
        }
    }

    /**
     * 处理特定类型的处罚记录请求
     */
    private void handleSpecificPunishment(HttpExchange exchange, String typeStr, Map<String, String> params)
            throws IOException {
        try {
            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendErrorResponse(exchange, 404, "未知的处罚类型");
                return;
            }

            // 获取分页参数
            int page = parseInt(params.get("page"), 1);
            int pageSize = plugin.getConfig().getInt("litebans.display.records-per-page", 10);

            // 检查是否有玩家参数（类似 next-litebans 的 ?player=PlayerName）
            String playerParam = params.get("player");
            String staffParam = params.get("staff");
            String searchQuery = params.get("search");
            // 过滤不等于玩家视图，过滤只是在当前页面添加过滤条件
            boolean isPlayerView = false;
            boolean isStaffFilter = staffParam != null && !staffParam.isEmpty();

            // 按照 next-litebans 逻辑，不再将过滤参数转换为搜索查询
            // 而是直接使用过滤参数进行数据库查询

            List<PunishmentRecord> records;
            int totalPages = 1;
            Map<String, Object> statistics;

            plugin.getLogger().info("=== 处理处罚记录请求 ===");
            plugin.getLogger().info("请求类型: " + type.getDisplayName());
            plugin.getLogger().info("页码: " + page + ", 每页: " + pageSize);
            plugin.getLogger().info("搜索查询: " + searchQuery);
            plugin.getLogger().info("玩家参数: " + playerParam);
            plugin.getLogger().info("是否玩家视图: " + isPlayerView);
            plugin.getLogger().info("处罚管理器状态: " + (punishmentManager != null ? "存在" : "null"));
            plugin.getLogger()
                    .info("处罚管理器启用状态: " + (punishmentManager != null ? punishmentManager.isEnabled() : "N/A"));

            // 检查处罚管理器是否可用
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                plugin.getLogger().info("开始获取处罚记录...");

                // 按照 next-litebans 逻辑处理过滤
                if ((playerParam != null && !playerParam.trim().isEmpty())
                        || (staffParam != null && !staffParam.trim().isEmpty())) {
                    plugin.getLogger().info("执行过滤查询 - 玩家: " + playerParam + ", 执行者: " + staffParam);
                    records = punishmentManager.searchPunishmentRecords(type, playerParam, staffParam, page, pageSize);
                } else if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                    plugin.getLogger().info("执行搜索查询: " + searchQuery);
                    records = punishmentManager.searchPunishmentRecords(type, searchQuery, page, pageSize);
                } else {
                    plugin.getLogger().info("获取所有记录");
                    records = punishmentManager.getPunishmentRecords(type, page, pageSize);
                }

                plugin.getLogger().info("获取到 " + records.size() + " 条记录");

                // 计算总页数
                int totalRecords;
                if (searchQuery != null && !searchQuery.trim().isEmpty()) {
                    // 搜索模式下，简化处理总数
                    totalRecords = records.size() + (page - 1) * pageSize;
                    if (records.size() < pageSize) {
                        totalRecords = (page - 1) * pageSize + records.size();
                    } else {
                        totalRecords = page * pageSize + 1; // 假设还有更多记录
                    }
                } else {
                    totalRecords = punishmentManager.getPunishmentCount(type);
                }
                totalPages = (int) Math.ceil((double) totalRecords / pageSize);

                plugin.getLogger().info("总记录数: " + totalRecords + ", 总页数: " + totalPages);

                // 获取统计信息
                statistics = punishmentManager.getPunishmentStatistics();
                plugin.getLogger().info("统计信息: " + statistics.size() + " 项");
            } else {
                // 数据库未连接，显示空记录
                records = new java.util.ArrayList<>();
                statistics = new java.util.HashMap<>();
                plugin.getLogger().warning("处罚管理器未启用，显示空" + type.getDisplayName() + "记录页面");
                plugin.getLogger().warning("punishmentManager == null: " + (punishmentManager == null));
                if (punishmentManager != null) {
                    plugin.getLogger().warning("punishmentManager.isEnabled(): " + punishmentManager.isEnabled());
                }
            }

            // 生成页面HTML（支持过滤器）
            String html = pageGenerator.generatePunishmentPage(type, records, page, totalPages, statistics,
                    searchQuery, isPlayerView, playerParam, staffParam);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取" + typeStr + "记录时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取处罚记录失败");
        }
    }

    /**
     * 解析处罚类型
     */
    private PunishmentRecord.PunishmentType parseType(String typeStr) {
        if (typeStr == null)
            return null;

        switch (typeStr.toLowerCase()) {
            case "ban":
            case "bans":
                return PunishmentRecord.PunishmentType.BAN;
            case "mute":
            case "mutes":
                return PunishmentRecord.PunishmentType.MUTE;
            case "warn":
            case "warns":
            case "warning":
            case "warnings":
                return PunishmentRecord.PunishmentType.WARN;
            case "kick":
            case "kicks":
                return PunishmentRecord.PunishmentType.KICK;
            default:
                return null;
        }
    }

    /**
     * 解析整数参数
     */
    private int parseInt(String value, int defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }

    /**
     * 解析查询参数
     */
    private Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    try {
                        String key = java.net.URLDecoder.decode(keyValue[0], "UTF-8");
                        String value = java.net.URLDecoder.decode(keyValue[1], "UTF-8");
                        params.put(key, value);
                    } catch (Exception e) {
                        // 忽略解码错误
                    }
                }
            }
        }
        return params;
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String content)
            throws IOException {
        byte[] bytes = content.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");
        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        String html = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>错误</title></head>" +
                "<body style=\"font-family: Arial, sans-serif; text-align: center; padding: 50px;\">" +
                "<h1>" + statusCode + " - " + message + "</h1>" +
                "<a href=\"/\">返回首页</a>" +
                "</body></html>";
        sendResponse(exchange, statusCode, "text/html; charset=utf-8", html);
    }

    /**
     * 检查字符串是否为数字
     */
    private boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 处理处罚详细信息页面请求（按照 next-litebans 详细页面）
     */
    private void handlePunishmentDetail(HttpExchange exchange, String typeStr, String idStr) throws IOException {
        try {
            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendErrorResponse(exchange, 404, "未知的处罚类型");
                return;
            }

            // 解析ID
            long id;
            try {
                id = Long.parseLong(idStr);
            } catch (NumberFormatException e) {
                sendErrorResponse(exchange, 400, "无效的记录ID");
                return;
            }

            plugin.getLogger().info("=== 处理处罚详细信息请求 ===");
            plugin.getLogger().info("类型: " + type.getDisplayName() + ", ID: " + id);

            // 获取处罚记录详情
            PunishmentRecord record = null;
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                record = punishmentManager.getPunishmentById(type, id);
            }

            if (record == null) {
                sendErrorResponse(exchange, 404, "未找到指定的处罚记录");
                return;
            }

            // 生成详细信息页面HTML（按照 next-litebans 详细页面布局）
            String html = pageGenerator.generatePunishmentDetailPage(record);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取处罚详细信息时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取处罚详细信息失败");
        }
    }

    /**
     * 处理玩家专用页面请求（按照 next-litebans /@{player} 逻辑）
     */
    private void handlePlayerPage(HttpExchange exchange, String playerName, Map<String, String> params)
            throws IOException {
        try {
            // URL解码玩家名称
            playerName = java.net.URLDecoder.decode(playerName, "UTF-8");

            plugin.getLogger().info("=== 处理玩家页面请求 ===");
            plugin.getLogger().info("玩家名称: " + playerName);

            // 检查玩家是否存在
            if (punishmentManager != null && punishmentManager.isEnabled()) {
                // 简单检查：尝试获取玩家的处罚记录
                List<PunishmentRecord> playerRecords = punishmentManager.getPunishmentsByPlayer(playerName, 1, 1);

                // 如果没有记录，检查玩家名是否有效
                if (playerRecords.isEmpty()) {
                    // 检查玩家名格式
                    if (!isValidPlayerName(playerName)) {
                        sendErrorResponse(exchange, 404, "无效的玩家名称");
                        return;
                    }
                }
            }

            // 获取分页参数
            int page = parseInt(params.get("page"), 1);
            String staffParam = params.get("staff");
            String playerParam = params.get("player");
            String typeParam = params.get("type");

            // 解析类型参数
            PunishmentRecord.PunishmentType typeFilter = null;
            if (typeParam != null && !typeParam.trim().isEmpty()) {
                typeFilter = parseType(typeParam);
                plugin.getLogger().info("类型过滤: " + typeParam + " -> " + typeFilter);
            }

            // 生成管理员玩家专用页面HTML（使用管理员样式和布局）
            String html = pageGenerator.generateAdminPlayerPage(playerName, page, playerParam, staffParam, typeFilter);

            // 发送响应
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);

        } catch (Exception e) {
            plugin.getLogger().severe("获取玩家页面时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "获取玩家页面失败");
        }
    }

    /**
     * 检查玩家名是否有效
     */
    private boolean isValidPlayerName(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return false;
        }

        String cleanName = playerName.trim();
        return cleanName.length() >= 3 && cleanName.length() <= 16 &&
                cleanName.matches("[a-zA-Z0-9_]+");
    }

    /**
     * 验证管理员权限 - 支持会话验证和API密钥验证，并检查封禁权限
     */
    private boolean isAuthorized(HttpExchange exchange) {
        // 首先检查会话
        if (isValidSession(exchange)) {
            // 获取当前登录的用户名并检查封禁权限
            String sessionId = getSessionFromCookie(exchange);
            String username = webServer.getAdminLoginHandler().getUsernameFromSession(sessionId);
            if (username != null) {
                return hasPunishmentPermission(username);
            }
            return false;
        }

        // 然后检查URL中的API密钥（向后兼容）
        String query = exchange.getRequestURI().getQuery();
        Map<String, String> params = parseQuery(query);
        String providedKey = params.get("key");
        // API密钥登录默认拥有所有权限
        return isValidAdminKey(providedKey);
    }

    /**
     * 验证会话
     */
    private boolean isValidSession(HttpExchange exchange) {
        try {
            // 获取Cookie中的会话ID
            String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
            if (cookieHeader == null) {
                return false;
            }

            String sessionId = null;
            String[] cookies = cookieHeader.split(";");
            for (String cookie : cookies) {
                String[] parts = cookie.trim().split("=", 2);
                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                    sessionId = parts[1];
                    break;
                }
            }

            if (sessionId == null) {
                return false;
            }

            // 验证会话是否有效
            return webServer.getAdminLoginHandler().isValidSession(sessionId);
        } catch (Exception e) {
            plugin.getLogger().warning("验证会话时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证管理员密钥
     */
    private boolean isValidAdminKey(String key) {
        return key != null && key.equals(webServer.getAdminKey());
    }

    /**
     * 检查用户是否有封禁权限
     */
    private boolean hasPunishmentPermission(String username) {
        try {
            AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);
            return accountHandler.hasPunishmentPermission(username);
        } catch (Exception e) {
            plugin.getLogger().warning("检查封禁权限时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从Cookie中获取会话ID
     */
    private String getSessionFromCookie(HttpExchange exchange) {
        String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
        if (cookieHeader != null) {
            String[] cookies = cookieHeader.split(";");
            for (String cookie : cookies) {
                String[] parts = cookie.trim().split("=", 2);
                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                    return parts[1];
                }
            }
        }
        return null;
    }

    /**
     * 处理未授权访问
     */
    private void handleUnauthorized(HttpExchange exchange) throws IOException {
        // 检查是否已登录但权限不足
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null && webServer.getAdminLoginHandler().isValidSession(sessionId)) {
            // 已登录但权限不足，显示权限不足页面
            String html = generatePermissionDeniedPage();
            sendResponse(exchange, 403, "text/html; charset=utf-8", html);
        } else {
            // 未登录，重定向到登录页面
            exchange.getResponseHeaders().set("Location", "/admin/login");
            sendResponse(exchange, 302, "text/plain", "Redirecting to login...");
        }
    }

    /**
     * 生成权限不足页面
     */
    private String generatePermissionDeniedPage() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>权限不足 - AceKey系统</title>\n" +
                "    <style>\n" +
                "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 0; min-height: 100vh; display: flex; align-items: center; justify-content: center; }\n" +
                "        .container { background: white; border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; }\n" +
                "        .icon { font-size: 80px; color: #ff6b6b; margin-bottom: 20px; }\n" +
                "        h1 { color: #333; margin-bottom: 20px; font-size: 28px; }\n" +
                "        p { color: #666; margin-bottom: 30px; font-size: 16px; line-height: 1.6; }\n" +
                "        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; border: none; border-radius: 25px; text-decoration: none; display: inline-block; font-size: 16px; transition: transform 0.3s ease; }\n" +
                "        .btn:hover { transform: translateY(-2px); }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <div class=\"icon\">🚫</div>\n" +
                "        <h1>权限不足</h1>\n" +
                "        <p>抱歉，您没有访问封禁管理页面的权限。<br>请联系超级管理员为您分配相应权限。</p>\n" +
                "        <a href=\"/admin\" class=\"btn\">返回管理控制台</a>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        try (java.io.BufferedReader reader = new java.io.BufferedReader(
                new java.io.InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            StringBuilder body = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        }
    }

    /**
     * 解析JSON请求
     */
    private org.json.simple.JSONObject parseJsonRequest(String requestBody) {
        try {
            org.json.simple.parser.JSONParser parser = new org.json.simple.parser.JSONParser();
            return (org.json.simple.JSONObject) parser.parse(requestBody);
        } catch (Exception e) {
            plugin.getLogger().warning("解析JSON请求失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpExchange exchange, int statusCode, boolean success, String message)
            throws IOException {
        org.json.simple.JSONObject response = new org.json.simple.JSONObject();
        response.put("success", success);
        response.put("message", message);

        sendResponse(exchange, statusCode, "application/json; charset=utf-8", response.toJSONString());
    }

    /**
     * 发送JSON响应（带数据）
     */
    private void sendJsonResponse(HttpExchange exchange, int statusCode, boolean success, String message, Object data)
            throws IOException {
        org.json.simple.JSONObject response = new org.json.simple.JSONObject();
        response.put("success", success);
        response.put("message", message);
        if (data != null) {
            response.put("data", data);
        }

        sendResponse(exchange, statusCode, "application/json; charset=utf-8", response.toJSONString());
    }

    /**
     * 处理撤销处罚请求
     */
    private void handleRevokePunishment(HttpExchange exchange, org.json.simple.JSONObject request) throws IOException {
        try {
            // 获取参数
            Object recordIdObj = request.get("recordId");
            String typeStr = (String) request.get("type");
            String reason = (String) request.get("reason");

            if (recordIdObj == null || typeStr == null) {
                sendJsonResponse(exchange, 400, false, "缺少必要参数");
                return;
            }

            // 解析记录ID
            long recordId;
            try {
                if (recordIdObj instanceof Number) {
                    recordId = ((Number) recordIdObj).longValue();
                } else {
                    recordId = Long.parseLong(recordIdObj.toString());
                }
            } catch (NumberFormatException e) {
                sendJsonResponse(exchange, 400, false, "无效的记录ID");
                return;
            }

            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendJsonResponse(exchange, 400, false, "无效的处罚类型");
                return;
            }

            plugin.getLogger().info("=== 处理撤销处罚请求 ===");
            plugin.getLogger().info("记录ID: " + recordId + ", 类型: " + type.getDisplayName() + ", 原因: " + reason);

            // 检查处罚管理器是否可用
            if (punishmentManager == null || !punishmentManager.isEnabled()) {
                sendJsonResponse(exchange, 503, false, "处罚管理器不可用");
                return;
            }

            // 获取当前记录
            PunishmentRecord record = punishmentManager.getPunishmentById(type, recordId);
            if (record == null) {
                sendJsonResponse(exchange, 404, false, "未找到指定的处罚记录");
                return;
            }

            if (!record.isActive()) {
                sendJsonResponse(exchange, 400, false, "该处罚记录已经不是生效状态");
                return;
            }

            // 执行撤销操作
            boolean success = punishmentManager.revokePunishment(type, recordId, "管理员",
                    reason != null ? reason : "管理员撤销");

            if (success) {
                plugin.getLogger().info("成功撤销处罚记录: " + recordId);
                sendJsonResponse(exchange, 200, true, "处罚记录已成功撤销");
            } else {
                plugin.getLogger().warning("撤销处罚记录失败: " + recordId);
                sendJsonResponse(exchange, 500, false, "撤销处罚记录失败");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理撤销处罚请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendJsonResponse(exchange, 500, false, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理批量撤销处罚请求
     */
    private void handleBatchRevokePunishment(HttpExchange exchange, org.json.simple.JSONObject request)
            throws IOException {
        try {
            // 获取参数
            org.json.simple.JSONArray recordIdsArray = (org.json.simple.JSONArray) request.get("recordIds");
            String typeStr = (String) request.get("type");
            String reason = (String) request.get("reason");

            if (recordIdsArray == null || typeStr == null) {
                sendJsonResponse(exchange, 400, false, "缺少必要参数");
                return;
            }

            // 解析记录ID列表
            java.util.List<Long> recordIds = new java.util.ArrayList<>();
            try {
                for (Object obj : recordIdsArray) {
                    if (obj instanceof Number) {
                        recordIds.add(((Number) obj).longValue());
                    } else {
                        recordIds.add(Long.parseLong(obj.toString()));
                    }
                }
            } catch (NumberFormatException e) {
                sendJsonResponse(exchange, 400, false, "无效的记录ID列表");
                return;
            }

            if (recordIds.isEmpty()) {
                sendJsonResponse(exchange, 400, false, "记录ID列表不能为空");
                return;
            }

            // 解析处罚类型
            PunishmentRecord.PunishmentType type = parseType(typeStr);
            if (type == null) {
                sendJsonResponse(exchange, 400, false, "无效的处罚类型");
                return;
            }

            plugin.getLogger().info("=== 处理批量撤销处罚请求 ===");
            plugin.getLogger().info("记录数量: " + recordIds.size() + ", 类型: " + type.getDisplayName() + ", 原因: " + reason);

            // 检查处罚管理器是否可用
            if (punishmentManager == null || !punishmentManager.isEnabled()) {
                sendJsonResponse(exchange, 503, false, "处罚管理器不可用");
                return;
            }

            // 执行批量撤销操作
            int successCount = punishmentManager.revokePunishments(type, recordIds, "管理员",
                    reason != null ? reason : "管理员批量撤销");

            if (successCount > 0) {
                String message = "成功撤销 " + successCount + "/" + recordIds.size() + " 个处罚记录";
                plugin.getLogger().info(message);
                sendJsonResponse(exchange, 200, true, message);
            } else {
                plugin.getLogger().warning("批量撤销处罚记录失败，没有记录被撤销");
                sendJsonResponse(exchange, 400, false, "没有记录被撤销，可能记录不存在或已经不是生效状态");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理批量撤销处罚请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendJsonResponse(exchange, 500, false, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理数据更新检测请求
     */
    private void handleCheckDataUpdate(HttpExchange exchange, org.json.simple.JSONObject request) throws IOException {
        try {
            // 获取参数
            String page = (String) request.get("page");
            String playerFilter = (String) request.get("player");
            String staffFilter = (String) request.get("staff");
            String typeFilter = (String) request.get("type");

            plugin.getLogger().info("=== 处理数据更新检测请求 ===");
            plugin.getLogger()
                    .info("页码: " + page + ", 玩家: " + playerFilter + ", 执行者: " + staffFilter + ", 类型: " + typeFilter);

            // 解析参数
            int pageNum = parseInt(page, 1);
            int pageSize = plugin.getConfig().getInt("litebans.display.records-per-page", 10);
            PunishmentRecord.PunishmentType type = null;
            if (typeFilter != null && !typeFilter.trim().isEmpty()) {
                type = parseType(typeFilter);
            }

            // 获取当前数据
            List<PunishmentRecord> records = new java.util.ArrayList<>();
            Map<String, Object> statistics = new java.util.HashMap<>();

            if (punishmentManager != null && punishmentManager.isEnabled()) {
                // 根据过滤条件获取数据
                if (type != null) {
                    // 特定类型的记录
                    if ((playerFilter != null && !playerFilter.trim().isEmpty()) ||
                            (staffFilter != null && !staffFilter.trim().isEmpty())) {
                        records = punishmentManager.searchPunishmentRecords(type, playerFilter, staffFilter, pageNum,
                                pageSize);
                    } else {
                        records = punishmentManager.getPunishmentRecords(type, pageNum, pageSize);
                    }
                } else {
                    // 所有类型的记录
                    if ((playerFilter != null && !playerFilter.trim().isEmpty()) ||
                            (staffFilter != null && !staffFilter.trim().isEmpty())) {
                        // 获取所有类型的过滤记录
                        for (PunishmentRecord.PunishmentType recordType : PunishmentRecord.PunishmentType.values()) {
                            List<PunishmentRecord> typeRecords = punishmentManager.searchPunishmentRecords(recordType,
                                    playerFilter, staffFilter, 1, pageSize * 10);
                            records.addAll(typeRecords);
                        }

                        // 按时间排序并分页
                        records.sort((a, b) -> {
                            if (a.getTime() == null && b.getTime() == null)
                                return 0;
                            if (a.getTime() == null)
                                return 1;
                            if (b.getTime() == null)
                                return -1;
                            return b.getTime().compareTo(a.getTime());
                        });

                        int start = (pageNum - 1) * pageSize;
                        int end = Math.min(start + pageSize, records.size());
                        records = (start < records.size()) ? records.subList(start, end) : new java.util.ArrayList<>();
                    } else {
                        records = punishmentManager.getAllPunishmentRecords(pageNum, pageSize);
                    }
                }

                // 获取统计信息
                statistics = punishmentManager.getPunishmentStatistics();
            }

            // 生成数据哈希值（用于检测变化）
            String dataHash = generateDataHash(records, statistics);

            // 生成表格行HTML（用于页面更新）
            String tableRowsHtml = "";
            if (!records.isEmpty()) {
                StringBuilder tableBuilder = new StringBuilder();

                // 检查是否是玩家页面（通过类型过滤参数判断）
                // 如果有玩家过滤参数且没有类型过滤参数，很可能是玩家页面
                boolean isPlayerPage = (playerFilter != null && !playerFilter.isEmpty() &&
                        (typeFilter == null || typeFilter.isEmpty()));

                for (PunishmentRecord record : records) {
                    if (isPlayerPage) {
                        // 玩家页面使用普通表格行
                        tableBuilder.append(pageGenerator.generateTableRow(record));
                    } else {
                        // 管理员页面使用管理员表格行
                        tableBuilder.append(pageGenerator.generateAdminTableRow(record));
                    }
                }
                tableRowsHtml = tableBuilder.toString();

                plugin.getLogger().info("生成表格行HTML - 是否玩家页面: " + isPlayerPage + ", 记录数: " + records.size());
            }

            // 构建响应
            org.json.simple.JSONObject response = new org.json.simple.JSONObject();
            response.put("success", true);
            response.put("dataHash", dataHash);
            response.put("recordCount", records.size());
            response.put("statistics", statistics);
            response.put("tableRows", tableRowsHtml);

            plugin.getLogger().info("数据检测完成，哈希值: " + dataHash + ", 记录数: " + records.size());

            sendResponse(exchange, 200, "application/json; charset=utf-8", response.toJSONString());

        } catch (Exception e) {
            plugin.getLogger().severe("处理数据更新检测请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendJsonResponse(exchange, 500, false, "数据检测失败");
        }
    }

    /**
     * 生成数据哈希值（用于检测数据变化）
     */
    private String generateDataHash(List<PunishmentRecord> records, Map<String, Object> statistics) {
        try {
            StringBuilder dataBuilder = new StringBuilder();

            // 添加记录信息到哈希计算
            for (PunishmentRecord record : records) {
                dataBuilder.append(record.getId())
                        .append(record.getType())
                        .append(record.isActive())
                        .append(record.getPlayerName())
                        .append(record.getBannedByName())
                        .append(record.getReason());
                if (record.getTime() != null) {
                    dataBuilder.append(record.getTime().toString());
                }
                if (record.getUntil() != null) {
                    dataBuilder.append(record.getUntil().toString());
                }
            }

            // 添加统计信息到哈希计算
            for (Map.Entry<String, Object> entry : statistics.entrySet()) {
                dataBuilder.append(entry.getKey()).append(entry.getValue());
            }

            // 计算简单哈希值
            String data = dataBuilder.toString();
            return String.valueOf(data.hashCode());

        } catch (Exception e) {
            plugin.getLogger().warning("生成数据哈希值时出错: " + e.getMessage());
            return String.valueOf(System.currentTimeMillis());
        }
    }
}
