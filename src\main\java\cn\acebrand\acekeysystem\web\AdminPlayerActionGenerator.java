package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentRecord;

/**
 * 管理员玩家页面操作功能生成器
 * 负责生成封禁、禁言等直接操作按钮和相关功能
 */
public class AdminPlayerActionGenerator {

        private final AceKeySystem plugin;

        public AdminPlayerActionGenerator(AceKeySystem plugin) {
                this.plugin = plugin;
        }

        /**
         * 生成管理员操作按钮区域
         */
        public String generateAdminActionButtons(String playerName) {
                StringBuilder html = new StringBuilder();

                html.append("            <div class=\"admin-action-section\">\n");
                html.append("                <h3 class=\"action-section-title\">🛠️ 管理员操作</h3>\n");
                html.append("                <div class=\"action-buttons-grid\">\n");

                // 封禁按钮
                html.append("                    <button class=\"action-btn ban-btn\" onclick=\"showBanModal('")
                                .append(escapeHtml(playerName)).append("')\">\n");
                html.append("                        <span class=\"btn-icon\">🚫</span>\n");
                html.append("                        <span class=\"btn-text\">封禁玩家</span>\n");
                html.append("                    </button>\n");

                // 禁言按钮
                html.append("                    <button class=\"action-btn mute-btn\" onclick=\"showMuteModal('")
                                .append(escapeHtml(playerName)).append("')\">\n");
                html.append("                        <span class=\"btn-icon\">🔇</span>\n");
                html.append("                        <span class=\"btn-text\">禁言玩家</span>\n");
                html.append("                    </button>\n");

                // 警告按钮
                html.append("                    <button class=\"action-btn warn-btn\" onclick=\"showWarnModal('")
                                .append(escapeHtml(playerName)).append("')\">\n");
                html.append("                        <span class=\"btn-icon\">⚠️</span>\n");
                html.append("                        <span class=\"btn-text\">警告玩家</span>\n");
                html.append("                    </button>\n");

                // 踢出按钮
                html.append("                    <button class=\"action-btn kick-btn\" onclick=\"showKickModal('")
                                .append(escapeHtml(playerName)).append("')\">\n");
                html.append("                        <span class=\"btn-icon\">👢</span>\n");
                html.append("                        <span class=\"btn-text\">踢出玩家</span>\n");
                html.append("                    </button>\n");

                html.append("                </div>\n");
                html.append("            </div>\n");

                return html.toString();
        }

        /**
         * 生成封禁模态框
         */
        public String generateBanModal() {
                StringBuilder html = new StringBuilder();

                html.append("    <!-- 封禁模态框 -->\n");
                html.append("    <div id=\"banModal\" class=\"action-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"modal-overlay\" onclick=\"closeBanModal()\"></div>\n");
                html.append("        <div class=\"modal-content ban-modal-content\">\n");
                html.append("            <div class=\"modal-header\">\n");
                html.append("                <h3 class=\"modal-title\" id=\"banModalTitle\">🚫 封禁玩家</h3>\n");
                html.append("                <button class=\"modal-close\" onclick=\"closeBanModal()\">✕</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-body\">\n");
                html.append("                <form id=\"banForm\">\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label for=\"banPlayer\">玩家名称</label>\n");
                html.append("                        <input type=\"text\" id=\"banPlayer\" name=\"player\" readonly>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 模板选择区域 -->\n");
                html.append("                    <div class=\"form-group\" id=\"banTemplateSection\">\n");
                html.append("                        <label>选择封禁模板</label>\n");
                html.append("                        <div class=\"template-tabs\">\n");
                html.append("                            <button type=\"button\" class=\"template-tab active\" ");
                html.append("onclick=\"switchBanTemplate('template')\">使用模板</button>\n");
                html.append("                            <button type=\"button\" class=\"template-tab\" ");
                html.append("onclick=\"switchBanTemplate('custom')\">自定义</button>\n");
                html.append("                        </div>\n");
                html.append("                        \n");
                html.append("                        <div id=\"templateOptions\" class=\"template-options\">\n");
                html.append("                            <div class=\"template-grid\">\n");
                html.append(
                                "                                <div class=\"template-item\" onclick=\"selectBanTemplate('grief', '恶意破坏', '7d')\">\n");
                html.append("                                    <div class=\"template-icon\">🏗️</div>\n");
                html.append("                                    <div class=\"template-name\">恶意破坏</div>\n");
                html.append("                                    <div class=\"template-duration\">7天</div>\n");
                html.append("                                </div>\n");
                html.append(
                                "                                <div class=\"template-item\" onclick=\"selectBanTemplate('cheat', '使用作弊', '30d')\">\n");
                html.append("                                    <div class=\"template-icon\">⚡</div>\n");
                html.append("                                    <div class=\"template-name\">使用作弊</div>\n");
                html.append("                                    <div class=\"template-duration\">30天</div>\n");
                html.append("                                </div>\n");
                html.append(
                                "                                <div class=\"template-item\" onclick=\"selectBanTemplate('toxic', '恶意言论', '3d')\">\n");
                html.append("                                    <div class=\"template-icon\">💬</div>\n");
                html.append("                                    <div class=\"template-name\">恶意言论</div>\n");
                html.append("                                    <div class=\"template-duration\">3天</div>\n");
                html.append("                                </div>\n");
                html.append(
                                "                                <div class=\"template-item\" onclick=\"selectBanTemplate('spam', '恶意刷屏', '1d')\">\n");
                html.append("                                    <div class=\"template-icon\">📢</div>\n");
                html.append("                                    <div class=\"template-name\">恶意刷屏</div>\n");
                html.append("                                    <div class=\"template-duration\">1天</div>\n");
                html.append("                                </div>\n");
                html.append(
                                "                                <div class=\"template-item\" onclick=\"selectBanTemplate('exploit', '利用漏洞', '14d')\">\n");
                html.append("                                    <div class=\"template-icon\">🐛</div>\n");
                html.append("                                    <div class=\"template-name\">利用漏洞</div>\n");
                html.append("                                    <div class=\"template-duration\">14天</div>\n");
                html.append("                                </div>\n");
                html.append(
                                "                                <div class=\"template-item\" onclick=\"selectBanTemplate('serious', '严重违规', 'permanent')\">\n");
                html.append("                                    <div class=\"template-icon\">🚨</div>\n");
                html.append("                                    <div class=\"template-name\">严重违规</div>\n");
                html.append("                                    <div class=\"template-duration\">永久</div>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 自定义选项区域 -->\n");
                html.append(
                                "                    <div id=\"customOptions\" class=\"custom-options\" style=\"display: none;\">\n");
                html.append("                        <div class=\"form-group\">\n");
                html.append("                            <label for=\"banReason\">封禁原因</label>\n");
                html.append("                            <input type=\"text\" id=\"banReason\" name=\"reason\" ");
                html.append("placeholder=\"请输入封禁原因\" required>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"form-group\">\n");
                html.append("                            <label for=\"banDuration\">封禁时长</label>\n");
                html.append("                            <select id=\"banDuration\" name=\"duration\">\n");
                html.append("                                <option value=\"1h\">1小时</option>\n");
                html.append("                                <option value=\"6h\">6小时</option>\n");
                html.append("                                <option value=\"1d\">1天</option>\n");
                html.append("                                <option value=\"3d\">3天</option>\n");
                html.append("                                <option value=\"7d\" selected>7天</option>\n");
                html.append("                                <option value=\"14d\">14天</option>\n");
                html.append("                                <option value=\"30d\">30天</option>\n");
                html.append("                                <option value=\"permanent\">永久</option>\n");
                html.append("                            </select>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label>\n");
                html.append("                            <input type=\"checkbox\" id=\"banSilent\" name=\"silent\">\n");
                html.append("                            静默封禁（不通知玩家）\n");
                html.append("                        </label>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 隐藏字段存储选中的模板信息 -->\n");
                html.append("                    <input type=\"hidden\" id=\"selectedTemplate\" name=\"template\">\n");
                html.append("                    <input type=\"hidden\" id=\"templateReason\" name=\"templateReason\">\n");
                html.append("                    <input type=\"hidden\" id=\"templateDuration\" name=\"templateDuration\">\n");
                html.append("                </form>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-footer\">\n");
                html.append("                <button class=\"btn btn-secondary\" onclick=\"closeBanModal()\">取消</button>\n");
                html.append(
                                "                <button class=\"btn btn-danger\" id=\"banSubmitBtn\" onclick=\"submitBan()\">确认封禁</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                return html.toString();
        }

        /**
         * 生成禁言模态框
         */
        public String generateMuteModal() {
                StringBuilder html = new StringBuilder();

                html.append("    <!-- 禁言模态框 -->\n");
                html.append("    <div id=\"muteModal\" class=\"action-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"modal-overlay\" onclick=\"closeMuteModal()\"></div>\n");
                html.append("        <div class=\"modal-content ban-modal-content\">\n");
                html.append("            <div class=\"modal-header\">\n");
                html.append("                <h3 class=\"modal-title\" id=\"muteModalTitle\">🔇 禁言玩家</h3>\n");
                html.append("                <button class=\"modal-close\" onclick=\"closeMuteModal()\">✕</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-body\">\n");
                html.append("                <form id=\"muteForm\">\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label for=\"mutePlayer\">玩家名称</label>\n");
                html.append("                        <input type=\"text\" id=\"mutePlayer\" name=\"player\" readonly>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 模板选择区域 -->\n");
                html.append("                    <div class=\"form-group\" id=\"muteTemplateSection\">\n");
                html.append("                        <label>选择禁言模板</label>\n");
                html.append("                        <div class=\"template-tabs\">\n");
                html.append("                            <button type=\"button\" class=\"template-tab active\" ");
                html.append("onclick=\"switchMuteTemplate('template')\">使用模板</button>\n");
                html.append("                            <button type=\"button\" class=\"template-tab\" ");
                html.append("onclick=\"switchMuteTemplate('custom')\">自定义</button>\n");
                html.append("                        </div>\n");
                html.append("                        \n");
                html.append("                        <div id=\"muteTemplateOptions\" class=\"template-options\">\n");
                html.append("                            <div class=\"template-grid\">\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectMuteTemplate('spam', '恶意刷屏', '1h')\">\n");
                html.append("                                    <div class=\"template-icon\">📢</div>\n");
                html.append("                                    <div class=\"template-name\">恶意刷屏</div>\n");
                html.append("                                    <div class=\"template-duration\">1小时</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectMuteTemplate('toxic', '恶意言论', '6h')\">\n");
                html.append("                                    <div class=\"template-icon\">💬</div>\n");
                html.append("                                    <div class=\"template-name\">恶意言论</div>\n");
                html.append("                                    <div class=\"template-duration\">6小时</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectMuteTemplate('advertise', '恶意广告', '1d')\">\n");
                html.append("                                    <div class=\"template-icon\">📺</div>\n");
                html.append("                                    <div class=\"template-name\">恶意广告</div>\n");
                html.append("                                    <div class=\"template-duration\">1天</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectMuteTemplate('harassment', '骚扰他人', '3d')\">\n");
                html.append("                                    <div class=\"template-icon\">😠</div>\n");
                html.append("                                    <div class=\"template-name\">骚扰他人</div>\n");
                html.append("                                    <div class=\"template-duration\">3天</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectMuteTemplate('serious', '严重违规', 'permanent')\">\n");
                html.append("                                    <div class=\"template-icon\">🚨</div>\n");
                html.append("                                    <div class=\"template-name\">严重违规</div>\n");
                html.append("                                    <div class=\"template-duration\">永久</div>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 自定义选项区域 -->\n");
                html.append("                    <div id=\"muteCustomOptions\" class=\"custom-options\" style=\"display: none;\">\n");
                html.append("                        <div class=\"form-group\">\n");
                html.append("                            <label for=\"muteReason\">禁言原因</label>\n");
                html.append("                            <input type=\"text\" id=\"muteReason\" name=\"reason\" ");
                html.append("placeholder=\"请输入禁言原因\" required>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"form-group\">\n");
                html.append("                            <label for=\"muteDuration\">禁言时长</label>\n");
                html.append("                            <select id=\"muteDuration\" name=\"duration\">\n");
                html.append("                                <option value=\"30m\">30分钟</option>\n");
                html.append("                                <option value=\"1h\" selected>1小时</option>\n");
                html.append("                                <option value=\"6h\">6小时</option>\n");
                html.append("                                <option value=\"1d\">1天</option>\n");
                html.append("                                <option value=\"3d\">3天</option>\n");
                html.append("                                <option value=\"7d\">7天</option>\n");
                html.append("                                <option value=\"permanent\">永久</option>\n");
                html.append("                            </select>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label>\n");
                html.append("                            <input type=\"checkbox\" id=\"muteSilent\" name=\"silent\">\n");
                html.append("                            静默禁言（不通知玩家）\n");
                html.append("                        </label>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 隐藏字段存储选中的模板信息 -->\n");
                html.append("                    <input type=\"hidden\" id=\"muteSelectedTemplate\" name=\"template\">\n");
                html.append("                    <input type=\"hidden\" id=\"muteTemplateReason\" name=\"templateReason\">\n");
                html.append("                    <input type=\"hidden\" id=\"muteTemplateDuration\" name=\"templateDuration\">\n");
                html.append("                </form>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-footer\">\n");
                html.append("                <button class=\"btn btn-secondary\" onclick=\"closeMuteModal()\">取消</button>\n");
                html.append("                <button class=\"btn btn-warning\" id=\"muteSubmitBtn\" onclick=\"submitMute()\">确认禁言</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                return html.toString();
        }

        /**
         * 生成解除禁言模态框
         */
        public String generateUnmuteModal() {
                StringBuilder html = new StringBuilder();

                html.append("    <!-- 解除禁言模态框 -->\n");
                html.append("    <div id=\"unmuteModal\" class=\"action-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"modal-overlay\" onclick=\"closeUnmuteModal()\"></div>\n");
                html.append("        <div class=\"modal-content\">\n");
                html.append("            <div class=\"modal-header\">\n");
                html.append("                <h3 class=\"modal-title\">🔊 解除禁言</h3>\n");
                html.append("                <button class=\"modal-close\" onclick=\"closeUnmuteModal()\">&times;</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-body\">\n");
                html.append("                <form id=\"unmuteForm\">\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label for=\"unmutePlayer\">玩家名称</label>\n");
                html.append("                        <input type=\"text\" id=\"unmutePlayer\" name=\"player\" readonly>\n");
                html.append("                    </div>\n");
                html.append("                    <p style=\"color: #666; font-size: 14px; margin-top: 10px;\">\n");
                html.append("                        确定要解除对该玩家的禁言吗？此操作将立即生效。\n");
                html.append("                    </p>\n");
                html.append("                </form>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-footer\">\n");
                html.append("                <button type=\"button\" class=\"btn btn-secondary\" onclick=\"closeUnmuteModal()\">取消</button>\n");
                html.append("                <button type=\"button\" class=\"btn btn-warning\" onclick=\"submitUnmute()\">确认解除禁言</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");
                html.append("    \n");

                return html.toString();
        }

        /**
         * 生成警告模态框
         */
        public String generateWarnModal() {
                StringBuilder html = new StringBuilder();

                html.append("    <!-- 警告模态框 -->\n");
                html.append("    <div id=\"warnModal\" class=\"action-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"modal-overlay\" onclick=\"closeWarnModal()\"></div>\n");
                html.append("        <div class=\"modal-content ban-modal-content\">\n");
                html.append("            <div class=\"modal-header\">\n");
                html.append("                <h3 class=\"modal-title\">⚠️ 警告玩家</h3>\n");
                html.append("                <button class=\"modal-close\" onclick=\"closeWarnModal()\">✕</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-body\">\n");
                html.append("                <form id=\"warnForm\">\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label for=\"warnPlayer\">玩家名称</label>\n");
                html.append("                        <input type=\"text\" id=\"warnPlayer\" name=\"player\" readonly>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 模板选择区域 -->\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label>选择警告模板</label>\n");
                html.append("                        <div class=\"template-tabs\">\n");
                html.append("                            <button type=\"button\" class=\"template-tab active\" ");
                html.append("onclick=\"switchWarnTemplate('template')\">使用模板</button>\n");
                html.append("                            <button type=\"button\" class=\"template-tab\" ");
                html.append("onclick=\"switchWarnTemplate('custom')\">自定义</button>\n");
                html.append("                        </div>\n");
                html.append("                        \n");
                html.append("                        <div id=\"warnTemplateOptions\" class=\"template-options\">\n");
                html.append("                            <div class=\"template-grid\">\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectWarnTemplate('behavior', '行为不当')\">\n");
                html.append("                                    <div class=\"template-icon\">😤</div>\n");
                html.append("                                    <div class=\"template-name\">行为不当</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectWarnTemplate('language', '言语不当')\">\n");
                html.append("                                    <div class=\"template-icon\">🤬</div>\n");
                html.append("                                    <div class=\"template-name\">言语不当</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectWarnTemplate('spam', '轻微刷屏')\">\n");
                html.append("                                    <div class=\"template-icon\">📢</div>\n");
                html.append("                                    <div class=\"template-name\">轻微刷屏</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectWarnTemplate('rules', '违反规则')\">\n");
                html.append("                                    <div class=\"template-icon\">📋</div>\n");
                html.append("                                    <div class=\"template-name\">违反规则</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectWarnTemplate('disturb', '干扰他人')\">\n");
                html.append("                                    <div class=\"template-icon\">😠</div>\n");
                html.append("                                    <div class=\"template-name\">干扰他人</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectWarnTemplate('warning', '最后警告')\">\n");
                html.append("                                    <div class=\"template-icon\">🚨</div>\n");
                html.append("                                    <div class=\"template-name\">最后警告</div>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 自定义选项区域 -->\n");
                html.append("                    <div id=\"warnCustomOptions\" class=\"custom-options\" style=\"display: none;\">\n");
                html.append("                        <div class=\"form-group\">\n");
                html.append("                            <label for=\"warnReason\">警告原因</label>\n");
                html.append("                            <input type=\"text\" id=\"warnReason\" name=\"reason\" ");
                html.append("placeholder=\"请输入警告原因\" required>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label>\n");
                html.append("                            <input type=\"checkbox\" id=\"warnSilent\" name=\"silent\">\n");
                html.append("                            静默警告（不通知玩家）\n");
                html.append("                        </label>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 隐藏字段存储选中的模板信息 -->\n");
                html.append("                    <input type=\"hidden\" id=\"warnSelectedTemplate\" name=\"template\">\n");
                html.append("                    <input type=\"hidden\" id=\"warnTemplateReason\" name=\"templateReason\">\n");
                html.append("                </form>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-footer\">\n");
                html.append("                <button class=\"btn btn-secondary\" onclick=\"closeWarnModal()\">取消</button>\n");
                html.append("                <button class=\"btn btn-warning\" onclick=\"submitWarn()\">确认警告</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                return html.toString();
        }

        /**
         * 生成踢出模态框
         */
        public String generateKickModal() {
                StringBuilder html = new StringBuilder();

                html.append("    <!-- 踢出模态框 -->\n");
                html.append("    <div id=\"kickModal\" class=\"action-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"modal-overlay\" onclick=\"closeKickModal()\"></div>\n");
                html.append("        <div class=\"modal-content ban-modal-content\">\n");
                html.append("            <div class=\"modal-header\">\n");
                html.append("                <h3 class=\"modal-title\">👢 踢出玩家</h3>\n");
                html.append("                <button class=\"modal-close\" onclick=\"closeKickModal()\">✕</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-body\">\n");
                html.append("                <form id=\"kickForm\">\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label for=\"kickPlayer\">玩家名称</label>\n");
                html.append("                        <input type=\"text\" id=\"kickPlayer\" name=\"player\" readonly>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 模板选择区域 -->\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label>选择踢出模板</label>\n");
                html.append("                        <div class=\"template-tabs\">\n");
                html.append("                            <button type=\"button\" class=\"template-tab active\" ");
                html.append("onclick=\"switchKickTemplate('template')\">使用模板</button>\n");
                html.append("                            <button type=\"button\" class=\"template-tab\" ");
                html.append("onclick=\"switchKickTemplate('custom')\">自定义</button>\n");
                html.append("                        </div>\n");
                html.append("                        \n");
                html.append("                        <div id=\"kickTemplateOptions\" class=\"template-options\">\n");
                html.append("                            <div class=\"template-grid\">\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectKickTemplate('afk', 'AFK挂机')\">\n");
                html.append("                                    <div class=\"template-icon\">😴</div>\n");
                html.append("                                    <div class=\"template-name\">AFK挂机</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectKickTemplate('spam', '恶意刷屏')\">\n");
                html.append("                                    <div class=\"template-icon\">📢</div>\n");
                html.append("                                    <div class=\"template-name\">恶意刷屏</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectKickTemplate('disturb', '干扰游戏')\">\n");
                html.append("                                    <div class=\"template-icon\">😠</div>\n");
                html.append("                                    <div class=\"template-name\">干扰游戏</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectKickTemplate('rules', '违反规则')\">\n");
                html.append("                                    <div class=\"template-icon\">📋</div>\n");
                html.append("                                    <div class=\"template-name\">违反规则</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectKickTemplate('warning', '警告踢出')\">\n");
                html.append("                                    <div class=\"template-icon\">⚠️</div>\n");
                html.append("                                    <div class=\"template-name\">警告踢出</div>\n");
                html.append("                                </div>\n");
                html.append("                                <div class=\"template-item\" onclick=\"selectKickTemplate('maintenance', '服务器维护')\">\n");
                html.append("                                    <div class=\"template-icon\">🔧</div>\n");
                html.append("                                    <div class=\"template-name\">服务器维护</div>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 自定义选项区域 -->\n");
                html.append("                    <div id=\"kickCustomOptions\" class=\"custom-options\" style=\"display: none;\">\n");
                html.append("                        <div class=\"form-group\">\n");
                html.append("                            <label for=\"kickReason\">踢出原因</label>\n");
                html.append("                            <input type=\"text\" id=\"kickReason\" name=\"reason\" ");
                html.append("placeholder=\"请输入踢出原因\" required>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"form-group\">\n");
                html.append("                        <label>\n");
                html.append("                            <input type=\"checkbox\" id=\"kickSilent\" name=\"silent\">\n");
                html.append("                            静默踢出（不通知玩家）\n");
                html.append("                        </label>\n");
                html.append("                    </div>\n");
                html.append("                    \n");
                html.append("                    <!-- 隐藏字段存储选中的模板信息 -->\n");
                html.append("                    <input type=\"hidden\" id=\"kickSelectedTemplate\" name=\"template\">\n");
                html.append("                    <input type=\"hidden\" id=\"kickTemplateReason\" name=\"templateReason\">\n");
                html.append("                </form>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"modal-footer\">\n");
                html.append("                <button class=\"btn btn-secondary\" onclick=\"closeKickModal()\">取消</button>\n");
                html.append("                <button class=\"btn btn-primary\" onclick=\"submitKick()\">确认踢出</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                return html.toString();
        }

        /**
         * 生成所有模态框
         */
        public String generateAllModals() {
                StringBuilder html = new StringBuilder();
                html.append(generateBanModal());
                html.append(generateMuteModal());
                html.append(generateUnmuteModal());
                html.append(generateWarnModal());
                html.append(generateKickModal());
                return html.toString();
        }

        /**
         * 生成CSS样式
         */
        public String generateActionStyles() {
                StringBuilder css = new StringBuilder();

                css.append("        /* 管理员操作区域样式 */\n");
                css.append("        .admin-action-section {\n");
                css.append("            background: #f8f9fa;\n");
                css.append("            border: 1px solid #dee2e6;\n");
                css.append("            border-radius: 8px;\n");
                css.append("            padding: 20px;\n");
                css.append("            margin: 20px 0;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .action-section-title {\n");
                css.append("            margin: 0 0 15px 0;\n");
                css.append("            color: #495057;\n");
                css.append("            font-size: 18px;\n");
                css.append("            font-weight: 600;\n");
                css.append("            text-align: center;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 夜间主题 - 管理员操作区域 */\n");
                css.append("        .dark .admin-action-section {\n");
                css.append("            background: hsl(240 10% 3.9%);\n");
                css.append("            border: 1px solid hsl(240 3.7% 15.9%);\n");
                css.append("            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .action-section-title {\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 夜间主题 - 模态框样式 */\n");
                css.append("        .dark .modal-content {\n");
                css.append("            background: hsl(240 10% 3.9%);\n");
                css.append("            border: 1px solid hsl(240 3.7% 15.9%);\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .modal-header {\n");
                css.append("            border-bottom: 1px solid hsl(240 3.7% 15.9%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .modal-title {\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .modal-close {\n");
                css.append("            color: hsl(240 5% 64.9%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .modal-close:hover {\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .modal-footer {\n");
                css.append("            border-top: 1px solid hsl(240 3.7% 15.9%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .form-group label {\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .form-group input[type=\"text\"],\n");
                css.append("        .dark .form-group select {\n");
                css.append("            background: hsl(240 3.7% 15.9%);\n");
                css.append("            border: 1px solid hsl(240 3.7% 25%);\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .form-group input[type=\"text\"]:focus,\n");
                css.append("        .dark .form-group select:focus {\n");
                css.append("            border-color: #667eea;\n");
                css.append("            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .form-group input[readonly] {\n");
                css.append("            background-color: hsl(240 3.7% 10%);\n");
                css.append("            opacity: 0.8;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .form-group input::placeholder {\n");
                css.append("            color: hsl(240 5% 64.9%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 夜间主题 - 封禁模板选择器 */\n");
                css.append("        .dark .template-selector {\n");
                css.append("            background: hsl(240 3.7% 15.9%);\n");
                css.append("            border: 1px solid hsl(240 3.7% 25%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-option {\n");
                css.append("            background: hsl(240 3.7% 15.9%);\n");
                css.append("            border: 1px solid hsl(240 3.7% 25%);\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-option:hover {\n");
                css.append("            background: hsl(240 3.7% 20%);\n");
                css.append("            border-color: #667eea;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-option.selected {\n");
                css.append("            background: rgba(102, 126, 234, 0.2);\n");
                css.append("            border-color: #667eea;\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-option .template-name {\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-option .template-duration {\n");
                css.append("            color: hsl(240 5% 64.9%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-option .template-icon {\n");
                css.append("            opacity: 0.8;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 夜间主题 - 模板选择标签页 */\n");
                css.append("        .dark .template-tabs {\n");
                css.append("            border: 1px solid hsl(240 3.7% 25%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-tab {\n");
                css.append("            background: hsl(240 3.7% 15.9%);\n");
                css.append("            color: hsl(240 5% 64.9%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-tab.active {\n");
                css.append("            background: #667eea;\n");
                css.append("            color: white;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-tab:hover:not(.active) {\n");
                css.append("            background: hsl(240 3.7% 20%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 夜间主题 - 模板网格和项目 */\n");
                css.append("        .dark .template-item {\n");
                css.append("            background: hsl(240 3.7% 15.9%);\n");
                css.append("            border: 2px solid hsl(240 3.7% 25%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-item:hover {\n");
                css.append("            border-color: #667eea;\n");
                css.append("            background: hsl(240 3.7% 20%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-item.selected {\n");
                css.append("            border-color: #667eea;\n");
                css.append("            background: rgba(102, 126, 234, 0.2);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-name {\n");
                css.append("            color: hsl(0 0% 98%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .dark .template-duration {\n");
                css.append("            color: hsl(240 5% 64.9%);\n");
                css.append("            background: hsl(240 3.7% 25%);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .action-buttons-grid {\n");
                css.append("            display: grid;\n");
                css.append("            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));\n");
                css.append("            gap: 12px;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .action-btn {\n");
                css.append("            display: flex;\n");
                css.append("            align-items: center;\n");
                css.append("            justify-content: center;\n");
                css.append("            gap: 8px;\n");
                css.append("            padding: 12px 16px;\n");
                css.append("            border: none;\n");
                css.append("            border-radius: 6px;\n");
                css.append("            font-size: 14px;\n");
                css.append("            font-weight: 500;\n");
                css.append("            cursor: pointer;\n");
                css.append("            transition: all 0.2s ease;\n");
                css.append("            color: white;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .ban-btn {\n");
                css.append("            background: #dc3545;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .ban-btn:hover {\n");
                css.append("            background: #c82333;\n");
                css.append("            transform: translateY(-1px);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .mute-btn {\n");
                css.append("            background: #fd7e14;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .mute-btn:hover {\n");
                css.append("            background: #e8690b;\n");
                css.append("            transform: translateY(-1px);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .warn-btn {\n");
                css.append("            background: #ffc107;\n");
                css.append("            color: #212529;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .warn-btn:hover {\n");
                css.append("            background: #e0a800;\n");
                css.append("            transform: translateY(-1px);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .kick-btn {\n");
                css.append("            background: #6f42c1;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .kick-btn:hover {\n");
                css.append("            background: #5a32a3;\n");
                css.append("            transform: translateY(-1px);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 解封按钮样式 */\n");
                css.append("        .unban-btn {\n");
                css.append("            background: #28a745;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .unban-btn:hover {\n");
                css.append("            background: #218838;\n");
                css.append("            transform: translateY(-1px);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        /* 模板选择样式 */\n");
                css.append("        .template-tabs {\n");
                css.append("            display: flex;\n");
                css.append("            margin-bottom: 15px;\n");
                css.append("            border-radius: 8px;\n");
                css.append("            overflow: hidden;\n");
                css.append("            border: 1px solid #dee2e6;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-tab {\n");
                css.append("            flex: 1;\n");
                css.append("            padding: 10px 16px;\n");
                css.append("            border: none;\n");
                css.append("            background: #f8f9fa;\n");
                css.append("            color: #6c757d;\n");
                css.append("            cursor: pointer;\n");
                css.append("            transition: all 0.2s ease;\n");
                css.append("            font-weight: 500;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-tab.active {\n");
                css.append("            background: #007bff;\n");
                css.append("            color: white;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-tab:hover:not(.active) {\n");
                css.append("            background: #e9ecef;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-grid {\n");
                css.append("            display: grid;\n");
                css.append("            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));\n");
                css.append("            gap: 12px;\n");
                css.append("            margin-top: 15px;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-item {\n");
                css.append("            display: flex;\n");
                css.append("            flex-direction: column;\n");
                css.append("            align-items: center;\n");
                css.append("            padding: 16px 12px;\n");
                css.append("            border: 2px solid #e9ecef;\n");
                css.append("            border-radius: 8px;\n");
                css.append("            cursor: pointer;\n");
                css.append("            transition: all 0.2s ease;\n");
                css.append("            background: white;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-item:hover {\n");
                css.append("            border-color: #007bff;\n");
                css.append("            transform: translateY(-2px);\n");
                css.append("            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.15);\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-item.selected {\n");
                css.append("            border-color: #007bff;\n");
                css.append("            background: #f8f9ff;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-icon {\n");
                css.append("            font-size: 24px;\n");
                css.append("            margin-bottom: 8px;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-name {\n");
                css.append("            font-size: 13px;\n");
                css.append("            font-weight: 600;\n");
                css.append("            color: #495057;\n");
                css.append("            margin-bottom: 4px;\n");
                css.append("            text-align: center;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .template-duration {\n");
                css.append("            font-size: 11px;\n");
                css.append("            color: #6c757d;\n");
                css.append("            background: #e9ecef;\n");
                css.append("            padding: 2px 8px;\n");
                css.append("            border-radius: 12px;\n");
                css.append("        }\n");
                css.append("        \n");
                css.append("        .ban-modal-content {\n");
                css.append("            max-width: 600px;\n");
                css.append("        }\n");

                return css.toString();
        }

        /**
         * 转义HTML特殊字符
         */
        private String escapeHtml(String text) {
                if (text == null)
                        return "";
                return text.replace("&", "&amp;")
                                .replace("<", "&lt;")
                                .replace(">", "&gt;")
                                .replace("\"", "&quot;")
                                .replace("'", "&#39;");
        }
}
