package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.checkin.CheckInManager;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 管理员签到奖励处理器
 */
public class AdminCheckInRewardsHandler implements HttpHandler {
    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final JSONParser parser = new JSONParser();

    public AdminCheckInRewardsHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        // 检查管理员权限
        if (!isAdminAuthenticated(exchange)) {
            sendResponse(exchange, 401, "{\"success\": false, \"message\": \"未授权访问\"}");
            return;
        }

        try {
            if ("GET".equals(method)) {
                handleGetRewards(exchange);
            } else if ("POST".equals(method)) {
                handleSaveReward(exchange);
            } else {
                sendResponse(exchange, 405, "{\"success\": false, \"message\": \"不支持的请求方法\"}");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理签到奖励请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "{\"success\": false, \"message\": \"服务器内部错误\"}");
        }
    }

    /**
     * 处理获取奖励配置请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetRewards(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        JSONObject rewards = new JSONObject();

        CheckInManager checkInManager = plugin.getCheckInManager();

        // 获取1-31号的奖励配置
        for (int day = 1; day <= 31; day++) {
            CheckInManager.CheckInReward reward = checkInManager.getDailyReward(day);
            if (reward != null) {
                JSONObject rewardData = new JSONObject();
                rewardData.put("points", reward.getPoints());
                rewardData.put("commands", reward.getCommands());
                rewards.put(String.valueOf(day), rewardData);
            }
        }

        response.put("success", true);
        response.put("rewards", rewards);

        sendResponse(exchange, 200, response.toJSONString());
    }

    /**
     * 处理保存奖励配置请求
     */
    @SuppressWarnings("unchecked")
    private void handleSaveReward(HttpExchange exchange) throws IOException {
        try {
            // 读取请求体
            InputStream inputStream = exchange.getRequestBody();
            String requestBody = readInputStream(inputStream);

            JSONObject request = (JSONObject) parser.parse(requestBody);

            // 获取参数
            Object dayObj = request.get("day");
            Object pointsObj = request.get("points");
            Object commandsObj = request.get("commands");

            if (dayObj == null || pointsObj == null) {
                sendResponse(exchange, 400, "{\"success\": false, \"message\": \"缺少必要参数\"}");
                return;
            }

            int day = ((Number) dayObj).intValue();
            int points = ((Number) pointsObj).intValue();

            // 验证日期范围
            if (day < 1 || day > 31) {
                sendResponse(exchange, 400, "{\"success\": false, \"message\": \"日期必须在1-31之间\"}");
                return;
            }

            // 验证积分范围
            if (points < 0 || points > 10000) {
                sendResponse(exchange, 400, "{\"success\": false, \"message\": \"积分必须在0-10000之间\"}");
                return;
            }

            // 处理命令列表
            List<String> commands = new ArrayList<>();
            if (commandsObj instanceof List) {
                List<?> commandsList = (List<?>) commandsObj;
                for (Object cmd : commandsList) {
                    if (cmd instanceof String) {
                        String command = ((String) cmd).trim();
                        if (!command.isEmpty()) {
                            commands.add(command);
                        }
                    }
                }
            }

            // 保存奖励配置
            CheckInManager checkInManager = plugin.getCheckInManager();
            checkInManager.setDailyReward(day, points, commands);

            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "第" + day + "天奖励配置保存成功");

            sendResponse(exchange, 200, response.toJSONString());

        } catch (Exception e) {
            plugin.getLogger().severe("保存签到奖励配置时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "{\"success\": false, \"message\": \"保存失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        // 设置CORS头
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");
        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");

        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    /**
     * 检查管理员认证
     */
    private boolean isAdminAuthenticated(HttpExchange exchange) {
        // 检查URL中的API密钥
        String query = exchange.getRequestURI().getQuery();
        Map<String, String> params = parseQuery(query);
        String providedKey = params.get("key");
        return isValidAdminKey(providedKey);
    }

    /**
     * 解析查询参数
     */
    private Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    try {
                        String key = java.net.URLDecoder.decode(keyValue[0], "UTF-8");
                        String value = java.net.URLDecoder.decode(keyValue[1], "UTF-8");
                        params.put(key, value);
                    } catch (Exception e) {
                        // 忽略解码错误
                    }
                }
            }
        }
        return params;
    }

    /**
     * 验证管理员密钥
     */
    private boolean isValidAdminKey(String key) {
        return key != null && key.equals(webServer.getAdminKey());
    }

    /**
     * 读取输入流内容
     */
    private String readInputStream(InputStream inputStream) throws IOException {
        StringBuilder result = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }
        }
        return result.toString();
    }
}
