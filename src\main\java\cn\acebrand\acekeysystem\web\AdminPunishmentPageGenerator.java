package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentRecord;
import java.util.ArrayList;

import java.util.List;
import java.util.Map;

/**
 * 管理员处罚页面生成器
 */
public class AdminPunishmentPageGenerator {

    private final AceKeySystem plugin;
    private final AdminPlayerActionGenerator actionGenerator;

    public AdminPunishmentPageGenerator(AceKeySystem plugin) {
        this.plugin = plugin;
        this.actionGenerator = new AdminPlayerActionGenerator(plugin);
    }

    /**
     * 生成背景CSS
     */
    private String generateBackgroundCSS() {
        // 管理员页面使用固定的渐变背景，不支持自定义背景图
        return "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n";
    }

    /**
     * 生成处罚记录页面HTML
     */
    public String generatePunishmentPage(PunishmentRecord.PunishmentType type, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics, String searchQuery) {
        return generatePunishmentPage(type, records, currentPage, totalPages, statistics, searchQuery, false);
    }

    /**
     * 生成处罚记录页面HTML（支持玩家视图）
     */
    public String generatePunishmentPage(PunishmentRecord.PunishmentType type, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics, String searchQuery, boolean isPlayerView) {
        return generatePunishmentPage(type, records, currentPage, totalPages, statistics, searchQuery, isPlayerView,
                null, null);
    }

    /**
     * 生成处罚记录页面HTML（支持过滤器）
     */
    public String generatePunishmentPage(PunishmentRecord.PunishmentType type, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics, String searchQuery, boolean isPlayerView,
            String playerFilter, String staffFilter) {
        StringBuilder html = new StringBuilder();

        String pageTitle = type != null ? type.getDisplayName() + "记录" : "处罚历史";
        String pageIcon = type != null ? type.getIcon() : "📋";

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>管理员 - ").append(pageTitle).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generateAdminPunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");

        html.append("<body>\n");

        // 管理员顶部导航栏
        html.append(generateAdminSiteHeader(type, statistics, searchQuery));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 页面标题
        if (isPlayerView && searchQuery != null && !searchQuery.isEmpty()) {
            // 玩家视图：在原页面结构中显示玩家信息
            html.append(generatePlayerHeaderInPage(searchQuery, records, type));
        } else {
            html.append("        <div class=\"page-header\">\n");
            html.append("            <h1>").append(pageIcon).append(" ").append(pageTitle).append("</h1>\n");
            html.append("            <p class=\"page-description\">查看服务器").append(pageTitle).append("</p>\n");
            html.append("            <div class=\"nav-buttons\">\n");
            html.append("                <a href=\"/admin\" class=\"nav-btn\">← 返回管理员控制台</a>\n");
            if (type != null) {
                html.append("                <a href=\"/admin-punishments\" class=\"nav-btn\">📋 所有记录</a>\n");
            }
            html.append("            </div>\n");
            html.append("        </div>\n");
        }

        // 统计信息（始终显示，除非是玩家专属页面）
        if (!isPlayerView && statistics != null && !statistics.isEmpty()) {
            html.append(generateAdminStatisticsSection(statistics, type));
        }

        // 过滤器显示（如果有过滤参数，无论是否玩家视图都显示）
        html.append(generateAdminFiltersSection(playerFilter, staffFilter));

        // 处罚记录表格
        html.append(generateAdminPunishmentTable(records));

        // 分页导航
        if (totalPages > 1) {
            html.append(generateAdminPagination(currentPage, totalPages, searchQuery, type));
        }

        html.append("    </div>\n");

        // JavaScript
        html.append("    <script>\n");
        html.append(generateAdminPunishmentPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成顶部导航栏（完全按照 next-litebans 布局）
     */
    private String generateSiteHeader(PunishmentRecord.PunishmentType currentType, Map<String, Object> statistics,
            String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("    <header class=\"site-header\">\n");
        html.append("        <div class=\"header-container\">\n");

        // 左侧：Logo 和主导航
        html.append("            <div class=\"header-left\">\n");
        html.append("                <a href=\"/\" class=\"logo-link\">\n");

        // 获取管理员Logo配置
        String adminLogoUrl = plugin.getConfig().getString("web-server.admin-logo-url", "");
        if (adminLogoUrl != null && !adminLogoUrl.trim().isEmpty()) {
            html.append("                    <img src=\"").append(adminLogoUrl).append("\" alt=\"LiteBans Logo\" class=\"logo-image\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='inline'\">\n");
        } else {
            html.append("                    <img src=\"/static/logo.webp\" alt=\"LiteBans Logo\" class=\"logo-image\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='inline'\">\n");
        }

        html.append("                    <span class=\"logo-text\">LiteBans</span>\n");
        html.append("                </a>\n");
        html.append("                <nav class=\"main-nav\">\n");
        html.append(generateMainNavigation(currentType, statistics));
        html.append("                </nav>\n");
        html.append("            </div>\n");

        // 右侧：搜索框和功能按钮
        html.append("            <div class=\"header-right\">\n");
        html.append("                <nav class=\"header-actions\">\n");
        html.append(generateHeaderSearch(searchQuery));
        html.append(
                "                    <button type=\"button\" class=\"theme-toggle-btn\" onclick=\"toggleTheme()\" title=\"切换主题\">\n");
        html.append("                        <span class=\"theme-icon\">🌙</span>\n");
        html.append("                    </button>\n");
        html.append("                </nav>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </header>\n");

        return html.toString();
    }

    /**
     * 生成主导航链接（类似 next-litebans MainNav）
     */
    private String generateMainNavigation(PunishmentRecord.PunishmentType currentType, Map<String, Object> statistics) {
        StringBuilder html = new StringBuilder();

        // 各种处罚类型的导航链接
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            String typeName = type.name().toLowerCase();
            boolean isActive = type == currentType;

            html.append("                    <a href=\"/punishments/").append(typeName).append("\" class=\"nav-link");
            if (isActive) {
                html.append(" active");
            }
            html.append("\">\n");
            html.append("                        ").append(type.getDisplayName()).append("\n");

            // 添加徽章显示数量
            Object count = statistics != null ? statistics.get("total_" + typeName) : null;
            if (count != null) {
                String badgeVariant = isActive ? "default" : "secondary";
                html.append("                        <span class=\"nav-badge badge-").append(badgeVariant).append("\">")
                        .append(count).append("</span>\n");
            }

            html.append("                    </a>\n");
        }

        return html.toString();
    }

    /**
     * 生成头部搜索框（类似 next-litebans PlayerInput）
     */
    private String generateHeaderSearch(String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("                    <div class=\"header-search\">\n");
        html.append(
                "                        <form method=\"get\" onsubmit=\"return handleSearchSubmit(event)\" class=\"search-form-header\">\n");
        html.append("                            <div class=\"search-input-wrapper\">\n");
        html.append("                                <input type=\"text\" name=\"player\" placeholder=\"搜索玩家...\" ");
        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("value=\"").append(escapeHtml(searchQuery)).append("\" ");
        }
        html.append(
                "class=\"header-search-input\" oninput=\"updatePlayerAvatar(this.value)\" onkeydown=\"handleSearchEnter(event)\">\n");
        html.append(
                "                                <img id=\"headerPlayerAvatar\" src=\"\" alt=\"玩家头像\" class=\"header-player-avatar\" style=\"display: none;\">\n");
        html.append("                            </div>\n");
        html.append("                        </form>\n");
        html.append("                    </div>\n");

        return html.toString();
    }

    /**
     * 在原页面结构中生成玩家信息头部（类似 next-litebans）
     */
    private String generatePlayerHeaderInPage(String playerName, List<PunishmentRecord> records,
            PunishmentRecord.PunishmentType currentType) {
        StringBuilder html = new StringBuilder();

        // 获取玩家统计信息 - 需要从数据库获取完整统计，而不是只从当前页面记录
        int banCount = 0, muteCount = 0, warnCount = 0, kickCount = 0;

        // 如果处罚管理器可用，获取准确的统计信息
        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            try {
                // 获取该玩家的所有类型处罚记录数量
                banCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.BAN, playerName, 1, 1000).size();
                muteCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.MUTE, playerName, 1, 1000).size();
                warnCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.WARN, playerName, 1, 1000).size();
                kickCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.KICK, playerName, 1, 1000).size();
            } catch (Exception e) {
                // 如果获取失败，回退到从当前记录计算
                for (PunishmentRecord record : records) {
                    String typeText = record.getPunishmentTypeText();
                    if (typeText != null) {
                        if (typeText.contains("封禁") || typeText.toLowerCase().contains("ban")) {
                            banCount++;
                        } else if (typeText.contains("禁言") || typeText.toLowerCase().contains("mute")) {
                            muteCount++;
                        } else if (typeText.contains("警告") || typeText.toLowerCase().contains("warn")) {
                            warnCount++;
                        } else if (typeText.contains("踢出") || typeText.toLowerCase().contains("kick")) {
                            kickCount++;
                        }
                    }
                }
            }
        } else {
            // 数据库不可用时，从当前记录计算
            for (PunishmentRecord record : records) {
                String typeText = record.getPunishmentTypeText();
                if (typeText != null) {
                    if (typeText.contains("封禁") || typeText.toLowerCase().contains("ban")) {
                        banCount++;
                    } else if (typeText.contains("禁言") || typeText.toLowerCase().contains("mute")) {
                        muteCount++;
                    } else if (typeText.contains("警告") || typeText.toLowerCase().contains("warn")) {
                        warnCount++;
                    } else if (typeText.contains("踢出") || typeText.toLowerCase().contains("kick")) {
                        kickCount++;
                    }
                }
            }
        }

        // 使用类似 next-litebans 的布局，但适配到我们的页面结构
        html.append("        <div class=\"player-view-header\">\n");
        html.append("            <div class=\"flex h-full flex-col items-center gap-4 py-8\">\n");
        html.append("                <div class=\"space-y-2 md:flex md:space-x-4\">\n");

        // 检查是否是控制台
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName)
                || "[Console]".equals(playerName);

        if (isConsolePlayer) {
            // 控制台头像（使用 console-bust.webp）
            html.append("                    <img src=\"/static/console-bust.webp\" ");
            html.append("alt=\"控制台\" ");
            html.append("width=\"192\" height=\"192\" class=\"mx-auto console-bust-large\" ");
            html.append("onerror=\"this.src='/static/console.webp'\">\n");
        } else {
            // 半身皮肤图片（多个API备选）
            String bustUrl = "https://vzge.me/bust/512/" + playerName + ".png";
            html.append("                    <img src=\"").append(bustUrl).append("\" alt=\"")
                    .append(escapeHtml(playerName)).append("\" width=\"192\" height=\"192\" class=\"mx-auto\" ")
                    .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(playerName)
                    .append("/192'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://crafatar.com/renders/body/")
                    .append(playerName).append("?size=192&overlay'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://minotar.net/armor/body/")
                    .append(playerName).append("/192';};}\">\n");
        }

        // 玩家详情区域
        html.append("                    <div class=\"md:w-[350px] md:py-4 space-y-1\">\n");
        if (isConsolePlayer) {
            html.append(
                    "                        <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">控制台</h1>\n");
        } else {
            html.append(
                    "                        <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">")
                    .append(escapeHtml(playerName)).append("</h1>\n");
        }
        html.append("                        <div class=\"flex space-x-2 whitespace-nowrap\">\n");

        // 徽章（完全按照 next-litebans 样式，始终显示所有类型，当前类型高亮）

        // 封禁徽章
        String banBadgeClass = (currentType == PunishmentRecord.PunishmentType.BAN) ? "badge badge-primary"
                : "badge badge-secondary";
        if (banCount > 0) {
            html.append("                            <a href=\"/admin-punishments/ban?player=")
                    .append(escapeHtml(playerName))
                    .append("\" class=\"").append(banBadgeClass)
                    .append("\" data-type=\"ban\" data-stat=\"total_ban\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">🚫</span> <span class=\"badge-count\">")
                    .append(banCount)
                    .append("</span> 封禁\n");
            html.append("                            </a>\n");
        }

        // 禁言徽章
        String muteBadgeClass = (currentType == PunishmentRecord.PunishmentType.MUTE) ? "badge badge-primary"
                : "badge badge-secondary";
        if (muteCount > 0) {
            html.append("                            <a href=\"/admin-punishments/mute?player=")
                    .append(escapeHtml(playerName)).append("\" class=\"").append(muteBadgeClass)
                    .append("\" data-type=\"mute\" data-stat=\"total_mute\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">🔇</span> <span class=\"badge-count\">")
                    .append(muteCount)
                    .append("</span> 禁言\n");
            html.append("                            </a>\n");
        }

        // 警告徽章
        String warnBadgeClass = (currentType == PunishmentRecord.PunishmentType.WARN) ? "badge badge-primary"
                : "badge badge-secondary";
        if (warnCount > 0) {
            html.append("                            <a href=\"/admin-punishments/warn?player=")
                    .append(escapeHtml(playerName)).append("\" class=\"").append(warnBadgeClass)
                    .append("\" data-type=\"warn\" data-stat=\"total_warn\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">⚠️</span> <span class=\"badge-count\">")
                    .append(warnCount)
                    .append("</span> 警告\n");
            html.append("                            </a>\n");
        }

        // 踢出徽章
        String kickBadgeClass = (currentType == PunishmentRecord.PunishmentType.KICK) ? "badge badge-primary"
                : "badge badge-secondary";
        if (kickCount > 0) {
            html.append("                            <a href=\"/admin-punishments/kick?player=")
                    .append(escapeHtml(playerName)).append("\" class=\"").append(kickBadgeClass)
                    .append("\" data-type=\"kick\" data-stat=\"total_kick\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">👢</span> <span class=\"badge-count\">")
                    .append(kickCount)
                    .append("</span> 踢出\n");
            html.append("                            </a>\n");
        }

        // 如果没有任何记录，显示所有记录链接
        if (banCount == 0 && muteCount == 0 && warnCount == 0 && kickCount == 0) {
            String allBadgeClass = (currentType == null) ? "badge badge-primary" : "badge badge-secondary";
            html.append("                            <a href=\"/admin-punishments?player=")
                    .append(escapeHtml(playerName))
                    .append("\" class=\"").append(allBadgeClass).append("\">\n");
            html.append("                                <span class=\"badge-icon\">📋</span> 查看所有记录\n");
            html.append("                            </a>\n");
        }

        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        // 添加过滤器显示（如果有过滤参数）
        // 注意：这里只显示玩家过滤器，因为这是玩家专属页面
        html.append(generateFiltersSection(playerName, null));

        return html.toString();
    }

    /**
     * 生成完整的玩家专属页面（完全按照 next-litebans 布局）
     */
    private String generatePlayerPage(String playerName, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics) {
        StringBuilder html = new StringBuilder();

        // HTML 头部
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(escapeHtml(playerName)).append(" - 玩家信息</title>\n");
        html.append("    <style>\n");
        html.append(generateAdminPunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <div class=\"container\">\n");

        // 玩家信息头部
        html.append(generatePlayerHeader(playerName, records));

        // 处罚记录表格
        html.append(generatePunishmentTable(records));

        // 分页
        if (totalPages > 1) {
            html.append(generatePagination(currentPage, totalPages,
                    "?search=" + escapeHtml(playerName) + "&player_view=true", null));
        }

        html.append("            </section>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("    <script>\n");
        html.append(generateAdminPunishmentPageJS());
        html.append("    </script>\n");
        html.append("</body>\n");
        html.append("</html>\n");

        return html.toString();
    }

    /**
     * 生成玩家专属页面头部（完全按照 next-litebans 布局）
     */
    private String generatePlayerHeader(String playerName, List<PunishmentRecord> records) {
        StringBuilder html = new StringBuilder();

        // 获取玩家统计信息
        int banCount = 0, muteCount = 0, warnCount = 0, kickCount = 0;
        for (PunishmentRecord record : records) {
            String typeText = record.getPunishmentTypeText();
            if (typeText != null) {
                if (typeText.contains("封禁") || typeText.toLowerCase().contains("ban")) {
                    banCount++;
                } else if (typeText.contains("禁言") || typeText.toLowerCase().contains("mute")) {
                    muteCount++;
                } else if (typeText.contains("警告") || typeText.toLowerCase().contains("warn")) {
                    warnCount++;
                } else if (typeText.contains("踢出") || typeText.toLowerCase().contains("kick")) {
                    kickCount++;
                }
            }
        }

        // 使用 next-litebans 的确切布局结构
        html.append("        <div class=\"flex h-full flex-col items-center gap-4 py-8 md:py-12 md:pb-8 lg:py-18\">\n");
        html.append("            <div class=\"space-y-2 md:flex md:space-x-4\">\n");

        // 半身皮肤图片（完全按照 next-litebans）
        String bustUrl = "https://vzge.me/bust/512/" + playerName + ".png";
        html.append("                <img src=\"").append(bustUrl).append("\" alt=\"").append(escapeHtml(playerName))
                .append("\" width=\"192\" height=\"192\" class=\"mx-auto\" onerror=\"this.src='https://mc-heads.net/body/")
                .append(playerName).append("/192'\">\n");

        // 玩家详情区域
        html.append("                <div class=\"md:w-[350px] md:py-4 space-y-1\">\n");
        html.append(
                "                    <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">")
                .append(escapeHtml(playerName)).append("</h1>\n");
        html.append("                    <div class=\"flex space-x-2 whitespace-nowrap\">\n");

        // 徽章（完全按照 next-litebans 样式）
        if (banCount > 0) {
            html.append("                        <a href=\"/punishments/ban?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"ban\" data-stat=\"total_ban\">\n");
            html.append("                            <span class=\"badge-icon\">🚫</span> <span class=\"badge-count\">")
                    .append(banCount)
                    .append("</span> 封禁\n");
            html.append("                        </a>\n");
        }

        if (muteCount > 0) {
            html.append("                        <a href=\"/punishments/mute?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"mute\" data-stat=\"total_mute\">\n");
            html.append("                            <span class=\"badge-icon\">🔇</span> <span class=\"badge-count\">")
                    .append(muteCount)
                    .append("</span> 禁言\n");
            html.append("                        </a>\n");
        }

        if (warnCount > 0) {
            html.append("                        <a href=\"/punishments/warn?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"warn\" data-stat=\"total_warn\">\n");
            html.append("                            <span class=\"badge-icon\">⚠️</span> <span class=\"badge-count\">")
                    .append(warnCount)
                    .append("</span> 警告\n");
            html.append("                        </a>\n");
        }

        if (kickCount > 0) {
            html.append("                        <a href=\"/punishments/kick?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"kick\" data-stat=\"total_kick\">\n");
            html.append("                            <span class=\"badge-icon\">👢</span> <span class=\"badge-count\">")
                    .append(kickCount)
                    .append("</span> 踢出\n");
            html.append("                        </a>\n");
        }

        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");

        // 表格区域（保持原有的表格显示）
        html.append("            <section class=\"w-full lg:w-[1024px]\">\n");

        return html.toString();
    }

    /**
     * 生成导航标签页
     */
    private String generateNavigationTabs(PunishmentRecord.PunishmentType currentType) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"nav-tabs\">\n");

        // 所有记录标签
        html.append("            <a href=\"/punishments\" class=\"tab-btn");
        if (currentType == null)
            html.append(" active");
        html.append("\">📋 所有记录</a>\n");

        // 各种类型的标签
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            html.append("            <a href=\"/punishments/").append(type.name().toLowerCase())
                    .append("\" class=\"tab-btn");
            if (type == currentType)
                html.append(" active");
            html.append("\">").append(type.getIcon()).append(" ").append(type.getDisplayName()).append("</a>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成统计信息区域
     */
    private String generateStatisticsSection(Map<String, Object> statistics, PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"statistics-section\">\n");
        html.append("            <div class=\"stats-grid\">\n");

        if (type != null) {
            // 单一类型的统计
            String typeName = type.name().toLowerCase();

            Object total = statistics.get("total_" + typeName);
            if (total != null) {
                // 总数卡片可以点击跳转到对应类型页面
                html.append("                <a href=\"/punishments/").append(typeName)
                        .append("\" class=\"stat-card-link\">\n");
                html.append("                    <div class=\"stat-card\" style=\"border-left-color: ")
                        .append(type.getColor()).append("\">\n");
                html.append("                        <div class=\"stat-icon\">").append(type.getIcon())
                        .append("</div>\n");
                html.append("                        <div class=\"stat-info\">\n");
                html.append("                            <div class=\"stat-value\">").append(total).append("</div>\n");
                html.append("                            <div class=\"stat-label\">总").append(type.getDisplayName())
                        .append("数</div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }

            Object active = statistics.get("active_" + typeName);
            if (active != null) {
                // 生效中的记录也可以点击，但添加过滤参数
                html.append("                <a href=\"/punishments/").append(typeName)
                        .append("?active=true\" class=\"stat-card-link\">\n");
                html.append("                    <div class=\"stat-card active\">\n");
                html.append("                        <div class=\"stat-icon\">🔴</div>\n");
                html.append("                        <div class=\"stat-info\">\n");
                html.append("                            <div class=\"stat-value\">").append(active).append("</div>\n");
                html.append("                            <div class=\"stat-label\">生效中</div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }

            Object today = statistics.get("today_" + typeName);
            if (today != null) {
                // 今日新增也可以点击，添加日期过滤参数
                html.append("                <a href=\"/punishments/").append(typeName)
                        .append("?today=true\" class=\"stat-card-link\">\n");
                html.append("                    <div class=\"stat-card today\">\n");
                html.append("                        <div class=\"stat-icon\">📅</div>\n");
                html.append("                        <div class=\"stat-info\">\n");
                html.append("                            <div class=\"stat-value\">").append(today).append("</div>\n");
                html.append("                            <div class=\"stat-label\">今日新增</div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }
        } else {
            // 所有类型的统计
            for (PunishmentRecord.PunishmentType punishmentType : PunishmentRecord.PunishmentType.values()) {
                String typeName = punishmentType.name().toLowerCase();
                Object total = statistics.get("total_" + typeName);
                if (total != null) {
                    // 使用链接包装整个卡片（类似 next-litebans PunishmentTypeCard）
                    html.append("                <a href=\"/punishments/").append(typeName)
                            .append("\" class=\"stat-card-link\">\n");
                    html.append("                    <div class=\"stat-card\" style=\"border-left-color: ")
                            .append(punishmentType.getColor()).append("\">\n");
                    html.append("                        <div class=\"stat-icon\">").append(punishmentType.getIcon())
                            .append("</div>\n");
                    html.append("                        <div class=\"stat-info\">\n");
                    html.append("                            <div class=\"stat-value\">").append(total)
                            .append("</div>\n");
                    html.append("                            <div class=\"stat-label\">")
                            .append(punishmentType.getDisplayName()).append("</div>\n");
                    html.append("                        </div>\n");
                    html.append("                    </div>\n");
                    html.append("                </a>\n");
                }
            }
        }

        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成搜索区域
     */
    private String generateSearchSection(String searchQuery, PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"search-section\">\n");
        html.append(
                "            <form class=\"search-form\" method=\"get\" onsubmit=\"return handleSearchSubmit(event)\">\n");
        html.append("                <div class=\"search-input-group\">\n");
        html.append("                    <div class=\"search-input-container\">\n");
        html.append(
                "                        <input type=\"text\" name=\"player\" id=\"playerSearchInput\" placeholder=\"搜索玩家名称或UUID...\" ");
        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("value=\"").append(escapeHtml(searchQuery)).append("\" ");
        }
        html.append(
                "class=\"search-input\" oninput=\"updatePlayerAvatar(this.value)\" onkeydown=\"handleSearchEnter(event)\">\n");
        html.append(
                "                        <img id=\"playerAvatarPreview\" src=\"\" alt=\"玩家头像\" class=\"player-avatar-preview\" style=\"display: none;\">\n");
        html.append("                    </div>\n");
        html.append("                    <button type=\"submit\" class=\"search-btn\">🔍 搜索</button>\n");
        html.append(
                "                    <button type=\"button\" class=\"theme-toggle-btn\" onclick=\"toggleTheme()\" title=\"切换主题\">\n");
        html.append("                        <span class=\"theme-icon\">🌙</span>\n");
        html.append("                    </button>\n");
        html.append("                </div>\n");
        html.append("            </form>\n");

        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("            <div class=\"search-result-info\">\n");
            html.append("                <span>玩家记录: \"").append(escapeHtml(searchQuery)).append("\"</span>\n");
            String clearUrl = type != null ? "/punishments/" + type.name().toLowerCase() : "/punishments";
            html.append("                <a href=\"").append(clearUrl).append("\" class=\"clear-search\">查看所有记录</a>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成处罚记录表格
     */
    private String generatePunishmentTable(List<PunishmentRecord> records) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"table-section\">\n");

        if (records.isEmpty()) {
            html.append("            <div class=\"no-records\">\n");
            html.append("                <div class=\"no-records-icon\">📝</div>\n");

            // 检查是否是数据库连接问题
            if (plugin.getPunishmentManager() == null || !plugin.getPunishmentManager().isEnabled()) {
                html.append("                <h3>数据库未连接</h3>\n");
                html.append("                <p>LiteBans数据库连接失败，无法显示处罚记录</p>\n");
                html.append("                <p class=\"db-status\">请检查数据库配置或联系管理员</p>\n");
            } else {
                html.append("                <h3>暂无处罚记录</h3>\n");
                html.append("                <p>当前没有找到任何处罚记录</p>\n");
            }

            html.append("            </div>\n");
        } else {
            html.append("            <div class=\"table-container\">\n");
            html.append("                <table class=\"punishment-table\">\n");
            html.append("                    <thead>\n");
            html.append("                        <tr>\n");
            html.append("                            <th>类型</th>\n");
            html.append("                            <th>玩家</th>\n");
            html.append("                            <th>执行者</th>\n");
            html.append("                            <th>处罚原因</th>\n");
            html.append("                            <th>处罚时间</th>\n");
            html.append("                            <th>到期时间</th>\n");
            html.append("                            <th>状态</th>\n");
            html.append("                            <th>详情</th>\n");
            html.append("                        </tr>\n");
            html.append("                    </thead>\n");
            html.append("                    <tbody>\n");

            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            boolean showAvatars = plugin.getConfig().getBoolean("litebans.display.show-player-avatars", true);

            for (PunishmentRecord record : records) {
                html.append("                        <tr class=\"punishment-row\">\n");

                // 类型列
                html.append("                            <td class=\"type-cell\">\n");
                html.append("                                <span class=\"type-badge\" style=\"background-color: ")
                        .append(record.getTypeColor()).append("\">");
                html.append(record.getTypeIcon()).append(" ").append(escapeHtml(record.getPunishmentTypeText()))
                        .append("</span>\n");
                html.append("                            </td>\n");

                // 玩家列（按照 next-litebans 布局：头像在上，名字在下，居中对齐，可点击过滤）
                html.append("                            <td class=\"player-cell\">\n");
                String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";

                // 构建玩家过滤链接（保留现有参数，按照 next-litebans AvatarName 逻辑）
                html.append("                                <a href=\"#\" onclick=\"addPlayerPageFilter('player', '")
                        .append(escapeHtml(playerName)).append("'); return false;\" class=\"avatar-name-link\">\n");
                html.append("                                    <div class=\"avatar-name-container\">\n");
                if (showAvatars && record.getUuid() != null && !record.getUuid().isEmpty()) {
                    html.append("                                        <img src=\"")
                            .append(record.getPlayerAvatarUrl(avatarProvider))
                            .append("\" alt=\"头像\" class=\"player-avatar\" onerror=\"this.style.display='none'\">\n");
                }
                html.append("                                        <div class=\"player-name\">")
                        .append(escapeHtml(playerName))
                        .append("</div>\n");
                html.append("                                    </div>\n");
                html.append("                                </a>\n");
                html.append("                            </td>\n");

                // 执行者（按照 next-litebans 布局：头像在上，名字在下，居中对齐，可点击过滤）
                html.append("                            <td class=\"staff-cell\">\n");
                String staffName = record.getBannedByName();
                String staffUuid = record.getBannedByUuid();
                boolean isConsole = false;

                if (staffName == null) {
                    staffName = "系统";
                    isConsole = true;
                } else if ("Console".equalsIgnoreCase(staffName)) {
                    staffName = "控制台";
                    isConsole = true;
                }

                // 构建执行者过滤链接（保留现有参数，按照 next-litebans AvatarName 逻辑）
                String staffFilterValue;
                if (isConsole) {
                    staffFilterValue = "Console";
                } else if (staffUuid != null && !staffUuid.isEmpty()) {
                    staffFilterValue = staffUuid;
                } else {
                    staffFilterValue = staffName;
                }

                html.append("                                <a href=\"#\" onclick=\"addPlayerPageFilter('staff', '")
                        .append(escapeHtml(staffFilterValue))
                        .append("'); return false;\" class=\"avatar-name-link\">\n");
                html.append("                                    <div class=\"avatar-name-container\">\n");
                if (!isConsole && showAvatars && staffUuid != null && !staffUuid.isEmpty()) {
                    String avatarUrl = generateAvatarUrl(staffUuid, avatarProvider);
                    html.append("                                        <img src=\"")
                            .append(avatarUrl)
                            .append("\" alt=\"头像\" class=\"staff-avatar\" onerror=\"this.style.display='none'\">\n");
                } else if (isConsole) {
                    html.append(
                            "                                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"staff-avatar console-avatar\" onerror=\"this.style.display='none'\">\n");
                }
                html.append("                                        <div class=\"staff-name\">")
                        .append(escapeHtml(staffName)).append("</div>\n");
                html.append("                                    </div>\n");
                html.append("                                </a>\n");
                html.append("                            </td>\n");

                // 处罚原因
                html.append("                            <td class=\"reason-cell\">\n");
                html.append("                                <span class=\"reason-text\" title=\"")
                        .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因"))
                        .append("\">").append(escapeHtml(record.getShortReason())).append("</span>\n");
                html.append("                            </td>\n");

                // 处罚时间
                html.append("                            <td class=\"time-cell\">\n");
                html.append("                                ").append(escapeHtml(record.getFormattedTime()))
                        .append("\n");
                html.append("                            </td>\n");

                // 到期时间（动态显示）
                html.append("                            <td class=\"until-cell\">\n");
                if (record.getUntil() != null && record.getUntil().getYear() <= 2100) {
                    // 非永久处罚，显示动态时间
                    html.append("                                <div class=\"flex items-center justify-center\">\n");
                    if (record.isActive()) {
                        html.append(
                                "                                    <span class=\"status-dot status-active\" title=\"生效中\"></span>\n");
                    } else {
                        html.append(
                                "                                    <span class=\"status-dot status-expired\" title=\"已过期\"></span>\n");
                    }
                    // 添加动态时间显示
                    long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant()
                            .toEpochMilli();
                    html.append("                                    <span class=\"relative-time\" data-timestamp=\"")
                            .append(timestamp).append("\">");
                    html.append(escapeHtml(record.getFormattedUntil())).append("</span>\n");
                    html.append("                                </div>\n");
                } else {
                    // 永久处罚，直接显示"永久"
                    html.append("                                永久\n");
                }
                html.append("                            </td>\n");

                // 状态
                html.append("                            <td class=\"status-cell\">\n");
                html.append("                                <span class=\"status-badge ")
                        .append(record.getStatusClass())
                        .append("\">").append(escapeHtml(record.getStatusText())).append("</span>\n");
                html.append("                            </td>\n");

                // 详情按钮（按照 next-litebans PunishmentInfoButton）
                html.append("                            <td class=\"info-cell\">\n");
                String punishmentType = record.getType() != null ? record.getType().name().toLowerCase() : "punishment";
                html.append("                                <a href=\"/admin-punishments/").append(punishmentType)
                        .append("/").append(record.getId()).append("\" class=\"info-btn\" title=\"查看详细信息\">\n");
                html.append(
                        "                                    <svg class=\"info-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n");
                html.append(
                        "                                        <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n");
                html.append(
                        "                                        <polyline points=\"15,3 21,3 21,9\"></polyline>\n");
                html.append(
                        "                                        <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n");
                html.append("                                    </svg>\n");
                html.append("                                </a>\n");
                html.append("                            </td>\n");

                html.append("                        </tr>\n");
            }

            html.append("                    </tbody>\n");
            html.append("                </table>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成单个表格行（用于实时更新）
     */
    public String generateTableRow(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
        boolean showAvatars = plugin.getConfig().getBoolean("litebans.display.show-player-avatars", true);

        html.append("                        <tr class=\"punishment-row\">\n");

        // 类型列
        html.append("                            <td class=\"type-cell\">\n");
        html.append("                                <span class=\"type-badge\" style=\"background-color: ")
                .append(record.getTypeColor()).append("\">");
        html.append(record.getTypeIcon()).append(" ").append(escapeHtml(record.getPunishmentTypeText()))
                .append("</span>\n");
        html.append("                            </td>\n");

        // 玩家列（按照 next-litebans 布局：头像在上，名字在下，居中对齐，可点击过滤）
        html.append("                            <td class=\"player-cell\">\n");
        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";

        // 构建玩家过滤链接（保留现有参数，按照 next-litebans AvatarName 逻辑）
        html.append("                                <a href=\"#\" onclick=\"addPlayerPageFilter('player', '")
                .append(escapeHtml(playerName)).append("'); return false;\" class=\"avatar-name-link\">\n");
        html.append("                                    <div class=\"avatar-name-container\">\n");
        if (showAvatars && record.getUuid() != null && !record.getUuid().isEmpty()) {
            html.append("                                        <img src=\"")
                    .append(record.getPlayerAvatarUrl(avatarProvider))
                    .append("\" alt=\"头像\" class=\"player-avatar\" onerror=\"this.style.display='none'\">\n");
        }
        html.append("                                        <div class=\"player-name\">")
                .append(escapeHtml(playerName))
                .append("</div>\n");
        html.append("                                    </div>\n");
        html.append("                                </a>\n");
        html.append("                            </td>\n");

        // 执行者（按照 next-litebans 布局：头像在上，名字在下，居中对齐，可点击过滤）
        html.append("                            <td class=\"staff-cell\">\n");
        String staffName = record.getBannedByName();
        String staffUuid = record.getBannedByUuid();
        boolean isConsole = false;

        if (staffName == null) {
            staffName = "系统";
            isConsole = true;
        } else if ("Console".equalsIgnoreCase(staffName)) {
            staffName = "控制台";
            isConsole = true;
        }

        // 构建执行者过滤链接（保留现有参数，按照 next-litebans AvatarName 逻辑）
        String staffFilterValue;
        if (isConsole) {
            staffFilterValue = "Console";
        } else if (staffUuid != null && !staffUuid.isEmpty()) {
            staffFilterValue = staffUuid;
        } else {
            staffFilterValue = staffName;
        }

        html.append("                                <a href=\"#\" onclick=\"addPlayerPageFilter('staff', '")
                .append(escapeHtml(staffFilterValue))
                .append("'); return false;\" class=\"avatar-name-link\">\n");
        html.append("                                    <div class=\"avatar-name-container\">\n");
        if (!isConsole && showAvatars && staffUuid != null && !staffUuid.isEmpty()) {
            String avatarUrl = generateAvatarUrl(staffUuid, avatarProvider);
            html.append("                                        <img src=\"")
                    .append(avatarUrl)
                    .append("\" alt=\"头像\" class=\"staff-avatar\" onerror=\"this.style.display='none'\">\n");
        } else if (isConsole) {
            html.append(
                    "                                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"staff-avatar console-avatar\" onerror=\"this.style.display='none'\">\n");
        }
        html.append("                                        <div class=\"staff-name\">")
                .append(escapeHtml(staffName)).append("</div>\n");
        html.append("                                    </div>\n");
        html.append("                                </a>\n");
        html.append("                            </td>\n");

        // 处罚原因
        html.append("                            <td class=\"reason-cell\">\n");
        html.append("                                <span class=\"reason-text\" title=\"")
                .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因"))
                .append("\">").append(escapeHtml(record.getShortReason())).append("</span>\n");
        html.append("                            </td>\n");

        // 处罚时间
        html.append("                            <td class=\"time-cell\">\n");
        html.append("                                ").append(escapeHtml(record.getFormattedTime()))
                .append("\n");
        html.append("                            </td>\n");

        // 到期时间（动态显示）
        html.append("                            <td class=\"until-cell\">\n");
        if (record.getUntil() != null && record.getUntil().getYear() <= 2100) {
            // 非永久处罚，显示动态时间
            html.append("                                <div class=\"flex items-center justify-center\">\n");
            if (record.isActive()) {
                html.append(
                        "                                    <span class=\"status-dot status-active\" title=\"生效中\"></span>\n");
            } else {
                html.append(
                        "                                    <span class=\"status-dot status-expired\" title=\"已过期\"></span>\n");
            }
            // 添加动态时间显示
            long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant()
                    .toEpochMilli();
            html.append("                                    <span class=\"relative-time\" data-timestamp=\"")
                    .append(timestamp).append("\">");
            html.append(escapeHtml(record.getFormattedUntil())).append("</span>\n");
            html.append("                                </div>\n");
        } else {
            // 永久处罚，直接显示"永久"
            html.append("                                永久\n");
        }
        html.append("                            </td>\n");

        // 状态
        html.append("                            <td class=\"status-cell\">\n");
        html.append("                                <span class=\"status-badge ")
                .append(record.getStatusClass())
                .append("\">").append(escapeHtml(record.getStatusText())).append("</span>\n");
        html.append("                            </td>\n");

        // 详情按钮（按照 next-litebans PunishmentInfoButton）
        html.append("                            <td class=\"info-cell\">\n");
        String punishmentType = record.getType() != null ? record.getType().name().toLowerCase() : "punishment";
        html.append("                                <a href=\"/admin-punishments/").append(punishmentType)
                .append("/").append(record.getId()).append("\" class=\"info-btn\" title=\"查看详细信息\">\n");
        html.append(
                "                                    <svg class=\"info-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n");
        html.append(
                "                                        <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n");
        html.append(
                "                                        <polyline points=\"15,3 21,3 21,9\"></polyline>\n");
        html.append(
                "                                        <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n");
        html.append("                                    </svg>\n");
        html.append("                                </a>\n");
        html.append("                            </td>\n");

        html.append("                        </tr>\n");

        return html.toString();
    }

    /**
     * 生成管理员表格行（用于实时更新）
     */
    public String generateAdminTableRow(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        html.append("                        <tr class=\"admin-record-row\" data-record-id=\"")
                .append(record.getId()).append("\">\n");

        // 使用默认表格行结构（混合类型）
        html.append(generateDefaultTableRow(record));

        html.append("                        </tr>\n");

        return html.toString();
    }

    /**
     * 生成分页导航
     */
    private String generatePagination(int currentPage, int totalPages, String searchQuery,
            PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"pagination-section\">\n");
        html.append("            <div class=\"pagination\">\n");

        String baseUrl = type != null ? "/punishments/" + type.name().toLowerCase() : "/punishments";
        String queryParam = "";
        if (searchQuery != null && !searchQuery.isEmpty()) {
            queryParam = "&search=" + escapeUrl(searchQuery);
        }

        // 上一页
        if (currentPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage - 1)
                    .append(queryParam).append("\" class=\"page-btn prev-btn\">← 上一页</a>\n");
        }

        // 页码
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=1").append(queryParam)
                    .append("\" class=\"page-btn\">1</a>\n");
            if (startPage > 2) {
                html.append("                <span class=\"page-dots\">...</span>\n");
            }
        }

        for (int i = startPage; i <= endPage; i++) {
            if (i == currentPage) {
                html.append("                <span class=\"page-btn current\">").append(i).append("</span>\n");
            } else {
                html.append("                <a href=\"").append(baseUrl).append("?page=").append(i)
                        .append(queryParam).append("\" class=\"page-btn\">").append(i).append("</a>\n");
            }
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html.append("                <span class=\"page-dots\">...</span>\n");
            }
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(totalPages)
                    .append(queryParam).append("\" class=\"page-btn\">").append(totalPages).append("</a>\n");
        }

        // 下一页
        if (currentPage < totalPages) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage + 1)
                    .append(queryParam).append("\" class=\"page-btn next-btn\">下一页 →</a>\n");
        }

        html.append("            </div>\n");
        html.append("            <div class=\"page-info\">\n");
        html.append("                第 ").append(currentPage).append(" 页，共 ").append(totalPages).append(" 页\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null)
            return "";
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    /**
     * URL转义
     */
    private String escapeUrl(String text) {
        if (text == null)
            return "";
        try {
            return java.net.URLEncoder.encode(text, "UTF-8");
        } catch (Exception e) {
            return text;
        }
    }

    /**
     * 生成管理员处罚页面CSS样式
     */
    private String generateAdminPunishmentPageCSS() {
        // 使用独立的CSS生成器
        AdminPunishmentCSSGenerator cssGenerator = new AdminPunishmentCSSGenerator(plugin);
        return cssGenerator.generateAdminPunishmentPageCSS() +
                actionGenerator.generateActionStyles() +
                AdminPlayerActionJS.generateModalStyles();
    }



    /**
     * 生成管理员处罚页面JavaScript
     */
    private String generateAdminPunishmentPageJS() {
        return "// 页面加载完成后执行\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    // 初始化主题\n" +
                "    initTheme();\n" +
                "    \n" +
                "    // 初始化工具提示\n" +
                "    initTooltips();\n" +
                "    \n" +
                "    // 初始化表格功能\n" +
                "    initTableFeatures();\n" +
                "    \n" +
                "    // 初始化搜索功能\n" +
                "    initSearchFeatures();\n" +
                "    \n" +
                "    // 初始化动态时间显示\n" +
                "    initRelativeTime();\n" +
                "});\n" +
                "\n" +
                "// 初始化主题\n" +
                "function initTheme() {\n" +
                "    const savedTheme = localStorage.getItem('punishment-theme') || 'light';\n" +
                "    const themeIcon = document.querySelector('.theme-icon') || document.querySelector('.admin-theme-icon');\n"
                +
                "    \n" +
                "    if (savedTheme === 'dark') {\n" +
                "        document.documentElement.classList.add('dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '☀️';\n" +
                "    } else {\n" +
                "        document.documentElement.classList.remove('dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '🌙';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 切换主题\n" +
                "function toggleTheme() {\n" +
                "    const isDark = document.documentElement.classList.contains('dark');\n" +
                "    const themeIcon = document.querySelector('.theme-icon');\n" +
                "    \n" +
                "    if (isDark) {\n" +
                "        document.documentElement.classList.remove('dark');\n" +
                "        localStorage.setItem('punishment-theme', 'light');\n" +
                "        if (themeIcon) themeIcon.textContent = '🌙';\n" +
                "        showNotification('已切换到浅色主题', 'success');\n" +
                "    } else {\n" +
                "        document.documentElement.classList.add('dark');\n" +
                "        localStorage.setItem('punishment-theme', 'dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '☀️';\n" +
                "        showNotification('已切换到深色主题', 'success');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新玩家头像预览\n" +
                "function updatePlayerAvatar(playerName) {\n" +
                "    const avatarPreview = document.getElementById('playerAvatarPreview');\n" +
                "    const headerAvatarPreview = document.getElementById('headerPlayerAvatar');\n" +
                "    \n" +
                "    if (!playerName || playerName.trim().length < 3) {\n" +
                "        if (avatarPreview) avatarPreview.style.display = 'none';\n" +
                "        if (headerAvatarPreview) headerAvatarPreview.style.display = 'none';\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    // 清理玩家名（移除特殊字符，只保留字母数字和下划线）\n" +
                "    const cleanName = playerName.trim().replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "    \n" +
                "    if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "        const avatarUrl = `https://mc-heads.net/avatar/${cleanName}/32`;\n" +
                "        \n" +
                "        // 更新页面搜索框头像\n" +
                "        if (avatarPreview) {\n" +
                "            avatarPreview.src = avatarUrl;\n" +
                "            avatarPreview.style.display = 'block';\n" +
                "            \n" +
                "            // 处理头像加载失败\n" +
                "            avatarPreview.onerror = function() {\n" +
                "                this.style.display = 'none';\n" +
                "            };\n" +
                "        }\n" +
                "        \n" +
                "        // 更新头部搜索框头像\n" +
                "        if (headerAvatarPreview) {\n" +
                "            headerAvatarPreview.src = avatarUrl;\n" +
                "            headerAvatarPreview.style.display = 'block';\n" +
                "            \n" +
                "            // 处理头像加载失败\n" +
                "            headerAvatarPreview.onerror = function() {\n" +
                "                this.style.display = 'none';\n" +
                "            };\n" +
                "        }\n" +
                "    } else {\n" +
                "        if (avatarPreview) avatarPreview.style.display = 'none';\n" +
                "        if (headerAvatarPreview) headerAvatarPreview.style.display = 'none';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 处理搜索表单提交（按照 next-litebans PlayerInput 逻辑）\n" +
                "function handleSearchSubmit(event) {\n" +
                "    event.preventDefault();\n" +
                "    const form = event.target;\n" +
                "    const playerName = form.querySelector('input[name=\"player\"]').value.trim();\n" +
                "    \n" +
                "    if (playerName) {\n" +
                "        // 检查是否是有效的玩家名\n" +
                "        const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "        \n" +
                "        if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "            // 按照 next-litebans 逻辑，搜索玩家时跳转到管理员玩家专用页面\n" +
                "            checkAdminPlayerAndRedirect(cleanName);\n" +
                "        } else {\n" +
                "            showNotification('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）', 'error');\n" +
                "        }\n" +
                "    }\n" +
                "    return false;\n" +
                "}\n" +
                "\n" +
                "// 检查玩家是否存在并跳转（按照 next-litebans checkPlayer 逻辑）\n" +
                "function checkPlayerAndRedirect(playerName) {\n" +
                "    // 显示加载状态\n" +
                "    showNotification('正在搜索玩家...', 'info');\n" +
                "    \n" +
                "    // 简化版本：直接跳转到玩家页面，让服务器端处理玩家是否存在的检查\n" +
                "    window.location.href = '/player/' + encodeURIComponent(playerName);\n" +
                "}\n" +
                "\n" +
                "// 处理搜索回车事件\n" +
                "function handleSearchEnter(event) {\n" +
                "    if (event.key === 'Enter') {\n" +
                "        event.preventDefault();\n" +
                "        const playerName = event.target.value.trim();\n" +
                "        \n" +
                "        if (playerName) {\n" +
                "            // 检查是否是有效的玩家名\n" +
                "            const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "            \n" +
                "            if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "                // 按照 next-litebans 逻辑，搜索玩家时跳转到管理员玩家专用页面\n" +
                "                checkAdminPlayerAndRedirect(cleanName);\n" +
                "            } else {\n" +
                "                showNotification('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）', 'error');\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 初始化工具提示\n" +
                "function initTooltips() {\n" +
                "    const elements = document.querySelectorAll('[title]');\n" +
                "    elements.forEach(element => {\n" +
                "        element.addEventListener('mouseenter', showTooltip);\n" +
                "        element.addEventListener('mouseleave', hideTooltip);\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 显示工具提示\n" +
                "function showTooltip(event) {\n" +
                "    const element = event.target;\n" +
                "    const title = element.getAttribute('title');\n" +
                "    if (!title) return;\n" +
                "    \n" +
                "    // 创建工具提示元素\n" +
                "    const tooltip = document.createElement('div');\n" +
                "    tooltip.className = 'custom-tooltip';\n" +
                "    tooltip.textContent = title;\n" +
                "    tooltip.style.cssText = `\n" +
                "        position: absolute;\n" +
                "        background: rgba(0, 0, 0, 0.8);\n" +
                "        color: white;\n" +
                "        padding: 8px 12px;\n" +
                "        border-radius: 6px;\n" +
                "        font-size: 0.8em;\n" +
                "        z-index: 1000;\n" +
                "        pointer-events: none;\n" +
                "        max-width: 300px;\n" +
                "        word-wrap: break-word;\n" +
                "    `;\n" +
                "    \n" +
                "    document.body.appendChild(tooltip);\n" +
                "    \n" +
                "    // 定位工具提示\n" +
                "    const rect = element.getBoundingClientRect();\n" +
                "    tooltip.style.left = rect.left + 'px';\n" +
                "    tooltip.style.top = (rect.bottom + 5) + 'px';\n" +
                "    \n" +
                "    element.tooltipElement = tooltip;\n" +
                "    element.removeAttribute('title');\n" +
                "    element.originalTitle = title;\n" +
                "}\n" +
                "\n" +
                "// 隐藏工具提示\n" +
                "function hideTooltip(event) {\n" +
                "    const element = event.target;\n" +
                "    if (element.tooltipElement) {\n" +
                "        document.body.removeChild(element.tooltipElement);\n" +
                "        element.tooltipElement = null;\n" +
                "        element.setAttribute('title', element.originalTitle);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 初始化表格功能\n" +
                "function initTableFeatures() {\n" +
                "    // 表格行悬停效果现在通过CSS处理，不需要JavaScript\n" +
                "    // 这里可以添加其他表格相关的功能\n" +
                "}\n" +
                "\n" +
                "// 初始化搜索功能\n" +
                "function initSearchFeatures() {\n" +
                "    const searchInput = document.querySelector('.search-input');\n" +
                "    if (searchInput) {\n" +
                "        // 回车键搜索\n" +
                "        searchInput.addEventListener('keypress', function(event) {\n" +
                "            if (event.key === 'Enter') {\n" +
                "                event.preventDefault();\n" +
                "                this.closest('form').submit();\n" +
                "            }\n" +
                "        });\n" +
                "        \n" +
                "        // 自动聚焦\n" +
                "        if (!searchInput.value) {\n" +
                "            searchInput.focus();\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 复制UUID到剪贴板\n" +
                "function copyUUID(uuid) {\n" +
                "    if (navigator.clipboard) {\n" +
                "        navigator.clipboard.writeText(uuid).then(function() {\n" +
                "            showNotification('UUID已复制到剪贴板', 'success');\n" +
                "        }).catch(function() {\n" +
                "            fallbackCopyUUID(uuid);\n" +
                "        });\n" +
                "    } else {\n" +
                "        fallbackCopyUUID(uuid);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 备用复制方法\n" +
                "function fallbackCopyUUID(uuid) {\n" +
                "    const textArea = document.createElement('textarea');\n" +
                "    textArea.value = uuid;\n" +
                "    document.body.appendChild(textArea);\n" +
                "    textArea.select();\n" +
                "    try {\n" +
                "        document.execCommand('copy');\n" +
                "        showNotification('UUID已复制到剪贴板', 'success');\n" +
                "    } catch (err) {\n" +
                "        showNotification('复制失败，请手动复制', 'error');\n" +
                "    }\n" +
                "    document.body.removeChild(textArea);\n" +
                "}\n" +
                "\n" +
                "// 显示通知\n" +
                "function showNotification(message, type) {\n" +
                "    const notification = document.createElement('div');\n" +
                "    notification.className = 'notification notification-' + type;\n" +
                "    notification.textContent = message;\n" +
                "    notification.style.cssText = `\n" +
                "        position: fixed;\n" +
                "        top: 20px;\n" +
                "        right: 20px;\n" +
                "        padding: 15px 20px;\n" +
                "        border-radius: 8px;\n" +
                "        color: white;\n" +
                "        font-weight: 600;\n" +
                "        z-index: 10000;\n" +
                "        animation: slideIn 0.3s ease;\n" +
                "    `;\n" +
                "    \n" +
                "    if (type === 'success') {\n" +
                "        notification.style.backgroundColor = '#10ac84';\n" +
                "    } else if (type === 'error') {\n" +
                "        notification.style.backgroundColor = '#e74c3c';\n" +
                "    }\n" +
                "    \n" +
                "    document.body.appendChild(notification);\n" +
                "    \n" +
                "    setTimeout(() => {\n" +
                "        notification.style.animation = 'slideOut 0.3s ease';\n" +
                "        setTimeout(() => {\n" +
                "            if (notification.parentNode) {\n" +
                "                document.body.removeChild(notification);\n" +
                "            }\n" +
                "        }, 300);\n" +
                "    }, 3000);\n" +
                "}\n" +
                "\n" +
                "// 添加动画样式\n" +
                "const style = document.createElement('style');\n" +
                "style.textContent = `\n" +
                "    @keyframes slideIn {\n" +
                "        from { transform: translateX(100%); opacity: 0; }\n" +
                "        to { transform: translateX(0); opacity: 1; }\n" +
                "    }\n" +
                "    @keyframes slideOut {\n" +
                "        from { transform: translateX(0); opacity: 1; }\n" +
                "        to { transform: translateX(100%); opacity: 0; }\n" +
                "    }\n" +
                "`;\n" +
                "document.head.appendChild(style);\n" +
                "\n" +
                "// 过滤器功能（按照 next-litebans AvatarName 和 PlayerFilter 组件）\n" +
                "function addFilter(paramName, paramValue) {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.set(paramName, paramValue);\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "}\n" +
                "\n" +
                "function removePlayerFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('player');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = url.pathname;\n" +
                "    } else {\n" +
                "        window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "function removeStaffFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('staff');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = url.pathname;\n" +
                "    } else {\n" +
                "        window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 动态时间显示功能（按照 next-litebans RelativeTimeTooltip）\n" +
                "function initRelativeTime() {\n" +
                "    updateRelativeTime();\n" +
                "    // 每秒更新一次，提供实时的倒计时效果\n" +
                "    setInterval(updateRelativeTime, 1000);\n" +
                "}\n" +
                "\n" +
                "function updateRelativeTime() {\n" +
                "    const elements = document.querySelectorAll('.relative-time');\n" +
                "    const now = new Date().getTime();\n" +
                "    \n" +
                "    elements.forEach(element => {\n" +
                "        const timestamp = parseInt(element.getAttribute('data-timestamp'));\n" +
                "        if (timestamp && timestamp > 0) {\n" +
                "            // 检查处罚状态 - 如果处罚已被撤销或过期，停止倒计时\n" +
                "            const statusDot = element.parentElement.querySelector('.status-dot, .admin-status-dot');\n"
                +
                "            const isInactive = statusDot && (statusDot.classList.contains('status-expired') || \n" +
                "                                           statusDot.classList.contains('admin-status-expired') ||\n" +
                "                                           statusDot.classList.contains('status-removed') ||\n" +
                "                                           statusDot.classList.contains('admin-status-removed'));\n" +
                "            \n" +
                "            if (isInactive) {\n" +
                "                // 处罚已被撤销或过期，显示静态文本\n" +
                "                element.textContent = '已撤销';\n" +
                "                element.title = '此处罚已被撤销';\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            // 检查是否是管理员表格视图或普通表格视图\n" +
                "            const isTableView = element.closest('.until-cell') !== null || element.closest('.admin-until-cell') !== null;\n"
                +
                "            const relativeText = getRelativeTimeText(timestamp, now, isTableView);\n" +
                "            if (relativeText) {\n" +
                "                if (isTableView && relativeText.includes('\\n')) {\n" +
                "                    // 表格视图：分两行显示\n" +
                "                    element.innerHTML = relativeText.replace('\\n', '<br>');\n" +
                "                } else {\n" +
                "                    // 详细视图：一行显示\n" +
                "                    element.textContent = relativeText.replace('\\n', ' ');\n" +
                "                }\n" +
                "                element.title = new Date(timestamp).toLocaleString('zh-CN');\n" +
                "            }\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "function getRelativeTimeText(timestamp, now, isTableView = false) {\n" +
                "    const diff = timestamp - now;\n" +
                "    \n" +
                "    // 如果是过去的时间\n" +
                "    if (diff < 0) {\n" +
                "        const pastDiff = Math.abs(diff);\n" +
                "        const pastSeconds = Math.floor(pastDiff / 1000);\n" +
                "        const pastMinutes = Math.floor(pastSeconds / 60);\n" +
                "        const pastHours = Math.floor(pastMinutes / 60);\n" +
                "        const pastDays = Math.floor(pastHours / 24);\n" +
                "        \n" +
                "        if (pastDays > 0) {\n" +
                "            return `已过期 ${pastDays}天`;\n" +
                "        } else if (pastHours > 0) {\n" +
                "            return `已过期 ${pastHours}小时`;\n" +
                "        } else if (pastMinutes > 0) {\n" +
                "            return `已过期 ${pastMinutes}分钟`;\n" +
                "        } else {\n" +
                "            return '刚刚过期';\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    // 计算剩余时间\n" +
                "    const totalSeconds = Math.floor(diff / 1000);\n" +
                "    const totalMinutes = Math.floor(totalSeconds / 60);\n" +
                "    const totalHours = Math.floor(totalMinutes / 60);\n" +
                "    const totalDays = Math.floor(totalHours / 24);\n" +
                "    const totalMonths = Math.floor(totalDays / 30);\n" +
                "    const totalYears = Math.floor(totalDays / 365);\n" +
                "    \n" +
                "    // 计算各个单位的剩余值\n" +
                "    const years = totalYears;\n" +
                "    const months = Math.floor((totalDays % 365) / 30);\n" +
                "    const days = totalDays % 30;\n" +
                "    const hours = totalHours % 24;\n" +
                "    const minutes = totalMinutes % 60;\n" +
                "    const seconds = totalSeconds % 60;\n" +
                "    \n" +
                "    if (totalSeconds <= 0) {\n" +
                "        return '即将到期';\n" +
                "    }\n" +
                "    \n" +
                "    // 构建时间字符串\n" +
                "    let timeStr = '';\n" +
                "    let parts = [];\n" +
                "    \n" +
                "    // 添加年月日\n" +
                "    if (years > 0) {\n" +
                "        parts.push(`${years}年`);\n" +
                "    }\n" +
                "    if (months > 0) {\n" +
                "        parts.push(`${months}个月`);\n" +
                "    }\n" +
                "    if (days > 0) {\n" +
                "        parts.push(`${days}天`);\n" +
                "    }\n" +
                "    \n" +
                "    // 如果是表格视图且时间较短，分两行显示\n" +
                "    if (isTableView && years === 0 && months === 0) {\n" +
                "        let line1 = '';\n" +
                "        let line2 = '';\n" +
                "        \n" +
                "        if (days > 0) {\n" +
                "            line1 = `${days}天`;\n" +
                "            if (hours > 0) {\n" +
                "                line1 += `${hours}小时`;\n" +
                "            }\n" +
                "        } else if (hours > 0) {\n" +
                "            line1 = `${hours}小时`;\n" +
                "        }\n" +
                "        \n" +
                "        if (minutes > 0 || seconds > 0) {\n" +
                "            let timeParts = [];\n" +
                "            if (minutes > 0) timeParts.push(`${minutes}分`);\n" +
                "            if (seconds > 0) timeParts.push(`${seconds}秒`);\n" +
                "            line2 = timeParts.join('');\n" +
                "        }\n" +
                "        \n" +
                "        if (line1 && line2) {\n" +
                "            return timeStr + line1 + '\\n' + line2;\n" +
                "        } else if (line1) {\n" +
                "            return timeStr + line1;\n" +
                "        } else if (line2) {\n" +
                "            return timeStr + line2;\n" +
                "        }\n" +
                "    } else {\n" +
                "        // 详细视图或长时间：一行显示\n" +
                "        if (hours > 0 && years === 0 && months === 0) {\n" +
                "            parts.push(`${hours}小时`);\n" +
                "        }\n" +
                "        if (minutes > 0 && years === 0 && months === 0) {\n" +
                "            parts.push(`${minutes}分钟`);\n" +
                "        }\n" +
                "        if (seconds > 0 && years === 0 && months === 0 && days === 0) {\n" +
                "            parts.push(`${seconds}秒`);\n" +
                "        }\n" +
                "        \n" +
                "        // 最多显示3个单位\n" +
                "        if (parts.length > 3) {\n" +
                "            parts = parts.slice(0, 3);\n" +
                "        }\n" +
                "        \n" +
                "        return timeStr + parts.join('');\n" +
                "    }\n" +
                "    \n" +
                "    return '即将到期';\n" +
                "}\n" +
                "\n" +
                "// ==================== 管理员专用函数 ==================== \n" +
                "\n" +
                "// 管理员专用：更新玩家头像预览\n" +
                "function updateAdminPlayerAvatar(playerName) {\n" +
                "    const avatarPreview = document.getElementById('playerAvatarPreview');\n" +
                "    const headerAvatarPreview = document.getElementById('adminHeaderPlayerAvatar');\n" +
                "    \n" +
                "    if (!playerName || playerName.trim().length < 3) {\n" +
                "        if (avatarPreview) avatarPreview.style.display = 'none';\n" +
                "        if (headerAvatarPreview) headerAvatarPreview.style.display = 'none';\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    // 清理玩家名（移除特殊字符，只保留字母数字和下划线）\n" +
                "    const cleanName = playerName.trim().replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "    \n" +
                "    if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "        const avatarUrl = `https://mc-heads.net/avatar/${cleanName}/32`;\n" +
                "        \n" +
                "        // 更新页面搜索框头像\n" +
                "        if (avatarPreview) {\n" +
                "            avatarPreview.src = avatarUrl;\n" +
                "            avatarPreview.style.display = 'block';\n" +
                "            \n" +
                "            // 处理头像加载失败\n" +
                "            avatarPreview.onerror = function() {\n" +
                "                this.style.display = 'none';\n" +
                "            };\n" +
                "        }\n" +
                "        \n" +
                "        // 更新管理员头部搜索框头像\n" +
                "        if (headerAvatarPreview) {\n" +
                "            headerAvatarPreview.src = avatarUrl;\n" +
                "            headerAvatarPreview.style.display = 'block';\n" +
                "            \n" +
                "            // 处理头像加载失败\n" +
                "            headerAvatarPreview.onerror = function() {\n" +
                "                this.style.display = 'none';\n" +
                "            };\n" +
                "        }\n" +
                "    } else {\n" +
                "        if (avatarPreview) avatarPreview.style.display = 'none';\n" +
                "        if (headerAvatarPreview) headerAvatarPreview.style.display = 'none';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：处理搜索回车事件\n" +
                "function handleAdminSearchEnter(event) {\n" +
                "    if (event.key === 'Enter') {\n" +
                "        event.preventDefault();\n" +
                "        const playerName = event.target.value.trim();\n" +
                "        \n" +
                "        if (playerName) {\n" +
                "            // 检查是否是有效的玩家名\n" +
                "            const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "            \n" +
                "            if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "                // 按照 next-litebans 逻辑，搜索玩家时跳转到管理员玩家专用页面\n" +
                "                checkAdminPlayerAndRedirect(cleanName);\n" +
                "            } else {\n" +
                "                showNotification('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）', 'error');\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：移除玩家过滤器\n" +
                "function removeAdminPlayerFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('player');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    // 确保在管理员页面内跳转\n" +
                "    let pathname = url.pathname;\n" +
                "    if (!pathname.startsWith('/admin-punishments')) {\n" +
                "        pathname = '/admin-punishments';\n" +
                "    }\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = pathname;\n" +
                "    } else {\n" +
                "        window.location.href = pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：移除管理员过滤器\n" +
                "function removeAdminStaffFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('staff');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    // 确保在管理员页面内跳转\n" +
                "    let pathname = url.pathname;\n" +
                "    if (!pathname.startsWith('/admin-punishments')) {\n" +
                "        pathname = '/admin-punishments';\n" +
                "    }\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = pathname;\n" +
                "    } else {\n" +
                "        window.location.href = pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：清除所有过滤器\n" +
                "function clearAdminAllFilters() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('player');\n" +
                "    url.searchParams.delete('staff');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    // 确保在管理员页面内跳转\n" +
                "    let pathname = url.pathname;\n" +
                "    if (!pathname.startsWith('/admin-punishments')) {\n" +
                "        pathname = '/admin-punishments';\n" +
                "    }\n" +
                "    \n" +
                "    window.location.href = pathname;\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：添加过滤器\n" +
                "function addAdminFilter(paramName, paramValue) {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.set(paramName, paramValue);\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    // 确保在管理员页面内跳转\n" +
                "    let pathname = url.pathname;\n" +
                "    if (!pathname.startsWith('/admin-punishments')) {\n" +
                "        pathname = '/admin-punishments';\n" +
                "    }\n" +
                "    \n" +
                "    window.location.href = pathname + '?' + url.searchParams.toString();\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：添加玩家过滤器\n" +
                "function addAdminPlayerFilter(playerName) {\n" +
                "    addAdminFilter('player', playerName);\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：添加执行者过滤器\n" +
                "function addAdminStaffFilter(staffName) {\n" +
                "    addAdminFilter('staff', staffName);\n" +
                "}\n" +
                "\n" +
                "// 玩家页面专用：添加过滤器（在当前页面内过滤）\n" +
                "function addPlayerPageFilter(paramName, paramValue) {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.set(paramName, paramValue);\n" +
                "    url.searchParams.delete('page'); // 重置到第一页\n" +
                "    \n" +
                "    // 在当前玩家页面内跳转，不跳转到管理员页面\n" +
                "    window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "}\n" +
                "\n" +
                "// 玩家页面专用：移除玩家过滤器\n" +
                "function removePlayerPagePlayerFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('player');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = url.pathname;\n" +
                "    } else {\n" +
                "        window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 玩家页面专用：移除执行者过滤器\n" +
                "function removePlayerPageStaffFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('staff');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = url.pathname;\n" +
                "    } else {\n" +
                "        window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 玩家页面专用：清除所有过滤器\n" +
                "function clearPlayerPageAllFilters() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('player');\n" +
                "    url.searchParams.delete('staff');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    window.location.href = url.pathname;\n" +
                "}\n" +
                "\n" +
                "// 检查管理员玩家是否存在并跳转（按照 next-litebans checkPlayer 逻辑）\n" +
                "function checkAdminPlayerAndRedirect(playerName) {\n" +
                "    // 显示加载状态\n" +
                "    showNotification('正在搜索玩家...', 'info');\n" +
                "    \n" +
                "    // 跳转到管理员玩家页面，让服务器端处理玩家是否存在的检查\n" +
                "    window.location.href = '/admin-player/' + encodeURIComponent(playerName);\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：主题切换\n" +
                "function toggleAdminTheme() {\n" +
                "    const isDark = document.documentElement.classList.contains('dark');\n" +
                "    const themeIcon = document.querySelector('.admin-theme-icon');\n" +
                "    \n" +
                "    if (isDark) {\n" +
                "        document.documentElement.classList.remove('dark');\n" +
                "        localStorage.setItem('punishment-theme', 'light');\n" +
                "        if (themeIcon) themeIcon.textContent = '🌙';\n" +
                "        showAdminNotification('已切换到浅色主题', 'success');\n" +
                "    } else {\n" +
                "        document.documentElement.classList.add('dark');\n" +
                "        localStorage.setItem('punishment-theme', 'dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '☀️';\n" +
                "        showAdminNotification('已切换到深色主题', 'success');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：处理搜索表单提交\n" +
                "function handleAdminSearchSubmit(event) {\n" +
                "    event.preventDefault();\n" +
                "    const form = event.target;\n" +
                "    const playerName = form.querySelector('input[name=\"player\"]').value.trim();\n" +
                "    \n" +
                "    if (playerName) {\n" +
                "        // 检查是否是有效的玩家名\n" +
                "        const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "        \n" +
                "        if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "            // 按照 next-litebans 逻辑，搜索玩家时跳转到管理员玩家专用页面\n" +
                "            checkAdminPlayerAndRedirect(cleanName);\n" +
                "        } else {\n" +
                "            showNotification('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）', 'error');\n" +
                "        }\n" +
                "    }\n" +
                "    return false;\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：复制UUID\n" +
                "function copyAdminUUID(uuid) {\n" +
                "    copyUUID(uuid);\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：显示通知\n" +
                "function showAdminNotification(message, type) {\n" +
                "    showNotification(message, type);\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：查看记录详情\n" +
                "function viewAdminRecordDetail(recordId, type) {\n" +
                "    window.location.href = '/admin-punishments/' + type + '?id=' + recordId;\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：撤销记录\n" +
                "function revokeAdminRecord(recordId, type, playerName) {\n" +
                "    showRevokeModal(recordId, type, playerName);\n" +
                "}\n" +
                "\n" +
                "// 显示撤销确认弹窗\n" +
                "function showRevokeModal(recordId, type, playerName) {\n" +
                "    // 创建弹窗HTML\n" +
                "    const modalHtml = `\n" +
                "        <div class=\\\"revoke-modal-overlay\\\" id=\\\"revokeModalOverlay\\\">\n" +
                "            <div class=\\\"revoke-modal\\\">\n" +
                "                <div class=\\\"revoke-modal-header\\\">\n" +
                "                    <div class=\\\"revoke-modal-icon\\\">\n" +
                "                        <svg viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\">\n"
                +
                "                            <path d=\\\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\\\"/>\n"
                +
                "                        </svg>\n" +
                "                    </div>\n" +
                "                    <h3 class=\\\"revoke-modal-title\\\">确认撤销处罚</h3>\n" +
                "                    <button class=\\\"revoke-modal-close\\\" onclick=\\\"closeRevokeModal()\\\">\n" +
                "                        <svg viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\">\n"
                +
                "                            <path d=\\\"M18 6L6 18M6 6l12 12\\\"/>\n" +
                "                        </svg>\n" +
                "                    </button>\n" +
                "                </div>\n" +
                "                <div class=\\\"revoke-modal-body\\\">\n" +
                "                    <div class=\\\"revoke-player-section\\\">\n" +
                "                        <div class=\\\"revoke-player-info\\\">\n" +
                "                            <img class=\\\"revoke-player-avatar\\\" src=\\\"https://minotar.net/helm/${playerName || 'Steve'}/48\\\" alt=\\\"${playerName || 'Unknown'}\\\" onerror=\\\"this.src='https://minotar.net/helm/Steve/48'\\\">\n"
                +
                "                            <div class=\\\"revoke-player-details\\\">\n" +
                "                                <h4 class=\\\"revoke-player-name\\\">${playerName || 'Unknown Player'}</h4>\n"
                +
                "                                <span class=\\\"revoke-player-label\\\">被处罚玩家</span>\n" +
                "                            </div>\n" +
                "                        </div>\n" +
                "                    </div>\n" +
                "                    <div class=\\\"revoke-info-card\\\">\n" +
                "                        <div class=\\\"revoke-info-item\\\">\n" +
                "                            <span class=\\\"revoke-info-label\\\">记录ID:</span>\n" +
                "                            <span class=\\\"revoke-info-value\\\">${recordId}</span>\n" +
                "                        </div>\n" +
                "                        <div class=\\\"revoke-info-item\\\">\n" +
                "                            <span class=\\\"revoke-info-label\\\">类型:</span>\n" +
                "                            <span class=\\\"revoke-info-value revoke-type-${type}\\\">${getTypeDisplayName(type)}</span>\n"
                +
                "                        </div>\n" +
                "                    </div>\n" +
                "                    <div class=\\\"revoke-reason-section\\\">\n" +
                "                        <label for=\\\"revokeReason\\\" class=\\\"revoke-reason-label\\\">\n" +
                "                            <svg viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\">\n"
                +
                "                                <path d=\\\"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z\\\"/>\n"
                +
                "                                <polyline points=\\\"14,2 14,8 20,8\\\"/>\n" +
                "                                <line x1=\\\"16\\\" y1=\\\"13\\\" x2=\\\"8\\\" y2=\\\"13\\\"/>\n" +
                "                                <line x1=\\\"16\\\" y1=\\\"17\\\" x2=\\\"8\\\" y2=\\\"17\\\"/>\n" +
                "                                <polyline points=\\\"10,9 9,9 8,9\\\"/>\n" +
                "                            </svg>\n" +
                "                            撤销原因\n" +
                "                        </label>\n" +
                "                        <textarea id=\\\"revokeReason\\\" class=\\\"revoke-reason-input\\\" \n" +
                "                                  placeholder=\\\"请输入撤销原因（可选）...\\\" \n" +
                "                                  rows=\\\"3\\\">管理员撤销</textarea>\n" +
                "                    </div>\n" +
                "                    <div class=\\\"revoke-warning\\\">\n" +
                "                        <svg viewBox=\\\"0 0 24 24\\\" fill=\\\"none\\\" stroke=\\\"currentColor\\\" stroke-width=\\\"2\\\">\n"
                +
                "                            <path d=\\\"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126zM12 15.75h.007v.008H12v-.008z\\\"/>\n"
                +
                "                        </svg>\n" +
                "                        <span>此操作不可撤销，请谨慎操作</span>\n" +
                "                    </div>\n" +
                "                </div>\n" +
                "                <div class=\\\"revoke-modal-footer\\\">\n" +
                "                    <button class=\\\"revoke-btn-cancel\\\" onclick=\\\"closeRevokeModal()\\\">取消</button>\n"
                +
                "                    <button class=\\\"revoke-btn-confirm\\\" onclick=\\\"confirmRevoke(${recordId}, '${type}')\\\">确认撤销</button>\n"
                +
                "                </div>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    `;\n" +
                "    \n" +
                "    // 添加到页面\n" +
                "    document.body.insertAdjacentHTML('beforeend', modalHtml);\n" +
                "    \n" +
                "    // 显示动画\n" +
                "    setTimeout(() => {\n" +
                "        document.getElementById('revokeModalOverlay').classList.add('show');\n" +
                "    }, 10);\n" +
                "    \n" +
                "    // 聚焦到原因输入框\n" +
                "    setTimeout(() => {\n" +
                "        document.getElementById('revokeReason').focus();\n" +
                "        document.getElementById('revokeReason').select();\n" +
                "    }, 300);\n" +
                "    \n" +
                "    // 添加键盘事件监听\n" +
                "    document.addEventListener('keydown', handleRevokeModalKeydown);\n" +
                "    \n" +
                "    // 点击遮罩层关闭弹窗\n" +
                "    document.getElementById('revokeModalOverlay').addEventListener('click', function(e) {\n" +
                "        if (e.target === this) {\n" +
                "            closeRevokeModal();\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 处理弹窗键盘事件\n" +
                "function handleRevokeModalKeydown(e) {\n" +
                "    if (e.key === 'Escape') {\n" +
                "        closeRevokeModal();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 关闭撤销弹窗\n" +
                "function closeRevokeModal() {\n" +
                "    const overlay = document.getElementById('revokeModalOverlay');\n" +
                "    if (overlay) {\n" +
                "        overlay.classList.remove('show');\n" +
                "        setTimeout(() => {\n" +
                "            overlay.remove();\n" +
                "        }, 300);\n" +
                "    }\n" +
                "    \n" +
                "    // 移除键盘事件监听\n" +
                "    document.removeEventListener('keydown', handleRevokeModalKeydown);\n" +
                "}\n" +
                "\n" +
                "// 确认撤销\n" +
                "function confirmRevoke(recordId, type) {\n" +
                "    const reason = document.getElementById('revokeReason').value.trim() || '管理员撤销';\n" +
                "    \n" +
                "    // 关闭弹窗\n" +
                "    closeRevokeModal();\n" +
                "    \n" +
                "    // 显示加载状态\n" +
                "    showAdminNotification('正在撤销处罚记录...', 'info');\n" +
                "    \n" +
                "    // 发送撤销请求\n" +
                "    const requestData = {\n" +
                "        action: 'revoke',\n" +
                "        recordId: recordId,\n" +
                "        type: type,\n" +
                "        reason: reason\n" +
                "    };\n" +
                "    \n" +
                "    fetch('/admin-punishments', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json',\n" +
                "        },\n" +
                "        body: JSON.stringify(requestData)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showAdminNotification('处罚记录已成功撤销', 'success');\n" +
                "            // 触发立即数据检测，无需刷新页面\n" +
                "            setTimeout(() => {\n" +
                "                checkForDataUpdates();\n" +
                "            }, 1000);\n" +
                "        } else {\n" +
                "            showAdminNotification('撤销失败: ' + (data.message || '未知错误'), 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        showAdminNotification('撤销请求失败: ' + error.message, 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 获取类型显示名称\n" +
                "function getTypeDisplayName(type) {\n" +
                "    const typeMap = {\n" +
                "        'ban': '🚫 封禁',\n" +
                "        'mute': '🔇 禁言',\n" +
                "        'kick': '🚫 踢出',\n" +
                "        'warn': '⚠️ 警告'\n" +
                "    };\n" +
                "    return typeMap[type] || type;\n" +
                "}\n" +
                "\n" +
                "// 管理员专用：批量撤销记录\n" +
                "function batchRevokeAdminRecords(recordIds, type) {\n" +
                "    if (!recordIds || recordIds.length === 0) {\n" +
                "        showAdminNotification('请选择要撤销的记录', 'warning');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    const reason = prompt('请输入批量撤销原因（可选）:', '管理员批量撤销');\n" +
                "    \n" +
                "    if (reason === null) {\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    if (!confirm('确定要批量撤销 ' + recordIds.length + ' 个处罚记录吗？')) {\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    showAdminNotification('正在批量撤销处罚记录...', 'info');\n" +
                "    \n" +
                "    const requestData = {\n" +
                "        action: 'batchRevoke',\n" +
                "        recordIds: recordIds,\n" +
                "        type: type,\n" +
                "        reason: reason || '管理员批量撤销'\n" +
                "    };\n" +
                "    \n" +
                "    fetch('/admin-punishments', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json',\n" +
                "        },\n" +
                "        body: JSON.stringify(requestData)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showAdminNotification('批量撤销完成: ' + (data.message || '操作成功'), 'success');\n" +
                "            // 触发立即数据检测，无需刷新页面\n" +
                "            setTimeout(() => {\n" +
                "                checkForDataUpdates();\n" +
                "            }, 1500);\n" +
                "        } else {\n" +
                "            showAdminNotification('批量撤销失败: ' + (data.message || '未知错误'), 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        showAdminNotification('批量撤销请求失败: ' + error.message, 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                // 只有在数据库启用时才包含自动更新功能
                (plugin.getConfig().getBoolean("litebans.enabled", false) ?
                    new RealTimeDataDetector(plugin).generateAutoUpdateFunctions() : "");
    }

    /**
     * 生成头像URL（支持UUID和玩家名称）
     */
    private String generateAvatarUrl(String identifier, String provider) {
        if (identifier == null || identifier.isEmpty()) {
            return "";
        }

        switch (provider.toLowerCase()) {
            case "crafatar":
                return "https://crafatar.com/avatars/" + identifier + "?size=32&overlay";
            case "mc-heads":
                return "https://mc-heads.net/avatar/" + identifier + "/32";
            case "minotar":
            default:
                return "https://minotar.net/helm/" + identifier + "/32";
        }
    }

    /**
     * 生成过滤器标签区域（按照 next-litebans Filters 组件）
     */
    private String generateFiltersSection(String playerFilter, String staffFilter) {
        StringBuilder html = new StringBuilder();

        // 只有当有过滤参数时才显示过滤器区域
        if ((playerFilter != null && !playerFilter.isEmpty()) || (staffFilter != null && !staffFilter.isEmpty())) {
            html.append("        <div class=\"filters-section\">\n");
            html.append("            <div class=\"filters-container\">\n");

            // 玩家过滤器
            if (playerFilter != null && !playerFilter.isEmpty()) {
                html.append("                <div class=\"filter-tag player-filter\">\n");
                html.append("                    <span class=\"filter-label\">玩家</span>\n");
                html.append("                    <div class=\"filter-separator\"></div>\n");
                html.append("                    <div class=\"filter-badge\">\n");

                // 检查是否是控制台
                boolean isConsolePlayer = "Console".equalsIgnoreCase(playerFilter) || "控制台".equals(playerFilter);
                if (isConsolePlayer) {
                    html.append(
                            "                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"filter-avatar\">\n");
                    html.append("                        <span class=\"filter-name\">控制台</span>\n");
                } else {
                    // 玩家头像
                    String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
                    String avatarUrl = generateAvatarUrl(playerFilter, avatarProvider);
                    html.append("                        <img src=\"").append(avatarUrl)
                            .append("\" alt=\"头像\" class=\"filter-avatar\" onerror=\"this.style.display='none'\">\n");
                    html.append("                        <span class=\"filter-name\">").append(escapeHtml(playerFilter))
                            .append("</span>\n");
                }

                html.append("                    </div>\n");
                html.append(
                        "                    <button class=\"filter-remove\" onclick=\"removePlayerFilter()\" title=\"移除玩家过滤器\">\n");
                html.append("                        <span class=\"remove-icon\">✕</span>\n");
                html.append("                    </button>\n");
                html.append("                </div>\n");
            }

            // 执行者过滤器
            if (staffFilter != null && !staffFilter.isEmpty()) {
                html.append("                <div class=\"filter-tag staff-filter\">\n");
                html.append("                    <span class=\"filter-label\">执行者</span>\n");
                html.append("                    <div class=\"filter-separator\"></div>\n");
                html.append("                    <div class=\"filter-badge\">\n");

                // 检查是否是控制台
                boolean isConsoleStaff = "Console".equalsIgnoreCase(staffFilter) || "控制台".equals(staffFilter);
                if (isConsoleStaff) {
                    html.append(
                            "                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"filter-avatar\">\n");
                    html.append("                        <span class=\"filter-name\">控制台</span>\n");
                } else {
                    // 执行者头像和名称
                    String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
                    String avatarUrl = generateAvatarUrl(staffFilter, avatarProvider);
                    html.append("                        <img src=\"").append(avatarUrl)
                            .append("\" alt=\"头像\" class=\"filter-avatar\" onerror=\"this.style.display='none'\">\n");

                    // 如果是UUID格式，尝试获取玩家名称
                    String displayName = staffFilter;
                    if (isUuidFormat(staffFilter)) {
                        String playerName = getPlayerNameByUuid(staffFilter);
                        if (playerName != null && !playerName.isEmpty()) {
                            displayName = playerName;
                        }
                    }
                    html.append("                        <span class=\"filter-name\">").append(escapeHtml(displayName))
                            .append("</span>\n");
                }

                html.append("                    </div>\n");
                html.append(
                        "                    <button class=\"filter-remove\" onclick=\"removeStaffFilter()\" title=\"移除执行者过滤器\">\n");
                html.append("                        <span class=\"remove-icon\">✕</span>\n");
                html.append("                    </button>\n");
                html.append("                </div>\n");
            }

            html.append("            </div>\n");
            html.append("        </div>\n");
        }

        return html.toString();
    }

    /**
     * 生成处罚信息卡片（按照 next-litebans PunishmentInfoCard）
     */
    private String generatePunishmentInfoCard(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        html.append("            <section class=\"punishment-info-section\">\n");
        html.append("                <div class=\"punishment-info-card\">\n");

        // 玩家信息区域
        html.append("                    <div class=\"player-info-area\">\n");
        html.append("                        <h2 class=\"info-area-title\">玩家</h2>\n");

        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName);

        if (isConsolePlayer) {
            html.append(
                    "                        <img src=\"/static/console-body.webp\" alt=\"控制台\" class=\"player-body-image\" onerror=\"this.src='/static/console.webp'\">\n");
        } else {
            String bustUrl = "https://vzge.me/body/512/" + playerName + ".png";
            html.append("                        <img src=\"").append(bustUrl).append("\" alt=\"")
                    .append(escapeHtml(playerName)).append("\" class=\"player-body-image\" ")
                    .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(playerName)
                    .append("/512'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://visage.surgeplay.com/body/512/")
                    .append(playerName).append("';}\">\n");
        }

        html.append("                        <div class=\"player-name-area\">\n");
        String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
        String avatarUrl = generateAvatarUrl(playerName, avatarProvider);
        html.append("                            <img src=\"").append(avatarUrl)
                .append("\" alt=\"头像\" class=\"player-avatar-large\">\n");
        html.append("                            <p class=\"player-name-large\">").append(escapeHtml(playerName))
                .append("</p>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");

        // 详细信息区域
        html.append("                    <div class=\"detail-info-area\">\n");

        // 处罚原因
        html.append("                        <div class=\"detail-item\">\n");
        html.append("                            <h3 class=\"detail-label\">📜 处罚原因</h3>\n");
        html.append("                            <p class=\"detail-value\">")
                .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因")).append("</p>\n");
        html.append("                        </div>\n");

        // 处罚时间
        html.append("                        <div class=\"detail-item\">\n");
        html.append("                            <h3 class=\"detail-label\">📅 处罚时间</h3>\n");
        html.append("                            <p class=\"detail-value\">")
                .append(escapeHtml(record.getFormattedTime())).append("</p>\n");
        html.append("                        </div>\n");

        // 到期时间（如果有）
        if (record.getUntil() != null) {
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">⏰ 到期时间</h3>\n");
            html.append("                            <p class=\"detail-value\">");

            // 添加状态点（按照 next-litebans PunishmentStatusDot）
            if (record.isActive()) {
                html.append("<span class=\"status-dot status-active\" title=\"生效中\"></span>");
            } else {
                html.append("<span class=\"status-dot status-expired\" title=\"已过期\"></span>");
            }

            // 检查是否为永久处罚
            if (record.getUntil().getYear() > 2100) {
                // 永久处罚，直接显示"永久"
                html.append("永久</p>\n");
            } else {
                // 非永久处罚，添加动态时间显示
                long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                html.append("<span class=\"relative-time\" data-timestamp=\"").append(timestamp).append("\">");
                html.append(escapeHtml(record.getFormattedUntil())).append("</span></p>\n");
            }
            html.append("                        </div>\n");
        }

        // 服务器信息（按照 next-litebans originServer）
        if (record.getServerOrigin() != null && !record.getServerOrigin().isEmpty()) {
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">🌍 服务器</h3>\n");
            html.append("                            <p class=\"detail-value\">")
                    .append(escapeHtml(record.getServerOrigin())).append("</p>\n");
            html.append("                        </div>\n");
        } else {
            // 如果没有服务器信息，显示默认值
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">🌍 服务器</h3>\n");
            html.append("                            <p class=\"detail-value\">未知</p>\n");
            html.append("                        </div>\n");
        }

        // 根据处罚类型添加特定信息
        if (record.getType() == PunishmentRecord.PunishmentType.WARN) {
            // 警告特有信息：是否已通知
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">🔔 已通知</h3>\n");
            html.append("                            <p class=\"detail-value\">").append(record.isSilent() ? "否" : "是")
                    .append("</p>\n");
            html.append("                        </div>\n");
        }

        html.append("                    </div>\n");

        // 执行者信息区域
        html.append("                    <div class=\"staff-info-area\">\n");
        html.append("                        <h2 class=\"info-area-title\">执行者</h2>\n");

        String staffName = record.getBannedByName();
        boolean isConsoleStaff = staffName == null || "Console".equalsIgnoreCase(staffName);

        if (isConsoleStaff) {
            html.append(
                    "                        <img src=\"/static/console-body.webp\" alt=\"控制台\" class=\"staff-body-image scale-x-[-1]\" onerror=\"this.src='/static/console.webp'\">\n");
            staffName = "控制台";
        } else {
            String staffBustUrl = "https://vzge.me/body/512/" + staffName + ".png";
            html.append("                        <img src=\"").append(staffBustUrl).append("\" alt=\"")
                    .append(escapeHtml(staffName)).append("\" class=\"staff-body-image scale-x-[-1]\" ")
                    .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(staffName)
                    .append("/512'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://visage.surgeplay.com/body/512/")
                    .append(staffName).append("';}\">\n");
        }

        html.append("                        <div class=\"staff-name-area\">\n");
        if (isConsoleStaff) {
            html.append(
                    "                            <img src=\"/static/console.webp\" alt=\"控制台\" class=\"staff-avatar-large\">\n");
        } else {
            String staffAvatarUrl = generateAvatarUrl(staffName, avatarProvider);
            html.append("                            <img src=\"").append(staffAvatarUrl)
                    .append("\" alt=\"头像\" class=\"staff-avatar-large\">\n");
        }
        html.append("                            <p class=\"staff-name-large\">").append(escapeHtml(staffName))
                .append("</p>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");

        html.append("                </div>\n");
        html.append("            </section>\n");

        return html.toString();
    }

    /**
     * 生成玩家专用页面（按照 next-litebans /@{player} 页面布局）
     */
    public String generatePlayerPage(String playerName, int page, String staffFilter) {
        return generatePlayerPage(playerName, page, staffFilter, null);
    }

    /**
     * 生成玩家专用页面（支持类型过滤）
     */
    public String generatePlayerPage(String playerName, int page, String staffFilter,
            PunishmentRecord.PunishmentType typeFilter) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(escapeHtml(playerName)).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generateAdminPunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        // 顶部导航栏
        html.append(generateSiteHeader(null, null, null));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 玩家页面内容（按照 next-litebans 布局）
        html.append("        <div class=\"player-page-container\">\n");

        // 玩家信息头部（按照 next-litebans 玩家页面头部）
        html.append(generatePlayerPageHeader(playerName, typeFilter));

        // 处罚记录表格
        html.append("            <section class=\"player-punishments-section\">\n");

        // 获取玩家的处罚记录
        List<PunishmentRecord> records = new java.util.ArrayList<>();
        int totalRecords = 0;

        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            if (typeFilter != null) {
                // 如果有类型过滤，只获取该类型的记录
                records = plugin.getPunishmentManager().searchPunishmentRecords(typeFilter, playerName, page, 20);
                totalRecords = getPlayerPunishmentCount(playerName, typeFilter);
                plugin.getLogger().info(
                        "获取玩家 " + playerName + " 的 " + typeFilter.getDisplayName() + " 记录，共 " + records.size() + " 条");
            } else {
                // 没有类型过滤，获取所有记录
                records = plugin.getPunishmentManager().getPunishmentsByPlayer(playerName, page, 20);
                totalRecords = plugin.getPunishmentManager().getPlayerPunishmentCount(playerName);
                plugin.getLogger().info("获取玩家 " + playerName + " 的所有记录，共 " + records.size() + " 条");
            }
        }

        // 如果有执行者过滤，进一步过滤记录
        if (staffFilter != null && !staffFilter.isEmpty()) {
            records = records.stream()
                    .filter(record -> {
                        String staffName = record.getBannedByName();
                        String staffUuid = record.getBannedByUuid();

                        // 检查是否是控制台过滤（支持中英文）
                        boolean isConsoleFilter = "Console".equalsIgnoreCase(staffFilter) || "控制台".equals(staffFilter);
                        boolean isConsoleRecord = staffName == null || "Console".equalsIgnoreCase(staffName) || "控制台".equals(staffName);

                        return (staffName != null && staffName.equalsIgnoreCase(staffFilter)) ||
                                (staffUuid != null && staffUuid.equalsIgnoreCase(staffFilter)) ||
                                (isConsoleFilter && isConsoleRecord);
                    })
                    .collect(java.util.stream.Collectors.toList());
        }

        // 过滤器区域
        html.append(generateFiltersSection(playerName, staffFilter));

        // 记录表格
        if (records.isEmpty()) {
            html.append("                <div class=\"no-records\">\n");
            html.append("                    <h3>暂无处罚记录</h3>\n");
            html.append("                    <p>该玩家目前没有任何处罚记录。</p>\n");
            html.append("                </div>\n");
        } else {
            html.append(generatePlayerPunishmentTable(records));
        }

        html.append("            </section>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");

        // 添加模态框
        html.append(actionGenerator.generateAllModals());

        // JavaScript
        html.append("    <script>\n");
        html.append(generateAdminPunishmentPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成玩家页面专用的处罚记录表格（使用玩家页面过滤函数）
     */
    private String generatePlayerPunishmentTable(List<PunishmentRecord> records) {
        // 直接使用原来的表格生成方法，但替换其中的过滤函数调用
        String originalTable = generatePunishmentTable(records);

        // 将管理员过滤函数替换为玩家页面过滤函数
        String playerPageTable = originalTable
                .replace("addAdminFilter('player',", "addPlayerPageFilter('player',")
                .replace("addAdminFilter('staff',", "addPlayerPageFilter('staff',");

        return playerPageTable;
    }

    /**
     * 生成玩家页面信息头部（按照 next-litebans 玩家页面头部）
     */
    private String generatePlayerPageHeader(String playerName) {
        return generatePlayerPageHeader(playerName, null);
    }

    /**
     * 生成玩家页面信息头部（支持类型过滤高亮）
     */
    private String generatePlayerPageHeader(String playerName, PunishmentRecord.PunishmentType activeType) {
        StringBuilder html = new StringBuilder();

        // 使用 next-litebans 的确切布局结构
        html.append(
                "            <div class=\"flex h-full flex-col items-center gap-4 py-8 md:py-12 md:pb-8 lg:py-18\">\n");
        html.append("                <div class=\"space-y-2 md:flex md:space-x-4\">\n");

        // 半身皮肤图片（完全按照 next-litebans）
        String bustUrl = "https://vzge.me/bust/512/" + playerName + ".png";
        html.append("                    <img src=\"").append(bustUrl).append("\" alt=\"")
                .append(escapeHtml(playerName)).append("\" width=\"192\" height=\"192\" class=\"mx-auto\" ")
                .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(playerName)
                .append("/192'; ")
                .append("this.onerror=function(){this.onerror=null; this.src='https://visage.surgeplay.com/bust/256/")
                .append(playerName).append("';}\">\n");

        // 玩家详情区域
        html.append("                    <div class=\"md:w-[350px] md:py-4 space-y-1\">\n");
        html.append(
                "                        <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1] player-name-clickable\" ")
                .append("onclick=\"addPlayerPageFilter('player', '").append(escapeHtml(playerName)).append("'); return false;\" ")
                .append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\">")
                .append(escapeHtml(playerName)).append("</h1>\n");
        html.append("                        <div class=\"flex space-x-2 whitespace-nowrap\">\n");

        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            // 获取各类型处罚数量
            int banCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.BAN);
            int muteCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.MUTE);
            int warnCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.WARN);
            int kickCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.KICK);

            // 添加"所有记录"徽章
            int totalCount = banCount + muteCount + warnCount + kickCount;
            if (totalCount > 0) {
                String badgeClass = (activeType == null) ? "badge badge-primary" : "badge badge-secondary";
                html.append("                            <a href=\"/admin-player/").append(escapeUrl(playerName))
                        .append("\" class=\"").append(badgeClass)
                        .append("\" data-type=\"all\" data-stat=\"total_all\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">📋</span> <span class=\"badge-count\">")
                        .append(totalCount)
                        .append("</span> 所有记录\n");
                html.append("                            </a>\n");
            }

            // 徽章（完全按照 next-litebans 样式）
            if (banCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.BAN) ? " active" : "";
                html.append("                            <a href=\"/admin-player/").append(escapeUrl(playerName))
                        .append("?type=ban\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"ban\" data-stat=\"total_ban\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">🚫</span> <span class=\"badge-count\">")
                        .append(banCount)
                        .append("</span> 封禁\n");
                html.append("                            </a>\n");
            }
            if (muteCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.MUTE) ? " active" : "";
                html.append("                            <a href=\"/admin-player/").append(escapeUrl(playerName))
                        .append("?type=mute\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"mute\" data-stat=\"total_mute\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">🔇</span> <span class=\"badge-count\">")
                        .append(muteCount)
                        .append("</span> 禁言\n");
                html.append("                            </a>\n");
            }
            if (warnCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.WARN) ? " active" : "";
                html.append("                            <a href=\"/admin-player/").append(escapeUrl(playerName))
                        .append("?type=warn\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"warn\" data-stat=\"total_warn\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">⚠️</span> <span class=\"badge-count\">")
                        .append(warnCount)
                        .append("</span> 警告\n");
                html.append("                            </a>\n");
            }
            if (kickCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.KICK) ? " active" : "";
                html.append("                            <a href=\"/admin-player/").append(escapeUrl(playerName))
                        .append("?type=kick\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"kick\" data-stat=\"total_kick\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">👢</span> <span class=\"badge-count\">")
                        .append(kickCount)
                        .append("</span> 踢出\n");
                html.append("                            </a>\n");
            }
        }

        html.append("                        </div>\n");

        // 添加管理员操作按钮区域
        html.append(actionGenerator.generateAdminActionButtons(playerName));

        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");

        return html.toString();
    }

    /**
     * 获取玩家特定类型的处罚数量
     */
    private int getPlayerPunishmentCount(String playerName, PunishmentRecord.PunishmentType type) {
        if (plugin.getPunishmentManager() == null || !plugin.getPunishmentManager().isEnabled()) {
            return 0;
        }

        try {
            // 使用专门的计数方法获取准确数量
            return plugin.getPunishmentManager().getPlayerPunishmentCount(playerName, type);
        } catch (Exception e) {
            plugin.getLogger().warning("获取玩家处罚数量时发生错误: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 检查字符串是否为UUID格式
     */
    private boolean isUuidFormat(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        return str.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}");
    }

    /**
     * 根据UUID获取玩家名称
     */
    private String getPlayerNameByUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return null;
        }

        // 通过处罚管理器获取玩家名称
        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            try {
                String playerName = plugin.getPunishmentManager().getPlayerNameByUuid(uuid);
                if (playerName != null && !playerName.isEmpty()) {
                    return playerName;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("获取玩家名称时发生错误: " + e.getMessage());
            }
        }

        // 如果无法获取玩家名称，返回UUID的前8位
        return uuid.length() >= 8 ? uuid.substring(0, 8) + "..." : uuid;
    }

    /**
     * 生成管理员顶部导航栏
     */
    private String generateAdminSiteHeader(PunishmentRecord.PunishmentType currentType, Map<String, Object> statistics,
            String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("    <header class=\"admin-site-header\">\n");
        html.append("        <div class=\"admin-header-container\">\n");

        // 左侧：Logo 和主导航
        html.append("            <div class=\"admin-header-left\">\n");
        html.append("                <a href=\"/admin\" class=\"admin-logo-link\">\n");

        // 获取管理员Logo配置
        String adminLogoUrl = plugin.getConfig().getString("web-server.admin-logo-url", "");
        if (adminLogoUrl != null && !adminLogoUrl.trim().isEmpty()) {
            html.append("                    <img src=\"").append(adminLogoUrl).append("\" alt=\"管理员控制台\" class=\"admin-logo-image\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='inline'\">\n");
        } else {
            html.append("                    <img src=\"/static/logo.webp\" alt=\"管理员控制台\" class=\"admin-logo-image\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='inline'\">\n");
        }

        html.append("                    <span class=\"admin-logo-text\">管理员控制台</span>\n");
        html.append("                </a>\n");
        html.append("                <nav class=\"admin-main-nav\">\n");
        html.append(generateAdminMainNavigation(currentType, statistics));
        html.append("                </nav>\n");
        html.append("            </div>\n");

        // 右侧：搜索框和功能按钮
        html.append("            <div class=\"admin-header-right\">\n");
        html.append("                <nav class=\"admin-header-actions\">\n");
        html.append(generateAdminHeaderSearch(searchQuery));
        html.append(
                "                    <button type=\"button\" class=\"admin-theme-toggle-btn\" onclick=\"toggleAdminTheme()\" title=\"切换主题\">\n");
        html.append("                        <span class=\"admin-theme-icon\">🌙</span>\n");
        html.append("                    </button>\n");
        html.append("                </nav>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </header>\n");

        return html.toString();
    }

    /**
     * 生成管理员主导航链接
     */
    private String generateAdminMainNavigation(PunishmentRecord.PunishmentType currentType,
            Map<String, Object> statistics) {
        StringBuilder html = new StringBuilder();

        // 各种处罚类型的导航链接
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            String typeName = type.name().toLowerCase();
            boolean isActive = type == currentType;

            html.append("                    <a href=\"/admin-punishments/").append(typeName)
                    .append("\" class=\"admin-nav-link");
            if (isActive) {
                html.append(" active");
            }
            html.append("\">\n");
            html.append("                        ").append(type.getDisplayName()).append("\n");

            // 添加徽章显示数量
            Object count = statistics != null ? statistics.get("total_" + typeName) : null;
            if (count != null) {
                String badgeVariant = isActive ? "default" : "secondary";
                html.append("                        <span class=\"admin-nav-badge admin-badge-").append(badgeVariant)
                        .append("\" data-type=\"").append(typeName).append("\" data-stat=\"total_").append(typeName)
                        .append("\">").append(count).append("</span>\n");
            }

            html.append("                    </a>\n");
        }

        return html.toString();
    }

    /**
     * 生成管理员头部搜索框
     */
    private String generateAdminHeaderSearch(String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("                    <div class=\"admin-header-search\">\n");
        html.append(
                "                        <form method=\"get\" onsubmit=\"return handleAdminSearchSubmit(event)\" class=\"admin-search-form-header\">\n");
        html.append("                            <div class=\"admin-search-input-wrapper\">\n");
        html.append("                                <input type=\"text\" name=\"player\" placeholder=\"搜索玩家...\" ");
        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("value=\"").append(escapeHtml(searchQuery)).append("\" ");
        }
        html.append(
                "class=\"admin-header-search-input\" oninput=\"updateAdminPlayerAvatar(this.value)\" onkeydown=\"handleAdminSearchEnter(event)\">\n");
        html.append(
                "                                <img id=\"adminHeaderPlayerAvatar\" src=\"\" alt=\"玩家头像\" class=\"admin-header-player-avatar\" style=\"display: none;\">\n");
        html.append("                            </div>\n");
        html.append("                        </form>\n");
        html.append("                    </div>\n");

        return html.toString();
    }

    /**
     * 生成管理员统计信息区域
     */
    private String generateAdminStatisticsSection(Map<String, Object> statistics,
            PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"admin-statistics-section\">\n");
        html.append("            <h2 class=\"admin-statistics-title\">📊 处罚统计</h2>\n");
        html.append("            <div class=\"admin-statistics-grid\">\n");

        // 各种处罚类型的统计卡片
        for (PunishmentRecord.PunishmentType punishmentType : PunishmentRecord.PunishmentType.values()) {
            String typeName = punishmentType.name().toLowerCase();
            Object count = statistics.get("total_" + typeName);
            Object activeCount = statistics.get("active_" + typeName);

            if (count != null) {
                boolean isCurrentType = punishmentType == type;
                String cardClass = isCurrentType ? "admin-stat-card admin-stat-card-active" : "admin-stat-card";

                // 使用链接包装整个卡片，点击跳转到对应类型页面
                html.append("                <a href=\"/admin-punishments/").append(typeName)
                        .append("\" class=\"admin-stat-card-link\">\n");
                html.append("                    <div class=\"").append(cardClass).append("\" data-type=\"")
                        .append(typeName).append("\">\n");
                html.append("                        <div class=\"admin-stat-icon\">").append(punishmentType.getIcon())
                        .append("</div>\n");
                html.append("                        <div class=\"admin-stat-content\">\n");
                html.append("                            <div class=\"admin-stat-label\">")
                        .append(punishmentType.getDisplayName()).append("</div>\n");
                html.append("                            <div class=\"admin-stat-number\" data-stat=\"total_")
                        .append(typeName).append("\">").append(count)
                        .append("</div>\n");

                // 为所有类型预留生效中显示位置，保持卡片对齐
                html.append("                            <div class=\"admin-stat-active\" data-stat=\"active_")
                        .append(typeName).append("\">");

                // 对于踢出和警告，显示总数而不是活跃数（因为它们是瞬时操作）
                if (punishmentType == PunishmentRecord.PunishmentType.KICK ||
                        punishmentType == PunishmentRecord.PunishmentType.WARN) {
                    // 踢出和警告：只有总数大于0时才显示
                    int totalCountInt = 0;
                    if (count != null) {
                        if (count instanceof Integer) {
                            totalCountInt = (Integer) count;
                        } else {
                            try {
                                totalCountInt = Integer.parseInt(count.toString());
                            } catch (NumberFormatException e) {
                                totalCountInt = 0;
                            }
                        }
                    }

                    if (totalCountInt > 0) {
                        String statusLabel = getActiveStatusLabel(punishmentType);
                        html.append(statusLabel).append(": ").append("<span class=\"active-count\">")
                                .append(totalCountInt)
                                .append("</span>");
                    } else {
                        // 如果是0，显示空格保持卡片高度一致
                        html.append("&nbsp;");
                    }
                } else {
                    // 封禁和禁言：只有活跃数大于0时才显示生效中
                    int activeCountInt = 0;
                    if (activeCount != null) {
                        if (activeCount instanceof Integer) {
                            activeCountInt = (Integer) activeCount;
                        } else {
                            try {
                                activeCountInt = Integer.parseInt(activeCount.toString());
                            } catch (NumberFormatException e) {
                                activeCountInt = 0;
                            }
                        }
                    }

                    if (activeCountInt > 0) {
                        String statusLabel = getActiveStatusLabel(punishmentType);
                        html.append(statusLabel).append(": ").append("<span class=\"active-count\">")
                                .append(activeCountInt)
                                .append("</span>");
                    } else {
                        // 如果是0，显示空格保持卡片高度一致
                        html.append("&nbsp;");
                    }
                }
                html.append("</div>\n");

                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }
        }

        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成管理员过滤器区域
     */
    private String generateAdminFiltersSection(String playerFilter, String staffFilter) {
        StringBuilder html = new StringBuilder();

        // 只有当有过滤参数时才显示过滤器区域
        if ((playerFilter != null && !playerFilter.isEmpty()) || (staffFilter != null && !staffFilter.isEmpty())) {
            html.append("        <div class=\"admin-filters-section\">\n");
            html.append("            <div class=\"admin-filters-container\">\n");
            html.append("                <h3 class=\"admin-filters-title\">🔍 当前过滤条件</h3>\n");
            html.append("                <div class=\"admin-filter-tags\">\n");

            // 玩家过滤器
            if (playerFilter != null && !playerFilter.isEmpty()) {
                html.append("                    <div class=\"admin-filter-tag admin-filter-player\">\n");
                html.append("                        <span class=\"admin-filter-label\">玩家</span>\n");
                html.append("                        <div class=\"admin-filter-separator\"></div>\n");
                html.append("                        <div class=\"admin-filter-badge\">\n");

                // 检查是否是控制台
                boolean isConsolePlayer = "Console".equalsIgnoreCase(playerFilter) || "控制台".equals(playerFilter);
                if (isConsolePlayer) {
                    html.append(
                            "                            <img src=\"/static/console.webp\" alt=\"控制台\" class=\"admin-filter-avatar\">\n");
                    html.append("                            <span class=\"admin-filter-name\">控制台</span>\n");
                } else {
                    // 玩家头像
                    String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
                    String avatarUrl = generateAvatarUrl(playerFilter, avatarProvider);
                    html.append("                            <img src=\"").append(avatarUrl).append(
                            "\" alt=\"头像\" class=\"admin-filter-avatar\" onerror=\"this.style.display='none'\">\n");
                    html.append("                            <span class=\"admin-filter-name\">")
                            .append(escapeHtml(playerFilter)).append("</span>\n");
                }

                html.append("                        </div>\n");
                html.append(
                        "                        <button class=\"admin-filter-remove\" onclick=\"removePlayerPagePlayerFilter()\" title=\"移除玩家过滤器\">\n");
                html.append("                            <span class=\"admin-remove-icon\">✕</span>\n");
                html.append("                        </button>\n");
                html.append("                    </div>\n");
            }

            // 执行者过滤器
            if (staffFilter != null && !staffFilter.isEmpty()) {
                html.append("                    <div class=\"admin-filter-tag admin-filter-staff\">\n");
                html.append("                        <span class=\"admin-filter-label\">执行者</span>\n");
                html.append("                        <div class=\"admin-filter-separator\"></div>\n");
                html.append("                        <div class=\"admin-filter-badge\">\n");

                // 检查是否是控制台
                boolean isConsoleStaff = "Console".equalsIgnoreCase(staffFilter) || "控制台".equals(staffFilter);
                if (isConsoleStaff) {
                    html.append(
                            "                            <img src=\"/static/console.webp\" alt=\"控制台\" class=\"admin-filter-avatar\">\n");
                    html.append("                            <span class=\"admin-filter-name\">控制台</span>\n");
                } else {
                    // 执行者头像和名称
                    String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
                    String avatarUrl = generateAvatarUrl(staffFilter, avatarProvider);
                    html.append("                            <img src=\"").append(avatarUrl).append(
                            "\" alt=\"头像\" class=\"admin-filter-avatar\" onerror=\"this.style.display='none'\">\n");

                    // 如果是UUID格式，尝试获取玩家名称
                    String displayName = staffFilter;
                    if (isUuidFormat(staffFilter)) {
                        String playerName = getPlayerNameByUuid(staffFilter);
                        if (playerName != null && !playerName.isEmpty()) {
                            displayName = playerName;
                        }
                    }
                    html.append("                            <span class=\"admin-filter-name\">")
                            .append(escapeHtml(displayName)).append("</span>\n");
                }

                html.append("                        </div>\n");
                html.append(
                        "                        <button class=\"admin-filter-remove\" onclick=\"removePlayerPageStaffFilter()\" title=\"移除执行者过滤器\">\n");
                html.append("                            <span class=\"admin-remove-icon\">✕</span>\n");
                html.append("                        </button>\n");
                html.append("                    </div>\n");
            }

            html.append(
                    "                    <button onclick=\"clearPlayerPageAllFilters()\" class=\"admin-clear-filters-btn\">清除所有过滤</button>\n");
            html.append("                </div>\n");
            html.append("            </div>\n");
            html.append("        </div>\n");
        }

        return html.toString();
    }

    /**
     * 生成管理员处罚记录表格
     */
    private String generateAdminPunishmentTable(List<PunishmentRecord> records) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"admin-table-section\">\n");
        html.append("            <div class=\"admin-table-container\">\n");
        html.append("                <table class=\"admin-punishment-table\">\n");

        // 根据记录类型生成不同的表头
        html.append(generateAdminTableHeader(records));

        html.append("                    <tbody>\n");

        if (records.isEmpty()) {
            // 根据处罚类型确定colspan
            int colspan = 8; // 默认列数
            if (!records.isEmpty()) {
                PunishmentRecord.PunishmentType type = records.get(0).getType();
                if (type == PunishmentRecord.PunishmentType.KICK) {
                    colspan = 4; // 踢出：4列
                } else if (type == PunishmentRecord.PunishmentType.WARN) {
                    colspan = 5; // 警告：5列
                }
            }

            html.append("                        <tr>\n");
            html.append("                            <td colspan=\"").append(colspan)
                    .append("\" class=\"admin-no-records\">\n");
            html.append("                                <div class=\"admin-empty-state\">\n");
            html.append("                                    <div class=\"admin-empty-icon\">📋</div>\n");
            html.append("                                    <div class=\"admin-empty-text\">暂无处罚记录</div>\n");
            html.append("                                </div>\n");
            html.append("                            </td>\n");
            html.append("                        </tr>\n");
        } else {
            // 检查是否是单一类型的记录
            boolean isSingleType = true;
            PunishmentRecord.PunishmentType firstType = null;

            if (!records.isEmpty()) {
                firstType = records.get(0).getType();
                // 检查所有记录是否都是同一类型
                for (PunishmentRecord record : records) {
                    if (record.getType() != firstType) {
                        isSingleType = false;
                        break;
                    }
                }
            }

            for (PunishmentRecord record : records) {
                html.append("                        <tr class=\"admin-record-row\" data-record-id=\"")
                        .append(record.getId()).append("\">\n");

                // 只有当所有记录都是同一类型时，才使用专用行格式
                if (isSingleType && record.getType() == PunishmentRecord.PunishmentType.KICK) {
                    // 踢出专用：玩家 | 踢人 | 原因 | 日期
                    html.append(generateKickTableRow(record));
                } else if (isSingleType && record.getType() == PunishmentRecord.PunishmentType.WARN) {
                    // 警告专用：玩家 | 警告者 | 原因 | 日期 | 通知
                    html.append(generateWarnTableRow(record));
                } else {
                    // 混合类型或其他类型：使用完整行结构
                    html.append(generateDefaultTableRow(record));
                }

                html.append("                        </tr>\n");
            }
        }

        html.append("                    </tbody>\n");
        html.append("                </table>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 根据处罚类型生成不同的表头
     */
    private String generateAdminTableHeader(List<PunishmentRecord> records) {
        StringBuilder html = new StringBuilder();

        // 检查是否是单一类型的记录
        boolean isSingleType = true;
        PunishmentRecord.PunishmentType firstType = null;

        if (!records.isEmpty()) {
            firstType = records.get(0).getType();
            // 检查所有记录是否都是同一类型
            for (PunishmentRecord record : records) {
                if (record.getType() != firstType) {
                    isSingleType = false;
                    break;
                }
            }
        }

        html.append("                    <thead>\n");
        html.append("                        <tr>\n");

        // 只有当所有记录都是同一类型时，才使用专用布局
        if (isSingleType && firstType == PunishmentRecord.PunishmentType.KICK) {
            // 踢出专用：玩家 | 踢人 | 处罚原因 | 处罚时间
            html.append("                            <th>玩家</th>\n");
            html.append("                            <th>踢人</th>\n");
            html.append("                            <th>处罚原因</th>\n");
            html.append("                            <th>处罚时间</th>\n");
        } else if (isSingleType && firstType == PunishmentRecord.PunishmentType.WARN) {
            // 警告专用：玩家 | 警告者 | 处罚原因 | 处罚时间 | 通知
            html.append("                            <th>玩家</th>\n");
            html.append("                            <th>警告者</th>\n");
            html.append("                            <th>处罚原因</th>\n");
            html.append("                            <th>处罚时间</th>\n");
            html.append("                            <th>通知</th>\n");
        } else {
            // 混合类型或其他类型：使用完整表头
            html.append("                            <th>类型</th>\n");
            html.append("                            <th>玩家</th>\n");
            html.append("                            <th>执行者</th>\n");
            html.append("                            <th>处罚原因</th>\n");
            html.append("                            <th>处罚时间</th>\n");
            html.append("                            <th>到期时间</th>\n");
            html.append("                            <th>状态</th>\n");
            html.append("                            <th>操作</th>\n");
        }

        html.append("                        </tr>\n");
        html.append("                    </thead>\n");

        return html.toString();
    }

    /**
     * 生成踢出表格行：玩家 | 踢人 | 原因 | 日期
     */
    private String generateKickTableRow(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        // 玩家列
        html.append("                            <td class=\"admin-player-cell\">\n");
        html.append("                                <div class=\"admin-player-info\">\n");

        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName);

        if (isConsolePlayer) {
            html.append("                                    <img src=\"/static/console.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-player-avatar\" ");
            html.append("onclick=\"addPlayerPageFilter('player', '控制台'); return false;\" ");
            html.append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\">\n");
        } else {
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = record.getPlayerAvatarUrl(avatarProvider);
            if (avatarUrl.isEmpty()) {
                avatarUrl = generateAvatarUrl(playerName, avatarProvider);
            }
            html.append("                                    <img src=\"").append(avatarUrl).append("\" ");
            html.append("alt=\"").append(escapeHtml(playerName)).append("\" class=\"admin-player-avatar\" ");
            html.append("onclick=\"addPlayerPageFilter('player', '").append(escapeHtml(playerName)).append("'); return false;\" ");
            html.append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\" ");
            html.append("onerror=\"this.style.display='none'\">\n");
        }

        html.append("                                    <span class=\"admin-player-name\">")
                .append(escapeHtml(playerName)).append("</span>\n");
        html.append("                                </div>\n");
        html.append("                            </td>\n");

        // 踢人列（执行者）
        html.append("                            <td class=\"admin-staff-cell\">\n");
        String staffName = record.getBannedByName();
        boolean isConsole = false;

        if (staffName == null) {
            staffName = "系统";
            isConsole = true;
        } else if ("Console".equalsIgnoreCase(staffName)) {
            staffName = "控制台";
            isConsole = true;
        }

        html.append("                                <a href=\"javascript:void(0)\" onclick=\"addPlayerPageFilter('staff', '")
                .append(escapeHtml(staffName)).append("')\" class=\"admin-avatar-name-link\">\n");
        html.append("                                    <div class=\"admin-avatar-name-container\">\n");

        if (isConsole) {
            html.append("                                        <img src=\"/static/console.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-staff-avatar\">\n");
        } else {
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = "";
            String staffUuid = record.getBannedByUuid();

            if (staffUuid != null && !staffUuid.isEmpty()) {
                avatarUrl = generateAvatarUrl(staffUuid, avatarProvider);
            } else if (staffName != null && !staffName.isEmpty()) {
                avatarUrl = generateAvatarUrl(staffName, avatarProvider);
            }

            if (!avatarUrl.isEmpty()) {
                html.append("                                        <img src=\"").append(avatarUrl).append("\" ");
                html.append("alt=\"").append(escapeHtml(staffName)).append("\" class=\"admin-staff-avatar\" ");
                html.append("onerror=\"this.style.display='none'\">\n");
            }
        }

        html.append("                                        <div class=\"admin-staff-name\">")
                .append(escapeHtml(staffName)).append("</div>\n");
        html.append("                                    </div>\n");
        html.append("                                </a>\n");
        html.append("                            </td>\n");

        // 原因列
        html.append("                            <td class=\"admin-reason-cell\">\n");
        html.append("                                <span class=\"admin-reason-text\" title=\"")
                .append(escapeHtml(record.getReason())).append("\">\n");
        html.append("                                    ")
                .append(escapeHtml(truncateText(record.getReason(), 30))).append("\n");
        html.append("                                </span>\n");
        html.append("                            </td>\n");

        // 日期列
        html.append("                            <td class=\"admin-time-cell\">\n");
        html.append("                                <span class=\"admin-time-text\">")
                .append(record.getFormattedTime()).append("</span>\n");
        html.append("                            </td>\n");

        return html.toString();
    }

    /**
     * 生成警告表格行：玩家 | 警告者 | 原因 | 日期 | 通知
     */
    private String generateWarnTableRow(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        // 玩家列
        html.append("                            <td class=\"admin-player-cell\">\n");
        html.append("                                <div class=\"admin-player-info\">\n");

        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName);

        if (isConsolePlayer) {
            html.append("                                    <img src=\"/static/console.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-player-avatar\" ");
            html.append("onclick=\"addPlayerPageFilter('player', '控制台'); return false;\" ");
            html.append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\">\n");
        } else {
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = record.getPlayerAvatarUrl(avatarProvider);
            if (avatarUrl.isEmpty()) {
                avatarUrl = generateAvatarUrl(playerName, avatarProvider);
            }
            html.append("                                    <img src=\"").append(avatarUrl).append("\" ");
            html.append("alt=\"").append(escapeHtml(playerName)).append("\" class=\"admin-player-avatar\" ");
            html.append("onclick=\"addPlayerPageFilter('player', '").append(escapeHtml(playerName)).append("'); return false;\" ");
            html.append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\" ");
            html.append("onerror=\"this.style.display='none'\">\n");
        }

        html.append("                                    <span class=\"admin-player-name\">")
                .append(escapeHtml(playerName)).append("</span>\n");
        html.append("                                </div>\n");
        html.append("                            </td>\n");

        // 警告者列（执行者）
        html.append("                            <td class=\"admin-staff-cell\">\n");
        String staffName = record.getBannedByName();
        boolean isConsole = false;

        if (staffName == null) {
            staffName = "系统";
            isConsole = true;
        } else if ("Console".equalsIgnoreCase(staffName)) {
            staffName = "控制台";
            isConsole = true;
        }

        html.append("                                <a href=\"javascript:void(0)\" onclick=\"addPlayerPageFilter('staff', '")
                .append(escapeHtml(staffName)).append("')\" class=\"admin-avatar-name-link\">\n");
        html.append("                                    <div class=\"admin-avatar-name-container\">\n");

        if (isConsole) {
            html.append("                                        <img src=\"/static/console.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-staff-avatar\">\n");
        } else {
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = "";
            String staffUuid = record.getBannedByUuid();

            if (staffUuid != null && !staffUuid.isEmpty()) {
                avatarUrl = generateAvatarUrl(staffUuid, avatarProvider);
            } else if (staffName != null && !staffName.isEmpty()) {
                avatarUrl = generateAvatarUrl(staffName, avatarProvider);
            }

            if (!avatarUrl.isEmpty()) {
                html.append("                                        <img src=\"").append(avatarUrl).append("\" ");
                html.append("alt=\"").append(escapeHtml(staffName)).append("\" class=\"admin-staff-avatar\" ");
                html.append("onerror=\"this.style.display='none'\">\n");
            }
        }

        html.append("                                        <div class=\"admin-staff-name\">")
                .append(escapeHtml(staffName)).append("</div>\n");
        html.append("                                    </div>\n");
        html.append("                                </a>\n");
        html.append("                            </td>\n");

        // 原因列
        html.append("                            <td class=\"admin-reason-cell\">\n");
        html.append("                                <span class=\"admin-reason-text\" title=\"")
                .append(escapeHtml(record.getReason())).append("\">\n");
        html.append("                                    ")
                .append(escapeHtml(truncateText(record.getReason(), 30))).append("\n");
        html.append("                                </span>\n");
        html.append("                            </td>\n");

        // 日期列
        html.append("                            <td class=\"admin-time-cell\">\n");
        html.append("                                <span class=\"admin-time-text\">")
                .append(record.getFormattedTime()).append("</span>\n");
        html.append("                            </td>\n");

        // 通知列（使用图片符号显示）
        html.append("                            <td class=\"admin-notify-cell\">\n");

        // 检查是否已通知（这里需要根据实际的通知状态字段来判断）
        // 假设有一个方法来检查通知状态，如果没有可以根据其他字段推断
        boolean isNotified = checkWarnNotificationStatus(record);

        if (isNotified) {
            // 已通知 - 显示绿色勾号
            html.append(
                    "                                <div class=\"admin-notify-status admin-notify-sent\" title=\"已通知\">\n");
            html.append(
                    "                                    <svg class=\"admin-notify-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n");
            html.append("                                        <path d=\"M20 6L9 17l-5-5\"></path>\n");
            html.append("                                    </svg>\n");
            html.append("                                </div>\n");
        } else {
            // 未通知 - 显示红色叉号
            html.append(
                    "                                <div class=\"admin-notify-status admin-notify-failed\" title=\"未通知\">\n");
            html.append(
                    "                                    <svg class=\"admin-notify-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n");
            html.append("                                        <path d=\"M18 6L6 18\"></path>\n");
            html.append("                                        <path d=\"M6 6l12 12\"></path>\n");
            html.append("                                    </svg>\n");
            html.append("                                </div>\n");
        }

        html.append("                            </td>\n");

        return html.toString();
    }

    /**
     * 生成默认表格行（其他处罚类型）
     */
    private String generateDefaultTableRow(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        // 类型列
        html.append("                            <td class=\"admin-type-cell\">\n");
        html.append("                                <span class=\"admin-type-badge admin-type-")
                .append(record.getType().name().toLowerCase()).append("\">\n");
        html.append("                                    ").append(record.getType().getIcon()).append(" ")
                .append(record.getType().getDisplayName()).append("\n");
        html.append("                                </span>\n");
        html.append("                            </td>\n");

        // 玩家列
        html.append("                            <td class=\"admin-player-cell\">\n");
        html.append("                                <div class=\"admin-player-info\">\n");

        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName);

        if (isConsolePlayer) {
            html.append("                                    <img src=\"/static/console.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-player-avatar\" ");
            html.append("onclick=\"addPlayerPageFilter('player', '控制台'); return false;\" ");
            html.append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\">\n");
        } else {
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = record.getPlayerAvatarUrl(avatarProvider);
            if (avatarUrl.isEmpty()) {
                avatarUrl = generateAvatarUrl(playerName, avatarProvider);
            }
            html.append("                                    <img src=\"").append(avatarUrl).append("\" ");
            html.append("alt=\"").append(escapeHtml(playerName)).append("\" class=\"admin-player-avatar\" ");
            html.append("onclick=\"addPlayerPageFilter('player', '").append(escapeHtml(playerName)).append("'); return false;\" ");
            html.append("title=\"点击添加玩家过滤\" style=\"cursor: pointer;\" ");
            html.append("onerror=\"this.style.display='none'\">\n");
        }

        html.append("                                    <span class=\"admin-player-name\">")
                .append(escapeHtml(playerName)).append("</span>\n");
        html.append("                                </div>\n");
        html.append("                            </td>\n");

        // 执行者列
        html.append("                            <td class=\"admin-staff-cell\">\n");
        String staffName = record.getBannedByName();
        String staffUuid = record.getBannedByUuid();
        boolean isConsole = false;

        if (staffName == null) {
            staffName = "系统";
            isConsole = true;
        } else if ("Console".equalsIgnoreCase(staffName)) {
            staffName = "控制台";
            isConsole = true;
        }

        html.append("                                <a href=\"javascript:void(0)\" onclick=\"addPlayerPageFilter('staff', '")
                .append(escapeHtml(staffName)).append("')\" class=\"admin-avatar-name-link\">\n");
        html.append("                                    <div class=\"admin-avatar-name-container\">\n");

        if (isConsole) {
            html.append("                                        <img src=\"/static/console.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-staff-avatar\">\n");
        } else {
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = "";

            if (staffUuid != null && !staffUuid.isEmpty()) {
                avatarUrl = generateAvatarUrl(staffUuid, avatarProvider);
            } else if (staffName != null && !staffName.isEmpty()) {
                avatarUrl = generateAvatarUrl(staffName, avatarProvider);
            }

            if (!avatarUrl.isEmpty()) {
                html.append("                                        <img src=\"").append(avatarUrl).append("\" ");
                html.append("alt=\"").append(escapeHtml(staffName)).append("\" class=\"admin-staff-avatar\" ");
                html.append("onerror=\"this.style.display='none'\">\n");
            }
        }

        html.append("                                        <div class=\"admin-staff-name\">")
                .append(escapeHtml(staffName)).append("</div>\n");
        html.append("                                    </div>\n");
        html.append("                                </a>\n");
        html.append("                            </td>\n");

        // 原因列
        html.append("                            <td class=\"admin-reason-cell\">\n");
        html.append("                                <span class=\"admin-reason-text\" title=\"")
                .append(escapeHtml(record.getReason())).append("\">\n");
        html.append("                                    ")
                .append(escapeHtml(truncateText(record.getReason(), 30))).append("\n");
        html.append("                                </span>\n");
        html.append("                            </td>\n");

        // 时间列
        html.append("                            <td class=\"admin-time-cell\">\n");
        html.append("                                <span class=\"admin-time-text\">")
                .append(record.getFormattedTime()).append("</span>\n");
        html.append("                            </td>\n");

        // 到期时间列
        html.append("                            <td class=\"admin-until-cell\">\n");

        // 踢出和警告是瞬时操作，没有到期时间
        if (record.getType() == PunishmentRecord.PunishmentType.KICK ||
                record.getType() == PunishmentRecord.PunishmentType.WARN) {
            html.append("                                <span class=\"admin-until-na\">不适用</span>\n");
        } else if (record.getUntil() != null && record.getUntil().getYear() <= 2100) {
            html.append("                                <div class=\"admin-until-container\">\n");
            if (record.isActive()) {
                html.append(
                        "                                    <span class=\"admin-status-dot admin-status-active\" title=\"生效中\"></span>\n");
            } else {
                html.append(
                        "                                    <span class=\"admin-status-dot admin-status-expired\" title=\"已过期\"></span>\n");
            }
            long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
            html.append(
                    "                                    <span class=\"relative-time admin-relative-time\" data-timestamp=\"")
                    .append(timestamp).append("\">");
            html.append(escapeHtml(record.getFormattedUntil())).append("</span>\n");
            html.append("                                </div>\n");
        } else {
            // 永久封禁，显示状态图标
            html.append("                                <div class=\"admin-until-container\">\n");
            if (record.isActive()) {
                html.append(
                        "                                    <span class=\"admin-status-dot admin-status-permanent\" title=\"永久生效\"></span>\n");
            } else {
                html.append(
                        "                                    <span class=\"admin-status-dot admin-status-expired\" title=\"已撤销\"></span>\n");
            }
            html.append("                                    <span class=\"admin-until-permanent\">永久</span>\n");
            html.append("                                </div>\n");
        }
        html.append("                            </td>\n");

        // 状态列
        html.append("                            <td class=\"admin-status-cell\">\n");

        // 踢出和警告是瞬时操作，显示"已完成"状态
        if (record.getType() == PunishmentRecord.PunishmentType.KICK ||
                record.getType() == PunishmentRecord.PunishmentType.WARN) {
            html.append("                                <span class=\"admin-status-badge admin-status-completed\">\n");
            html.append("                                    已完成\n");
            html.append("                                </span>\n");
        } else {
            String statusClass = record.isActive() ? "admin-status-active" : "admin-status-inactive";
            html.append("                                <span class=\"admin-status-badge ").append(statusClass)
                    .append("\">\n");
            html.append("                                    ").append(record.getStatusText()).append("\n");
            html.append("                                </span>\n");
        }
        html.append("                            </td>\n");

        // 操作列
        html.append("                            <td class=\"admin-actions-cell\">\n");
        html.append("                                <div class=\"admin-action-buttons\">\n");
        String punishmentType = record.getType() != null ? record.getType().name().toLowerCase() : "punishment";
        html.append("                                    <a href=\"/admin-punishments/").append(punishmentType)
                .append("/").append(record.getId()).append("\" class=\"admin-info-btn\" title=\"查看详细信息\">\n");
        html.append(
                "                                        <svg class=\"admin-info-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n");
        html.append(
                "                                            <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n");
        html.append(
                "                                            <path d=\"M15 3l6 6v11a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V9a2 2 0 0 1 2-2h8z\"></path>\n");
        html.append("                                            <path d=\"M10 12h4\"></path>\n");
        html.append("                                            <path d=\"M10 16h4\"></path>\n");
        html.append("                                        </svg>\n");
        html.append("                                    </a>\n");

        // 踢出和警告是瞬时操作，不能撤销
        if (record.isActive() &&
                record.getType() != PunishmentRecord.PunishmentType.KICK &&
                record.getType() != PunishmentRecord.PunishmentType.WARN) {
            html.append("                                    <button onclick=\"revokeAdminRecord(")
                    .append(record.getId()).append(", '").append(record.getType().name().toLowerCase())
                    .append("', '")
                    .append(escapeHtml(record.getPlayerName() != null ? record.getPlayerName() : "Unknown"))
                    .append("')\" ");
            html.append("class=\"admin-action-btn admin-revoke-btn\" title=\"撤销处罚\">\n");
            html.append("                                        🚫\n");
            html.append("                                    </button>\n");
        }
        html.append("                                </div>\n");
        html.append("                            </td>\n");

        return html.toString();
    }

    /**
     * 检查警告通知状态
     */
    private boolean checkWarnNotificationStatus(PunishmentRecord record) {
        // 这里需要根据实际的数据库字段来判断通知状态
        // 如果LiteBans有专门的通知状态字段，使用该字段
        // 否则可以根据其他逻辑来推断，比如：
        // 1. 如果玩家在线且警告时间较近，认为已通知
        // 2. 如果有专门的通知日志表，查询该表
        // 3. 默认情况下，假设都已通知（因为LiteBans通常会自动通知）

        // 临时实现：假设所有警告都已通知
        // 在实际使用中，您可能需要根据具体的数据库结构来修改这个方法
        return true;
    }

    /**
     * 生成管理员处罚详细页面
     */
    public String generatePunishmentDetailPage(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        String pageTitle = record.getType().getDisplayName() + " #" + record.getId();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>管理员 - ").append(pageTitle).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generateAdminPunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        // 管理员顶部导航栏
        html.append(generateAdminSiteHeader(record.getType(), null, null));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 详细信息页面内容（按照首页处罚记录详细信息布局）
        html.append("        <div class=\"detail-page-container\">\n");

        // 页面标题（显示处罚编号和状态）
        html.append("            <div class=\"detail-header\">\n");
        html.append("                <h1 class=\"detail-title\">").append(pageTitle).append("</h1>\n");

        // 状态徽章
        html.append("                <div class=\"detail-badges\">\n");
        if (record.getType() == PunishmentRecord.PunishmentType.BAN && record.isIpban()) {
            html.append("                    <span class=\"status-badge status-ipban\">IP封禁</span>\n");
        }
        if (record.isActive()) {
            html.append("                    <span class=\"status-badge status-active\">生效中</span>\n");
        } else {
            html.append("                    <span class=\"status-badge status-expired\">已过期</span>\n");
        }
        html.append("                </div>\n");
        html.append("            </div>\n");

        // 详细信息卡片（按照 next-litebans PunishmentInfoCard）
        html.append(generatePunishmentInfoCard(record));

        // 操作按钮（只有当处罚生效中时才显示）
        if (record.isActive()) {
            html.append("            <div class=\"admin-detail-actions\">\n");

            // 根据处罚类型显示不同的按钮
            String actionText = "";
            String actionIcon = "";
            switch (record.getType()) {
                case BAN:
                    actionText = "解封玩家";
                    actionIcon = "🔓";
                    break;
                case MUTE:
                    actionText = "解除禁言";
                    actionIcon = "🔊";
                    break;
                case WARN:
                    actionText = "撤销警告";
                    actionIcon = "🚫";
                    break;
                case KICK:
                    actionText = "撤销踢出";
                    actionIcon = "🚫";
                    break;
                default:
                    actionText = "撤销处罚";
                    actionIcon = "🚫";
                    break;
            }

            html.append("                <button onclick=\"revokeAdminRecord(")
                    .append(record.getId()).append(", '").append(record.getType().name().toLowerCase())
                    .append("', '")
                    .append(escapeHtml(record.getPlayerName() != null ? record.getPlayerName() : "Unknown"))
                    .append("')\" class=\"admin-action-btn admin-revoke-btn\">\n");
            html.append("                    ").append(actionIcon).append(" ").append(actionText).append("\n");
            html.append("                </button>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        html.append("    </div>\n");

        // JavaScript
        html.append("    <script>\n");
        html.append(generateAdminPunishmentPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成管理员详细信息内容
     */
    private String generateAdminDetailContent(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        // 玩家信息卡片
        html.append("        <div class=\"admin-detail-section\">\n");
        html.append("            <div class=\"admin-detail-grid\">\n");

        // 左侧：玩家信息
        html.append("                <div class=\"admin-detail-card admin-player-card\">\n");
        html.append("                    <h3>被处罚玩家</h3>\n");
        html.append("                    <div class=\"admin-player-detail\">\n");

        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName);

        if (isConsolePlayer) {
            html.append("                        <img src=\"/static/console-bust.webp\" ");
            html.append("alt=\"控制台\" class=\"admin-detail-avatar\" ");
            html.append("onerror=\"this.src='/static/console.webp'\">\n");
        } else {
            // 使用配置的头像提供商和正确的UUID/玩家名
            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            String avatarUrl = record.getPlayerAvatarUrl(avatarProvider);
            if (avatarUrl.isEmpty()) {
                avatarUrl = generateAvatarUrl(playerName, avatarProvider);
            }
            html.append("                        <img src=\"").append(avatarUrl).append("\" ");
            html.append("alt=\"").append(escapeHtml(playerName)).append("\" class=\"admin-detail-avatar\" ");
            html.append("onerror=\"this.style.display='none'\">\n");
        }

        html.append("                        <div class=\"admin-player-info\">\n");
        html.append("                            <h4>").append(escapeHtml(playerName)).append("</h4>\n");
        if (record.getUuid() != null && !record.getUuid().isEmpty()) {
            html.append("                            <p class=\"admin-uuid\">UUID: ")
                    .append(escapeHtml(record.getUuid())).append("</p>\n");
        }
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");

        // 右侧：处罚信息
        html.append("                <div class=\"admin-detail-card admin-punishment-card\">\n");
        html.append("                    <h3>处罚信息</h3>\n");
        html.append("                    <div class=\"admin-punishment-detail\">\n");

        // 处罚类型
        html.append("                        <div class=\"admin-detail-item\">\n");
        html.append("                            <label>处罚类型:</label>\n");
        html.append("                            <span class=\"admin-type-badge admin-type-")
                .append(record.getType().name().toLowerCase()).append("\">\n");
        html.append("                                ").append(record.getType().getIcon()).append(" ")
                .append(record.getType().getDisplayName()).append("\n");
        html.append("                            </span>\n");
        html.append("                        </div>\n");

        // 处罚原因
        html.append("                        <div class=\"admin-detail-item\">\n");
        html.append("                            <label>处罚原因:</label>\n");
        html.append("                            <span>").append(escapeHtml(record.getReason())).append("</span>\n");
        html.append("                        </div>\n");

        // 执行者
        html.append("                        <div class=\"admin-detail-item\">\n");
        html.append("                            <label>执行者:</label>\n");
        String staffName = record.getBannedByName() != null ? record.getBannedByName() : "系统";
        html.append("                            <span>").append(escapeHtml(staffName)).append("</span>\n");
        html.append("                        </div>\n");

        // 处罚时间
        html.append("                        <div class=\"admin-detail-item\">\n");
        html.append("                            <label>处罚时间:</label>\n");
        html.append("                            <span>").append(record.getFormattedTime()).append("</span>\n");
        html.append("                        </div>\n");

        // 到期时间（如果有）
        if (record.getUntil() != null) {
            html.append("                        <div class=\"admin-detail-item\">\n");
            html.append("                            <label>到期时间:</label>\n");
            if (record.getUntil().getYear() > 2100) {
                html.append("                            <span>永久</span>\n");
            } else {
                // 添加动态时间显示
                long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                html.append("                            <span class=\"relative-time\" data-timestamp=\"")
                        .append(timestamp).append("\">");
                html.append(escapeHtml(record.getFormattedUntil())).append("</span>\n");
            }
            html.append("                        </div>\n");
        }

        // 状态
        html.append("                        <div class=\"admin-detail-item\">\n");
        html.append("                            <label>状态:</label>\n");
        String statusClass = record.isActive() ? "admin-status-active" : "admin-status-inactive";
        html.append("                            <span class=\"admin-status-badge ").append(statusClass)
                .append("\">\n");
        html.append("                                ").append(record.getStatusText()).append("\n");
        html.append("                            </span>\n");
        html.append("                        </div>\n");

        html.append("                    </div>\n");
        html.append("                </div>\n");

        html.append("            </div>\n");
        html.append("        </div>\n");

        // 操作按钮
        if (record.isActive()) {
            html.append("        <div class=\"admin-detail-actions\">\n");
            html.append("            <button onclick=\"revokeAdminRecord(")
                    .append(record.getId()).append(", '").append(record.getType().name().toLowerCase())
                    .append("', '")
                    .append(escapeHtml(record.getPlayerName() != null ? record.getPlayerName() : "Unknown"))
                    .append("')\" class=\"admin-action-btn admin-revoke-btn\">\n");
            html.append("                🚫 撤销处罚\n");
            html.append("            </button>\n");
            html.append("        </div>\n");
        }

        return html.toString();
    }

    /**
     * 根据处罚类型获取活跃状态标签
     */
    private String getActiveStatusLabel(PunishmentRecord.PunishmentType type) {
        switch (type) {
            case KICK:
                return "总踢出"; // 踢出是瞬时操作，显示总踢出数量
            case WARN:
                return "总警告"; // 警告是瞬时操作，显示总警告数量
            case BAN:
                return "生效中"; // 封禁有持续时间，显示生效中
            case MUTE:
                return "生效中"; // 禁言有持续时间，显示生效中
            default:
                return "生效中"; // 其他类型默认显示生效中
        }
    }

    /**
     * 生成管理员分页导航
     */
    private String generateAdminPagination(int currentPage, int totalPages, String searchQuery,
            PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"admin-pagination-section\">\n");
        html.append("            <nav class=\"admin-pagination\">\n");

        // 构建基础URL
        String baseUrl = "/admin-punishments";
        if (type != null) {
            baseUrl += "/" + type.name().toLowerCase();
        }

        // 上一页按钮
        if (currentPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage - 1);
            if (searchQuery != null && !searchQuery.isEmpty()) {
                html.append("&search=").append(escapeHtml(searchQuery));
            }
            html.append("\" class=\"admin-pagination-btn admin-pagination-prev\">‹ 上一页</a>\n");
        } else {
            html.append(
                    "                <span class=\"admin-pagination-btn admin-pagination-prev disabled\">‹ 上一页</span>\n");
        }

        // 页码按钮
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=1");
            if (searchQuery != null && !searchQuery.isEmpty()) {
                html.append("&search=").append(escapeHtml(searchQuery));
            }
            html.append("\" class=\"admin-pagination-btn\">1</a>\n");
            if (startPage > 2) {
                html.append("                <span class=\"admin-pagination-ellipsis\">...</span>\n");
            }
        }

        for (int i = startPage; i <= endPage; i++) {
            if (i == currentPage) {
                html.append("                <span class=\"admin-pagination-btn admin-pagination-current\">").append(i)
                        .append("</span>\n");
            } else {
                html.append("                <a href=\"").append(baseUrl).append("?page=").append(i);
                if (searchQuery != null && !searchQuery.isEmpty()) {
                    html.append("&search=").append(escapeHtml(searchQuery));
                }
                html.append("\" class=\"admin-pagination-btn\">").append(i).append("</a>\n");
            }
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html.append("                <span class=\"admin-pagination-ellipsis\">...</span>\n");
            }
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(totalPages);
            if (searchQuery != null && !searchQuery.isEmpty()) {
                html.append("&search=").append(escapeHtml(searchQuery));
            }
            html.append("\" class=\"admin-pagination-btn\">").append(totalPages).append("</a>\n");
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage + 1);
            if (searchQuery != null && !searchQuery.isEmpty()) {
                html.append("&search=").append(escapeHtml(searchQuery));
            }
            html.append("\" class=\"admin-pagination-btn admin-pagination-next\">下一页 ›</a>\n");
        } else {
            html.append(
                    "                <span class=\"admin-pagination-btn admin-pagination-next disabled\">下一页 ›</span>\n");
        }

        html.append("            </nav>\n");
        html.append("            <div class=\"admin-pagination-info\">\n");
        html.append("                第 ").append(currentPage).append(" 页，共 ").append(totalPages).append(" 页\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 截断文本
     */
    private String truncateText(String text, int maxLength) {
        if (text == null)
            return "";
        if (text.length() <= maxLength)
            return text;
        return text.substring(0, maxLength) + "...";
    }

    /**
     * 生成管理员玩家专用页面（使用管理员样式和表格格式）
     */
    public String generateAdminPlayerPage(String playerName, int page, String playerFilter, String staffFilter,
            PunishmentRecord.PunishmentType typeFilter) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>管理员 - ").append(escapeHtml(playerName)).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generateAdminPunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        // 管理员顶部导航栏
        html.append(generateAdminSiteHeader(typeFilter, null, playerName));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 玩家信息头部（重用现有的方法）
        html.append(generatePlayerPageHeader(playerName, typeFilter));

        // 获取玩家的处罚记录（使用分页）
        List<PunishmentRecord> records = new ArrayList<>();
        int totalRecords = 0;
        int pageSize = 10; // 每页显示10条记录

        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            // 管理员玩家页面应该只显示指定玩家的记录，使用精确匹配
            records = plugin.getPunishmentManager().getPunishmentsByPlayer(playerName, page, pageSize);
            // 获取总数（用于分页）
            totalRecords = plugin.getPunishmentManager().getPlayerPunishmentCount(playerName);

            // 如果有类型过滤，进一步过滤记录
            if (typeFilter != null) {
                records = records.stream()
                        .filter(record -> record.getType() == typeFilter)
                        .collect(java.util.stream.Collectors.toList());
                // 重新计算总数
                totalRecords = plugin.getPunishmentManager().getPlayerPunishmentCount(playerName, typeFilter);
            }

            // 如果有执行者过滤，进一步过滤记录
            if (staffFilter != null && !staffFilter.trim().isEmpty()) {
                records = records.stream()
                        .filter(record -> {
                            String staffName = record.getBannedByName();
                            String staffUuid = record.getBannedByUuid();

                            // 检查是否是控制台过滤（支持中英文）
                            boolean isConsoleFilter = "Console".equalsIgnoreCase(staffFilter) || "控制台".equals(staffFilter);
                            boolean isConsoleRecord = staffName == null || "Console".equalsIgnoreCase(staffName) || "控制台".equals(staffName);

                            return (staffName != null && staffName.equalsIgnoreCase(staffFilter)) ||
                                    (staffUuid != null && staffUuid.equalsIgnoreCase(staffFilter)) ||
                                    (isConsoleFilter && isConsoleRecord) ||
                                    (staffName != null && staffName.toLowerCase().contains(staffFilter.toLowerCase()));
                        })
                        .collect(java.util.stream.Collectors.toList());
                // 注意：过滤后的总数可能不准确，但这是一个权衡
            }
        }

        // 计算分页信息
        int totalPages = (int) Math.ceil((double) totalRecords / pageSize);

        // 生成过滤器区域（如果有过滤条件）
        if ((playerFilter != null && !playerFilter.trim().isEmpty()) ||
            (staffFilter != null && !staffFilter.trim().isEmpty())) {
            html.append(generateAdminFiltersSection(playerFilter, staffFilter));
        }

        // 生成处罚记录表格（重用现有的管理员表格方法）
        html.append(generateAdminPunishmentTable(records));

        // 添加分页控件
        if (totalPages > 1) {
            html.append(generateAdminPlayerPagination(playerName, page, totalPages, typeFilter, playerFilter, staffFilter));
        }

        // 添加操作按钮（重用现有的方法）
        AdminPlayerActionGenerator actionGenerator = new AdminPlayerActionGenerator(plugin);
        html.append(actionGenerator.generateAdminActionButtons(playerName));
        html.append(actionGenerator.generateAllModals());

        html.append("    </div>\n");

        // 添加JavaScript
        html.append("    <script>\n");
        html.append(generateAdminPunishmentPageJS());
        html.append(AdminPlayerActionJS.generateActionJavaScript());
        html.append("        \n");
        html.append("        // 管理员玩家页面专用：增强自动更新功能\n");
        html.append("        document.addEventListener('DOMContentLoaded', function() {\n");
        html.append("            const playerName = '").append(escapeHtml(playerName)).append("';\n");
        html.append("            \n");
        html.append("            // 重写updatePageContent函数以支持管理员玩家页面\n");
        html.append("            const originalUpdatePageContent = window.updatePageContent;\n");
        html.append("            window.updatePageContent = function(data) {\n");
        html.append("                // 调用原始更新函数\n");
        html.append("                if (originalUpdatePageContent) {\n");
        html.append("                    originalUpdatePageContent(data);\n");
        html.append("                }\n");
        html.append("                \n");
        html.append("                // 管理员玩家页面特殊处理：更新操作按钮状态\n");
        html.append("                if (typeof updateAllActionButtonsStatus === 'function') {\n");
        html.append("                    updateAllActionButtonsStatus(playerName);\n");
        html.append("                }\n");
        html.append("            };\n");
        html.append("            \n");
        html.append("            // 初始化按钮状态检测\n");
        html.append("            if (typeof updateAllActionButtonsStatus === 'function') {\n");
        html.append("                updateAllActionButtonsStatus(playerName);\n");
        html.append("            }\n");
        html.append("        });\n");
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>\n");

        return html.toString();
    }

    /**
     * 生成管理员玩家页面分页控件
     */
    private String generateAdminPlayerPagination(String playerName, int currentPage, int totalPages,
            PunishmentRecord.PunishmentType typeFilter, String playerFilter, String staffFilter) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"admin-pagination-section\">\n");
        html.append("            <nav class=\"admin-pagination-nav\">\n");

        // 构建基础URL
        StringBuilder baseUrl = new StringBuilder("/admin-player/").append(escapeUrl(playerName));
        boolean hasParams = false;

        if (typeFilter != null) {
            baseUrl.append("?type=").append(typeFilter.name().toLowerCase());
            hasParams = true;
        }

        if (playerFilter != null && !playerFilter.trim().isEmpty()) {
            baseUrl.append(hasParams ? "&" : "?").append("player=").append(escapeUrl(playerFilter));
            hasParams = true;
        }

        if (staffFilter != null && !staffFilter.trim().isEmpty()) {
            baseUrl.append(hasParams ? "&" : "?").append("staff=").append(escapeUrl(staffFilter));
            hasParams = true;
        }

        String baseUrlStr = baseUrl.toString();
        String pageParam = hasParams ? "&page=" : "?page=";

        // 上一页按钮
        if (currentPage > 1) {
            html.append("                <a href=\"").append(baseUrlStr).append(pageParam).append(currentPage - 1)
                    .append("\" class=\"admin-pagination-btn admin-pagination-prev\">‹ 上一页</a>\n");
        } else {
            html.append(
                    "                <span class=\"admin-pagination-btn admin-pagination-prev disabled\">‹ 上一页</span>\n");
        }

        // 页码按钮
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);

        // 如果当前页靠近开始，显示更多后面的页码
        if (currentPage <= 3) {
            endPage = Math.min(totalPages, 5);
        }

        // 如果当前页靠近结束，显示更多前面的页码
        if (currentPage >= totalPages - 2) {
            startPage = Math.max(1, totalPages - 4);
        }

        // 第一页
        if (startPage > 1) {
            html.append("                <a href=\"").append(baseUrlStr).append(pageParam).append("1")
                    .append("\" class=\"admin-pagination-btn\">1</a>\n");
            if (startPage > 2) {
                html.append("                <span class=\"admin-pagination-ellipsis\">...</span>\n");
            }
        }

        // 中间页码
        for (int i = startPage; i <= endPage; i++) {
            if (i == currentPage) {
                html.append("                <span class=\"admin-pagination-btn admin-pagination-current\">").append(i)
                        .append("</span>\n");
            } else {
                html.append("                <a href=\"").append(baseUrlStr).append(pageParam).append(i)
                        .append("\" class=\"admin-pagination-btn\">").append(i).append("</a>\n");
            }
        }

        // 最后一页
        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html.append("                <span class=\"admin-pagination-ellipsis\">...</span>\n");
            }
            html.append("                <a href=\"").append(baseUrlStr).append(pageParam).append(totalPages)
                    .append("\" class=\"admin-pagination-btn\">").append(totalPages).append("</a>\n");
        }

        // 下一页按钮
        if (currentPage < totalPages) {
            html.append("                <a href=\"").append(baseUrlStr).append(pageParam).append(currentPage + 1)
                    .append("\" class=\"admin-pagination-btn admin-pagination-next\">下一页 ›</a>\n");
        } else {
            html.append(
                    "                <span class=\"admin-pagination-btn admin-pagination-next disabled\">下一页 ›</span>\n");
        }

        html.append("            </nav>\n");
        html.append("            <div class=\"admin-pagination-info\">\n");
        html.append("                第 ").append(currentPage).append(" 页，共 ").append(totalPages).append(" 页\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }
}
