<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>奖励信息测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .reward-item {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 10px 0;
        }
        .reward-item h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .reward-item p {
            margin: 0;
            color: #6c757d;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 奖励信息测试页面</h1>
        <p>这个页面用于测试奖励信息配置功能</p>
        
        <div class="controls">
            <button onclick="loadRewardInfo()">加载奖励信息</button>
            <button onclick="testSaveRewardInfo()">测试保存奖励信息</button>
        </div>
        
        <div id="status"></div>
        
        <div id="rewardDisplay">
            <h2>当前奖励配置</h2>
            <div id="rewardContent">
                <p>点击"加载奖励信息"按钮来获取当前配置</p>
            </div>
        </div>
    </div>

    <script>
        // 显示状态信息
        function showStatus(message, type = 'success') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            
            setTimeout(() => {
                statusDiv.textContent = '';
                statusDiv.className = 'status';
            }, 5000);
        }

        // 加载奖励信息
        async function loadRewardInfo() {
            try {
                showStatus('正在加载奖励信息...', 'info');
                
                const response = await fetch('/checkin/api', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        action: 'get_reward_info'
                    })
                });
                
                const data = await response.json();
                
                if (data.success && data.reward_info) {
                    displayRewardInfo(data.reward_info);
                    showStatus('奖励信息加载成功！', 'success');
                } else {
                    showStatus('加载失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('加载奖励信息失败:', error);
                showStatus('网络错误: ' + error.message, 'error');
            }
        }

        // 显示奖励信息
        function displayRewardInfo(rewardInfo) {
            const contentDiv = document.getElementById('rewardContent');
            let html = '';
            
            // 每日奖励
            if (rewardInfo.daily) {
                html += `
                    <div class="reward-item">
                        <h4>${rewardInfo.daily.title}</h4>
                        <p>${rewardInfo.daily.description}</p>
                    </div>
                `;
            }
            
            // 连续奖励
            if (rewardInfo.consecutive) {
                if (rewardInfo.consecutive.day7) {
                    html += `
                        <div class="reward-item">
                            <h4>${rewardInfo.consecutive.day7.title}</h4>
                            <p>${rewardInfo.consecutive.day7.description}</p>
                        </div>
                    `;
                }
                
                if (rewardInfo.consecutive.day15) {
                    html += `
                        <div class="reward-item">
                            <h4>${rewardInfo.consecutive.day15.title}</h4>
                            <p>${rewardInfo.consecutive.day15.description}</p>
                        </div>
                    `;
                }
                
                if (rewardInfo.consecutive.day30) {
                    html += `
                        <div class="reward-item">
                            <h4>${rewardInfo.consecutive.day30.title}</h4>
                            <p>${rewardInfo.consecutive.day30.description}</p>
                        </div>
                    `;
                }
            }
            
            contentDiv.innerHTML = html || '<p>没有找到奖励配置信息</p>';
        }

        // 测试保存奖励信息
        async function testSaveRewardInfo() {
            const testData = {
                daily: {
                    title: '测试每日奖励',
                    description: '这是一个测试的每日奖励描述'
                },
                consecutive: {
                    day7: {
                        title: '测试7天奖励',
                        description: '这是一个测试的7天连续奖励'
                    },
                    day15: {
                        title: '测试15天奖励',
                        description: '这是一个测试的15天连续奖励'
                    },
                    day30: {
                        title: '测试30天奖励',
                        description: '这是一个测试的30天连续奖励'
                    }
                }
            };
            
            try {
                showStatus('正在保存测试数据...', 'info');
                
                const response = await fetch('/admin/checkin-reward-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showStatus('测试数据保存成功！', 'success');
                    // 重新加载显示
                    setTimeout(() => loadRewardInfo(), 1000);
                } else {
                    showStatus('保存失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('保存测试数据失败:', error);
                showStatus('网络错误: ' + error.message, 'error');
            }
        }

        // 页面加载时自动加载奖励信息
        document.addEventListener('DOMContentLoaded', function() {
            loadRewardInfo();
        });
    </script>
</body>
</html>
