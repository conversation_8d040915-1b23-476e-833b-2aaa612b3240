package cn.acebrand.acekeysystem.task;

import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 玩家奖励处理任务
 * 当玩家上线时延迟处理其待处理的奖励
 */
public final class PlayerRewardProcessTask extends BukkitRunnable {

    private final AceKeySystem plugin;
    private final Player player;
    private final String playerName;

    /**
     * 构造函数
     *
     * @param plugin     插件实例
     * @param player     目标玩家
     * @param playerName 玩家名称
     */
    public PlayerRewardProcessTask(AceKeySystem plugin, Player player, String playerName) {
        this.plugin = plugin;
        this.player = player;
        this.playerName = playerName;
    }

    @Override
    public void run() {
        // 检查玩家是否仍然在线
        if (this.player.isOnline()) {
            // 处理该玩家的待处理奖励
            this.plugin.checkPendingRewardsForPlayer(this.playerName);
        }
    }
}
