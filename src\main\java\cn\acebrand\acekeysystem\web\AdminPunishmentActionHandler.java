package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentManager;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 管理员处罚操作处理器
 * 处理来自管理员页面的封禁、禁言、警告、踢出等操作请求
 */
public class AdminPunishmentActionHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final Gson gson;

    public AdminPunishmentActionHandler(AceKeySystem plugin) {
        this.plugin = plugin;
        this.gson = new Gson();
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        if ("POST".equals(method)) {
            handlePostRequest(exchange);
        } else {
            sendErrorResponse(exchange, 405, "Method Not Allowed");
        }
    }

    private void handlePostRequest(HttpExchange exchange) throws IOException {
        try {
            // 读取请求体
            String requestBody = readRequestBody(exchange);
            JsonObject jsonRequest = gson.fromJson(requestBody, JsonObject.class);

            // 验证请求数据
            if (!jsonRequest.has("action") || !jsonRequest.has("player")) {
                sendErrorResponse(exchange, 400, "缺少必要参数");
                return;
            }

            String action = jsonRequest.get("action").getAsString();
            String playerName = jsonRequest.get("player").getAsString();
            String reason = jsonRequest.has("reason") ? jsonRequest.get("reason").getAsString() : "";
            boolean silent = jsonRequest.has("silent") && jsonRequest.get("silent").getAsBoolean();

            // 验证玩家名
            if (playerName.trim().isEmpty()) {
                sendErrorResponse(exchange, 400, "玩家名不能为空");
                return;
            }

            // 对于需要原因的操作，验证原因不能为空
            // unban 和 unmute 操作不需要原因
            if (!"unban".equals(action.toLowerCase()) && !"unmute".equals(action.toLowerCase()) && reason.trim().isEmpty()) {
                sendErrorResponse(exchange, 400, "原因不能为空");
                return;
            }

            // 执行相应的处罚操作
            boolean success = false;
            String message = "";

            switch (action.toLowerCase()) {
                case "ban":
                    String banDuration = jsonRequest.has("duration") ? jsonRequest.get("duration").getAsString() : "7d";
                    success = executeBan(playerName, reason, banDuration, silent);
                    message = success ? "封禁成功" : "封禁失败";
                    break;

                case "mute":
                    String muteDuration = jsonRequest.has("duration") ? jsonRequest.get("duration").getAsString()
                            : "1h";
                    success = executeMute(playerName, reason, muteDuration, silent);
                    message = success ? "禁言成功" : "禁言失败";
                    break;

                case "warn":
                    success = executeWarn(playerName, reason, silent);
                    message = success ? "警告成功" : "警告失败";
                    break;

                case "kick":
                    Map<String, Object> kickResult = executeKickWithCheck(playerName, reason, silent);
                    success = (Boolean) kickResult.get("success");
                    message = (String) kickResult.get("message");

                    // 如果有特殊代码，添加到响应中
                    if (kickResult.containsKey("code")) {
                        Map<String, Object> response = new HashMap<>();
                        response.put("success", success);
                        response.put("message", message);
                        response.put("code", kickResult.get("code"));
                        sendJsonResponse(exchange, 200, response);
                        return;
                    }
                    break;

                case "unban":
                    success = executeUnban(playerName);
                    message = success ? "解封成功" : "解封失败";
                    break;

                case "unmute":
                    success = executeUnmute(playerName);
                    message = success ? "解除禁言成功" : "解除禁言失败";
                    break;

                default:
                    sendErrorResponse(exchange, 400, "未知的操作类型: " + action);
                    return;
            }

            // 发送响应
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", message);

            sendJsonResponse(exchange, 200, response);

        } catch (Exception e) {
            plugin.getLogger().warning("处理管理员操作请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "服务器内部错误");
        }
    }

    private boolean executeBan(String playerName, String reason, String duration, boolean silent) {
        try {
            // 构建LiteBans命令
            String command = buildBanCommand(playerName, reason, duration, silent);

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页封禁了玩家 %s，原因: %s，时长: %s",
                        playerName, reason, duration));
            }

            return success;
        } catch (Exception e) {
            plugin.getLogger().warning("执行封禁操作失败: " + e.getMessage());
            return false;
        }
    }

    private boolean executeMute(String playerName, String reason, String duration, boolean silent) {
        try {
            // 构建LiteBans命令
            String command = buildMuteCommand(playerName, reason, duration, silent);

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页禁言了玩家 %s，原因: %s，时长: %s",
                        playerName, reason, duration));
            }

            return success;
        } catch (Exception e) {
            plugin.getLogger().warning("执行禁言操作失败: " + e.getMessage());
            return false;
        }
    }

    private boolean executeWarn(String playerName, String reason, boolean silent) {
        try {
            // 构建LiteBans命令
            String command = buildWarnCommand(playerName, reason, silent);

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页警告了玩家 %s，原因: %s",
                        playerName, reason));
            }

            return success;
        } catch (Exception e) {
            plugin.getLogger().warning("执行警告操作失败: " + e.getMessage());
            return false;
        }
    }

    private boolean executeKick(String playerName, String reason, boolean silent) {
        try {
            // 构建LiteBans命令
            String command = buildKickCommand(playerName, reason, silent);

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页踢出了玩家 %s，原因: %s",
                        playerName, reason));
            }

            return success;
        } catch (Exception e) {
            plugin.getLogger().warning("执行踢出操作失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 构建封禁命令
     */
    private String buildBanCommand(String playerName, String reason, String duration, boolean silent) {
        StringBuilder cmd = new StringBuilder();
        if (silent) {
            cmd.append("litebans:bansilent ");
        } else {
            cmd.append("litebans:ban ");
        }
        cmd.append(playerName).append(" ");
        if (!"permanent".equals(duration)) {
            cmd.append(duration).append(" ");
        }
        cmd.append(reason);
        return cmd.toString();
    }

    /**
     * 构建禁言命令
     */
    private String buildMuteCommand(String playerName, String reason, String duration, boolean silent) {
        StringBuilder cmd = new StringBuilder();
        if (silent) {
            cmd.append("litebans:mutesilent ");
        } else {
            cmd.append("litebans:mute ");
        }
        cmd.append(playerName).append(" ");
        if (!"permanent".equals(duration)) {
            cmd.append(duration).append(" ");
        }
        cmd.append(reason);
        return cmd.toString();
    }

    /**
     * 构建警告命令
     */
    private String buildWarnCommand(String playerName, String reason, boolean silent) {
        StringBuilder cmd = new StringBuilder();
        if (silent) {
            cmd.append("litebans:warnsilent ");
        } else {
            cmd.append("litebans:warn ");
        }
        cmd.append(playerName).append(" ").append(reason);
        return cmd.toString();
    }

    /**
     * 构建踢出命令
     */
    private String buildKickCommand(String playerName, String reason, boolean silent) {
        StringBuilder cmd = new StringBuilder();
        if (silent) {
            cmd.append("litebans:kicksilent ");
        } else {
            cmd.append("litebans:kick ");
        }
        cmd.append(playerName).append(" ").append(reason);
        return cmd.toString();
    }

    /**
     * 在主线程中执行命令
     */
    private boolean executeCommand(String command) {
        try {
            // 使用同步任务在主线程中执行命令
            final boolean[] result = { false };
            final String[] errorMessage = { null };

            Runnable task = () -> {
                try {
                    result[0] = plugin.getServer().dispatchCommand(
                            plugin.getServer().getConsoleSender(),
                            command);
                } catch (Exception e) {
                    plugin.getLogger().warning("执行命令失败: " + command + " - " + e.getMessage());
                    errorMessage[0] = e.getMessage();
                    result[0] = false;
                }
            };

            if (plugin.getServer().isPrimaryThread()) {
                // 如果已经在主线程中，直接执行
                task.run();
            } else {
                // 如果在其他线程中，同步到主线程执行
                plugin.getServer().getScheduler().runTask(plugin, task);
                // 等待命令执行完成（最多等待5秒）
                Thread.sleep(100);
            }

            return result[0];
        } catch (Exception e) {
            plugin.getLogger().warning("执行命令时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 执行踢出命令并检查玩家是否在线
     */
    private Map<String, Object> executeKickWithCheck(String playerName, String reason, boolean silent) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 首先检查玩家是否在线
            boolean isOnline = plugin.getServer().getPlayer(playerName) != null;

            if (!isOnline) {
                result.put("success", false);
                result.put("message", "玩家 " + playerName + " 不在线，无法踢出");
                result.put("code", "PLAYER_OFFLINE");
                return result;
            }

            // 构建LiteBans命令
            String command = buildKickCommand(playerName, reason, silent);

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页踢出了玩家 %s，原因: %s",
                        playerName, reason));
                result.put("success", true);
                result.put("message", "踢出成功");
            } else {
                result.put("success", false);
                result.put("message", "踢出失败");
            }

        } catch (Exception e) {
            plugin.getLogger().warning("执行踢出操作失败: " + e.getMessage());
            result.put("success", false);
            result.put("message", "踢出失败：" + e.getMessage());
        }

        return result;
    }

    private boolean executeUnban(String playerName) {
        try {
            // 构建LiteBans解封命令
            String command = "litebans:unban " + playerName;

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页解封了玩家 %s",
                        playerName));
            }

            return success;
        } catch (Exception e) {
            plugin.getLogger().warning("执行解封操作失败: " + e.getMessage());
            return false;
        }
    }

    private boolean executeUnmute(String playerName) {
        try {
            // 构建LiteBans解除禁言命令
            String command = "litebans:unmute " + playerName;

            // 在主线程中执行命令
            boolean success = executeCommand(command);

            if (success) {
                plugin.getLogger().info(String.format(
                        "Web管理员通过网页解除禁言了玩家 %s",
                        playerName));
            }

            return success;
        } catch (Exception e) {
            plugin.getLogger().warning("执行解除禁言操作失败: " + e.getMessage());
            return false;
        }
    }

    private String readRequestBody(HttpExchange exchange) throws IOException {
        InputStream inputStream = exchange.getRequestBody();

        // Java 8兼容的读取方式
        StringBuilder result = new StringBuilder();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            result.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
        }

        return result.toString();
    }

    private void sendJsonResponse(HttpExchange exchange, int statusCode, Object data) throws IOException {
        String jsonResponse = gson.toJson(data);
        byte[] responseBytes = jsonResponse.getBytes(StandardCharsets.UTF_8);

        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type, X-Requested-With");

        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream outputStream = exchange.getResponseBody()) {
            outputStream.write(responseBytes);
        }
    }

    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put("success", false);
        errorResponse.put("message", message);

        sendJsonResponse(exchange, statusCode, errorResponse);
    }
}
