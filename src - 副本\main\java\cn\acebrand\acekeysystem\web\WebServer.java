package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.concurrent.Executors;

/**
 * 内置Web服务器
 * 提供管理员界面和用户界面
 */
public class WebServer {

    private final AceKeySystem plugin;
    private HttpServer server;
    private boolean running = false;
    private AdminLoginHandler adminLoginHandler;

    // 配置参数
    private String host;
    private int port;
    private String adminKey;
    private String title;
    private boolean debug;

    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public WebServer(AceKeySystem plugin) {
        this.plugin = plugin;
        loadConfiguration();
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        this.host = plugin.getConfig().getString("web-server.host", "0.0.0.0");
        this.port = plugin.getConfig().getInt("web-server.port", 8080);
        this.adminKey = plugin.getConfig().getString("web-server.admin-key", "MCTV_ADMIN_2024_SECURE");
        this.title = plugin.getConfig().getString("web-server.title", "MCTV卡密系统");
        this.debug = plugin.getConfig().getBoolean("web-server.debug", false);
    }

    /**
     * 启动Web服务器
     */
    public boolean start() {
        if (running) {
            return true;
        }

        try {
            // 创建HTTP服务器
            server = HttpServer.create(new InetSocketAddress(host, port), 0);

            // 注册处理器
            server.createContext("/", new WebHandler(plugin, this));
            server.createContext("/api", new WebAPIHandler(plugin, this));
            adminLoginHandler = new AdminLoginHandler(plugin, this);
            server.createContext("/admin", adminLoginHandler);
            server.createContext("/admin/accounts", new AccountManagementHandler(plugin, this));
            server.createContext("/user", new UserHandler(plugin, this));
            server.createContext("/points-shop", new UserHandler(plugin, this));
            server.createContext("/leaderboard", new LeaderboardHandler(plugin, this));
            server.createContext("/rewards", new RewardManagementHandler(plugin, this));
            server.createContext("/punishments", new PunishmentHandler(plugin, this, plugin.getPunishmentManager()));
            server.createContext("/bans", new PunishmentHandler(plugin, this, plugin.getPunishmentManager())); // 向后兼容
            server.createContext("/player", new PunishmentHandler(plugin, this, plugin.getPunishmentManager())); // 玩家页面
            server.createContext("/admin-punishments",
                    new AdminPunishmentHandler(plugin, this, plugin.getPunishmentManager())); // 管理员处罚界面
            server.createContext("/admin-player",
                    new AdminPunishmentHandler(plugin, this, plugin.getPunishmentManager())); // 管理员玩家页面
            server.createContext("/admin-punishment-action",
                    new AdminPunishmentActionHandler(plugin)); // 管理员处罚操作API
            server.createContext("/admin-player-status",
                    new AdminPlayerStatusHandler(plugin)); // 管理员玩家状态检查API
            server.createContext("/static", new StaticHandler(plugin, this));

            // 设置线程池
            server.setExecutor(Executors.newFixedThreadPool(4));

            // 启动服务器
            server.start();
            running = true;

            plugin.getLogger().info("Web服务器已启动！");
            plugin.getLogger().info("访问地址: http://" + (host.equals("0.0.0.0") ? "localhost" : host) + ":" + port);
            plugin.getLogger()
                    .info("管理员界面: http://" + (host.equals("0.0.0.0") ? "localhost" : host) + ":" + port + "/admin");
            plugin.getLogger()
                    .info("用户界面: http://" + (host.equals("0.0.0.0") ? "localhost" : host) + ":" + port + "/user");

            return true;

        } catch (IOException e) {
            plugin.getLogger().severe("启动Web服务器失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 停止Web服务器
     */
    public void stop() {
        if (!running || server == null) {
            return;
        }

        try {
            server.stop(2); // 等待2秒后强制停止
            running = false;
            plugin.getLogger().info("Web服务器已停止！");
        } catch (Exception e) {
            plugin.getLogger().severe("停止Web服务器时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查服务器是否运行中
     */
    public boolean isRunning() {
        return running;
    }

    /**
     * 获取管理员密钥
     */
    public String getAdminKey() {
        return adminKey;
    }

    /**
     * 获取网站标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 是否启用调试模式
     */
    public boolean isDebug() {
        return debug;
    }

    /**
     * 获取服务器端口
     */
    public int getPort() {
        return port;
    }

    /**
     * 获取服务器主机
     */
    public String getHost() {
        return host;
    }

    /**
     * 获取管理员登录处理器
     */
    public AdminLoginHandler getAdminLoginHandler() {
        return adminLoginHandler;
    }

    /**
     * 重新加载配置
     */
    public void reloadConfiguration() {
        loadConfiguration();
    }
}
