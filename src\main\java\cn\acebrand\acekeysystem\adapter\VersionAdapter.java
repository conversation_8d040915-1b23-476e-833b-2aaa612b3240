package cn.acebrand.acekeysystem.adapter;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * 版本适配器接口
 * 用于处理不同Minecraft版本之间的兼容性问题
 */
public interface VersionAdapter {

    /**
     * 获取服务器版本字符串
     * 
     * @return 版本字符串
     */
    String getVersion();

    /**
     * 为物品设置NBT数据
     * 
     * @param itemStack 目标物品
     * @param key       数据键
     * @param value     数据值
     * @return 设置后的物品
     */
    ItemStack setItemNBT(ItemStack itemStack, String key, String value);

    /**
     * 从物品获取NBT数据
     * 
     * @param itemStack 目标物品
     * @param key       数据键
     * @return 数据值，如果不存在则返回null
     */
    String getItemNBT(ItemStack itemStack, String key);

    /**
     * 检查物品是否包含指定的NBT数据
     * 
     * @param itemStack 目标物品
     * @param key       数据键
     * @return 是否包含该数据
     */
    boolean hasItemNBT(ItemStack itemStack, String key);

    /**
     * 向玩家发送标题消息
     *
     * @param player   目标玩家
     * @param title    主标题
     * @param subtitle 副标题
     * @param fadeIn   淡入时间(tick)
     * @param stay     停留时间(tick)
     * @param fadeOut  淡出时间(tick)
     */
    void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut);

    /**
     * 向玩家发送可点击的链接
     *
     * @param player      目标玩家
     * @param url         链接URL
     * @param displayText 显示文本
     */
    void sendClickableLink(Player player, String url, String displayText);
}
