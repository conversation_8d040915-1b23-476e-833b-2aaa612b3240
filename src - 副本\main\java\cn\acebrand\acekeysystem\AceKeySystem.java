package cn.acebrand.acekeysystem;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.Reader;
import java.io.Writer;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;
import org.bukkit.scheduler.BukkitRunnable;

import cn.acebrand.acekeysystem.adapter.LegacyVersionAdapter;
import cn.acebrand.acekeysystem.adapter.ModernVersionAdapter;
import cn.acebrand.acekeysystem.adapter.NMSVersionDetector;
import cn.acebrand.acekeysystem.adapter.VersionAdapter;
import cn.acebrand.acekeysystem.logging.WebLogHandler;
import cn.acebrand.acekeysystem.lottery.LotteryManager;
import cn.acebrand.acekeysystem.points.PointsManager;
import cn.acebrand.acekeysystem.points.PointsShopManager;
import cn.acebrand.acekeysystem.points.PendingPurchaseManager;
import cn.acebrand.acekeysystem.data.BindingDataManager;
import cn.acebrand.acekeysystem.task.KeySyncTask;
import cn.acebrand.acekeysystem.task.ManualSyncTask;
import cn.acebrand.acekeysystem.task.PendingRewardCleanupTask;
import cn.acebrand.acekeysystem.task.PlayerRewardProcessTask;
import cn.acebrand.acekeysystem.task.RewardCheckTask;
import cn.acebrand.acekeysystem.task.RewardExecutionTask;
import cn.acebrand.acekeysystem.task.ScheduledRewardCheckTask;
import cn.acebrand.acekeysystem.task.ScheduledSyncTask;
import cn.acebrand.acekeysystem.util.VersionUtils;
import cn.acebrand.acekeysystem.web.WebServer;

import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * AceKeySystem 卡密生成插件主类
 *
 * 功能包括:
 * - 卡密生成和管理
 * - 网站API集成
 * - 抽奖奖励处理
 * - 版本兼容性处理
 */
public final class AceKeySystem extends JavaPlugin implements Listener, CommandExecutor, TabCompleter {

    // 随机数生成器
    private final Random random = new Random();

    // 配置参数
    private int letterCount;
    private int numberCount;
    private String keysFolder;
    private String defaultMessage;

    // 文件和配置
    private File keysFile;
    private FileConfiguration keysConfig;

    // 版本适配器
    private VersionAdapter versionAdapter;
    private static final String KEY_DATA_KEY = "key_data";

    // 物品配置
    private Material keyItemMaterial;
    private String keyItemName;
    private List<String> keyItemLore;
    private boolean keyItemGlow;

    // 网站集成配置
    private boolean websiteEnabled;
    private String websiteUrl;
    private String apiKey;
    private int syncInterval;
    private boolean syncOnGenerate;
    private int rewardsCheckInterval;

    // 任务管理
    private BukkitRunnable syncTask;
    private BukkitRunnable rewardsCheckTask;
    private BukkitRunnable pendingRewardsTask;
    private BukkitRunnable cleanupTask;

    // 奖励处理
    private Set<String> processedRewardIds = new HashSet<>();
    private File processedRewardsFile;
    private File pendingRewardsFile;
    private FileConfiguration pendingRewardsConfig;
    private int pendingRewardsExpireDays = 15;
    private int pendingRewardsCheckInterval = 300;
    private Set<String> knownPendingRewardIds = new HashSet<>();

    // 兑换券失效设置
    private int voucherExpireSeconds = 86400;
    private int voucherCheckIntervalSeconds = 3600;

    // Web服务器
    private WebServer webServer;
    private boolean webServerEnabled;

    // 抽奖管理器
    private LotteryManager lotteryManager;

    // 积分管理器
    private PointsManager pointsManager;

    // 积分商店管理器
    private PointsShopManager pointsShopManager;

    // 待处理购买管理器
    private PendingPurchaseManager pendingPurchaseManager;

    // 绑定数据管理器
    private BindingDataManager bindingDataManager;

    // 封禁管理器
    private cn.acebrand.acekeysystem.ban.BanManager banManager;
    private cn.acebrand.acekeysystem.punishment.PunishmentManager punishmentManager;

    // 自定义日志处理器
    private WebLogHandler webLogHandler;

    @Override
    public void onEnable() {
        // 初始化版本适配器
        initializeVersionAdapter();

        // 注册事件监听器
        getServer().getPluginManager().registerEvents(this, this);

        // 保存默认配置
        saveDefaultConfig();

        // 加载配置
        loadConfiguration();

        // 初始化自定义日志处理器
        initializeWebLogHandler();

        // 初始化文件系统
        initializeFileSystem();

        // 注册命令
        registerCommands();

        // 启动定时任务
        startScheduledTasks();

        // 初始化抽奖管理器
        initializeLotteryManager();

        // 初始化积分管理器
        initializePointsManager();

        // 初始化绑定数据管理器
        initializeBindingDataManager();

        // 初始化封禁管理器
        initializeBanManager();

        // 初始化处罚管理器
        initializePunishmentManager();

        // 启动Web服务器（必须在所有管理器初始化之后）
        startWebServer();

        // 输出启动信息
        printStartupInfo();
    }

    @Override
    public void onDisable() {
        // 停止Web服务器
        stopWebServer();

        // 取消所有任务
        cancelAllTasks();

        // 关闭并保存网站日志
        if (this.webLogHandler != null) {
            try {
                getLogger().info("正在保存网站日志到插件logs文件夹...");
                this.webLogHandler.close();
                getLogger().removeHandler(this.webLogHandler);
            } catch (Exception e) {
                getLogger().severe("关闭网站日志处理器时出错: " + e.getMessage());
            }
        }

        // 最后一次同步（忽略网络错误）
        if (websiteEnabled) {
            try {
                getLogger().info("正在进行最后一次卡密同步...");
                syncKeysToWebsite();
            } catch (Exception e) {
                // 在服务器关闭时，网络连接可能不可用，忽略同步错误
                getLogger().info("服务器关闭时卡密同步失败（这是正常的）: " + e.getMessage());
            }
        }

        // 关闭封禁管理器
        if (this.banManager != null) {
            try {
                this.banManager.shutdown();
                getLogger().info("封禁管理器已关闭");
            } catch (Exception e) {
                getLogger().severe("关闭封禁管理器时出错: " + e.getMessage());
            }
        }

        // 关闭处罚管理器
        if (this.punishmentManager != null) {
            try {
                this.punishmentManager.shutdown();
                getLogger().info("处罚管理器已关闭");
            } catch (Exception e) {
                getLogger().severe("关闭处罚管理器时出错: " + e.getMessage());
            }
        }

        // 保存所有数据
        saveAllData();

        getLogger().info("AceKeySystem 已禁用！");
    }

    /**
     * 初始化版本适配器
     */
    private void initializeVersionAdapter() {
        try {
            // 使用新的版本检测器
            NMSVersionDetector detector = new NMSVersionDetector(this);

            // 输出版本兼容性信息
            getLogger().info("版本兼容性信息:");
            for (String line : detector.getCompatibilityInfo().split("\n")) {
                getLogger().info("  " + line);
            }

            // 创建适配器
            this.versionAdapter = detector.createAdapter();

            getLogger().info("版本适配器初始化成功: " + this.versionAdapter.getClass().getSimpleName());

            // 如果不是官方支持的版本，给出警告
            if (!detector.isSupportedVersion()) {
                getLogger().warning("当前服务器版本可能不被官方支持，如果遇到问题请联系开发者");
                getLogger().warning("支持的版本: 1.8.8 - 1.21.4");
            }

        } catch (Exception e) {
            getLogger().severe("初始化版本适配器失败: " + e.getMessage());
            e.printStackTrace();

            // 降级处理：使用传统版本适配器
            getLogger().info("尝试使用传统版本适配器作为降级方案");
            this.versionAdapter = new LegacyVersionAdapter();
        }
    }

    /**
     * 初始化自定义日志处理器
     */
    private void initializeWebLogHandler() {
        this.webLogHandler = new WebLogHandler(this);

        // 添加到插件的日志记录器
        getLogger().addHandler(this.webLogHandler);

        // 根据配置设置日志级别
        boolean webLogging = getConfig().getBoolean("website.web-logging", true);
        boolean consoleLogging = getConfig().getBoolean("website.console-logging", false);

        if (!webLogging) {
            // 如果禁用网站日志，移除处理器
            getLogger().removeHandler(this.webLogHandler);
        }

        // 如果启用了网站日志但禁用了控制台日志，则禁用插件的默认控制台输出
        if (webLogging && !consoleLogging) {
            // 设置插件日志记录器不使用父处理器（避免输出到控制台）
            getLogger().setUseParentHandlers(false);
        }

        getLogger().info("自定义日志处理器已初始化，网站日志显示: " + webLogging + ", 控制台日志显示: " + consoleLogging);
    }

    /**
     * 加载配置文件
     */
    private void loadConfiguration() {
        FileConfiguration config = getConfig();

        // 卡密格式配置
        this.letterCount = config.getInt("key-format.letters", 4);
        this.numberCount = config.getInt("key-format.numbers", 6);
        this.keysFolder = config.getString("keys-folder", "keys");
        this.defaultMessage = config.getString("default-message", "请复制这个卡密并在网站上使用！");

        // 物品配置
        loadItemConfiguration(config);

        // 网站集成配置
        loadWebsiteConfiguration(config);

        // 待处理奖励配置
        loadPendingRewardsConfiguration(config);

        // 兑换券失效配置
        loadVoucherExpiryConfiguration(config);

        // Web服务器配置
        loadWebServerConfiguration(config);
    }

    /**
     * 加载物品配置
     */
    private void loadItemConfiguration(FileConfiguration config) {
        String materialName = config.getString("key-item.material", "PAPER");
        try {
            this.keyItemMaterial = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            getLogger().warning("配置文件中的物品类型 " + materialName + " 无效，使用默认值 PAPER");
            this.keyItemMaterial = Material.PAPER;
        }

        this.keyItemName = config.getString("key-item.name", "§6§l卡密兑换券");
        this.keyItemLore = config.getStringList("key-item.lore");
        if (this.keyItemLore.isEmpty()) {
            this.keyItemLore = Arrays.asList(
                    "§7这是一张神奇的兑换券",
                    "§7右键点击可以获得卡密",
                    "§c注意：仅可使用一次！");
        }
        this.keyItemGlow = config.getBoolean("key-item.glow", true);
    }

    /**
     * 加载网站集成配置
     */
    private void loadWebsiteConfiguration(FileConfiguration config) {
        this.websiteEnabled = config.getBoolean("website.enabled", false);
        this.websiteUrl = config.getString("website.url", "");
        this.apiKey = config.getString("website.api-key", "");
        this.syncInterval = config.getInt("website.sync-interval", 5);
        this.syncOnGenerate = config.getBoolean("website.sync-on-generate", true);
        this.rewardsCheckInterval = config.getInt("website.rewards-check-interval", 30);
    }

    /**
     * 加载待处理奖励配置
     */
    private void loadPendingRewardsConfiguration(FileConfiguration config) {
        this.pendingRewardsExpireDays = config.getInt("pending-rewards.expire-days", 15);
        this.pendingRewardsCheckInterval = config.getInt("pending-rewards.check-interval", 300);
    }

    /**
     * 加载兑换券失效配置
     */
    private void loadVoucherExpiryConfiguration(FileConfiguration config) {
        this.voucherExpireSeconds = config.getInt("voucher-expiry.expire-seconds", 86400);
        this.voucherCheckIntervalSeconds = config.getInt("voucher-expiry.check-interval-seconds", 3600);
    }

    /**
     * 加载Web服务器配置
     */
    private void loadWebServerConfiguration(FileConfiguration config) {
        this.webServerEnabled = config.getBoolean("web-server.enabled", true);
    }

    /**
     * 初始化文件系统
     */
    private void initializeFileSystem() {
        // 创建插件数据目录
        if (!getDataFolder().exists()) {
            getDataFolder().mkdirs();
        }

        // 创建卡密存储目录
        File keysDir = new File(getDataFolder(), this.keysFolder);
        if (!keysDir.exists()) {
            keysDir.mkdirs();
        }

        // 初始化卡密配置文件
        initializeKeysFile();

        // 初始化奖励相关文件
        initializeRewardFiles();
    }

    /**
     * 初始化卡密文件
     */
    private void initializeKeysFile() {
        this.keysFile = new File(getDataFolder(), "keys.yml");
        if (!this.keysFile.exists()) {
            try {
                this.keysFile.createNewFile();
                YamlConfiguration config = new YamlConfiguration();
                config.createSection("keys");
                config.save(this.keysFile);
            } catch (IOException e) {
                getLogger().severe("无法创建卡密存储文件！");
                e.printStackTrace();
            }
        }
        loadKeysConfig();
    }

    /**
     * 初始化奖励相关文件
     */
    private void initializeRewardFiles() {
        // 已处理奖励文件
        this.processedRewardsFile = new File(getDataFolder(), "processed_rewards.json");
        if (!this.processedRewardsFile.exists()) {
            try {
                this.processedRewardsFile.createNewFile();
                try (FileWriter writer = new FileWriter(this.processedRewardsFile)) {
                    writer.write("[]");
                }
            } catch (IOException e) {
                getLogger().severe("无法创建已处理奖励记录文件！");
                e.printStackTrace();
            }
        } else {
            loadProcessedRewards();
        }

        // 待处理奖励文件
        this.pendingRewardsFile = new File(getDataFolder(), "pending_rewards.yml");
        if (!this.pendingRewardsFile.exists()) {
            try {
                this.pendingRewardsFile.createNewFile();
                YamlConfiguration config = new YamlConfiguration();
                config.createSection("rewards");
                config.save(this.pendingRewardsFile);
            } catch (IOException e) {
                getLogger().severe("无法创建待处理奖励存储文件！");
                e.printStackTrace();
            }
        }
        loadPendingRewardsConfig();
        loadKnownPendingRewards();
    }

    /**
     * 注册命令
     */
    private void registerCommands() {
        getCommand("acekeys").setExecutor(this);
        getCommand("acereload").setExecutor(this);
        getCommand("acecount").setExecutor(this);
        getCommand("aceget").setExecutor(this);
        getCommand("aceitem").setExecutor(this);
        getCommand("acesync").setExecutor(this);
        getCommand("acerewards").setExecutor(this);
        getCommand("acehelp").setExecutor(this);

        // 设置 acepoints 命令的执行器和 Tab 补全
        getCommand("acepoints").setExecutor(this);
        getCommand("acepoints").setTabCompleter(this);
    }

    /**
     * 启动定时任务
     */
    private void startScheduledTasks() {
        if (websiteEnabled && syncInterval > 0) {
            startSyncTask();
        }
        if (websiteEnabled && rewardsCheckInterval > 0) {
            startRewardsCheckTask();
        }
        startPendingRewardsTask();

        // 启动清理过期分配的定时任务（每小时执行一次）
        startCleanupTask();
    }

    /**
     * 初始化抽奖管理器
     */
    private void initializeLotteryManager() {
        this.lotteryManager = new LotteryManager(this);
        getLogger().info("抽奖管理器已初始化");
    }

    /**
     * 初始化积分管理器
     */
    private void initializePointsManager() {
        this.pointsManager = new PointsManager(this);
        this.pointsShopManager = new PointsShopManager(this);
        this.pendingPurchaseManager = new PendingPurchaseManager(this);
        getLogger().info("积分管理器已初始化");
        getLogger().info("积分商店管理器已初始化");
        getLogger().info("待处理购买管理器已初始化");
    }

    /**
     * 初始化绑定数据管理器
     */
    private void initializeBindingDataManager() {
        this.bindingDataManager = new BindingDataManager(this);
        getLogger().info("绑定数据管理器已初始化");

        // 启动定期清理任务
        Bukkit.getScheduler().runTaskTimerAsynchronously(this, () -> {
            bindingDataManager.cleanupExpiredBindCodes();
            bindingDataManager.cleanupExpiredSessions();
        }, 20L * 60 * 30, 20L * 60 * 60); // 30分钟后开始，每小时执行一次
    }

    /**
     * 初始化封禁管理器
     */
    private void initializeBanManager() {
        this.banManager = new cn.acebrand.acekeysystem.ban.BanManager(this);
        if (this.banManager.initialize()) {
            getLogger().info("封禁管理器已初始化");
        } else {
            getLogger().warning("封禁管理器初始化失败，封禁记录功能将不可用");
        }
    }

    /**
     * 初始化处罚管理器
     */
    private void initializePunishmentManager() {
        getLogger().info("开始初始化处罚管理器...");

        // 检查配置是否启用
        boolean enabled = getConfig().getBoolean("litebans.enabled", false);
        getLogger().info("LiteBans功能配置状态: " + (enabled ? "启用" : "禁用"));

        if (!enabled) {
            getLogger().warning("LiteBans功能在配置中被禁用，跳过处罚管理器初始化");
            return;
        }

        // 显示数据库配置信息
        String host = getConfig().getString("litebans.database.host", "localhost");
        int port = getConfig().getInt("litebans.database.port", 3306);
        String database = getConfig().getString("litebans.database.database", "litebans");
        String username = getConfig().getString("litebans.database.username", "root");
        getLogger().info("数据库配置: " + username + "@" + host + ":" + port + "/" + database);

        this.punishmentManager = new cn.acebrand.acekeysystem.punishment.PunishmentManager(this);
        if (this.punishmentManager.initialize()) {
            getLogger().info("处罚管理器已成功初始化");
        } else {
            getLogger().warning("处罚管理器初始化失败，处罚记录功能将不可用");
            getLogger().warning("请检查:");
            getLogger().warning("1. LiteBans插件是否已安装并正常运行");
            getLogger().warning("2. 数据库连接信息是否正确");
            getLogger().warning("3. 数据库中是否存在LiteBans相关表");
        }
    }

    /**
     * 获取抽奖管理器
     */
    public LotteryManager getLotteryManager() {
        return lotteryManager;
    }

    /**
     * 获取积分管理器
     */
    public PointsManager getPointsManager() {
        return pointsManager;
    }

    /**
     * 获取积分商店管理器
     */
    public PointsShopManager getPointsShopManager() {
        return pointsShopManager;
    }

    /**
     * 获取待处理购买管理器
     */
    public PendingPurchaseManager getPendingPurchaseManager() {
        return pendingPurchaseManager;
    }

    /**
     * 获取绑定数据管理器
     */
    public BindingDataManager getBindingDataManager() {
        return bindingDataManager;
    }

    /**
     * 获取封禁管理器
     */
    public cn.acebrand.acekeysystem.ban.BanManager getBanManager() {
        return banManager;
    }

    /**
     * 获取处罚管理器
     */
    public cn.acebrand.acekeysystem.punishment.PunishmentManager getPunishmentManager() {
        return punishmentManager;
    }

    /**
     * 获取Web日志处理器
     */
    public WebLogHandler getWebLogHandler() {
        return webLogHandler;
    }

    /**
     * 输出启动信息
     */
    private void printStartupInfo() {
        getLogger().info("AceKeySystem 已启用！");
        getLogger().info("插件目录: " + getDataFolder().getAbsolutePath());
        getLogger().info("使用 /acekeys <数量> 批量生成卡密");
        getLogger().info("使用 /acecount 查看剩余卡密数量");
        getLogger().info("使用 /acereload 重载配置");
        getLogger().info("使用 /acerewards 立即检查网站奖励");
        getLogger().info("玩家可使用 /aceget 获取一个卡密");
    }

    /**
     * 加载卡密配置
     */
    private void loadKeysConfig() {
        this.keysConfig = YamlConfiguration.loadConfiguration(this.keysFile);
        if (this.keysConfig.getConfigurationSection("keys") == null) {
            this.keysConfig.createSection("keys");
            saveKeysConfig();
        }
    }

    /**
     * 保存卡密配置
     */
    public void saveKeysConfig() {
        try {
            this.keysConfig.save(this.keysFile);
        } catch (IOException e) {
            getLogger().severe("无法保存卡密数据！");
            e.printStackTrace();
        }
    }

    /**
     * 加载待处理奖励配置
     */
    private void loadPendingRewardsConfig() {
        this.pendingRewardsConfig = YamlConfiguration.loadConfiguration(this.pendingRewardsFile);
        if (this.pendingRewardsConfig.getConfigurationSection("rewards") == null) {
            this.pendingRewardsConfig.createSection("rewards");
            savePendingRewardsConfig();
        }
    }

    /**
     * 保存待处理奖励配置
     */
    private void savePendingRewardsConfig() {
        try {
            this.pendingRewardsConfig.save(this.pendingRewardsFile);
        } catch (IOException e) {
            getLogger().severe("无法保存待处理奖励数据！");
            e.printStackTrace();
        }
    }

    /**
     * 加载已处理奖励
     */
    private void loadProcessedRewards() {
        try {
            JSONParser parser = new JSONParser();
            try (FileReader reader = new FileReader(this.processedRewardsFile)) {
                JSONArray array = (JSONArray) parser.parse(reader);
                this.processedRewardIds.clear();
                for (Object obj : array) {
                    this.processedRewardIds.add((String) obj);
                }
            }
            getLogger().info("加载了 " + this.processedRewardIds.size() + " 个已处理的奖励ID");
        } catch (Exception e) {
            getLogger().severe("加载已处理奖励ID时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存已处理奖励
     */
    private void saveProcessedRewards() {
        try {
            JSONArray array = new JSONArray();
            array.addAll(this.processedRewardIds);
            try (FileWriter writer = new FileWriter(this.processedRewardsFile)) {
                writer.write(array.toJSONString());
            }
        } catch (IOException e) {
            getLogger().severe("保存已处理奖励ID时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 添加已处理奖励ID
     */
    private void addProcessedRewardId(String rewardId) {
        this.processedRewardIds.add(rewardId);
        saveProcessedRewards();
    }

    /**
     * 取消所有任务
     */
    private void cancelAllTasks() {
        if (this.syncTask != null) {
            this.syncTask.cancel();
        }
        if (this.rewardsCheckTask != null) {
            this.rewardsCheckTask.cancel();
        }
        if (this.pendingRewardsTask != null) {
            this.pendingRewardsTask.cancel();
        }
        if (this.cleanupTask != null) {
            this.cleanupTask.cancel();
        }
    }

    /**
     * 保存所有数据
     */
    private void saveAllData() {
        saveKeysConfig();
        savePendingRewardsConfig();
        saveProcessedRewards();

        // 保存待处理购买数据
        if (pendingPurchaseManager != null) {
            pendingPurchaseManager.shutdown();
        }
    }

    /**
     * 同步卡密到网站
     */
    public void syncKeysToWebsite() {
        if (!websiteEnabled) {
            return;
        }

        try {
            // 获取所有卡密
            ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
            if (keysSection == null) {
                return;
            }

            JSONArray keysArray = new JSONArray();
            for (String key : keysSection.getKeys(false)) {
                keysArray.add(key);
            }

            // 发送到网站API
            sendKeysToWebsite(keysArray);
            getLogger().info("已同步 " + keysArray.size() + " 个卡密到网站");

        } catch (java.net.ConnectException e) {
            // 网络连接异常，通常在服务器关闭时发生
            getLogger().info("网络连接不可用，跳过卡密同步: " + e.getMessage());
        } catch (Exception e) {
            // 检查是否是网络连接异常
            if (e.getCause() instanceof java.net.ConnectException ||
                    e.getMessage().contains("Connection refused") ||
                    e.getMessage().contains("ConnectException") ||
                    e.getMessage().contains("网络连接失败")) {
                // 网络连接异常，通常在服务器关闭时发生
                getLogger().info("网络连接不可用，跳过卡密同步: " + e.getMessage());
            } else {
                getLogger().severe("同步卡密到网站时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 发送卡密数据到网站
     */
    private void sendKeysToWebsite(JSONArray keys) throws java.net.ConnectException {
        try {
            // 构建请求数据
            JSONObject requestData = new JSONObject();
            requestData.put("action", "sync_keys");
            requestData.put("api_key", apiKey);
            requestData.put("keys", keys);

            // 发送HTTP请求
            String response = sendHttpRequest(websiteUrl, requestData.toJSONString());

            // 解析响应
            JSONParser parser = new JSONParser();
            JSONObject responseObj = (JSONObject) parser.parse(response);

            Boolean success = (Boolean) responseObj.get("success");
            if (success != null && success) {
                getLogger().info("成功同步 " + keys.size() + " 个卡密到网站");
            } else {
                String message = (String) responseObj.get("message");
                getLogger().warning("同步卡密失败: " + message);
            }

        } catch (java.net.ConnectException e) {
            // 重新抛出ConnectException，让调用者处理
            throw e;
        } catch (Exception e) {
            getLogger().severe("发送卡密到网站时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 发送HTTP请求
     */
    private String sendHttpRequest(String urlString, String jsonData) throws Exception {
        try {
            URL url = new URL(urlString);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();

            // 设置请求方法和属性
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setRequestProperty("User-Agent", "KeyGenPlugin/1.0");
            connection.setDoOutput(true);
            connection.setConnectTimeout(5000); // 5秒连接超时（减少等待时间）
            connection.setReadTimeout(10000); // 10秒读取超时（减少等待时间）

            // 发送请求数据
            try (DataOutputStream outputStream = new DataOutputStream(connection.getOutputStream())) {
                byte[] input = jsonData.getBytes(StandardCharsets.UTF_8);
                outputStream.write(input, 0, input.length);
            }

            // 读取响应
            int responseCode = connection.getResponseCode();
            StringBuilder response = new StringBuilder();

            try (BufferedReader reader = new BufferedReader(new InputStreamReader(
                    responseCode >= 200 && responseCode < 300 ? connection.getInputStream()
                            : connection.getErrorStream(),
                    StandardCharsets.UTF_8))) {

                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
            }

            if (responseCode >= 200 && responseCode < 300) {
                return response.toString();
            } else {
                throw new Exception("HTTP请求失败，响应码: " + responseCode + ", 响应: " + response.toString());
            }
        } catch (java.net.ConnectException e) {
            // 直接抛出ConnectException，让调用者处理
            throw new java.net.ConnectException("网络连接失败: " + e.getMessage());
        } catch (java.net.SocketTimeoutException e) {
            // 超时异常
            throw new java.net.ConnectException("网络连接超时: " + e.getMessage());
        } catch (java.net.UnknownHostException e) {
            // 主机不存在
            throw new java.net.ConnectException("无法解析主机: " + e.getMessage());
        }
    }

    /**
     * 加载已知待处理奖励
     */
    public void loadKnownPendingRewards() {
        knownPendingRewardIds.clear();
        ConfigurationSection rewardsSection = pendingRewardsConfig.getConfigurationSection("rewards");
        if (rewardsSection != null) {
            for (String playerName : rewardsSection.getKeys(false)) {
                ConfigurationSection playerSection = rewardsSection.getConfigurationSection(playerName);
                if (playerSection != null) {
                    for (String rewardId : playerSection.getKeys(false)) {
                        knownPendingRewardIds.add(rewardId);
                    }
                }
            }
        }
        getLogger().info("加载了 " + knownPendingRewardIds.size() + " 个已知待处理奖励");
    }

    /**
     * 检查并执行网站奖励
     */
    public void checkAndExecuteRewards() {
        if (!websiteEnabled) {
            return;
        }

        try {
            // 构建请求数据
            JSONObject requestData = new JSONObject();
            requestData.put("action", "get_rewards");
            requestData.put("api_key", apiKey);
            requestData.put("limit", 100);

            // 发送HTTP请求
            String response = sendHttpRequest(websiteUrl, requestData.toJSONString());

            // 解析响应
            JSONParser parser = new JSONParser();
            JSONObject responseObj = (JSONObject) parser.parse(response);

            Boolean success = (Boolean) responseObj.get("success");
            if (success != null && success) {
                JSONObject data = (JSONObject) responseObj.get("data");
                if (data != null) {
                    JSONArray rewards = (JSONArray) data.get("rewards");
                    if (rewards != null) {
                        processRewards(rewards);
                    }
                }
            } else {
                String message = (String) responseObj.get("message");
                getLogger().warning("获取奖励失败: " + message);
            }

        } catch (Exception e) {
            getLogger().severe("检查网站奖励时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理奖励列表
     */
    private void processRewards(JSONArray rewards) {
        for (Object rewardObj : rewards) {
            if (rewardObj instanceof JSONObject) {
                JSONObject reward = (JSONObject) rewardObj;
                String rewardId = (String) reward.get("reward_id");
                String username = (String) reward.get("username");
                String command = (String) reward.get("command");

                if (rewardId != null && username != null && command != null) {
                    // 检查是否已经处理过
                    if (!knownPendingRewardIds.contains(rewardId)) {
                        knownPendingRewardIds.add(rewardId);

                        // 执行奖励命令
                        executeRewardCommand(rewardId, username, command);
                    }
                }
            }
        }
    }

    /**
     * 执行奖励命令
     */
    private void executeRewardCommand(String rewardId, String username, String command) {
        // 在主线程中执行命令
        Bukkit.getScheduler().runTask(this, () -> {
            try {
                // 替换命令中的玩家名
                String finalCommand = command.replace("{player}", username);

                // 执行控制台命令
                boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), finalCommand);

                if (success) {
                    getLogger().info("成功执行奖励命令: " + finalCommand + " (奖励ID: " + rewardId + ")");
                    // 标记奖励为已完成
                    markRewardCompleted(rewardId);
                } else {
                    getLogger().warning("执行奖励命令失败: " + finalCommand + " (奖励ID: " + rewardId + ")");
                }

            } catch (Exception e) {
                getLogger().severe("执行奖励命令时出错: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * 标记奖励为已完成
     */
    public void markRewardCompleted(String rewardId) {
        try {
            // 构建请求数据
            JSONObject requestData = new JSONObject();
            requestData.put("action", "mark_reward_completed");
            requestData.put("api_key", apiKey);
            requestData.put("reward_id", rewardId);

            // 发送HTTP请求
            String response = sendHttpRequest(websiteUrl, requestData.toJSONString());

            // 解析响应
            JSONParser parser = new JSONParser();
            JSONObject responseObj = (JSONObject) parser.parse(response);

            Boolean success = (Boolean) responseObj.get("success");
            if (success != null && success) {
                getLogger().info("成功标记奖励为已完成: " + rewardId);
            } else {
                String message = (String) responseObj.get("message");
                getLogger().warning("标记奖励完成失败: " + message);
            }

        } catch (Exception e) {
            getLogger().severe("标记奖励完成时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 启动同步任务
     */
    public void startSyncTask() {
        if (syncTask != null) {
            syncTask.cancel();
        }
        syncTask = new ScheduledSyncTask(this);
        syncTask.runTaskTimerAsynchronously(this, 0L, syncInterval * 60 * 20L); // 转换为tick
        getLogger().info("已启动卡密同步任务，间隔: " + syncInterval + " 分钟");
    }

    /**
     * 启动奖励检查任务
     */
    public void startRewardsCheckTask() {
        if (rewardsCheckTask != null) {
            rewardsCheckTask.cancel();
        }
        rewardsCheckTask = new ScheduledRewardCheckTask(this);
        rewardsCheckTask.runTaskTimerAsynchronously(this, 0L, rewardsCheckInterval * 20L); // 转换为tick
        getLogger().info("已启动奖励检查任务，间隔: " + rewardsCheckInterval + " 秒");
    }

    /**
     * 启动待处理奖励任务
     */
    public void startPendingRewardsTask() {
        if (pendingRewardsTask != null) {
            pendingRewardsTask.cancel();
        }
        pendingRewardsTask = new PendingRewardCleanupTask(this);
        pendingRewardsTask.runTaskTimerAsynchronously(this, 0L, pendingRewardsCheckInterval * 20L); // 转换为tick
        getLogger().info("已启动待处理奖励清理任务，间隔: " + pendingRewardsCheckInterval + " 秒");
    }

    /**
     * 启动清理过期分配任务
     */
    public void startCleanupTask() {
        if (cleanupTask != null) {
            cleanupTask.cancel();
        }

        // 如果设置为-1，表示永不失效，不启动清理任务
        if (voucherExpireSeconds <= -1) {
            getLogger().info("兑换券失效功能已禁用（expire-seconds = -1）");
            return;
        }

        cleanupTask = new BukkitRunnable() {
            @Override
            public void run() {
                cleanupExpiredAssignments();
            }
        };

        long intervalTicks = voucherCheckIntervalSeconds * 20L; // 转换为tick (秒 * 20tick)
        cleanupTask.runTaskTimerAsynchronously(this, 0L, intervalTicks);
        getLogger().info("已启动卡密分配清理任务，间隔: " + voucherCheckIntervalSeconds + " 秒，失效时间: " + voucherExpireSeconds + " 秒");
    }

    /**
     * 添加待处理奖励
     */
    public void addPendingReward(String playerName, String rewardName, List<String> commands) {
        if (commands == null || commands.isEmpty()) {
            return; // 没有命令的奖励不需要保存
        }

        String rewardId = System.currentTimeMillis() + "_" + rewardName.replaceAll("[^a-zA-Z0-9]", "_");
        String configPath = "rewards." + playerName + "." + rewardId;

        pendingRewardsConfig.set(configPath + ".name", rewardName);
        pendingRewardsConfig.set(configPath + ".timestamp", System.currentTimeMillis());
        pendingRewardsConfig.set(configPath + ".commands", commands);

        savePendingRewardsConfig();

        getLogger().info("为玩家 " + playerName + " 添加了待处理奖励: " + rewardName + " (ID: " + rewardId + ")");
    }

    /**
     * 检查玩家待处理奖励
     */
    public void checkPendingRewardsForPlayer(String playerName) {
        ConfigurationSection playerSection = pendingRewardsConfig.getConfigurationSection("rewards." + playerName);
        if (playerSection == null) {
            return;
        }

        Player player = Bukkit.getPlayer(playerName);
        if (player == null || !player.isOnline()) {
            return;
        }

        for (String rewardId : playerSection.getKeys(false)) {
            ConfigurationSection rewardSection = playerSection.getConfigurationSection(rewardId);
            if (rewardSection != null) {
                List<String> commands = rewardSection.getStringList("commands");
                String rewardName = rewardSection.getString("name", "未知奖励");

                if (commands != null && !commands.isEmpty()) {
                    // 在主线程中执行奖励命令
                    for (String command : commands) {
                        String processedCommand = command.replace("{player}", playerName);
                        new RewardExecutionTask(this, processedCommand, playerName).runTask(this);
                    }

                    // 移除已处理的奖励
                    playerSection.set(rewardId, null);
                    savePendingRewardsConfig();

                    getLogger().info("为玩家 " + playerName + " 处理了待处理奖励: " + rewardName + " (ID: " + rewardId + ")");
                }
            }
        }

        // 如果玩家没有更多待处理奖励，移除整个玩家节点
        if (playerSection.getKeys(false).isEmpty()) {
            pendingRewardsConfig.set("rewards." + playerName, null);
            savePendingRewardsConfig();
        }
    }

    /**
     * 检查过期待处理奖励
     */
    public void checkExpiredPendingRewards() {
        ConfigurationSection rewardsSection = pendingRewardsConfig.getConfigurationSection("rewards");
        if (rewardsSection == null) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        long expireTime = pendingRewardsExpireDays * 24 * 60 * 60 * 1000L; // 转换为毫秒

        for (String playerName : rewardsSection.getKeys(false)) {
            ConfigurationSection playerSection = rewardsSection.getConfigurationSection(playerName);
            if (playerSection != null) {
                for (String rewardId : playerSection.getKeys(false)) {
                    ConfigurationSection rewardSection = playerSection.getConfigurationSection(rewardId);
                    if (rewardSection != null) {
                        long timestamp = rewardSection.getLong("timestamp", 0);
                        if (timestamp > 0 && (currentTime - timestamp) > expireTime) {
                            // 移除过期奖励
                            playerSection.set(rewardId, null);
                            getLogger().info("移除了玩家 " + playerName + " 的过期待处理奖励: " + rewardId);
                        }
                    }
                }

                // 如果玩家没有更多待处理奖励，移除整个玩家节点
                if (playerSection.getKeys(false).isEmpty()) {
                    rewardsSection.set(playerName, null);
                }
            }
        }

        savePendingRewardsConfig();
    }

    /**
     * 玩家右键物品事件
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        if (item == null) {
            return;
        }

        if (versionAdapter.hasItemNBT(item, KEY_DATA_KEY)) {
            String keyData = versionAdapter.getItemNBT(item, KEY_DATA_KEY);
            if (keyData != null && !keyData.isEmpty()) {
                // 检查卡密状态
                ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
                if (keysSection == null || !keysSection.contains(keyData)) {
                    player.sendMessage("§c§l═══════════════════════════════════");
                    player.sendMessage("§c§l❌ 卡密验证失败！");
                    player.sendMessage("§c§l═══════════════════════════════════");
                    player.sendMessage("");
                    player.sendMessage("§e🎫 您的卡密: §c§l" + keyData);
                    player.sendMessage("");
                    player.sendMessage("§c❌ 此卡密不存在！");
                    player.sendMessage("§7📋 请联系管理员或获取新的卡密");
                    player.sendMessage("");
                    player.sendMessage("§c§l═══════════════════════════════════");
                    event.setCancelled(true);
                    return;
                }

                // 检查卡密是否已被使用
                boolean isUsed = keysSection.getBoolean(keyData + ".used", false);
                boolean isExpired = keysSection.getBoolean(keyData + ".expired", false);

                if (isUsed) {
                    player.sendMessage("§c§l═══════════════════════════════════");
                    player.sendMessage("§c§l❌ 卡密已被使用！");
                    player.sendMessage("§c§l═══════════════════════════════════");
                    player.sendMessage("");
                    player.sendMessage("§e🎫 您的卡密: §c§l" + keyData);
                    player.sendMessage("");
                    player.sendMessage("§c❌ 此卡密已在网站上使用过！");
                    player.sendMessage("§7📋 请联系管理员或获取新的卡密");
                    player.sendMessage("");
                    player.sendMessage("§c§l═══════════════════════════════════");
                    event.setCancelled(true);
                    return;
                }

                if (isExpired) {
                    player.sendMessage("§c§l═══════════════════════════════════");
                    player.sendMessage("§c§l❌ 卡密已失效！");
                    player.sendMessage("§c§l═══════════════════════════════════");
                    player.sendMessage("");
                    player.sendMessage("§e🎫 您的卡密: §c§l" + keyData);
                    player.sendMessage("");
                    player.sendMessage("§c❌ 此卡密已超时失效！");
                    player.sendMessage("§7📋 请联系管理员或获取新的卡密");
                    player.sendMessage("");
                    player.sendMessage("§c§l═══════════════════════════════════");
                    event.setCancelled(true);
                    return;
                }

                // 右键只是显示卡密信息，不标记为已使用
                // 只有在网站上点击抽奖按钮才会标记为已使用

                // 减少物品数量
                int amount = item.getAmount();
                if (amount > 1) {
                    item.setAmount(amount - 1);
                } else {
                    // 版本兼容的方式清空主手物品
                    setMainHandItem(player, null);
                }

                // 发送美化的卡密信息给玩家
                player.sendMessage("§6§l═══════════════════════════════════");
                player.sendMessage("§a§l卡密兑换券信息");
                player.sendMessage("§6§l═══════════════════════════════════");
                player.sendMessage("");
                player.sendMessage("§e您的卡密: §6§l" + keyData);
                player.sendMessage("");
                player.sendMessage("§7请前往抽奖网站并点击抽奖按钮使用");
                player.sendMessage("§c注意：只有在网站上抽奖才算真正使用！");
                player.sendMessage("");

                // 显示失效时间提示
                if (voucherExpireSeconds > 0) {
                    String expireTimeText = formatExpireTime(voucherExpireSeconds);
                    player.sendMessage("§e失效提醒：此卡密将在 §c" + expireTimeText + " §e后失效");
                    player.sendMessage("§7请尽快前往网站使用，避免失效！");
                } else {
                    player.sendMessage("§a此卡密永不失效，请放心使用");
                }
                player.sendMessage("");

                // 发送可点击的网站链接，包含卡密参数
                if (webServer != null && webServer.isRunning()) {
                    String websiteUrl = "http://localhost:" + webServer.getPort() + "/user?key=" + keyData;
                    player.sendMessage("§a抽奖网站 §7(点击下方链接前往):");
                    sendClickableLink(player, websiteUrl, "点击进入抽奖网站");
                    player.sendMessage("");
                    player.sendMessage("§d提示: 点击上方链接将自动填入卡密！");
                }

                player.sendMessage("§6§l═══════════════════════════════════");
                player.sendMessage("§c🎲 请前往网站抽奖，祝您好运！");
                player.sendMessage("§6§l═══════════════════════════════════");

                event.setCancelled(true);
            }
        }
    }

    /**
     * 玩家加入事件
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        String playerName = player.getName();

        if (this.pendingRewardsConfig.getConfigurationSection("rewards." + playerName) == null) {
            return;
        }

        getLogger().info("玩家 " + playerName + " 上线，将在10秒后处理待处理奖励");
        new PlayerRewardProcessTask(this, player, playerName).runTaskLater(this, 200L);
    }

    /**
     * 启动Web服务器
     */
    private void startWebServer() {
        if (!webServerEnabled) {
            getLogger().info("Web服务器已禁用，跳过启动");
            return;
        }

        try {
            webServer = new WebServer(this);
            if (webServer.start()) {
                getLogger().info("Web服务器启动成功！");
            } else {
                getLogger().warning("Web服务器启动失败！");
            }
        } catch (Exception e) {
            getLogger().severe("启动Web服务器时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 停止Web服务器
     */
    private void stopWebServer() {
        if (webServer != null) {
            try {
                webServer.stop();
                webServer = null;
            } catch (Exception e) {
                getLogger().severe("停止Web服务器时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取卡密配置
     */
    public FileConfiguration getKeysConfig() {
        return keysConfig;
    }

    /**
     * 命令处理
     */
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        String commandName = command.getName().toLowerCase();

        // 调试信息
        for (int i = 0; i < args.length; i++) {
            getLogger().info("命令参数[" + i + "]: '" + args[i] + "'");
        }

        // 特殊处理：如果参数数量为1且参数看起来像绑定码，可能是acebind命令
        if (args.length == 1 && args[0].matches("[A-Z0-9]{6}")) {
            getLogger().info("检测到可能的绑定码格式，尝试作为acebind命令处理");
            return handleBindCommand(sender, args);
        }

        switch (commandName) {
            case "acekeys":
                return handleGenKeysCommand(sender, args);
            case "acecount":
                return handleListKeysCommand(sender, args);
            case "acereload":
                return handleReloadConfigCommand(sender, args);
            case "aceget":
                return handleGetOneKeyCommand(sender, args);
            case "aceitem":
                return handleGetKeyItemCommand(sender, args);
            case "acesync":
                return handleSyncKeysCommand(sender, args);
            case "acerewards":
                return handleCheckRewardsCommand(sender, args);
            case "acehelp":
                return handleHelpCommand(sender, args);
            case "acepoints":
                return handlePointsCommand(sender, args);
            case "acebind":
                return handleBindCommand(sender, args);
            default:
                return false;
        }
    }

    /**
     * 处理生成卡密命令
     */
    private boolean handleGenKeysCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        if (args.length < 1) {
            sender.sendMessage("§c用法: /acekeys <数量> [显示信息]");
            sender.sendMessage("§e示例:");
            sender.sendMessage("§7  /acekeys 10 §f- 生成10个卡密，使用默认消息");
            sender.sendMessage("§7  /acekeys 5 恭喜获得VIP奖励！ §f- 生成5个卡密，自定义消息");
            sender.sendMessage("§7  /acekeys 20 感谢您的支持！ §f- 生成20个卡密，自定义消息");
            return true;
        }

        try {
            int count = Integer.parseInt(args[0]);
            if (count <= 0 || count > 1000) {
                sender.sendMessage("§c数量必须在1-1000之间！");
                return true;
            }

            String message = args.length > 1 ? String.join(" ", Arrays.copyOfRange(args, 1, args.length))
                    : this.defaultMessage;

            List<String> generatedKeys = new ArrayList<>();
            long currentTime = System.currentTimeMillis();
            String currentDate = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
                    .format(new java.util.Date(currentTime));

            for (int i = 0; i < count; i++) {
                String key = generateKey();
                generatedKeys.add(key);

                // 保存到配置文件（新格式，包含时间戳）
                String keyPath = "keys." + key;
                keysConfig.set(keyPath + ".message", message);
                keysConfig.set(keyPath + ".created_time", currentTime);
                keysConfig.set(keyPath + ".created_date", currentDate);
            }

            saveKeysConfig();

            sender.sendMessage("§a成功生成 " + count + " 个卡密！");
            sender.sendMessage("§7消息: " + message);

            // 如果启用了网站同步，立即同步
            if (websiteEnabled && syncOnGenerate) {
                syncKeysToWebsite();
            }

            return true;

        } catch (NumberFormatException e) {
            sender.sendMessage("§c请输入有效的数字！");
            return true;
        }
    }

    /**
     * 处理查看卡密命令
     */
    private boolean handleListKeysCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
        int count = keysSection != null ? keysSection.getKeys(false).size() : 0;

        sender.sendMessage("§a当前剩余卡密数量: §6" + count);
        return true;
    }

    /**
     * 处理重载配置命令
     */
    private boolean handleReloadConfigCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        sender.sendMessage("§e正在重载配置文件...");

        // 重载主配置
        reloadConfig();
        loadConfiguration();

        // 重载绑定数据配置
        if (bindingDataManager != null) {
            bindingDataManager.reloadConfig();
            sender.sendMessage("§a绑定数据配置已重载并更新解绑时间！");
        }

        // 重载Web服务器配置
        if (webServer != null) {
            webServer.reloadConfiguration();
        }

        sender.sendMessage("§a所有配置文件已重载完成！");
        return true;
    }

    /**
     * 处理获取一个卡密命令
     */
    private boolean handleGetOneKeyCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.use")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        // 确定目标玩家
        Player targetPlayer = null;
        String targetName = null;

        if (args.length > 0) {
            // 指定了玩家名
            targetName = args[0];
            targetPlayer = getServer().getPlayer(targetName);

            if (targetPlayer == null) {
                sender.sendMessage("§c玩家 " + targetName + " 不在线或不存在！");
                return true;
            }

            // 检查权限：给其他玩家需要管理员权限
            if (!sender.getName().equals(targetName) && !sender.hasPermission("acekeysystem.admin")) {
                sender.sendMessage("§c你没有权限给其他玩家分配卡密！");
                return true;
            }
        } else {
            // 没有指定玩家，给自己
            if (!(sender instanceof Player)) {
                sender.sendMessage("§c控制台必须指定玩家名！");
                sender.sendMessage("§e用法: /aceget <玩家名>");
                return true;
            }

            targetPlayer = (Player) sender;
            targetName = sender.getName();
        }

        // 获取一个未使用且未分配的卡密
        String keyData = getUnusedKey();
        if (keyData == null) {
            sender.sendMessage("§c当前没有可用的未分配卡密！");
            return true;
        }

        // 标记卡密为已分配（但未使用），并设置为永不失效
        ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
        if (keysSection != null) {
            keysSection.set(keyData + ".assigned", true);
            keysSection.set(keyData + ".assigned_time", System.currentTimeMillis());
            keysSection.set(keyData + ".assigned_to", targetName);
            // 设置为永不失效（特殊标记）
            keysSection.set(keyData + ".never_expire", true);
            saveKeysConfig();
        }

        // 获取卡密消息
        String message;
        if (keysSection != null && keysSection.isConfigurationSection(keyData)) {
            message = keysSection.getString(keyData + ".message", this.defaultMessage);
        } else {
            message = this.defaultMessage;
        }

        // 发送美化的卡密信息给目标玩家
        targetPlayer.sendMessage("§6§l═══════════════════════════════════");
        targetPlayer.sendMessage("§a§l卡密获取成功");
        targetPlayer.sendMessage("§6§l═══════════════════════════════════");
        targetPlayer.sendMessage("");
        targetPlayer.sendMessage("§e您的卡密: §6§l" + keyData);
        targetPlayer.sendMessage("");
        targetPlayer.sendMessage("§7📋 " + message);
        targetPlayer.sendMessage("");

        // /aceget 指令获取的卡密永不失效
        targetPlayer.sendMessage("§a此卡密永不失效，请放心使用");
        targetPlayer.sendMessage("§7可以随时前往网站使用");
        targetPlayer.sendMessage("");

        // 发送可复制的卡密
        sendCopyableKey(targetPlayer, keyData);

        targetPlayer.sendMessage("§6§l═══════════════════════════════════");

        // 如果是给其他玩家，通知执行者
        if (!sender.equals(targetPlayer)) {
            sender.sendMessage("§a已成功给玩家 " + targetName + " 分配永久卡密！");
        }

        return true;
    }

    /**
     * 处理获取卡密物品命令
     */
    private boolean handleGetKeyItemCommand(CommandSender sender, String[] args) {
        // 确定目标玩家
        Player targetPlayer = null;
        String targetName = null;

        if (args.length > 0) {
            // 指定了玩家名
            targetName = args[0];
            targetPlayer = getServer().getPlayer(targetName);

            if (targetPlayer == null) {
                sender.sendMessage("§c玩家 " + targetName + " 不在线或不存在！");
                return true;
            }

            // 检查权限：给其他玩家需要管理员权限
            if (!sender.getName().equals(targetName) && !sender.hasPermission("acekeysystem.admin")) {
                sender.sendMessage("§c你没有权限给其他玩家卡密兑换券！");
                return true;
            }
        } else {
            // 没有指定玩家，给自己
            if (!(sender instanceof Player)) {
                sender.sendMessage("§c控制台必须指定玩家名！");
                sender.sendMessage("§e用法: /aceitem <玩家名>");
                return true;
            }

            targetPlayer = (Player) sender;
            targetName = sender.getName();
        }

        // 检查基本权限
        if (!sender.hasPermission("acekeysystem.use") && !sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        // 获取一个未使用且未分配的卡密
        String keyData = getUnusedKey();
        if (keyData == null) {
            sender.sendMessage("§c当前没有可用的未分配卡密！");
            return true;
        }

        // 标记卡密为已分配（但未使用）
        ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
        if (keysSection != null) {
            keysSection.set(keyData + ".assigned", true);
            keysSection.set(keyData + ".assigned_time", System.currentTimeMillis());
            keysSection.set(keyData + ".assigned_to", targetName);
            saveKeysConfig();
        }

        // 创建卡密物品（卡密已标记为分配，不会重复分配）
        ItemStack keyItem = createKeyItem(keyData);
        targetPlayer.getInventory().addItem(keyItem);

        if (sender.equals(targetPlayer)) {
            sender.sendMessage("§a已获得卡密兑换券！右键使用获取卡密。");
        } else {
            sender.sendMessage("§a已给玩家 " + targetName + " 发放卡密兑换券！");
            targetPlayer.sendMessage("§a你收到了一个卡密兑换券！右键使用获取卡密。");
        }

        return true;
    }

    /**
     * 处理同步卡密命令
     */
    private boolean handleSyncKeysCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        if (!websiteEnabled) {
            sender.sendMessage("§c网站同步功能已禁用！");
            return true;
        }

        sender.sendMessage("§a正在同步卡密到网站...");
        syncKeysToWebsite();
        sender.sendMessage("§a同步完成！");

        return true;
    }

    /**
     * 处理检查奖励命令
     */
    private boolean handleCheckRewardsCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        if (!websiteEnabled) {
            sender.sendMessage("§c网站同步功能已禁用！");
            return true;
        }

        sender.sendMessage("§a正在检查网站奖励...");
        checkAndExecuteRewards();
        sender.sendMessage("§a检查完成！");

        return true;
    }

    /**
     * 处理帮助命令
     */
    private boolean handleHelpCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        sender.sendMessage("§6§l=== AceKeySystem 指令帮助 ===");
        sender.sendMessage("");
        sender.sendMessage("§e管理员指令:");
        sender.sendMessage("§a/acekeys <数量> [显示信息] §7- 批量生成指定数量的卡密");
        sender.sendMessage("§7  示例: /acekeys 10 恭喜获得VIP奖励！");
        sender.sendMessage("§a/acecount §7- 查看剩余卡密数量");
        sender.sendMessage("§a/acereload §7- 重新加载插件配置");
        sender.sendMessage("§a/acesync §7- 手动同步卡密到网站");
        sender.sendMessage("§a/acerewards §7- 检查并执行网站上的抽奖奖励");
        sender.sendMessage("§a/acehelp §7- 显示此帮助信息");
        sender.sendMessage("");
        sender.sendMessage("§e用户指令:");
        sender.sendMessage("§b/aceget [玩家名] §7- 获取一个永久卡密（永不失效）");
        sender.sendMessage("§7  示例: /aceget §f- 给自己");
        sender.sendMessage("§7  示例: /aceget Steve §f- 给指定玩家（需要管理员权限）");
        sender.sendMessage("§b/aceitem [玩家名] §7- 获取卡密兑换券（有失效时间）");
        sender.sendMessage("§7  示例: /aceitem §f- 给自己");
        sender.sendMessage("§7  示例: /aceitem Steve §f- 给指定玩家（需要管理员权限）");
        sender.sendMessage("");
        sender.sendMessage("§e权限说明:");
        sender.sendMessage("§7  acekeysystem.admin §f- 管理员权限（默认OP）");
        sender.sendMessage("§7  acekeysystem.use §f- 用户权限（需要手动给予）");
        sender.sendMessage("");
        sender.sendMessage("§e网站功能:");
        if (websiteEnabled) {
            sender.sendMessage("§a✓ 网站同步已启用");
            sender.sendMessage("§7  管理界面: §fhttp://服务器IP:" + webServer.getPort() + "/admin");
            sender.sendMessage("§7  用户界面: §fhttp://服务器IP:" + webServer.getPort() + "/user");
        } else {
            sender.sendMessage("§c✗ 网站同步已禁用");
        }
        sender.sendMessage("§6§l========================");

        return true;
    }

    /**
     * 生成卡密
     */
    public String generateKey() {
        StringBuilder key = new StringBuilder();

        // 生成字母部分
        for (int i = 0; i < letterCount; i++) {
            char letter = (char) ('A' + random.nextInt(26));
            key.append(letter);
        }

        // 生成数字部分
        for (int i = 0; i < numberCount; i++) {
            int digit = random.nextInt(10);
            key.append(digit);
        }

        return key.toString();
    }

    /**
     * 创建卡密物品
     */
    private ItemStack createKeyItem(String keyData) {
        ItemStack item = new ItemStack(keyItemMaterial);
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            // 设置显示名称
            meta.setDisplayName(keyItemName);

            // 设置Lore
            meta.setLore(keyItemLore);

            // 设置发光效果
            if (keyItemGlow) {
                meta.addEnchant(Enchantment.DURABILITY, 1, true);
                meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            }

            item.setItemMeta(meta);
        }

        // 设置NBT数据
        item = versionAdapter.setItemNBT(item, KEY_DATA_KEY, keyData);

        return item;
    }

    /**
     * 格式化失效时间显示
     */
    private String formatExpireTime(int seconds) {
        if (seconds <= 0) {
            return "永不失效";
        }

        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        int remainingSeconds = seconds % 60;

        StringBuilder timeText = new StringBuilder();

        if (hours > 0) {
            timeText.append(hours).append("小时");
        }
        if (minutes > 0) {
            if (timeText.length() > 0)
                timeText.append(" ");
            timeText.append(minutes).append("分钟");
        }
        if (remainingSeconds > 0 && hours == 0) { // 只有在小时为0时才显示秒
            if (timeText.length() > 0)
                timeText.append(" ");
            timeText.append(remainingSeconds).append("秒");
        }

        return timeText.toString();
    }

    /**
     * 通知玩家卡密已失效
     */
    private void notifyPlayerKeyExpired(String playerName, String keyData) {
        if (playerName == null || playerName.equals("未知")) {
            return;
        }

        // 在主线程中执行通知
        Bukkit.getScheduler().runTask(this, () -> {
            Player player = Bukkit.getPlayer(playerName);
            if (player != null && player.isOnline()) {
                // 发送失效通知消息
                player.sendMessage("§c§l═══════════════════════════════════");
                player.sendMessage("§c§l⏰ 卡密失效通知");
                player.sendMessage("§c§l═══════════════════════════════════");
                player.sendMessage("");
                player.sendMessage("§e🎫 您的卡密: §c§l" + keyData);
                player.sendMessage("");
                player.sendMessage("§c❌ 此卡密已超时失效！");
                player.sendMessage("§7📋 原因：超过 §e" + formatExpireTime(voucherExpireSeconds) + " §7未使用");
                player.sendMessage("§7💡 请联系管理员获取新的卡密");
                player.sendMessage("");
                player.sendMessage("§c§l═══════════════════════════════════");

                getLogger().info("已通知玩家 " + playerName + " 卡密失效: " + keyData);
            }
        });
    }

    /**
     * 验证卡密是否有效（已分配但未使用且未失效）
     */
    private boolean isKeyValid(String keyData) {
        try {
            // 检查卡密是否存在于配置文件中
            ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
            if (keysSection == null) {
                return false;
            }

            // 检查卡密是否存在且已分配但未被使用且未失效
            if (keysSection.contains(keyData)) {
                boolean isUsed = keysSection.getBoolean(keyData + ".used", false);
                boolean isAssigned = keysSection.getBoolean(keyData + ".assigned", false);
                boolean isExpired = keysSection.getBoolean(keyData + ".expired", false);

                // 卡密必须已分配、未使用且未失效才有效
                return isAssigned && !isUsed && !isExpired;
            }

            return false;
        } catch (Exception e) {
            getLogger().warning("验证卡密时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取一个未使用且未分配的卡密
     */
    private String getUnusedKey() {
        try {
            ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
            if (keysSection == null || keysSection.getKeys(false).isEmpty()) {
                return null;
            }

            // 遍历所有卡密，找到第一个未使用且未分配的
            for (String key : keysSection.getKeys(false)) {
                boolean isUsed = keysSection.getBoolean(key + ".used", false);
                boolean isAssigned = keysSection.getBoolean(key + ".assigned", false);

                // 只有既未使用也未分配的卡密才能被选择
                if (!isUsed && !isAssigned) {
                    return key;
                }
            }

            return null; // 没有找到可用的卡密
        } catch (Exception e) {
            getLogger().warning("获取未使用卡密时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 清理过期的卡密分配
     */
    private void cleanupExpiredAssignments() {
        try {
            // 如果设置为-1，表示永不失效
            if (voucherExpireSeconds <= -1) {
                return;
            }

            ConfigurationSection keysSection = keysConfig.getConfigurationSection("keys");
            if (keysSection == null) {
                return;
            }

            long currentTime = System.currentTimeMillis();
            long expireTime = voucherExpireSeconds * 1000L; // 转换为毫秒
            int expiredCount = 0;

            for (String key : keysSection.getKeys(false)) {
                boolean isAssigned = keysSection.getBoolean(key + ".assigned", false);
                boolean isUsed = keysSection.getBoolean(key + ".used", false);
                boolean isExpired = keysSection.getBoolean(key + ".expired", false);

                if (isAssigned && !isUsed && !isExpired) {
                    // 检查是否为永不失效的卡密
                    boolean neverExpire = keysSection.getBoolean(key + ".never_expire", false);

                    if (!neverExpire) {
                        long assignedTime = keysSection.getLong(key + ".assigned_time", 0);

                        // 如果分配时间超过设定时间，标记为失效
                        if (currentTime - assignedTime > expireTime) {
                            String assignedTo = keysSection.getString(key + ".assigned_to", "未知");

                            keysSection.set(key + ".expired", true);
                            keysSection.set(key + ".expired_time", currentTime);
                            expiredCount++;

                            // 通知玩家卡密已失效
                            notifyPlayerKeyExpired(assignedTo, key);

                            getLogger().info("卡密已失效: " + key + " (分配给: " + assignedTo + ")");
                        }
                    }
                }
            }

            if (expiredCount > 0) {
                saveKeysConfig();
                getLogger().info("已标记 " + expiredCount + " 个卡密为失效状态");
            }

        } catch (Exception e) {
            getLogger().warning("清理过期分配时出错: " + e.getMessage());
        }
    }

    /**
     * 发送可复制的卡密
     */
    private void sendCopyableKey(Player player, String keyData) {
        try {
            // 使用tellraw命令发送可复制的卡密
            String tellrawCommand = String.format(
                    "tellraw %s {\"text\":\"\",\"extra\":[{\"text\":\"§a点击复制卡密: \",\"color\":\"green\"},{\"text\":\"%s\",\"color\":\"gold\",\"bold\":true,\"clickEvent\":{\"action\":\"copy_to_clipboard\",\"value\":\"%s\"},\"hoverEvent\":{\"action\":\"show_text\",\"value\":\"§a点击复制卡密到剪贴板\\n§7卡密: §e%s\"}}]}",
                    player.getName(),
                    keyData,
                    keyData,
                    keyData);

            // 执行tellraw命令
            getServer().dispatchCommand(getServer().getConsoleSender(), tellrawCommand);
            getLogger().info("已为玩家 " + player.getName() + " 发送可复制卡密: " + keyData);

        } catch (Exception e) {
            // 如果发送可复制卡密失败，则发送普通消息
            player.sendMessage("§a卡密（手动复制）: §6§l" + keyData);
            getLogger().warning("发送可复制卡密失败，已降级为普通消息: " + e.getMessage());
        }
    }

    /**
     * 发送可点击的链接
     */
    private void sendClickableLink(Player player, String url, String displayText) {
        try {
            // 使用tellraw命令发送可点击链接
            String tellrawCommand = String.format(
                    "tellraw %s {\"text\":\"\",\"extra\":[{\"text\":\"%s\",\"color\":\"aqua\",\"underlined\":true,\"clickEvent\":{\"action\":\"open_url\",\"value\":\"%s\"},\"hoverEvent\":{\"action\":\"show_text\",\"value\":\"§a点击打开抽奖网站\\n§7%s\"}}]}",
                    player.getName(),
                    displayText.replace("§b§n", "").replace("§", ""), // 移除颜色代码
                    url,
                    url);

            // 执行tellraw命令
            getServer().dispatchCommand(getServer().getConsoleSender(), tellrawCommand);
            getLogger().info("已为玩家 " + player.getName() + " 发送可点击链接: " + url);

        } catch (Exception e) {
            // 如果发送可点击链接失败，则发送普通消息
            player.sendMessage(displayText);
            getLogger().warning("发送可点击链接失败，已降级为普通消息: " + e.getMessage());
        }
    }

    /**
     * 版本兼容的设置主手物品方法
     */
    private void setMainHandItem(Player player, ItemStack item) {
        try {
            // 如果item为null，设置为AIR
            if (item == null) {
                item = new ItemStack(Material.AIR);
            }

            // 尝试反射调用setItemInMainHand（1.9+）
            try {
                java.lang.reflect.Method setMainHandMethod = player.getInventory().getClass()
                        .getMethod("setItemInMainHand", ItemStack.class);
                setMainHandMethod.invoke(player.getInventory(), item);
                return;
            } catch (Exception e) {
                // 现代API不可用，尝试传统方法
            }

            // 降级到传统API - 设置手持物品槽位（1.8及更早版本）
            player.getInventory().setItem(player.getInventory().getHeldItemSlot(), item);

        } catch (Exception e) {
            getLogger().warning("设置主手物品失败: " + e.getMessage());
            // 最后的降级方案 - 直接设置第一个槽位
            try {
                player.getInventory().setItem(0, item);
            } catch (Exception ex) {
                getLogger().severe("无法设置玩家物品: " + ex.getMessage());
            }
        }
    }

    /**
     * 处理积分命令
     */
    private boolean handlePointsCommand(CommandSender sender, String[] args) {
        if (args.length == 0) {
            // 显示帮助信息
            sender.sendMessage("§6§l=== 积分系统指令帮助 ===");
            sender.sendMessage("§a/acepoints check [玩家名] §7- 查看积分");
            sender.sendMessage("§a/acepoints add <玩家名> <数量> §7- 增加积分（管理员）");
            sender.sendMessage("§a/acepoints remove <玩家名> <数量> §7- 扣除积分（管理员）");
            sender.sendMessage("§a/acepoints set <玩家名> <数量> §7- 设置积分（管理员）");
            sender.sendMessage("§a/acepoints top §7- 查看积分排行榜");
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "check":
                return handlePointsCheckCommand(sender, args);
            case "add":
                return handlePointsAddCommand(sender, args);
            case "remove":
                return handlePointsRemoveCommand(sender, args);
            case "set":
                return handlePointsSetCommand(sender, args);
            case "top":
                return handlePointsTopCommand(sender, args);
            default:
                sender.sendMessage("§c未知的子命令！使用 /acepoints 查看帮助。");
                return true;
        }
    }

    /**
     * 处理查看积分命令
     */
    private boolean handlePointsCheckCommand(CommandSender sender, String[] args) {
        String targetName;

        if (args.length > 1) {
            targetName = args[1];
        } else {
            if (!(sender instanceof Player)) {
                sender.sendMessage("§c控制台必须指定玩家名！");
                return true;
            }
            targetName = sender.getName();
        }

        int points = pointsManager.getPlayerPoints(targetName);
        if (targetName.equals(sender.getName())) {
            sender.sendMessage("§a你当前的积分: §6" + points);
        } else {
            sender.sendMessage("§a玩家 " + targetName + " 的积分: §6" + points);
        }
        return true;
    }

    /**
     * 处理增加积分命令
     */
    private boolean handlePointsAddCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage("§c用法: /acepoints add <玩家名> <数量>");
            return true;
        }

        String targetName = args[1];
        try {
            int amount = Integer.parseInt(args[2]);
            if (amount <= 0) {
                sender.sendMessage("§c数量必须大于0！");
                return true;
            }

            pointsManager.addPlayerPoints(targetName, amount);
            int newPoints = pointsManager.getPlayerPoints(targetName);

            sender.sendMessage("§a成功为玩家 " + targetName + " 增加 " + amount + " 积分！");
            sender.sendMessage("§7当前积分: " + newPoints);

            // 通知目标玩家
            Player targetPlayer = Bukkit.getPlayer(targetName);
            if (targetPlayer != null) {
                targetPlayer.sendMessage("§a你获得了 " + amount + " 积分！当前积分: " + newPoints);
            }

            return true;
        } catch (NumberFormatException e) {
            sender.sendMessage("§c请输入有效的数字！");
            return true;
        }
    }

    /**
     * 处理扣除积分命令
     */
    private boolean handlePointsRemoveCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage("§c用法: /acepoints remove <玩家名> <数量>");
            return true;
        }

        String targetName = args[1];
        try {
            int amount = Integer.parseInt(args[2]);
            if (amount <= 0) {
                sender.sendMessage("§c数量必须大于0！");
                return true;
            }

            boolean success = pointsManager.deductPlayerPoints(targetName, amount);
            if (success) {
                int newPoints = pointsManager.getPlayerPoints(targetName);
                sender.sendMessage("§a成功为玩家 " + targetName + " 扣除 " + amount + " 积分！");
                sender.sendMessage("§7当前积分: " + newPoints);

                // 通知目标玩家
                Player targetPlayer = Bukkit.getPlayer(targetName);
                if (targetPlayer != null) {
                    targetPlayer.sendMessage("§c你被扣除了 " + amount + " 积分！当前积分: " + newPoints);
                }
            } else {
                int currentPoints = pointsManager.getPlayerPoints(targetName);
                sender.sendMessage("§c积分不足！玩家 " + targetName + " 当前只有 " + currentPoints + " 积分。");
            }

            return true;
        } catch (NumberFormatException e) {
            sender.sendMessage("§c请输入有效的数字！");
            return true;
        }
    }

    /**
     * 处理设置积分命令
     */
    private boolean handlePointsSetCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("acekeysystem.admin")) {
            sender.sendMessage("§c你没有权限使用此命令！");
            return true;
        }

        if (args.length < 3) {
            sender.sendMessage("§c用法: /acepoints set <玩家名> <数量>");
            return true;
        }

        String targetName = args[1];
        try {
            int amount = Integer.parseInt(args[2]);
            if (amount < 0) {
                sender.sendMessage("§c数量不能小于0！");
                return true;
            }

            pointsManager.setPlayerPoints(targetName, amount);
            sender.sendMessage("§a成功设置玩家 " + targetName + " 的积分为 " + amount + "！");

            // 通知目标玩家
            Player targetPlayer = Bukkit.getPlayer(targetName);
            if (targetPlayer != null) {
                targetPlayer.sendMessage("§a你的积分被设置为 " + amount + "！");
            }

            return true;
        } catch (NumberFormatException e) {
            sender.sendMessage("§c请输入有效的数字！");
            return true;
        }
    }

    /**
     * 处理积分排行榜命令
     */
    private boolean handlePointsTopCommand(CommandSender sender, String[] args) {
        // TODO: 实现积分排行榜功能
        sender.sendMessage("§e积分排行榜功能正在开发中...");
        return true;
    }

    /**
     * 处理绑定命令
     */
    private boolean handleBindCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家使用！");
            return true;
        }

        Player player = (Player) sender;

        // 调试信息
        getLogger().info("绑定命令调试 - 参数数量: " + args.length);
        for (int i = 0; i < args.length; i++) {
            getLogger().info("参数[" + i + "]: '" + args[i] + "'");
        }

        String bindCode;
        if (args.length == 1) {
            // 特殊情况：直接传入绑定码（可能是命令解析问题的临时修复）
            bindCode = args[0].trim().toUpperCase();
            getLogger().info("使用特殊处理：绑定码来自参数[0]: " + bindCode);
        } else if (args.length >= 2) {
            // 正常情况：/acebind <绑定码>
            bindCode = args[1].trim().toUpperCase();
            getLogger().info("使用正常处理：绑定码来自参数[1]: " + bindCode);
        } else {
            player.sendMessage("§c用法: /acebind <绑定码>");
            player.sendMessage("§7请在积分商店网页上获取绑定码");
            player.sendMessage("§e调试信息: 参数数量不足 (" + args.length + ")");
            return true;
        }
        String username = player.getName();

        // 更多调试信息
        getLogger().info("处理绑定 - 玩家: " + username + ", 绑定码: '" + bindCode + "'");
        player.sendMessage("§e 正在验证绑定码 '" + bindCode + "'...");

        // 发送API请求验证绑定码
        getServer().getScheduler().runTaskAsynchronously(this, () -> {
            try {
                // 构建请求数据
                org.json.simple.JSONObject requestData = new org.json.simple.JSONObject();
                requestData.put("action", "verify_bind_code");
                requestData.put("api_key", getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE"));
                requestData.put("bind_code", bindCode);
                requestData.put("username", username);

                // 发送HTTP请求到本地API
                String response = sendHttpRequest("http://localhost:" +
                        getConfig().getInt("website.port", 8080) + "/api", requestData.toJSONString());

                // 解析响应
                org.json.simple.parser.JSONParser parser = new org.json.simple.parser.JSONParser();
                org.json.simple.JSONObject responseObj = (org.json.simple.JSONObject) parser.parse(response);

                boolean success = (Boolean) responseObj.get("success");
                String message = (String) responseObj.get("message");

                // 在主线程中发送消息给玩家
                getServer().getScheduler().runTask(this, () -> {
                    if (success) {
                        player.sendMessage("§a§l═══════════════════════════════════");
                        player.sendMessage("§a§l✅ 绑定成功！");
                        player.sendMessage("§a§l═══════════════════════════════════");
                        player.sendMessage("");
                        player.sendMessage("§e🎉 恭喜！您已成功绑定积分商店账户");
                        player.sendMessage("§7📱 现在您可以在网页上使用积分购买物品了");
                        player.sendMessage("");
                        player.sendMessage("§b💰 您的积分: §a" + pointsManager.getPlayerPoints(player.getUniqueId()));
                        player.sendMessage("");
                        player.sendMessage("§a§l═══════════════════════════════════");
                    } else {
                        player.sendMessage("§c§l═══════════════════════════════════");
                        player.sendMessage("§c§l❌ 绑定失败！");
                        player.sendMessage("§c§l═══════════════════════════════════");
                        player.sendMessage("");
                        player.sendMessage("§e🎫 绑定码: §c" + bindCode);
                        player.sendMessage("§c❌ " + message);
                        player.sendMessage("");
                        player.sendMessage("§7💡 请确保：");
                        player.sendMessage("§7  • 绑定码输入正确");
                        player.sendMessage("§7  • 绑定码未过期（5分钟内有效）");
                        player.sendMessage("§7  • 绑定码未被其他人使用");
                        player.sendMessage("");
                        player.sendMessage("§c§l═══════════════════════════════════");
                    }
                });

            } catch (Exception e) {
                getServer().getScheduler().runTask(this, () -> {
                    player.sendMessage("§c绑定失败：网络错误或服务器不可用");
                    getLogger().severe("绑定验证失败: " + e.getMessage());
                });
            }
        });

        return true;
    }

    /**
     * Tab 补全处理
     */
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        String commandName = command.getName().toLowerCase();

        if ("acepoints".equals(commandName)) {
            return handlePointsTabComplete(sender, args);
        }

        return null;
    }

    /**
     * 处理积分命令的 Tab 补全
     */
    private List<String> handlePointsTabComplete(CommandSender sender, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数：子命令
            List<String> subCommands = Arrays.asList("check", "add", "remove", "set", "top");
            String input = args[0].toLowerCase();

            for (String subCommand : subCommands) {
                if (subCommand.startsWith(input)) {
                    completions.add(subCommand);
                }
            }
        } else if (args.length == 2) {
            // 第二个参数：玩家名
            String subCommand = args[0].toLowerCase();
            String input = args[1].toLowerCase();

            // 对于所有需要玩家名的子命令，提供在线玩家列表
            if (Arrays.asList("check", "add", "remove", "set").contains(subCommand)) {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    String playerName = player.getName();
                    if (playerName.toLowerCase().startsWith(input)) {
                        completions.add(playerName);
                    }
                }
            }
        } else if (args.length == 3) {
            // 第三个参数：数量（仅对 add, remove, set 命令）
            String subCommand = args[0].toLowerCase();

            if (Arrays.asList("add", "remove", "set").contains(subCommand)) {
                // 提供一些常用的数量建议
                List<String> amounts = Arrays.asList("1", "5", "10", "50", "100", "500", "1000");
                String input = args[2];

                for (String amount : amounts) {
                    if (amount.startsWith(input)) {
                        completions.add(amount);
                    }
                }
            }
        }

        return completions;
    }
}
