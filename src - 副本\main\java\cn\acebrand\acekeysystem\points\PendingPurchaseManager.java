package cn.acebrand.acekeysystem.points;

import cn.acebrand.acekeysystem.AceKeySystem;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 待处理购买管理器
 * 处理玩家离线时的积分商店购买，玩家上线后自动发放
 */
public class PendingPurchaseManager implements Listener {

    private final AceKeySystem plugin;
    private final Map<String, List<PendingPurchase>> pendingPurchases; // 玩家名 -> 待处理购买列表
    private final File pendingFile;
    private FileConfiguration pendingConfig;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 配置参数
    private int expireDays = 15; // 过期天数
    private int checkInterval = 300; // 检查间隔（秒）

    public PendingPurchaseManager(AceKeySystem plugin) {
        this.plugin = plugin;
        this.pendingPurchases = new ConcurrentHashMap<>();
        this.pendingFile = new File(plugin.getDataFolder(), "pending_purchases.yml");

        loadConfig();
        loadPendingPurchases();
        startCleanupTask();

        // 注册事件监听器
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * 加载配置
     */
    private void loadConfig() {
        FileConfiguration config = plugin.getConfig();
        this.expireDays = config.getInt("pending-purchases.expire-days", 15);
        this.checkInterval = config.getInt("pending-purchases.check-interval", 300);

        plugin.getLogger().info("待处理购买配置 - 过期天数: " + expireDays + ", 检查间隔: " + checkInterval + "秒");
    }

    /**
     * 加载待处理购买数据
     */
    private void loadPendingPurchases() {
        if (!pendingFile.exists()) {
            try {
                pendingFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("创建待处理购买文件失败: " + e.getMessage());
                return;
            }
        }

        pendingConfig = YamlConfiguration.loadConfiguration(pendingFile);
        pendingPurchases.clear();

        if (pendingConfig.getConfigurationSection("pending") != null) {
            for (String playerName : pendingConfig.getConfigurationSection("pending").getKeys(false)) {
                List<PendingPurchase> purchases = new ArrayList<>();

                if (pendingConfig.getConfigurationSection("pending." + playerName) != null) {
                    for (String purchaseId : pendingConfig.getConfigurationSection("pending." + playerName)
                            .getKeys(false)) {
                        String path = "pending." + playerName + "." + purchaseId;

                        String itemId = pendingConfig.getString(path + ".item_id");
                        String itemName = pendingConfig.getString(path + ".item_name");
                        int quantity = pendingConfig.getInt(path + ".quantity", 1);
                        String purchaseTimeStr = pendingConfig.getString(path + ".purchase_time");
                        List<String> commands = pendingConfig.getStringList(path + ".commands");

                        if (itemId != null && itemName != null && purchaseTimeStr != null) {
                            try {
                                Date purchaseTime = dateFormat.parse(purchaseTimeStr);
                                PendingPurchase purchase = new PendingPurchase(
                                        purchaseId, itemId, itemName, quantity, purchaseTime, commands);
                                purchases.add(purchase);
                            } catch (Exception e) {
                                plugin.getLogger().warning("解析待处理购买时间失败: " + purchaseTimeStr);
                            }
                        }
                    }
                }

                if (!purchases.isEmpty()) {
                    pendingPurchases.put(playerName, purchases);
                }
            }
        }

        plugin.getLogger().info("已加载 " + pendingPurchases.size() + " 个玩家的待处理购买数据");
    }

    /**
     * 保存待处理购买数据
     */
    public void savePendingPurchases() {
        pendingConfig.set("pending", null); // 清空现有数据

        for (Map.Entry<String, List<PendingPurchase>> entry : pendingPurchases.entrySet()) {
            String playerName = entry.getKey();
            List<PendingPurchase> purchases = entry.getValue();

            for (PendingPurchase purchase : purchases) {
                String path = "pending." + playerName + "." + purchase.getId();
                pendingConfig.set(path + ".item_id", purchase.getItemId());
                pendingConfig.set(path + ".item_name", purchase.getItemName());
                pendingConfig.set(path + ".quantity", purchase.getQuantity());
                pendingConfig.set(path + ".purchase_time", dateFormat.format(purchase.getPurchaseTime()));
                pendingConfig.set(path + ".commands", purchase.getCommands());
            }
        }

        try {
            pendingConfig.save(pendingFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存待处理购买数据失败: " + e.getMessage());
        }
    }

    /**
     * 添加待处理购买
     */
    public void addPendingPurchase(String playerName, String itemId, String itemName, int quantity,
            List<String> commands) {
        String purchaseId = "purchase_" + System.currentTimeMillis() + "_"
                + UUID.randomUUID().toString().substring(0, 8);
        PendingPurchase purchase = new PendingPurchase(purchaseId, itemId, itemName, quantity, new Date(), commands);

        pendingPurchases.computeIfAbsent(playerName, k -> new ArrayList<>()).add(purchase);
        savePendingPurchases();

        plugin.getLogger().info("为玩家 " + playerName + " 添加待处理购买: " + itemName + " x" + quantity);
    }

    /**
     * 玩家上线事件处理
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        String playerName = player.getName();

        // 延迟1秒执行，确保玩家完全加载
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            processPendingPurchases(player);
        }, 20L);
    }

    /**
     * 处理玩家的待处理购买
     */
    private void processPendingPurchases(Player player) {
        String playerName = player.getName();
        List<PendingPurchase> purchases = pendingPurchases.get(playerName);

        if (purchases == null || purchases.isEmpty()) {
            return;
        }

        plugin.getLogger().info("为玩家 " + playerName + " 处理 " + purchases.size() + " 个待处理购买");

        int processedCount = 0;
        int totalItems = 0;
        List<String> processedItems = new ArrayList<>();
        Iterator<PendingPurchase> iterator = purchases.iterator();

        while (iterator.hasNext()) {
            PendingPurchase purchase = iterator.next();

            // 按购买数量执行命令
            for (int i = 0; i < purchase.getQuantity(); i++) {
                for (String command : purchase.getCommands()) {
                    String processedCommand = command.replace("{player}", playerName);
                    Bukkit.getScheduler().runTask(plugin, () -> {
                        boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
                        if (!success) {
                            plugin.getLogger().warning("执行待处理购买命令失败: " + processedCommand);
                        }
                    });
                }
            }

            // 收集处理的物品信息
            if (purchase.getQuantity() == 1) {
                processedItems.add("§e" + purchase.getItemName());
            } else {
                processedItems.add("§e" + purchase.getItemName() + " §fx" + purchase.getQuantity());
            }
            totalItems += purchase.getQuantity();

            iterator.remove();
            processedCount++;
        }

        // 发送汇总通知消息
        if (processedCount > 0) {
            if (processedCount == 1) {
                // 单个购买记录
                String message = String.format("§a[积分商店] §f您离线期间购买的 %s §f已发放！", processedItems.get(0));
                player.sendMessage(message);
            } else {
                // 多个购买记录，发送汇总消息
                player.sendMessage("§a[积分商店] §f您离线期间的购买已发放：");
                for (String item : processedItems) {
                    player.sendMessage("§f  - " + item);
                }
                player.sendMessage("§f共计 §b" + processedCount + " §f种物品，§b" + totalItems + " §f个");
            }
        }

        // 如果列表为空，移除玩家记录
        if (purchases.isEmpty()) {
            pendingPurchases.remove(playerName);
        }

        // 保存更改
        if (processedCount > 0) {
            savePendingPurchases();
            plugin.getLogger().info("为玩家 " + playerName + " 成功处理了 " + processedCount + " 个待处理购买");
        }
    }

    /**
     * 启动清理任务
     */
    private void startCleanupTask() {
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, this::cleanupExpiredPurchases,
                checkInterval * 20L, checkInterval * 20L);
    }

    /**
     * 清理过期的待处理购买
     */
    private void cleanupExpiredPurchases() {
        long currentTime = System.currentTimeMillis();
        long expireTime = expireDays * 24 * 60 * 60 * 1000L; // 转换为毫秒

        int removedCount = 0;
        Iterator<Map.Entry<String, List<PendingPurchase>>> playerIterator = pendingPurchases.entrySet().iterator();

        while (playerIterator.hasNext()) {
            Map.Entry<String, List<PendingPurchase>> entry = playerIterator.next();
            List<PendingPurchase> purchases = entry.getValue();

            Iterator<PendingPurchase> purchaseIterator = purchases.iterator();
            while (purchaseIterator.hasNext()) {
                PendingPurchase purchase = purchaseIterator.next();

                if (currentTime - purchase.getPurchaseTime().getTime() > expireTime) {
                    plugin.getLogger().info("清理过期的待处理购买: " + entry.getKey() + " - " + purchase.getItemName());
                    purchaseIterator.remove();
                    removedCount++;
                }
            }

            // 如果玩家没有待处理购买了，移除玩家记录
            if (purchases.isEmpty()) {
                playerIterator.remove();
            }
        }

        if (removedCount > 0) {
            savePendingPurchases();
            plugin.getLogger().info("清理了 " + removedCount + " 个过期的待处理购买");
        }
    }

    /**
     * 获取玩家的待处理购买数量
     */
    public int getPendingPurchaseCount(String playerName) {
        List<PendingPurchase> purchases = pendingPurchases.get(playerName);
        return purchases != null ? purchases.size() : 0;
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        savePendingPurchases();
    }

    /**
     * 待处理购买数据类
     */
    public static class PendingPurchase {
        private final String id;
        private final String itemId;
        private final String itemName;
        private final int quantity;
        private final Date purchaseTime;
        private final List<String> commands;

        public PendingPurchase(String id, String itemId, String itemName, int quantity, Date purchaseTime,
                List<String> commands) {
            this.id = id;
            this.itemId = itemId;
            this.itemName = itemName;
            this.quantity = quantity;
            this.purchaseTime = purchaseTime;
            this.commands = new ArrayList<>(commands);
        }

        // Getters
        public String getId() {
            return id;
        }

        public String getItemId() {
            return itemId;
        }

        public String getItemName() {
            return itemName;
        }

        public int getQuantity() {
            return quantity;
        }

        public Date getPurchaseTime() {
            return purchaseTime;
        }

        public List<String> getCommands() {
            return commands;
        }
    }
}
