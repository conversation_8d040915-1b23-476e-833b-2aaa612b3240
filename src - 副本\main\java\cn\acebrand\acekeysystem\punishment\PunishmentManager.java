package cn.acebrand.acekeysystem.punishment;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.ban.LiteBansDatabase;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.logging.Level;

/**
 * 处罚记录管理器
 */
public class PunishmentManager {

    private final AceKeySystem plugin;
    private final LiteBansDatabase database;

    // 异步查询相关
    private BukkitTask updateTask;
    private boolean autoUpdateEnabled = true;
    private int updateIntervalSeconds = 1; // 默认1秒更新一次

    // 数据变化检测（用于前端实时更新）
    private final Map<String, String> dataHashes = new ConcurrentHashMap<>();
    private final Map<String, Long> lastUpdateTimes = new ConcurrentHashMap<>();

    public PunishmentManager(AceKeySystem plugin) {
        this.plugin = plugin;
        this.database = new LiteBansDatabase(plugin);
    }

    /**
     * 初始化处罚管理器
     */
    public boolean initialize() {
        if (!database.initialize()) {
            return false;
        }

        // 检查数据表是否存在
        if (!database.checkTablesExist()) {
            plugin.getLogger().warning("LiteBans数据表不存在，处罚记录功能将不可用");
            return false;
        }

        // 测试数据获取功能
        database.testDataRetrieval();

        // 启动定时更新任务
        startAutoUpdateTask();

        plugin.getLogger().info("处罚记录管理器初始化成功");
        return true;
    }

    /**
     * 获取指定类型的处罚记录列表（分页）- 异步查询
     */
    public List<PunishmentRecord> getPunishmentRecords(PunishmentRecord.PunishmentType type, int page, int pageSize) {
        return getPunishmentRecordsFromDatabase(type, page, pageSize);
    }

    /**
     * 异步获取指定类型的处罚记录列表（分页）
     */
    public void getPunishmentRecordsAsync(PunishmentRecord.PunishmentType type, int page, int pageSize,
            java.util.function.Consumer<List<PunishmentRecord>> callback) {
        Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
            try {
                List<PunishmentRecord> records = getPunishmentRecordsFromDatabase(type, page, pageSize);
                // 在主线程中执行回调
                Bukkit.getScheduler().runTask(plugin, () -> callback.accept(records));
            } catch (Exception e) {
                plugin.getLogger().severe("异步获取处罚记录时发生错误: " + e.getMessage());
                e.printStackTrace();
                // 即使出错也要执行回调，返回空列表
                Bukkit.getScheduler().runTask(plugin, () -> callback.accept(new ArrayList<>()));
            }
        });
    }

    /**
     * 从数据库获取处罚记录（原始实现）
     */
    private List<PunishmentRecord> getPunishmentRecordsFromDatabase(PunishmentRecord.PunishmentType type, int page,
            int pageSize) {
        List<PunishmentRecord> records = new ArrayList<>();

        plugin.getLogger().info("从数据库获取处罚记录 - 类型: " + type.getDisplayName() + ", 页码: " + page + ", 每页: " + pageSize);

        if (!database.isEnabled()) {
            plugin.getLogger().warning("数据库未启用，返回空记录列表");
            return records;
        }

        String tableName = getTableName(type);
        plugin.getLogger().info("使用数据表: " + tableName);

        // 根据不同表类型构建查询SQL
        String sql = buildSelectSQL(tableName, type);

        plugin.getLogger().info("执行SQL查询: " + sql);
        plugin.getLogger().info("查询参数: pageSize=" + pageSize + ", offset=" + ((page - 1) * pageSize));

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {

            stmt.setInt(1, pageSize);
            stmt.setInt(2, (page - 1) * pageSize);

            try (ResultSet rs = stmt.executeQuery()) {
                int count = 0;
                while (rs.next()) {
                    count++;
                    PunishmentRecord record = createPunishmentRecordFromResultSet(rs, type);
                    records.add(record);
                    plugin.getLogger().info("处理记录 " + count + ": ID=" + record.getId() +
                            ", UUID=" + record.getUuid() +
                            ", 玩家=" + record.getPlayerName() +
                            ", 原因=" + record.getReason());
                }
                plugin.getLogger().info("成功获取 " + count + " 条" + type.getDisplayName() + "记录");
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "获取" + type.getDisplayName() + "记录失败: " + e.getMessage(), e);
        }

        return records;
    }

    /**
     * 获取所有类型的处罚记录（历史记录）
     */
    public List<PunishmentRecord> getAllPunishmentRecords(int page, int pageSize) {
        List<PunishmentRecord> allRecords = new ArrayList<>();

        // 获取各种类型的处罚记录
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            List<PunishmentRecord> typeRecords = getPunishmentRecords(type, 1, pageSize * 2); // 获取更多记录用于排序
            allRecords.addAll(typeRecords);
        }

        // 按时间排序
        allRecords.sort((a, b) -> {
            if (a.getTime() == null && b.getTime() == null)
                return 0;
            if (a.getTime() == null)
                return 1;
            if (b.getTime() == null)
                return -1;
            return b.getTime().compareTo(a.getTime());
        });

        // 分页
        int start = (page - 1) * pageSize;
        int end = Math.min(start + pageSize, allRecords.size());

        if (start >= allRecords.size()) {
            return new ArrayList<>();
        }

        return allRecords.subList(start, end);
    }

    /**
     * 获取指定类型的处罚记录总数
     */
    public int getPunishmentCount(PunishmentRecord.PunishmentType type) {
        if (!database.isEnabled()) {
            return 0;
        }

        String tableName = getTableName(type);
        String sql = "SELECT COUNT(*) FROM " + tableName;

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql);
                ResultSet rs = stmt.executeQuery()) {

            if (rs.next()) {
                return rs.getInt(1);
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "获取" + type.getDisplayName() + "记录总数失败: " + e.getMessage(), e);
        }

        return 0;
    }

    /**
     * 根据玩家名称搜索所有类型的处罚记录
     */
    public List<PunishmentRecord> searchPunishmentRecords(String playerName, int page, int pageSize) {
        List<PunishmentRecord> allRecords = new ArrayList<>();

        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty()) {
            return allRecords;
        }

        // 搜索所有类型的处罚记录
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            List<PunishmentRecord> typeRecords = searchPunishmentRecords(type, playerName, 1, pageSize * 2); // 获取更多记录用于排序
            allRecords.addAll(typeRecords);
        }

        // 按时间排序
        allRecords.sort((a, b) -> {
            if (a.getTime() == null && b.getTime() == null)
                return 0;
            if (a.getTime() == null)
                return 1;
            if (b.getTime() == null)
                return -1;
            return b.getTime().compareTo(a.getTime());
        });

        // 分页
        int start = (page - 1) * pageSize;
        int end = Math.min(start + pageSize, allRecords.size());

        if (start >= allRecords.size()) {
            return new ArrayList<>();
        }

        return allRecords.subList(start, end);
    }

    /**
     * 根据玩家和执行者过滤搜索处罚记录（按照 next-litebans 逻辑）
     */
    public List<PunishmentRecord> searchPunishmentRecords(PunishmentRecord.PunishmentType type, String playerFilter,
            String staffFilter, int page, int pageSize) {
        List<PunishmentRecord> records = new ArrayList<>();

        if (!database.isEnabled()) {
            return records;
        }

        String tableName = getTableName(type);
        if (tableName == null) {
            return records;
        }

        try (Connection conn = database.getConnection()) {
            StringBuilder sql = new StringBuilder();
            sql.append("SELECT id, uuid, banned_by_name, banned_by_uuid, reason, time, until, active, silent, ipban ");
            sql.append("FROM ").append(tableName).append(" WHERE 1=1 ");

            List<Object> params = new ArrayList<>();

            // 玩家过滤（按照 next-litebans 的 uuid 过滤）
            if (playerFilter != null && !playerFilter.trim().isEmpty()) {
                // 如果是玩家名，先转换为UUID
                String playerUuid = getPlayerUuid(playerFilter);
                if (playerUuid != null) {
                    sql.append("AND uuid = ? ");
                    params.add(playerUuid);
                } else {
                    // 如果找不到UUID，尝试按名称搜索
                    sql.append(
                            "AND uuid IN (SELECT uuid FROM litebans_history WHERE name = ? ORDER BY date DESC LIMIT 1) ");
                    params.add(playerFilter);
                }
            }

            // 执行者过滤（按照 next-litebans 的 banned_by_uuid 过滤）
            if (staffFilter != null && !staffFilter.trim().isEmpty()) {
                if ("Console".equalsIgnoreCase(staffFilter) || "控制台".equals(staffFilter)) {
                    sql.append("AND (banned_by_uuid IS NULL OR banned_by_uuid = '' OR banned_by_name = 'Console') ");
                } else {
                    // 如果是执行者名，先转换为UUID
                    String staffUuid = getPlayerUuid(staffFilter);
                    if (staffUuid != null) {
                        sql.append("AND banned_by_uuid = ? ");
                        params.add(staffUuid);
                    } else {
                        // 如果找不到UUID，尝试按名称搜索
                        sql.append("AND banned_by_name = ? ");
                        params.add(staffFilter);
                    }
                }
            }

            sql.append("ORDER BY time DESC LIMIT ? OFFSET ?");
            params.add(pageSize);
            params.add((page - 1) * pageSize);

            try (PreparedStatement stmt = conn.prepareStatement(sql.toString())) {
                for (int i = 0; i < params.size(); i++) {
                    stmt.setObject(i + 1, params.get(i));
                }

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        PunishmentRecord record = createRecordFromResultSet(rs, type);
                        if (record != null) {
                            records.add(record);
                        }
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("查询处罚记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        return records;
    }

    /**
     * 根据玩家名称搜索指定类型的处罚记录
     */
    public List<PunishmentRecord> searchPunishmentRecords(PunishmentRecord.PunishmentType type, String playerName,
            int page, int pageSize) {
        List<PunishmentRecord> records = new ArrayList<>();

        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty()) {
            return records;
        }

        String tableName = getTableName(type);
        String sql = buildSearchSQL(tableName, type);

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {

            String searchPattern = "%" + playerName.trim() + "%";
            stmt.setString(1, searchPattern);
            stmt.setString(2, searchPattern);
            stmt.setInt(3, pageSize);
            stmt.setInt(4, (page - 1) * pageSize);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    PunishmentRecord record = createPunishmentRecordFromResultSet(rs, type);
                    // 对于搜索查询，直接使用查询结果中的 player_name
                    try {
                        String searchPlayerName = rs.getString("player_name");
                        if (searchPlayerName != null && !searchPlayerName.trim().isEmpty()) {
                            record.setPlayerName(searchPlayerName);
                        }
                    } catch (SQLException e) {
                        // 如果没有 player_name 字段，保持原有逻辑
                    }
                    records.add(record);
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "搜索" + type.getDisplayName() + "记录失败: " + e.getMessage(), e);
        }

        return records;
    }

    /**
     * 获取处罚统计信息 - 异步查询
     */
    public Map<String, Object> getPunishmentStatistics() {
        return getPunishmentStatisticsFromDatabase();
    }

    /**
     * 从数据库获取处罚统计信息（原始实现）
     */
    private Map<String, Object> getPunishmentStatisticsFromDatabase() {
        Map<String, Object> stats = new HashMap<>();

        if (!database.isEnabled()) {
            return stats;
        }

        plugin.getLogger().info("从数据库获取处罚统计信息");

        try (Connection connection = database.getConnection()) {
            // 获取各种类型的统计
            for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                String tableName = getTableName(type);

                // 总数
                try (PreparedStatement stmt = connection.prepareStatement("SELECT COUNT(*) FROM " + tableName);
                        ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        stats.put("total_" + type.name().toLowerCase(), rs.getInt(1));
                    }
                }

                // 活跃数
                try (PreparedStatement stmt = connection
                        .prepareStatement("SELECT COUNT(*) FROM " + tableName + " WHERE active = 1");
                        ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        stats.put("active_" + type.name().toLowerCase(), rs.getInt(1));
                    }
                }

                // 今日数量
                long todayStart = LocalDateTime.now().toLocalDate().atStartOfDay().toEpochSecond(ZoneOffset.UTC) * 1000;
                try (PreparedStatement stmt = connection
                        .prepareStatement("SELECT COUNT(*) FROM " + tableName + " WHERE time >= ?")) {
                    stmt.setLong(1, todayStart);
                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            stats.put("today_" + type.name().toLowerCase(), rs.getInt(1));
                        }
                    }
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "获取处罚统计信息失败: " + e.getMessage(), e);
        }

        return stats;
    }

    /**
     * 根据处罚类型获取表名
     */
    private String getTableName(PunishmentRecord.PunishmentType type) {
        switch (type) {
            case BAN:
                return "litebans_bans";
            case MUTE:
                return "litebans_mutes";
            case WARN:
                return "litebans_warnings";
            case KICK:
                return "litebans_kicks";
            default:
                throw new IllegalArgumentException("未知的处罚类型: " + type);
        }
    }

    /**
     * 根据表类型构建SELECT SQL语句
     */
    private String buildSelectSQL(String tableName, PunishmentRecord.PunishmentType type) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT id, uuid, ip, reason, banned_by_uuid, banned_by_name, ");

        // kicks 表没有 removed_by_* 字段
        if (type != PunishmentRecord.PunishmentType.KICK) {
            sql.append("       removed_by_uuid, removed_by_name, removed_by_reason, removed_by_date, ");
        }

        sql.append("       time, until, template, server_scope, server_origin, ");
        sql.append("       silent, ipban, ipban_wildcard, active");

        // 为 warnings 表添加额外的 warned 字段
        if (type == PunishmentRecord.PunishmentType.WARN) {
            sql.append(", warned");
        }

        sql.append(" FROM ").append(tableName);
        sql.append(" ORDER BY time DESC");
        sql.append(" LIMIT ? OFFSET ?");

        return sql.toString();
    }

    /**
     * 根据表类型构建搜索 SQL语句
     */
    private String buildSearchSQL(String tableName, PunishmentRecord.PunishmentType type) {
        StringBuilder sql = new StringBuilder();
        sql.append("SELECT p.id, p.uuid, p.ip, p.reason, p.banned_by_uuid, p.banned_by_name, ");

        // kicks 表没有 removed_by_* 字段
        if (type != PunishmentRecord.PunishmentType.KICK) {
            sql.append("       p.removed_by_uuid, p.removed_by_name, p.removed_by_reason, p.removed_by_date, ");
        }

        sql.append("       p.time, p.until, p.template, p.server_scope, p.server_origin, ");
        sql.append("       p.silent, p.ipban, p.ipban_wildcard, p.active");

        // 为 warnings 表添加额外的 warned 字段
        if (type == PunishmentRecord.PunishmentType.WARN) {
            sql.append(", p.warned");
        }

        sql.append(", h.name as player_name ");
        sql.append("FROM ").append(tableName).append(" p ");
        sql.append("LEFT JOIN litebans_history h ON p.uuid = h.uuid ");
        sql.append("WHERE (h.name LIKE ? OR p.uuid LIKE ?) ");
        sql.append("AND (h.date = ( ");
        sql.append("    SELECT MAX(h2.date) ");
        sql.append("    FROM litebans_history h2 ");
        sql.append("    WHERE h2.uuid = p.uuid ");
        sql.append(") OR h.uuid IS NULL) ");
        sql.append("ORDER BY p.time DESC ");
        sql.append("LIMIT ? OFFSET ?");

        return sql.toString();
    }

    /**
     * 从ResultSet创建PunishmentRecord对象
     */
    private PunishmentRecord createPunishmentRecordFromResultSet(ResultSet rs, PunishmentRecord.PunishmentType type)
            throws SQLException {
        PunishmentRecord record = new PunishmentRecord();

        record.setId(rs.getLong("id"));
        record.setUuid(rs.getString("uuid"));
        record.setIp(rs.getString("ip"));
        record.setReason(rs.getString("reason"));
        record.setBannedByUuid(rs.getString("banned_by_uuid"));
        record.setBannedByName(rs.getString("banned_by_name"));

        // kicks 表没有 removed_by_* 字段
        if (type != PunishmentRecord.PunishmentType.KICK) {
            try {
                record.setRemovedByUuid(rs.getString("removed_by_uuid"));
                record.setRemovedByName(rs.getString("removed_by_name"));
                record.setRemovedByReason(rs.getString("removed_by_reason"));
            } catch (SQLException e) {
                // 如果字段不存在，设置为null
                record.setRemovedByUuid(null);
                record.setRemovedByName(null);
                record.setRemovedByReason(null);
            }
        } else {
            // kicks 表没有这些字段，设置为null
            record.setRemovedByUuid(null);
            record.setRemovedByName(null);
            record.setRemovedByReason(null);
        }

        record.setType(type);

        // 尝试从history表获取玩家名称
        String playerName = getPlayerNameFromHistory(record.getUuid());
        record.setPlayerName(playerName != null ? playerName : "未知玩家");

        // 处理时间戳转换 - LiteBans 使用毫秒时间戳
        long timeMs = rs.getLong("time");
        if (timeMs > 0) {
            // 将毫秒时间戳转换为 LocalDateTime
            record.setTime(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(timeMs),
                    ZoneOffset.UTC));
        }

        long untilMs = rs.getLong("until");
        if (untilMs == 0) {
            // 永久处罚，设置一个很远的未来时间表示永久
            record.setUntil(LocalDateTime.of(2999, 12, 31, 23, 59, 59));
        } else if (untilMs > 0) {
            // 将毫秒时间戳转换为 LocalDateTime
            record.setUntil(LocalDateTime.ofInstant(
                    java.time.Instant.ofEpochMilli(untilMs),
                    ZoneOffset.UTC));
        }

        record.setTemplate(rs.getInt("template"));
        record.setServerScope(rs.getString("server_scope"));
        record.setServerOrigin(rs.getString("server_origin"));
        record.setSilent(rs.getBoolean("silent"));
        record.setIpban(rs.getBoolean("ipban"));
        record.setIpbanWildcard(rs.getBoolean("ipban_wildcard"));
        record.setActive(rs.getBoolean("active"));

        // 处理 warnings 表的额外字段
        if (type == PunishmentRecord.PunishmentType.WARN) {
            try {
                // 尝试获取 warned 字段，如果不存在则忽略
                boolean warned = rs.getBoolean("warned");
                // 可以将这个信息存储到 record 的某个字段中，或者记录日志
                plugin.getLogger().fine("警告记录 warned 状态: " + warned);
            } catch (SQLException e) {
                // 如果字段不存在，忽略错误
                plugin.getLogger().fine("警告记录没有 warned 字段");
            }
        }

        plugin.getLogger().info("创建处罚记录: ID=" + record.getId() +
                ", UUID=" + record.getUuid() +
                ", 玩家=" + record.getPlayerName() +
                ", 时间=" + record.getFormattedTime() +
                ", 到期=" + record.getFormattedUntil() +
                ", 活跃=" + record.isActive());

        return record;
    }

    /**
     * 从history表获取玩家名称
     */
    private String getPlayerNameFromHistory(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return null;
        }

        String sql = "SELECT name FROM litebans_history WHERE uuid = ? ORDER BY date DESC LIMIT 1";

        try (Connection connection = database.getConnection();
                PreparedStatement stmt = connection.prepareStatement(sql)) {

            stmt.setString(1, uuid);

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return rs.getString("name");
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.WARNING, "获取玩家名称失败 (UUID: " + uuid + "): " + e.getMessage());
        }

        return null;
    }

    /**
     * 检查数据库是否可用
     */
    public boolean isEnabled() {
        return database.isEnabled();
    }

    /**
     * 获取数据库状态
     */
    public String getDatabaseStatus() {
        return database.getPoolStatus();
    }

    /**
     * 撤销处罚记录
     */
    public boolean revokePunishment(PunishmentRecord.PunishmentType type, long recordId, String revokedBy,
            String reason) {
        if (!database.isEnabled()) {
            plugin.getLogger().warning("数据库未启用，无法撤销处罚记录");
            return false;
        }

        String tableName = getTableName(type);
        if (tableName == null) {
            plugin.getLogger().warning("未知的处罚类型: " + type);
            return false;
        }

        plugin.getLogger()
                .info("开始撤销处罚记录 - 类型: " + type.getDisplayName() + ", ID: " + recordId + ", 撤销者: " + revokedBy);

        try (Connection conn = database.getConnection()) {
            // 首先检查记录是否存在且为活跃状态
            String checkSql = "SELECT active FROM " + tableName + " WHERE id = ?";
            try (PreparedStatement checkStmt = conn.prepareStatement(checkSql)) {
                checkStmt.setLong(1, recordId);

                try (ResultSet rs = checkStmt.executeQuery()) {
                    if (!rs.next()) {
                        plugin.getLogger().warning("未找到ID为 " + recordId + " 的处罚记录");
                        return false;
                    }

                    boolean isActive = rs.getBoolean("active");
                    if (!isActive) {
                        plugin.getLogger().warning("处罚记录 " + recordId + " 已经不是活跃状态");
                        return false;
                    }
                }
            }

            // 更新记录状态为非活跃，并记录撤销信息
            String updateSql = "UPDATE " + tableName
                    + " SET active = 0, removed_by_name = ?, removed_by_reason = ?, removed_by_date = FROM_UNIXTIME(?) WHERE id = ?";
            try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                updateStmt.setString(1, revokedBy);
                updateStmt.setString(2, reason);
                updateStmt.setLong(3, System.currentTimeMillis() / 1000); // 转换为秒级时间戳
                updateStmt.setLong(4, recordId);

                int rowsAffected = updateStmt.executeUpdate();

                if (rowsAffected > 0) {
                    plugin.getLogger().info("成功撤销处罚记录: " + recordId + " (类型: " + type.getDisplayName() + ")");

                    // 清除相关数据哈希，触发数据变化检测
                    invalidateDataHash(type);

                    return true;
                } else {
                    plugin.getLogger().warning("撤销处罚记录失败，没有行被更新: " + recordId);
                    return false;
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().severe("撤销处罚记录时发生数据库错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        } catch (Exception e) {
            plugin.getLogger().severe("撤销处罚记录时发生未知错误: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 批量撤销处罚记录
     */
    public int revokePunishments(PunishmentRecord.PunishmentType type, List<Long> recordIds, String revokedBy,
            String reason) {
        if (!database.isEnabled()) {
            plugin.getLogger().warning("数据库未启用，无法批量撤销处罚记录");
            return 0;
        }

        if (recordIds == null || recordIds.isEmpty()) {
            return 0;
        }

        String tableName = getTableName(type);
        if (tableName == null) {
            plugin.getLogger().warning("未知的处罚类型: " + type);
            return 0;
        }

        plugin.getLogger().info(
                "开始批量撤销处罚记录 - 类型: " + type.getDisplayName() + ", 数量: " + recordIds.size() + ", 撤销者: " + revokedBy);

        int successCount = 0;

        try (Connection conn = database.getConnection()) {
            // 使用事务确保数据一致性
            conn.setAutoCommit(false);

            String updateSql = "UPDATE " + tableName
                    + " SET active = 0, removed_by_name = ?, removed_by_reason = ?, removed_by_date = FROM_UNIXTIME(?) WHERE id = ? AND active = 1";
            try (PreparedStatement updateStmt = conn.prepareStatement(updateSql)) {
                long currentTime = System.currentTimeMillis() / 1000; // 转换为秒级时间戳

                for (Long recordId : recordIds) {
                    updateStmt.setString(1, revokedBy);
                    updateStmt.setString(2, reason);
                    updateStmt.setLong(3, currentTime);
                    updateStmt.setLong(4, recordId);

                    int rowsAffected = updateStmt.executeUpdate();
                    if (rowsAffected > 0) {
                        successCount++;
                        plugin.getLogger().info("成功撤销处罚记录: " + recordId);
                    } else {
                        plugin.getLogger().warning("撤销处罚记录失败或记录已非活跃状态: " + recordId);
                    }
                }

                // 提交事务
                conn.commit();
                plugin.getLogger().info("批量撤销处罚记录完成 - 成功: " + successCount + "/" + recordIds.size());

                // 如果有成功撤销的记录，清除相关数据哈希
                if (successCount > 0) {
                    invalidateDataHash(type);
                }

            } catch (SQLException e) {
                // 回滚事务
                conn.rollback();
                plugin.getLogger().severe("批量撤销处罚记录时发生错误，已回滚: " + e.getMessage());
                e.printStackTrace();
                successCount = 0;
            } finally {
                conn.setAutoCommit(true);
            }

        } catch (SQLException e) {
            plugin.getLogger().severe("批量撤销处罚记录时发生数据库错误: " + e.getMessage());
            e.printStackTrace();
        }

        return successCount;
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        stopAutoUpdateTask();
        clearDataHashes();
        database.shutdown();
        plugin.getLogger().info("处罚记录管理器已关闭");
    }

    /**
     * 根据ID获取特定的处罚记录（按照 next-litebans 详细页面需求）
     */
    public PunishmentRecord getPunishmentById(PunishmentRecord.PunishmentType type, long id) {
        if (!database.isEnabled()) {
            return null;
        }

        String tableName = getTableName(type);
        if (tableName == null) {
            return null;
        }

        try (Connection conn = database.getConnection()) {
            String sql = "SELECT id, uuid, banned_by_name, banned_by_uuid, reason, time, until, active, silent, ipban "
                    +
                    "FROM " + tableName + " WHERE id = ?";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setLong(1, id);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return createRecordFromResultSet(rs, type);
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("根据ID查询处罚记录时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    /**
     * 根据玩家名称获取处罚记录（按照 next-litebans 玩家页面需求）
     */
    public List<PunishmentRecord> getPunishmentsByPlayer(String playerName, int page, int pageSize) {
        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty()) {
            return new ArrayList<>();
        }

        List<PunishmentRecord> allRecords = new ArrayList<>();

        // 获取玩家UUID
        String playerUuid = getPlayerUuidByName(playerName);
        if (playerUuid == null) {
            return allRecords;
        }

        // 查询所有类型的处罚记录
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            String tableName = getTableName(type);
            if (tableName == null)
                continue;

            try (Connection conn = database.getConnection()) {
                String sql = "SELECT id, uuid, banned_by_name, banned_by_uuid, reason, time, until, active, silent, ipban "
                        +
                        "FROM " + tableName + " WHERE uuid = ? " +
                        "ORDER BY time DESC";

                try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                    stmt.setString(1, playerUuid);

                    try (ResultSet rs = stmt.executeQuery()) {
                        while (rs.next()) {
                            PunishmentRecord record = createRecordFromResultSet(rs, type);
                            if (record != null) {
                                allRecords.add(record);
                            }
                        }
                    }
                }
            } catch (SQLException e) {
                plugin.getLogger().severe("查询玩家处罚记录时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 按时间排序
        allRecords.sort((a, b) -> {
            if (a.getTime() == null && b.getTime() == null)
                return 0;
            if (a.getTime() == null)
                return 1;
            if (b.getTime() == null)
                return -1;
            return b.getTime().compareTo(a.getTime());
        });

        // 分页
        int startIndex = (page - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, allRecords.size());

        if (startIndex >= allRecords.size()) {
            return new ArrayList<>();
        }

        return allRecords.subList(startIndex, endIndex);
    }

    /**
     * 获取玩家的处罚记录总数
     */
    public int getPlayerPunishmentCount(String playerName) {
        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty()) {
            return 0;
        }

        String playerUuid = getPlayerUuidByName(playerName);
        if (playerUuid == null) {
            return 0;
        }

        int totalCount = 0;

        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            String tableName = getTableName(type);
            if (tableName == null)
                continue;

            try (Connection conn = database.getConnection()) {
                String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE uuid = ?";

                try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                    stmt.setString(1, playerUuid);

                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            totalCount += rs.getInt(1);
                        }
                    }
                }
            } catch (SQLException e) {
                plugin.getLogger().severe("查询玩家处罚记录数量时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        }

        return totalCount;
    }

    /**
     * 获取玩家特定类型的处罚记录数量
     */
    public int getPlayerPunishmentCount(String playerName, PunishmentRecord.PunishmentType type) {
        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty() || type == null) {
            return 0;
        }

        String playerUuid = getPlayerUuidByName(playerName);
        if (playerUuid == null) {
            return 0;
        }

        String tableName = getTableName(type);
        if (tableName == null) {
            return 0;
        }

        try (Connection conn = database.getConnection()) {
            String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE uuid = ?";

            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, playerUuid);

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("查询玩家" + type.getDisplayName() + "记录数量时发生错误: " + e.getMessage());
            e.printStackTrace();
        }

        return 0;
    }

    /**
     * 根据玩家名称获取UUID
     */
    public String getPlayerUuidByName(String playerName) {
        if (!database.isEnabled() || playerName == null || playerName.trim().isEmpty()) {
            return null;
        }

        // 首先从history表中查找玩家UUID
        try (Connection conn = database.getConnection()) {
            String sql = "SELECT uuid FROM litebans_history WHERE name = ? ORDER BY date DESC LIMIT 1";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, playerName);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("uuid");
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("从history表获取玩家UUID时发生错误: " + e.getMessage());
        }

        // 如果history表中没有找到，尝试从处罚记录中查找
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            String tableName = getTableName(type);
            if (tableName == null)
                continue;

            try (Connection conn = database.getConnection()) {
                // 通过JOIN查询获取玩家UUID
                String sql = "SELECT DISTINCT p.uuid FROM " + tableName + " p " +
                        "LEFT JOIN litebans_history h ON p.uuid = h.uuid " +
                        "WHERE h.name = ? ORDER BY p.time DESC LIMIT 1";

                try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                    stmt.setString(1, playerName);

                    try (ResultSet rs = stmt.executeQuery()) {
                        if (rs.next()) {
                            return rs.getString("uuid");
                        }
                    }
                }
            } catch (SQLException e) {
                // 继续尝试其他表
                plugin.getLogger().fine("从" + tableName + "表查找玩家UUID失败: " + e.getMessage());
            }
        }

        plugin.getLogger().warning("无法找到玩家 " + playerName + " 的UUID");
        return null;
    }

    /**
     * 重新加载配置
     */
    public boolean reload() {
        return database.reload();
    }

    /**
     * 根据玩家名获取UUID
     */
    private String getPlayerUuid(String playerName) {
        if (playerName == null || playerName.trim().isEmpty()) {
            return null;
        }

        // 如果已经是UUID格式，直接返回
        if (playerName.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}")) {
            return playerName;
        }

        try (Connection conn = database.getConnection()) {
            String sql = "SELECT uuid FROM litebans_history WHERE name = ? ORDER BY date DESC LIMIT 1";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, playerName);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("uuid");
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("获取玩家UUID时发生错误: " + e.getMessage());
        }

        return null;
    }

    /**
     * 从ResultSet创建PunishmentRecord
     */
    private PunishmentRecord createRecordFromResultSet(ResultSet rs, PunishmentRecord.PunishmentType type)
            throws SQLException {
        PunishmentRecord record = new PunishmentRecord();

        record.setId(rs.getLong("id"));
        record.setUuid(rs.getString("uuid"));
        record.setBannedByName(rs.getString("banned_by_name"));
        record.setBannedByUuid(rs.getString("banned_by_uuid"));
        record.setReason(rs.getString("reason"));

        // 转换时间戳为LocalDateTime
        long timeMillis = rs.getLong("time");
        if (timeMillis > 0) {
            record.setTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(timeMillis), ZoneId.systemDefault()));
        }

        long untilMillis = rs.getLong("until");
        if (untilMillis > 0) {
            record.setUntil(LocalDateTime.ofInstant(Instant.ofEpochMilli(untilMillis), ZoneId.systemDefault()));
        }

        record.setActive(rs.getBoolean("active"));

        // 设置处罚类型
        record.setType(type);

        // 尝试获取玩家名称
        String playerName = getPlayerNameByUuid(record.getUuid());
        record.setPlayerName(playerName != null ? playerName : "未知玩家");

        return record;
    }

    /**
     * 根据UUID获取玩家名称（公共方法）
     */
    public String getPlayerNameByUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return null;
        }

        try (Connection conn = database.getConnection()) {
            String sql = "SELECT name FROM litebans_history WHERE uuid = ? ORDER BY date DESC LIMIT 1";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, uuid);
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getString("name");
                    }
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().warning("获取玩家名称时发生错误: " + e.getMessage());
        }

        return null;
    }

    // ==================== 异步数据更新功能 ====================

    /**
     * 启动自动更新任务（用于数据变化检测）
     */
    public void startAutoUpdateTask() {
        // 检查数据库配置是否启用
        boolean litebansEnabled = plugin.getConfig().getBoolean("litebans.enabled", false);
        if (!litebansEnabled) {
            plugin.getLogger().info("LiteBans功能在配置中被禁用，跳过处罚记录数据变化检测任务启动");
            return;
        }

        if (!autoUpdateEnabled) {
            plugin.getLogger().info("处罚记录数据变化检测已禁用");
            return;
        }

        // 停止现有任务
        stopAutoUpdateTask();

        // 启动新的定时任务 - 用于检测数据变化，不缓存数据
        updateTask = Bukkit.getScheduler().runTaskTimerAsynchronously(plugin,
                this::updateDataHashes,
                20L * updateIntervalSeconds, // 延迟启动
                20L * updateIntervalSeconds // 更新间隔
        );

        plugin.getLogger().info("处罚记录数据变化检测任务已启动，检测间隔: " + updateIntervalSeconds + " 秒（实时更新）");
    }

    /**
     * 停止自动更新任务
     */
    public void stopAutoUpdateTask() {
        if (updateTask != null) {
            updateTask.cancel();
            updateTask = null;
            plugin.getLogger().info("处罚记录数据变化检测任务已停止");
        }
    }

    /**
     * 更新数据哈希值（用于检测数据变化，不缓存实际数据）
     */
    private void updateDataHashes() {
        if (!database.isEnabled()) {
            return;
        }

        try {
            plugin.getLogger().info("开始检测处罚记录数据变化...");

            // 异步检测各种类型的处罚记录变化
            Bukkit.getScheduler().runTaskAsynchronously(plugin, () -> {
                for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
                    updateTypeDataHash(type);
                }

                // 检测统计信息变化
                updateStatisticsDataHash();
            });

            plugin.getLogger().info("处罚记录数据变化检测完成");

        } catch (Exception e) {
            plugin.getLogger().severe("检测处罚记录数据变化时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 更新指定类型的数据哈希值（仅用于变化检测）
     */
    private void updateTypeDataHash(PunishmentRecord.PunishmentType type) {
        try {
            // 异步获取最新数据进行哈希计算
            List<PunishmentRecord> records = getPunishmentRecordsFromDatabase(type, 1, 50); // 获取前50条记录

            // 计算数据哈希值
            String dataHash = calculateDataHash(records);

            // 生成缓存键
            String cacheKey = "type_" + type.name().toLowerCase();

            // 检查数据是否发生变化
            String oldHash = dataHashes.get(cacheKey);
            if (oldHash == null || !oldHash.equals(dataHash)) {
                // 数据发生变化，更新哈希值和时间戳
                dataHashes.put(cacheKey, dataHash);
                lastUpdateTimes.put(cacheKey, System.currentTimeMillis());

                plugin.getLogger().info("检测到 " + type.getDisplayName() + " 数据变化，记录数: " + records.size());
            }

        } catch (Exception e) {
            plugin.getLogger().warning("检测 " + type.getDisplayName() + " 数据变化时发生错误: " + e.getMessage());
        }
    }

    /**
     * 更新统计信息数据哈希值（仅用于变化检测）
     */
    private void updateStatisticsDataHash() {
        try {
            Map<String, Object> stats = getPunishmentStatisticsFromDatabase();
            String dataHash = calculateStatisticsHash(stats);

            String cacheKey = "statistics";
            String oldHash = dataHashes.get(cacheKey);

            if (oldHash == null || !oldHash.equals(dataHash)) {
                dataHashes.put(cacheKey, dataHash);
                lastUpdateTimes.put(cacheKey, System.currentTimeMillis());

                plugin.getLogger().info("检测到统计信息数据变化");
            }

        } catch (Exception e) {
            plugin.getLogger().warning("检测统计信息数据变化时发生错误: " + e.getMessage());
        }
    }

    /**
     * 清除指定类型的数据哈希（触发数据变化检测）
     */
    private void invalidateDataHash(PunishmentRecord.PunishmentType type) {
        String cacheKey = "type_" + type.name().toLowerCase();

        dataHashes.remove(cacheKey);
        lastUpdateTimes.remove(cacheKey);

        // 同时清除统计信息哈希，因为统计数据可能也发生了变化
        dataHashes.remove("statistics");
        lastUpdateTimes.remove("statistics");

        plugin.getLogger().info("已清除 " + type.getDisplayName() + " 相关数据哈希");
    }

    /**
     * 清除所有数据哈希
     */
    public void clearDataHashes() {
        dataHashes.clear();
        lastUpdateTimes.clear();

        plugin.getLogger().info("已清除所有处罚记录数据哈希");
    }

    /**
     * 计算数据哈希值
     */
    private String calculateDataHash(List<PunishmentRecord> records) {
        if (records == null || records.isEmpty()) {
            return "empty";
        }

        StringBuilder sb = new StringBuilder();
        for (PunishmentRecord record : records) {
            sb.append(record.getId())
                    .append(record.getUuid())
                    .append(record.isActive())
                    .append(record.getReason())
                    .append(record.getTime() != null ? record.getTime().toString() : "null");
        }

        return String.valueOf(sb.toString().hashCode());
    }

    /**
     * 计算统计信息哈希值
     */
    private String calculateStatisticsHash(Map<String, Object> stats) {
        if (stats == null || stats.isEmpty()) {
            return "empty";
        }

        return String.valueOf(stats.toString().hashCode());
    }

    /**
     * 获取数据哈希值（用于检测数据变化）
     */
    public String getDataHash(String cacheKey) {
        return dataHashes.get(cacheKey);
    }

    /**
     * 设置自动更新状态
     */
    public void setAutoUpdateEnabled(boolean enabled) {
        this.autoUpdateEnabled = enabled;
        if (enabled) {
            startAutoUpdateTask();
        } else {
            stopAutoUpdateTask();
        }
    }

    /**
     * 设置更新间隔
     */
    public void setUpdateInterval(int seconds) {
        this.updateIntervalSeconds = Math.max(10, seconds); // 最小10秒
        if (autoUpdateEnabled) {
            startAutoUpdateTask(); // 重启任务以应用新间隔
        }
    }

    /**
     * 获取自动更新状态
     */
    public boolean isAutoUpdateEnabled() {
        return autoUpdateEnabled;
    }

    /**
     * 获取更新间隔
     */
    public int getUpdateInterval() {
        return updateIntervalSeconds;
    }

    /**
     * 获取数据变化检测统计信息
     */
    public Map<String, Object> getDataChangeStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("tracked_data_types", dataHashes.size());
        stats.put("auto_update_enabled", autoUpdateEnabled);
        stats.put("update_interval_seconds", updateIntervalSeconds);

        // 获取最后更新时间
        long latestUpdateTime = 0;
        for (Long updateTime : lastUpdateTimes.values()) {
            if (updateTime > latestUpdateTime) {
                latestUpdateTime = updateTime;
            }
        }
        stats.put("latest_update_time", latestUpdateTime);

        return stats;
    }

}
