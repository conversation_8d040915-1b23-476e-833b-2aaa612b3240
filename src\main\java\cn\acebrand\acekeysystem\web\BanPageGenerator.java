package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.ban.BanRecord;

import java.util.List;
import java.util.Map;

/**
 * 封禁页面生成器
 */
public class BanPageGenerator {

    private final AceKeySystem plugin;

    public BanPageGenerator(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    /**
     * 生成封禁记录页面HTML
     */
    public String generateBanPage(List<BanRecord> records, int currentPage, int totalPages,
            Map<String, Object> statistics, String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>封禁记录 - ").append(plugin.getConfig().getString("web-server.title", "服务器管理"))
                .append("</title>\n");
        html.append("    <style>\n");
        html.append(generateBanPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 页面标题
        html.append("        <div class=\"page-header\">\n");
        html.append("            <h1>🚫 封禁记录</h1>\n");
        html.append("            <p class=\"page-description\">查看服务器封禁历史记录</p>\n");
        html.append("            <a href=\"/\" class=\"back-btn\">← 返回首页</a>\n");
        html.append("        </div>\n");

        // 统计信息
        if (statistics != null && !statistics.isEmpty()) {
            html.append(generateStatisticsSection(statistics));
        }

        // 搜索框
        html.append(generateSearchSection(searchQuery));

        // 封禁记录表格
        html.append(generateBanTable(records));

        // 分页导航
        if (totalPages > 1) {
            html.append(generatePagination(currentPage, totalPages, searchQuery));
        }

        html.append("    </div>\n");

        // JavaScript
        html.append("    <script>\n");
        html.append(generateBanPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成统计信息区域
     */
    private String generateStatisticsSection(Map<String, Object> statistics) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"statistics-section\">\n");
        html.append("            <div class=\"stats-grid\">\n");

        // 总封禁数
        Object totalBans = statistics.get("total_bans");
        if (totalBans != null) {
            html.append("                <div class=\"stat-card\">\n");
            html.append("                    <div class=\"stat-icon\">📊</div>\n");
            html.append("                    <div class=\"stat-info\">\n");
            html.append("                        <div class=\"stat-value\">").append(totalBans).append("</div>\n");
            html.append("                        <div class=\"stat-label\">总封禁数</div>\n");
            html.append("                    </div>\n");
            html.append("                </div>\n");
        }

        // 活跃封禁数
        Object activeBans = statistics.get("active_bans");
        if (activeBans != null) {
            html.append("                <div class=\"stat-card active\">\n");
            html.append("                    <div class=\"stat-icon\">🔴</div>\n");
            html.append("                    <div class=\"stat-info\">\n");
            html.append("                        <div class=\"stat-value\">").append(activeBans).append("</div>\n");
            html.append("                        <div class=\"stat-label\">活跃封禁</div>\n");
            html.append("                    </div>\n");
            html.append("                </div>\n");
        }

        // IP封禁数
        Object ipBans = statistics.get("ip_bans");
        if (ipBans != null) {
            html.append("                <div class=\"stat-card ip\">\n");
            html.append("                    <div class=\"stat-icon\">🌐</div>\n");
            html.append("                    <div class=\"stat-info\">\n");
            html.append("                        <div class=\"stat-value\">").append(ipBans).append("</div>\n");
            html.append("                        <div class=\"stat-label\">IP封禁</div>\n");
            html.append("                    </div>\n");
            html.append("                </div>\n");
        }

        // 今日封禁数
        Object todayBans = statistics.get("today_bans");
        if (todayBans != null) {
            html.append("                <div class=\"stat-card today\">\n");
            html.append("                    <div class=\"stat-icon\">📅</div>\n");
            html.append("                    <div class=\"stat-info\">\n");
            html.append("                        <div class=\"stat-value\">").append(todayBans).append("</div>\n");
            html.append("                        <div class=\"stat-label\">今日封禁</div>\n");
            html.append("                    </div>\n");
            html.append("                </div>\n");
        }

        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成搜索区域
     */
    private String generateSearchSection(String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"search-section\">\n");
        html.append("            <form class=\"search-form\" method=\"get\">\n");
        html.append("                <div class=\"search-input-group\">\n");
        html.append("                    <input type=\"text\" name=\"search\" placeholder=\"搜索玩家名称或UUID...\" ");
        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("value=\"").append(escapeHtml(searchQuery)).append("\" ");
        }
        html.append("class=\"search-input\">\n");
        html.append("                    <button type=\"submit\" class=\"search-btn\">🔍 搜索</button>\n");
        html.append("                </div>\n");
        html.append("            </form>\n");

        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("            <div class=\"search-result-info\">\n");
            html.append("                <span>搜索结果: \"").append(escapeHtml(searchQuery)).append("\"</span>\n");
            html.append("                <a href=\"/bans\" class=\"clear-search\">清除搜索</a>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成封禁记录表格
     */
    private String generateBanTable(List<BanRecord> records) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"table-section\">\n");

        if (records.isEmpty()) {
            html.append("            <div class=\"no-records\">\n");
            html.append("                <div class=\"no-records-icon\">📝</div>\n");
            html.append("                <h3>暂无封禁记录</h3>\n");
            html.append("                <p>当前没有找到任何封禁记录</p>\n");
            html.append("            </div>\n");
        } else {
            html.append("            <div class=\"table-container\">\n");
            html.append("                <table class=\"ban-table\">\n");
            html.append("                    <thead>\n");
            html.append("                        <tr>\n");
            html.append("                            <th>玩家</th>\n");
            html.append("                            <th>封禁原因</th>\n");
            html.append("                            <th>执行者</th>\n");
            html.append("                            <th>封禁时间</th>\n");
            html.append("                            <th>到期时间</th>\n");
            html.append("                            <th>状态</th>\n");
            html.append("                            <th>类型</th>\n");
            html.append("                        </tr>\n");
            html.append("                    </thead>\n");
            html.append("                    <tbody>\n");

            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            boolean showAvatars = plugin.getConfig().getBoolean("litebans.display.show-player-avatars", true);

            for (BanRecord record : records) {
                html.append("                        <tr class=\"ban-row\">\n");

                // 玩家列
                html.append("                            <td class=\"player-cell\">\n");
                if (showAvatars && record.getUuid() != null && !record.getUuid().isEmpty()) {
                    html.append("                                <img src=\"")
                            .append(record.getPlayerAvatarUrl(avatarProvider))
                            .append("\" alt=\"头像\" class=\"player-avatar\" onerror=\"this.style.display='none'\">\n");
                }
                html.append("                                <div class=\"player-info\">\n");
                html.append("                                    <div class=\"player-name\">")
                        .append(escapeHtml(record.getPlayerName() != null ? record.getPlayerName() : "未知玩家"))
                        .append("</div>\n");
                if (record.getUuid() != null && !record.getUuid().isEmpty()) {
                    html.append("                                    <div class=\"player-uuid\">")
                            .append(escapeHtml(record.getUuid().substring(0, 8))).append("...</div>\n");
                }
                html.append("                                </div>\n");
                html.append("                            </td>\n");

                // 封禁原因
                html.append("                            <td class=\"reason-cell\">\n");
                html.append("                                <span class=\"reason-text\" title=\"")
                        .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因"))
                        .append("\">").append(escapeHtml(record.getShortReason())).append("</span>\n");
                html.append("                            </td>\n");

                // 执行者
                html.append("                            <td class=\"staff-cell\">\n");
                html.append("                                ")
                        .append(escapeHtml(record.getBannedByName() != null ? record.getBannedByName() : "系统"))
                        .append("\n");
                html.append("                            </td>\n");

                // 封禁时间
                html.append("                            <td class=\"time-cell\">\n");
                html.append("                                ").append(escapeHtml(record.getFormattedTime()))
                        .append("\n");
                html.append("                            </td>\n");

                // 到期时间
                html.append("                            <td class=\"until-cell\">\n");
                html.append("                                ").append(escapeHtml(record.getFormattedUntil()))
                        .append("\n");
                html.append("                            </td>\n");

                // 状态
                html.append("                            <td class=\"status-cell\">\n");
                html.append("                                <span class=\"status-badge ")
                        .append(record.getStatusClass())
                        .append("\">").append(escapeHtml(record.getStatusText())).append("</span>\n");
                html.append("                            </td>\n");

                // 类型
                html.append("                            <td class=\"type-cell\">\n");
                html.append("                                <span class=\"type-badge\">")
                        .append(escapeHtml(record.getBanTypeText())).append("</span>\n");
                html.append("                            </td>\n");

                html.append("                        </tr>\n");
            }

            html.append("                    </tbody>\n");
            html.append("                </table>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成分页导航
     */
    private String generatePagination(int currentPage, int totalPages, String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"pagination-section\">\n");
        html.append("            <div class=\"pagination\">\n");

        String baseUrl = "/bans";
        String queryParam = "";
        if (searchQuery != null && !searchQuery.isEmpty()) {
            queryParam = "&search=" + escapeUrl(searchQuery);
        }

        // 上一页
        if (currentPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage - 1)
                    .append(queryParam).append("\" class=\"page-btn prev-btn\">← 上一页</a>\n");
        }

        // 页码
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=1").append(queryParam)
                    .append("\" class=\"page-btn\">1</a>\n");
            if (startPage > 2) {
                html.append("                <span class=\"page-dots\">...</span>\n");
            }
        }

        for (int i = startPage; i <= endPage; i++) {
            if (i == currentPage) {
                html.append("                <span class=\"page-btn current\">").append(i).append("</span>\n");
            } else {
                html.append("                <a href=\"").append(baseUrl).append("?page=").append(i)
                        .append(queryParam).append("\" class=\"page-btn\">").append(i).append("</a>\n");
            }
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html.append("                <span class=\"page-dots\">...</span>\n");
            }
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(totalPages)
                    .append(queryParam).append("\" class=\"page-btn\">").append(totalPages).append("</a>\n");
        }

        // 下一页
        if (currentPage < totalPages) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage + 1)
                    .append(queryParam).append("\" class=\"page-btn next-btn\">下一页 →</a>\n");
        }

        html.append("            </div>\n");
        html.append("            <div class=\"page-info\">\n");
        html.append("                第 ").append(currentPage).append(" 页，共 ").append(totalPages).append(" 页\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null)
            return "";
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    /**
     * URL转义
     */
    private String escapeUrl(String text) {
        if (text == null)
            return "";
        try {
            return java.net.URLEncoder.encode(text, "UTF-8");
        } catch (Exception e) {
            return text;
        }
    }

    /**
     * 生成封禁页面CSS样式
     */
    private String generateBanPageCSS() {
        return "/* 基础样式 */\n" +
                "* {\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                "body {\n" +
                "    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n" +
                "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "    min-height: 100vh;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".container {\n" +
                "    max-width: 1200px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 20px;\n" +
                "}\n" +
                "\n" +
                "/* 页面标题 */\n" +
                ".page-header {\n" +
                "    text-align: center;\n" +
                "    margin-bottom: 30px;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    padding: 30px;\n" +
                "    border-radius: 15px;\n" +
                "    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".page-header h1 {\n" +
                "    font-size: 2.5em;\n" +
                "    color: #333;\n" +
                "    margin-bottom: 10px;\n" +
                "}\n" +
                "\n" +
                ".page-description {\n" +
                "    font-size: 1.1em;\n" +
                "    color: #666;\n" +
                "    margin-bottom: 20px;\n" +
                "}\n" +
                "\n" +
                ".back-btn {\n" +
                "    display: inline-block;\n" +
                "    padding: 10px 20px;\n" +
                "    background: #667eea;\n" +
                "    color: white;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 8px;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".back-btn:hover {\n" +
                "    background: #5a6fd8;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                "/* 统计信息 */\n" +
                ".statistics-section {\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".stats-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n" +
                "    gap: 20px;\n" +
                "}\n" +
                "\n" +
                ".stat-card {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    padding: 20px;\n" +
                "    border-radius: 12px;\n" +
                "    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".stat-card:hover {\n" +
                "    transform: translateY(-3px);\n" +
                "}\n" +
                "\n" +
                ".stat-card.active {\n" +
                "    border-left: 4px solid #e74c3c;\n" +
                "}\n" +
                "\n" +
                ".stat-card.ip {\n" +
                "    border-left: 4px solid #f39c12;\n" +
                "}\n" +
                "\n" +
                ".stat-card.today {\n" +
                "    border-left: 4px solid #27ae60;\n" +
                "}\n" +
                "\n" +
                ".stat-icon {\n" +
                "    font-size: 2em;\n" +
                "    margin-right: 15px;\n" +
                "}\n" +
                "\n" +
                ".stat-value {\n" +
                "    font-size: 1.8em;\n" +
                "    font-weight: bold;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".stat-label {\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                "/* 搜索区域 */\n" +
                ".search-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    padding: 20px;\n" +
                "    border-radius: 12px;\n" +
                "    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".search-form {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".search-input-group {\n" +
                "    display: flex;\n" +
                "    max-width: 500px;\n" +
                "    width: 100%;\n" +
                "}\n" +
                "\n" +
                ".search-input {\n" +
                "    flex: 1;\n" +
                "    padding: 12px 15px;\n" +
                "    border: 2px solid #ddd;\n" +
                "    border-radius: 8px 0 0 8px;\n" +
                "    font-size: 1em;\n" +
                "    outline: none;\n" +
                "    transition: border-color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".search-input:focus {\n" +
                "    border-color: #667eea;\n" +
                "}\n" +
                "\n" +
                ".search-btn {\n" +
                "    padding: 12px 20px;\n" +
                "    background: #667eea;\n" +
                "    color: white;\n" +
                "    border: none;\n" +
                "    border-radius: 0 8px 8px 0;\n" +
                "    cursor: pointer;\n" +
                "    font-size: 1em;\n" +
                "    transition: background 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".search-btn:hover {\n" +
                "    background: #5a6fd8;\n" +
                "}\n" +
                "\n" +
                ".search-result-info {\n" +
                "    margin-top: 15px;\n" +
                "    text-align: center;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".clear-search {\n" +
                "    color: #667eea;\n" +
                "    text-decoration: none;\n" +
                "    margin-left: 10px;\n" +
                "}\n" +
                "\n" +
                ".clear-search:hover {\n" +
                "    text-decoration: underline;\n" +
                "}\n" +
                "\n" +
                "/* 表格样式 */\n" +
                ".table-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 12px;\n" +
                "    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n" +
                "    overflow: hidden;\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".table-container {\n" +
                "    overflow-x: auto;\n" +
                "}\n" +
                "\n" +
                ".ban-table {\n" +
                "    width: 100%;\n" +
                "    border-collapse: collapse;\n" +
                "    font-size: 0.9em;\n" +
                "}\n" +
                "\n" +
                ".ban-table th {\n" +
                "    background: #f8f9fa;\n" +
                "    padding: 15px 10px;\n" +
                "    text-align: left;\n" +
                "    font-weight: 600;\n" +
                "    color: #333;\n" +
                "    border-bottom: 2px solid #dee2e6;\n" +
                "}\n" +
                "\n" +
                ".ban-table td {\n" +
                "    padding: 12px 10px;\n" +
                "    border-bottom: 1px solid #dee2e6;\n" +
                "    vertical-align: middle;\n" +
                "}\n" +
                "\n" +
                ".ban-row:hover {\n" +
                "    background-color: #f8f9fa;\n" +
                "    cursor: pointer;\n" +
                "}\n" +
                "\n" +
                ".player-cell {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    min-width: 150px;\n" +
                "}\n" +
                "\n" +
                ".player-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 4px;\n" +
                "    margin-right: 10px;\n" +
                "}\n" +
                "\n" +
                ".player-info {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "}\n" +
                "\n" +
                ".player-name {\n" +
                "    font-weight: 600;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".player-uuid {\n" +
                "    font-size: 0.8em;\n" +
                "    color: #666;\n" +
                "    font-family: monospace;\n" +
                "}\n" +
                "\n" +
                ".reason-cell {\n" +
                "    max-width: 200px;\n" +
                "}\n" +
                "\n" +
                ".reason-text {\n" +
                "    display: block;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "}\n" +
                "\n" +
                ".status-badge {\n" +
                "    padding: 4px 8px;\n" +
                "    border-radius: 12px;\n" +
                "    font-size: 0.8em;\n" +
                "    font-weight: 600;\n" +
                "    text-transform: uppercase;\n" +
                "}\n" +
                "\n" +
                ".status-active {\n" +
                "    background: #e74c3c;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".status-expired {\n" +
                "    background: #f39c12;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".status-unbanned {\n" +
                "    background: #27ae60;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".type-badge {\n" +
                "    padding: 4px 8px;\n" +
                "    background: #667eea;\n" +
                "    color: white;\n" +
                "    border-radius: 8px;\n" +
                "    font-size: 0.8em;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".no-records {\n" +
                "    text-align: center;\n" +
                "    padding: 60px 20px;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".no-records-icon {\n" +
                "    font-size: 4em;\n" +
                "    margin-bottom: 20px;\n" +
                "}\n" +
                "\n" +
                ".no-records h3 {\n" +
                "    margin-bottom: 10px;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                "/* 分页样式 */\n" +
                ".pagination-section {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    padding: 20px;\n" +
                "    border-radius: 12px;\n" +
                "    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".pagination {\n" +
                "    display: flex;\n" +
                "    gap: 5px;\n" +
                "}\n" +
                "\n" +
                ".page-btn {\n" +
                "    padding: 8px 12px;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 6px;\n" +
                "    transition: all 0.3s ease;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                ".page-btn:not(.current) {\n" +
                "    background: #f8f9fa;\n" +
                "    color: #667eea;\n" +
                "    border: 1px solid #dee2e6;\n" +
                "}\n" +
                "\n" +
                ".page-btn:not(.current):hover {\n" +
                "    background: #667eea;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".page-btn.current {\n" +
                "    background: #667eea;\n" +
                "    color: white;\n" +
                "    border: 1px solid #667eea;\n" +
                "}\n" +
                "\n" +
                ".page-dots {\n" +
                "    padding: 8px 4px;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".page-info {\n" +
                "    color: #666;\n" +
                "    font-size: 0.9em;\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .container {\n" +
                "        padding: 10px;\n" +
                "    }\n" +
                "\n" +
                "    .page-header {\n" +
                "        padding: 20px;\n" +
                "    }\n" +
                "\n" +
                "    .page-header h1 {\n" +
                "        font-size: 2em;\n" +
                "    }\n" +
                "\n" +
                "    .stats-grid {\n" +
                "        grid-template-columns: 1fr;\n" +
                "    }\n" +
                "\n" +
                "    .search-input-group {\n" +
                "        flex-direction: column;\n" +
                "    }\n" +
                "\n" +
                "    .search-input {\n" +
                "        border-radius: 8px;\n" +
                "        margin-bottom: 10px;\n" +
                "    }\n" +
                "\n" +
                "    .search-btn {\n" +
                "        border-radius: 8px;\n" +
                "    }\n" +
                "\n" +
                "    .ban-table {\n" +
                "        font-size: 0.8em;\n" +
                "    }\n" +
                "\n" +
                "    .ban-table th,\n" +
                "    .ban-table td {\n" +
                "        padding: 8px 5px;\n" +
                "    }\n" +
                "\n" +
                "    .pagination-section {\n" +
                "        flex-direction: column;\n" +
                "        gap: 15px;\n" +
                "    }\n" +
                "\n" +
                "    .pagination {\n" +
                "        flex-wrap: wrap;\n" +
                "        justify-content: center;\n" +
                "    }\n" +
                "}";
    }

    /**
     * 生成封禁页面JavaScript
     */
    private String generateBanPageJS() {
        return "// 页面加载完成后执行\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    // 搜索框回车提交\n" +
                "    const searchInput = document.querySelector('.search-input');\n" +
                "    if (searchInput) {\n" +
                "        searchInput.addEventListener('keypress', function(e) {\n" +
                "            if (e.key === 'Enter') {\n" +
                "                e.preventDefault();\n" +
                "                this.closest('form').submit();\n" +
                "            }\n" +
                "        });\n" +
                "    }\n" +
                "\n" +
                "    // 表格行点击效果\n" +
                "    const tableRows = document.querySelectorAll('.ban-row');\n" +
                "    tableRows.forEach(row => {\n" +
                "        row.addEventListener('click', function() {\n" +
                "            // 可以在这里添加点击行的处理逻辑\n" +
                "            this.style.backgroundColor = '#f8f9fa';\n" +
                "            setTimeout(() => {\n" +
                "                this.style.backgroundColor = '';\n" +
                "            }, 200);\n" +
                "        });\n" +
                "    });\n" +
                "\n" +
                "    // 统计卡片动画\n" +
                "    const statCards = document.querySelectorAll('.stat-card');\n" +
                "    statCards.forEach((card, index) => {\n" +
                "        setTimeout(() => {\n" +
                "            card.style.opacity = '0';\n" +
                "            card.style.transform = 'translateY(20px)';\n" +
                "            card.style.transition = 'all 0.5s ease';\n" +
                "\n" +
                "            setTimeout(() => {\n" +
                "                card.style.opacity = '1';\n" +
                "                card.style.transform = 'translateY(0)';\n" +
                "            }, 100);\n" +
                "        }, index * 100);\n" +
                "    });\n" +
                "});";
    }
}
