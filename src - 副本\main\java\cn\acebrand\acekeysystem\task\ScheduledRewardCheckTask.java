package cn.acebrand.acekeysystem.task;

import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 定时奖励检查任务
 * 定期检查网站上的新奖励
 */
public final class ScheduledRewardCheckTask extends BukkitRunnable {

    private final AceKeySystem plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ScheduledRewardCheckTask(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void run() {
        // 执行定时奖励检查
        this.plugin.checkAndExecuteRewards();
    }
}
