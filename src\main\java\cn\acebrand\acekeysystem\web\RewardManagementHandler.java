package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.lottery.LotteryReward;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 奖励管理界面处理器
 */
public class RewardManagementHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;

    public RewardManagementHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();
        String query = exchange.getRequestURI().getQuery();

        // 验证管理员权限
        if (!isAdminAccess(query)) {
            sendUnauthorizedResponse(exchange);
            return;
        }

        if ("GET".equals(method)) {
            sendRewardManagementPage(exchange);
        } else {
            sendErrorResponse(exchange, 405, "方法不允许");
        }
    }

    /**
     * 验证管理员访问权限
     */
    private boolean isAdminAccess(String query) {
        if (query == null)
            return false;

        String[] params = query.split("&");
        for (String param : params) {
            if (param.startsWith("key=")) {
                String key = param.substring(4);
                return webServer.getAdminKey().equals(key);
            }
        }
        return false;
    }

    /**
     * 发送奖励管理页面
     */
    private void sendRewardManagementPage(HttpExchange exchange) throws IOException {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>奖励管理 - AceKeySystem插件</title>\n");
        html.append("    <style>\n");
        html.append(getRewardManagementCSS());
        html.append("    </style>\n");
        html.append("    <script>\n");
        html.append("        // 初始化主题\n");
        html.append("        function initializeAdminTheme() {\n");
        html.append("            const savedTheme = localStorage.getItem('adminTheme');\n");
        html.append("            const defaultMode = '")
                .append(plugin.getConfig().getString("web-server.admin-theme.default-mode", "light")).append("';\n");
        html.append("            const enableDarkMode = ")
                .append(plugin.getConfig().getBoolean("web-server.admin-theme.dark-mode-enabled", true)).append(";\n");
        html.append("            \n");
        html.append("            let currentTheme = savedTheme || defaultMode;\n");
        html.append("            \n");
        html.append("            // 自动模式处理\n");
        html.append("            if (currentTheme === 'auto') {\n");
        html.append("                const hour = new Date().getHours();\n");
        html.append("                currentTheme = (hour < 6 || hour >= 18) ? 'dark' : 'light';\n");
        html.append("            }\n");
        html.append("            \n");
        html.append("            // 如果禁用了夜间模式，强制使用浅色主题\n");
        html.append("            if (!enableDarkMode && currentTheme === 'dark') {\n");
        html.append("                currentTheme = 'light';\n");
        html.append("            }\n");
        html.append("            \n");
        html.append("            document.body.classList.remove('theme-light', 'theme-dark');\n");
        html.append("            document.body.classList.add('theme-' + currentTheme);\n");
        html.append("        }\n");
        html.append("        \n");
        html.append("        // 页面加载时初始化主题\n");
        html.append("        initializeAdminTheme();\n");
        html.append("    </script>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <div class=\"container\">\n");
        html.append("        <div class=\"header\">\n");
        html.append("            <h1>🎁 奖励管理系统</h1>\n");
        html.append("            <p>管理抽奖奖励配置</p>\n");
        html.append("        </div>\n");
        html.append("        \n");
        html.append("        <div class=\"content\">\n");
        html.append("            <!-- 当前奖励列表 -->\n");
        html.append("            <div class=\"section\">\n");
        html.append("                <h2>📋 当前奖励列表</h2>\n");
        html.append("                <div id=\"rewardsList\">\n");
        html.append(getCurrentRewardsList());
        html.append("                </div>\n");
        html.append(
                "                <button onclick=\"refreshRewards()\" class=\"btn btn-secondary\">🔄 刷新列表</button>\n");
        html.append("            </div>\n");
        html.append("            \n");
        html.append("            <!-- 添加新奖励 -->\n");
        html.append("            <div class=\"section\">\n");
        html.append("                <h2>➕ 添加新奖励</h2>\n");
        html.append("                <form id=\"addRewardForm\">\n");
        html.append("                    <div class=\"form-row\">\n");
        html.append("                        <div class=\"form-group\">\n");
        html.append("                            <label>奖励ID:</label>\n");
        html.append(
                "                            <input type=\"text\" id=\"rewardId\" placeholder=\"例如: super_diamond\" required>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"form-group\">\n");
        html.append("                            <label>奖励名称:</label>\n");
        html.append(
                "                            <input type=\"text\" id=\"rewardName\" placeholder=\"例如: 超级钻石礼包\" required>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                    \n");
        html.append("                    <div class=\"form-group\">\n");
        html.append("                        <label>奖励描述:</label>\n");
        html.append(
                "                        <textarea id=\"rewardDescription\" placeholder=\"例如: 获得50个钻石和特殊附魔书\" rows=\"2\"></textarea>\n");
        html.append("                    </div>\n");
        html.append("                    \n");
        html.append("                    <div class=\"form-row\">\n");
        html.append("                        <div class=\"form-group\">\n");
        html.append("                            <label>权重:</label>\n");
        html.append(
                "                            <input type=\"number\" id=\"rewardWeight\" value=\"10\" min=\"1\" max=\"100\" required>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"form-group\">\n");
        html.append("                            <label>概率 (%):</label>\n");
        html.append(
                "                            <input type=\"number\" id=\"rewardProbability\" value=\"10.0\" min=\"0.1\" max=\"100\" step=\"0.1\" required>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                    \n");
        html.append("                    <div class=\"form-group\">\n");
        html.append("                        <label>执行命令 (每行一个):</label>\n");
        html.append(
                "                        <textarea id=\"rewardCommands\" placeholder=\"give {player} diamond 50&#10;tellraw {player} {&quot;text&quot;:&quot;恭喜获得超级钻石礼包！&quot;,&quot;color&quot;:&quot;aqua&quot;}\" rows=\"4\"></textarea>\n");
        html.append("                        <small>支持变量: {player}, {player_name}, {world}, {x}, {y}, {z} 等</small>\n");
        html.append("                    </div>\n");
        html.append("                    \n");
        html.append("                    <div class=\"form-actions\">\n");
        html.append("                        <button type=\"submit\" class=\"btn btn-primary\">💾 保存奖励</button>\n");
        html.append(
                "                        <button type=\"button\" onclick=\"clearForm()\" class=\"btn btn-secondary\">🗑️ 清空表单</button>\n");
        html.append("                    </div>\n");
        html.append("                </form>\n");
        html.append("            </div>\n");
        html.append("            \n");
        html.append("            <!-- 变量说明 -->\n");
        html.append("            <div class=\"section\">\n");
        html.append("                <h2>📖 变量说明</h2>\n");
        html.append("                <div class=\"variables-help\">\n");
        html.append("                    <h3>基本变量:</h3>\n");
        html.append("                    <ul>\n");
        html.append("                        <li><code>{player}</code> - 玩家名称</li>\n");
        html.append("                        <li><code>{player_name}</code> - 玩家名称</li>\n");
        html.append("                        <li><code>{player_uuid}</code> - 玩家UUID</li>\n");
        html.append("                        <li><code>{player_displayname}</code> - 玩家显示名称</li>\n");
        html.append("                        <li><code>{world}</code> - 玩家所在世界</li>\n");
        html.append("                        <li><code>{x}</code>, <code>{y}</code>, <code>{z}</code> - 玩家坐标</li>\n");
        html.append("                    </ul>\n");
        html.append("                    <h3>PlaceholderAPI变量:</h3>\n");
        html.append("                    <p>如果安装了PlaceholderAPI，还支持所有PAPI变量，例如:</p>\n");
        html.append("                    <ul>\n");
        html.append("                        <li><code>%player_level%</code> - 玩家等级</li>\n");
        html.append("                        <li><code>%vault_eco_balance%</code> - 玩家金币</li>\n");
        html.append("                        <li><code>%player_health%</code> - 玩家血量</li>\n");
        html.append("                    </ul>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("        \n");
        html.append("        <!-- 结果显示区域 -->\n");
        html.append("        <div id=\"result\" class=\"result\"></div>\n");
        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <script>\n");
        html.append(getRewardManagementJS());
        html.append("    </script>\n");
        html.append("</body>\n");
        html.append("</html>");

        sendHtmlResponse(exchange, 200, html.toString());
    }

    /**
     * 获取当前奖励列表HTML
     */
    private String getCurrentRewardsList() {
        StringBuilder html = new StringBuilder();
        List<LotteryReward> rewards = plugin.getLotteryManager().getAllRewards();

        if (rewards.isEmpty()) {
            html.append("<p class=\"no-rewards\">暂无奖励配置</p>");
        } else {
            html.append("<div class=\"rewards-grid\">");
            for (LotteryReward reward : rewards) {
                html.append("<div class=\"reward-card\" data-reward-id=\"").append(reward.getId()).append("\">");
                html.append("<div class=\"reward-header\">");
                html.append("<h4>").append(escapeHtml(reward.getName())).append("</h4>");
                html.append("<span class=\"reward-probability\">").append(reward.getProbability()).append("%</span>");
                html.append("</div>");
                html.append("<p class=\"reward-description\">").append(escapeHtml(reward.getDescription()))
                        .append("</p>");
                html.append("<div class=\"reward-stats\">");
                html.append("<span class=\"weight\">权重: ").append(reward.getWeight()).append("</span>");
                html.append("<span class=\"commands-count\">指令: ").append(reward.getCommands().size())
                        .append("个</span>");
                html.append("</div>");
                html.append("<div class=\"reward-actions\">");
                html.append("<button onclick=\"editReward('").append(reward.getId())
                        .append("')\" class=\"btn btn-sm btn-primary\">编辑</button>");
                html.append("<button onclick=\"deleteReward('").append(reward.getId())
                        .append("')\" class=\"btn btn-sm btn-danger\">删除</button>");
                html.append("</div>");
                html.append("</div>");
            }
            html.append("</div>");
        }

        return html.toString();
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null)
            return "";
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#39;");
    }

    /**
     * 获取CSS样式
     */
    private String getRewardManagementCSS() {
        return ":root {\n" +
                "    --bg-primary: #ffffff;\n" +
                "    --bg-secondary: #f8f9fa;\n" +
                "    --bg-card: #ffffff;\n" +
                "    --text-primary: #2c3e50;\n" +
                "    --text-secondary: #7f8c8d;\n" +
                "    --text-muted: #adb5bd;\n" +
                "    --border-color: #e9ecef;\n" +
                "    --accent-color: #667eea;\n" +
                "    --shadow-light: rgba(0, 0, 0, 0.1);\n" +
                "    --shadow-medium: rgba(0, 0, 0, 0.15);\n" +
                "    --shadow-heavy: rgba(0, 0, 0, 0.2);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark {\n" +
                "    --bg-primary: #1a202c;\n" +
                "    --bg-secondary: #2d3748;\n" +
                "    --bg-card: #2d3748;\n" +
                "    --text-primary: #f7fafc;\n" +
                "    --text-secondary: #e2e8f0;\n" +
                "    --text-muted: #a0aec0;\n" +
                "    --border-color: #4a5568;\n" +
                "    --accent-color: #667eea;\n" +
                "    --shadow-light: rgba(0, 0, 0, 0.3);\n" +
                "    --shadow-medium: rgba(0, 0, 0, 0.4);\n" +
                "    --shadow-heavy: rgba(0, 0, 0, 0.5);\n" +
                "}\n" +
                "\n" +
                "* { margin: 0; padding: 0; box-sizing: border-box; transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease; }\n"
                +
                "body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; padding: 20px; }\n"
                +
                ".container { max-width: 1200px; margin: 0 auto; background: var(--bg-primary); border-radius: 15px; box-shadow: 0 10px 30px var(--shadow-heavy); overflow: hidden; }\n"
                +
                ".header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }\n"
                +
                ".header h1 { font-size: 2.5em; margin-bottom: 10px; }\n" +
                ".content { padding: 30px; background: var(--bg-primary); }\n" +
                ".section { background: var(--bg-secondary); border-radius: 10px; padding: 25px; margin-bottom: 20px; border-left: 4px solid var(--accent-color); border: 1px solid var(--border-color); }\n"
                +
                ".section h2 { color: var(--text-primary); margin-bottom: 20px; font-size: 1.5em; }\n" +
                ".form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }\n" +
                ".form-group { margin-bottom: 15px; }\n" +
                ".form-group label { display: block; margin-bottom: 5px; color: var(--text-primary); font-weight: bold; }\n"
                +
                ".form-group input, .form-group textarea { width: 100%; padding: 12px; border: 2px solid var(--border-color); border-radius: 6px; font-size: 14px; transition: border-color 0.3s; background: #ffffff; color: var(--text-primary); }\n"
                +
                ".form-group input:focus, .form-group textarea:focus { border-color: var(--accent-color); outline: none; box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1); }\n"
                +
                ".form-group small { color: var(--text-secondary); font-size: 12px; }\n" +
                ".form-actions { display: flex; gap: 10px; margin-top: 20px; }\n" +
                ".btn { padding: 12px 24px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; transition: all 0.3s; margin: 5px; text-decoration: none; display: inline-block; }\n"
                +
                ".btn-primary { background: #667eea; color: white; }\n" +
                ".btn-primary:hover { background: #5a6fd8; transform: translateY(-2px); box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3); }\n"
                +
                ".btn-secondary { background: #6c757d; color: white; }\n" +
                ".btn-secondary:hover { background: #5a6268; }\n" +
                ".rewards-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; }\n"
                +
                ".reward-card { background: var(--bg-card); padding: 20px; border-radius: 12px; border: 1px solid var(--border-color); transition: all 0.3s; position: relative; }\n"
                +
                ".reward-card:hover { transform: translateY(-4px); box-shadow: 0 8px 25px var(--shadow-medium); }\n" +
                ".reward-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }\n"
                +
                ".reward-header h4 { color: var(--text-primary); margin: 0; font-size: 1.2em; }\n" +
                ".reward-probability { background: var(--accent-color); color: white; padding: 4px 8px; border-radius: 12px; font-size: 0.8em; font-weight: bold; }\n"
                +
                ".reward-description { color: var(--text-secondary); margin: 10px 0; font-size: 0.9em; line-height: 1.4; }\n"
                +
                ".reward-stats { display: flex; justify-content: space-between; margin: 15px 0; }\n" +
                ".reward-stats span { background: var(--bg-secondary); padding: 4px 8px; border-radius: 6px; font-size: 0.8em; color: var(--text-secondary); border: 1px solid var(--border-color); }\n"
                +
                "/* 浅色主题下的奖品卡片紫色渐变样式 */\n" +
                "body.theme-light .reward-card, body:not(.theme-dark):not(.theme-light) .reward-card { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; border: 1px solid rgba(255, 255, 255, 0.2) !important; }\n"
                +
                "body.theme-light .reward-header h4, body:not(.theme-dark):not(.theme-light) .reward-header h4 { color: white !important; }\n"
                +
                "body.theme-light .reward-probability, body:not(.theme-dark):not(.theme-light) .reward-probability { background: rgba(255, 255, 255, 0.2) !important; color: white !important; border: 1px solid rgba(255, 255, 255, 0.3) !important; }\n"
                +
                "body.theme-light .reward-description, body:not(.theme-dark):not(.theme-light) .reward-description { color: rgba(255, 255, 255, 0.9) !important; }\n"
                +
                "body.theme-light .reward-stats span, body:not(.theme-dark):not(.theme-light) .reward-stats span { background: rgba(255, 255, 255, 0.15) !important; color: rgba(255, 255, 255, 0.9) !important; border: 1px solid rgba(255, 255, 255, 0.2) !important; }\n"
                +
                ".reward-actions { display: flex; gap: 8px; margin-top: 15px; }\n" +
                ".btn-sm { padding: 6px 12px; font-size: 0.8em; }\n" +
                ".btn-danger { background: #dc3545; color: white; }\n" +
                ".btn-danger:hover { background: #c82333; }\n" +
                ".no-rewards { text-align: center; color: var(--text-secondary); padding: 20px; }\n" +
                ".variables-help { background: #ffffff; padding: 20px; border-radius: 8px; border: 1px solid var(--border-color); }\n"
                +
                ".variables-help h3 { color: var(--text-primary); margin: 15px 0 10px 0; }\n" +
                ".variables-help ul { margin-left: 20px; }\n" +
                ".variables-help li { margin: 5px 0; color: var(--text-secondary); }\n" +
                ".variables-help code { background: var(--bg-secondary); padding: 2px 6px; border-radius: 3px; font-family: monospace; color: var(--text-primary); border: 1px solid var(--border-color); }\n"
                +
                ".result { margin: 20px; padding: 15px; border-radius: 6px; display: none; }\n" +
                ".result.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }\n" +
                ".result.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }\n" +
                ".result.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }";
    }

    /**
     * 获取JavaScript代码
     */
    private String getRewardManagementJS() {
        return "const API_BASE = '/api';\n" +
                "const ADMIN_KEY = new URLSearchParams(window.location.search).get('key');\n" +
                "\n" +
                "function showResult(message, type = 'info') {\n" +
                "    const resultDiv = document.getElementById('result');\n" +
                "    resultDiv.innerHTML = message;\n" +
                "    resultDiv.className = 'result ' + type;\n" +
                "    resultDiv.style.display = 'block';\n" +
                "    setTimeout(() => { resultDiv.style.display = 'none'; }, 8000);\n" +
                "}\n" +
                "\n" +
                "function refreshRewards() {\n" +
                "    showResult('正在刷新奖励列表...', 'info');\n" +
                "    location.reload();\n" +
                "}\n" +
                "\n" +
                "function editReward(rewardId) {\n" +
                "    // 获取奖励详情并填充表单\n" +
                "    fetch(API_BASE, {\n" +
                "        method: 'POST',\n" +
                "        headers: { 'Content-Type': 'application/json' },\n" +
                "        body: JSON.stringify({\n" +
                "            action: 'get_lottery_rewards',\n" +
                "            api_key: ADMIN_KEY\n" +
                "        })\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            const reward = data.rewards.find(r => r.id === rewardId);\n" +
                "            if (reward) {\n" +
                "                document.getElementById('rewardId').value = reward.id;\n" +
                "                document.getElementById('rewardName').value = reward.name;\n" +
                "                document.getElementById('rewardDescription').value = reward.description;\n" +
                "                document.getElementById('rewardWeight').value = reward.weight;\n" +
                "                document.getElementById('rewardProbability').value = reward.probability;\n" +
                "                document.getElementById('rewardCommands').value = reward.commands.join('\\n');\n" +
                "                \n" +
                "                // 滚动到表单\n" +
                "                document.getElementById('addRewardForm').scrollIntoView({ behavior: 'smooth' });\n" +
                "                showResult('奖励信息已加载到表单中', 'info');\n" +
                "            }\n" +
                "        } else {\n" +
                "            showResult('获取奖励信息失败: ' + data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        showResult('网络错误: ' + error.message, 'error');\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "function deleteReward(rewardId) {\n" +
                "    if (!confirm('确定要删除这个奖励吗？此操作不可撤销！')) {\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    // 这里应该调用删除API\n" +
                "    showResult('删除功能开发中...', 'info');\n" +
                "}\n" +
                "\n" +
                "function clearForm() {\n" +
                "    document.getElementById('addRewardForm').reset();\n" +
                "    showResult('表单已清空', 'info');\n" +
                "}\n" +
                "\n" +
                "document.getElementById('addRewardForm').addEventListener('submit', function(e) {\n" +
                "    e.preventDefault();\n" +
                "    \n" +
                "    const formData = {\n" +
                "        action: 'save_lottery_reward',\n" +
                "        api_key: ADMIN_KEY,\n" +
                "        id: document.getElementById('rewardId').value.trim(),\n" +
                "        name: document.getElementById('rewardName').value.trim(),\n" +
                "        description: document.getElementById('rewardDescription').value.trim(),\n" +
                "        weight: parseInt(document.getElementById('rewardWeight').value),\n" +
                "        probability: parseFloat(document.getElementById('rewardProbability').value),\n" +
                "        commands: document.getElementById('rewardCommands').value.split('\\n').filter(cmd => cmd.trim())\n"
                +
                "    };\n" +
                "    \n" +
                "    // 验证表单\n" +
                "    if (!formData.id) {\n" +
                "        showResult('请输入奖励ID', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    if (!formData.name) {\n" +
                "        showResult('请输入奖励名称', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    if (isNaN(formData.weight) || formData.weight < 1) {\n" +
                "        showResult('权重必须是大于0的数字', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    if (isNaN(formData.probability) || formData.probability < 0 || formData.probability > 100) {\n" +
                "        showResult('概率必须是0-100之间的数字', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    showResult('正在保存奖励...', 'info');\n" +
                "    \n" +
                "    fetch(API_BASE, {\n" +
                "        method: 'POST',\n" +
                "        headers: { 'Content-Type': 'application/json' },\n" +
                "        body: JSON.stringify(formData)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            showResult('奖励保存成功！', 'success');\n" +
                "            setTimeout(() => {\n" +
                "                location.reload();\n" +
                "            }, 1500);\n" +
                "        } else {\n" +
                "            showResult('保存失败: ' + data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        showResult('网络错误: ' + error.message, 'error');\n" +
                "    });\n" +
                "});";
    }

    /**
     * 发送HTML响应
     */
    private void sendHtmlResponse(HttpExchange exchange, int statusCode, String html) throws IOException {
        byte[] bytes = html.getBytes(StandardCharsets.UTF_8);

        exchange.getResponseHeaders().set("Content-Type", "text/html; charset=utf-8");
        exchange.getResponseHeaders().set("Cache-Control", "no-cache");

        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }

    /**
     * 发送未授权响应
     */
    private void sendUnauthorizedResponse(HttpExchange exchange) throws IOException {
        String html = "<html><body><h1>401 - 未授权访问</h1><p>请提供有效的管理员密钥</p></body></html>";
        sendHtmlResponse(exchange, 401, html);
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        String html = "<html><body><h1>" + statusCode + " - " + message + "</h1></body></html>";
        sendHtmlResponse(exchange, statusCode, html);
    }
}
