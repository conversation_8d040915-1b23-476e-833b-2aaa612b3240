<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
        }
        
        .status-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .test-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .test-card:hover {
            transform: translateY(-5px);
        }
        
        .test-card h3 {
            color: #2d3748;
            margin-bottom: 15px;
        }
        
        .test-link {
            display: inline-block;
            margin: 5px 10px 5px 0;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .test-link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .player-demo {
            background: #e8f5e8;
            border-left: 4px solid #28a745;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .skin-preview {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }
        
        .skin-item {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .skin-item img {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-online {
            background: #28a745;
        }
        
        .status-offline {
            background: #dc3545;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 服务器功能测试页面</h1>
        
        <div class="status-section">
            <h3>📊 服务器状态</h3>
            <p><span class="status-indicator status-offline"></span>Web服务器: <span id="serverStatus">检测中...</span></p>
            <p><span class="status-indicator status-offline"></span>数据库连接: <span id="dbStatus">检测中...</span></p>
            <p>🌐 测试端口: 8080</p>
            <p>📍 测试地址: http://localhost:8080</p>
        </div>
        
        <div class="test-links">
            <div class="test-card">
                <h3>🎮 玩家页面测试</h3>
                <p>测试 /@PlayerName 路由功能</p>
                <a href="http://localhost:8080/@Moonlads" class="test-link" target="_blank">/@Moonlads</a>
                <a href="http://localhost:8080/@Steve" class="test-link" target="_blank">/@Steve</a>
                <a href="http://localhost:8080/@Alex" class="test-link" target="_blank">/@Alex</a>
                <a href="http://localhost:8080/@Notch" class="test-link" target="_blank">/@Notch</a>
            </div>
            
            <div class="test-card">
                <h3>📋 处罚记录测试</h3>
                <p>测试处罚记录页面功能</p>
                <a href="http://localhost:8080/punishments" class="test-link" target="_blank">所有记录</a>
                <a href="http://localhost:8080/punishments/ban" class="test-link" target="_blank">封禁记录</a>
                <a href="http://localhost:8080/punishments/mute" class="test-link" target="_blank">禁言记录</a>
                <a href="http://localhost:8080/punishments?search=Moonlads&player_view=true" class="test-link" target="_blank">玩家视图</a>
            </div>
            
            <div class="test-card">
                <h3>🔍 搜索功能测试</h3>
                <p>测试搜索和跳转功能</p>
                <a href="http://localhost:8080/punishments?search=Moonlads" class="test-link" target="_blank">搜索 Moonlads</a>
                <a href="http://localhost:8080/punishments?search=Steve" class="test-link" target="_blank">搜索 Steve</a>
                <a href="http://localhost:8080/punishments/ban?search=Moonlads" class="test-link" target="_blank">搜索封禁</a>
            </div>
            
            <div class="test-card">
                <h3>🏠 其他页面</h3>
                <p>测试其他功能页面</p>
                <a href="http://localhost:8080/" class="test-link" target="_blank">首页</a>
                <a href="http://localhost:8080/admin" class="test-link" target="_blank">管理页面</a>
                <a href="http://localhost:8080/user" class="test-link" target="_blank">用户页面</a>
            </div>
        </div>
        
        <div class="player-demo">
            <h3>🖼️ 半身皮肤预览效果</h3>
            <p>这是我们实现的半身皮肤显示效果，类似 next-litebans：</p>
            
            <div class="skin-preview">
                <div class="skin-item">
                    <img src="https://visage.surgeplay.com/bust/192/Moonlads" alt="Moonlads" width="96" height="96">
                    <p><strong>Moonlads</strong></p>
                    <p style="font-size: 0.8em; color: #666;">半身皮肤</p>
                </div>
                <div class="skin-item">
                    <img src="https://visage.surgeplay.com/bust/192/Steve" alt="Steve" width="96" height="96">
                    <p><strong>Steve</strong></p>
                    <p style="font-size: 0.8em; color: #666;">半身皮肤</p>
                </div>
                <div class="skin-item">
                    <img src="https://visage.surgeplay.com/bust/192/Alex" alt="Alex" width="96" height="96">
                    <p><strong>Alex</strong></p>
                    <p style="font-size: 0.8em; color: #666;">半身皮肤</p>
                </div>
            </div>
        </div>
        
        <div class="status-section">
            <h3>🔧 实现说明</h3>
            <p><strong>路由处理：</strong></p>
            <div class="code-block">
// WebHandler.java 中的路由处理
if (path.startsWith("/@") && path.length() > 2) {
    handlePlayerPage(exchange, path.substring(2));
}
            </div>
            
            <p><strong>皮肤 API：</strong></p>
            <div class="code-block">
// 半身皮肤
https://visage.surgeplay.com/bust/512/{playerName}

// 备用皮肤
https://mc-heads.net/body/{playerName}/192
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <button onclick="checkServerStatus()" style="padding: 12px 24px; background: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 1em;">
                🔄 检查服务器状态
            </button>
        </div>
    </div>

    <script>
        // 检查服务器状态
        async function checkServerStatus() {
            const serverStatusEl = document.getElementById('serverStatus');
            const dbStatusEl = document.getElementById('dbStatus');
            
            serverStatusEl.textContent = '检测中...';
            dbStatusEl.textContent = '检测中...';
            
            try {
                const response = await fetch('http://localhost:8080/', { 
                    method: 'GET',
                    mode: 'no-cors'
                });
                
                serverStatusEl.textContent = '✅ 在线';
                serverStatusEl.previousElementSibling.className = 'status-indicator status-online';
                
                // 尝试检查API状态
                try {
                    const apiResponse = await fetch('http://localhost:8080/api/status', {
                        method: 'GET',
                        mode: 'no-cors'
                    });
                    dbStatusEl.textContent = '✅ 连接正常';
                    dbStatusEl.previousElementSibling.className = 'status-indicator status-online';
                } catch (e) {
                    dbStatusEl.textContent = '⚠️ 状态未知';
                }
                
            } catch (error) {
                serverStatusEl.textContent = '❌ 离线';
                dbStatusEl.textContent = '❌ 无法连接';
                console.log('服务器检测:', error);
            }
        }
        
        // 页面加载时检查状态
        window.addEventListener('load', function() {
            setTimeout(checkServerStatus, 1000);
        });
        
        // 测试玩家页面跳转
        function testPlayerPage(playerName) {
            const url = `http://localhost:8080/@${playerName}`;
            window.open(url, '_blank');
        }
    </script>
</body>
</html>
