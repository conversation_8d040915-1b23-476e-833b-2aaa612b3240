# AceKeySystem - PlaceholderAPI 和 Vault 集成指南

## 📋 概述

AceKeySystem 现已支持 PlaceholderAPI 扩展和 Vault 经济系统集成，为您的服务器提供更强大的功能。

## 🔌 PlaceholderAPI 扩展

### 安装要求
- PlaceholderAPI 插件 (版本 2.11.5+)
- AceKeySystem 插件

### 可用占位符

#### 基础积分信息
- `%acekeysystem_points%` - 玩家当前积分
- `%acekeysystem_points_formatted%` - 格式化的积分数量（带千分位分隔符）
- `%acekeysystem_points_rank%` - 玩家积分排名

#### 卡密相关
- `%acekeysystem_total_keys%` - 总卡密数量
- `%acekeysystem_used_keys%` - 已使用卡密数量
- `%acekeysystem_unused_keys%` - 未使用卡密数量
- `%acekeysystem_player_used_keys%` - 玩家使用的卡密数量

#### 积分商店
- `%acekeysystem_shop_items_count%` - 商店物品总数
- `%acekeysystem_player_purchases%` - 玩家购买次数
- `%acekeysystem_pending_purchases%` - 待处理购买数量

#### 绑定状态
- `%acekeysystem_is_bound%` - 是否已绑定（是/否）
- `%acekeysystem_is_bound_en%` - 是否已绑定（Yes/No）
- `%acekeysystem_bind_time%` - 绑定时间

#### 系统状态
- `%acekeysystem_web_server_status%` - Web服务器状态
- `%acekeysystem_web_server_port%` - Web服务器端口
- `%acekeysystem_total_players%` - 总玩家数

#### 动态占位符

##### 商店物品信息
- `%acekeysystem_shop_item_<物品ID>_name%` - 物品名称
- `%acekeysystem_shop_item_<物品ID>_price%` - 物品价格
- `%acekeysystem_shop_item_<物品ID>_price_formatted%` - 格式化价格
- `%acekeysystem_shop_item_<物品ID>_stock%` - 库存数量
- `%acekeysystem_shop_item_<物品ID>_purchased%` - 玩家购买次数
- `%acekeysystem_shop_item_<物品ID>_remaining%` - 剩余购买次数
- `%acekeysystem_shop_item_<物品ID>_available%` - 是否可用

##### 积分排行榜
- `%acekeysystem_top_points_<排名>_name%` - 排行榜玩家名
- `%acekeysystem_top_points_<排名>_points%` - 排行榜玩家积分
- `%acekeysystem_top_points_<排名>_points_formatted%` - 格式化积分

### 使用示例

```yaml
# 在其他插件中使用占位符
scoreboard:
  title: "服务器信息"
  lines:
    - "积分: %acekeysystem_points_formatted%"
    - "排名: #%acekeysystem_points_rank%"
    - "已绑定: %acekeysystem_is_bound%"
    - "第1名: %acekeysystem_top_points_1_name%"
```

## 💰 Vault 经济系统集成

### 安装要求
- Vault 插件
- 任意经济插件（如 EssentialsX、CMI 等）
- AceKeySystem 插件

### 配置选项

在 `config.yml` 中配置兑换设置：

```yaml
vault:
  exchange:
    # 积分兑换金钱
    points-to-money:
      enabled: true
      rate: 0.1  # 1积分 = 0.1金钱
      min-points: 100
      max-points: 10000
    # 金钱兑换积分
    money-to-points:
      enabled: true
      rate: 10.0  # 1金钱 = 10积分
      min-money: 10.0
      max-money: 1000.0
```

### 兑换命令

#### 基本命令
- `/aceexchange` - 显示兑换帮助和当前状态
- `/aceexchange rate` - 查看兑换汇率信息

#### 积分兑换金钱
```
/aceexchange points <积分数量>
```
示例：
- `/aceexchange points 1000` - 将1000积分兑换为金钱

#### 金钱兑换积分
```
/aceexchange money <金钱数量>
```
示例：
- `/aceexchange money 100` - 将100金钱兑换为积分

### 权限
- `acekeysystem.use` - 使用兑换功能的基本权限

### 兑换限制
- 最小/最大兑换数量限制
- 余额/积分不足检查
- 实时汇率显示

## 🚀 功能特性

### PlaceholderAPI 特性
- ✅ 实时数据更新
- ✅ 格式化数字显示
- ✅ 多语言支持
- ✅ 动态占位符
- ✅ 排行榜集成

### Vault 集成特性
- ✅ 双向兑换（积分↔金钱）
- ✅ 可配置汇率
- ✅ 兑换限制
- ✅ 安全验证
- ✅ 事务回滚

## 🔧 故障排除

### PlaceholderAPI 问题
1. 确保 PlaceholderAPI 插件已安装
2. 检查占位符是否正确注册：`/papi list`
3. 重载 PlaceholderAPI：`/papi reload`

### Vault 集成问题
1. 确保 Vault 插件已安装
2. 确保有经济插件（如 EssentialsX）
3. 检查经济系统是否正常：`/money`
4. 查看插件启动日志中的 Vault 集成信息

### 常见错误
- "经济系统集成未启用" - 检查 Vault 和经济插件
- "PlaceholderAPI 扩展注册失败" - 检查 PlaceholderAPI 版本
- "兑换失败" - 检查余额和配置限制

## 📝 更新日志

### v1.0 新增功能
- ✅ PlaceholderAPI 扩展支持
- ✅ Vault 经济系统集成
- ✅ 积分与金钱双向兑换
- ✅ 40+ 个可用占位符
- ✅ 完整的配置选项

## 🎯 未来计划
- 更多占位符支持
- 兑换手续费功能
- 兑换历史记录
- 更多经济插件兼容

---

**注意**: 这些功能需要相应的依赖插件才能正常工作。如果没有安装对应插件，相关功能将被自动禁用。
