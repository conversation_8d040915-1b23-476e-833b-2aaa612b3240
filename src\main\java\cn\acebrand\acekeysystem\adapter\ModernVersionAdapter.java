package cn.acebrand.acekeysystem.adapter;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.Plugin;

/**
 * 现代版本适配器 (Minecraft 1.13及以上)
 * 使用反射访问PersistentDataContainer API处理NBT数据
 * 避免编译时依赖问题
 */
public final class ModernVersionAdapter implements VersionAdapter {

    private final String serverVersion;
    private final Plugin plugin;
    private final boolean apiAvailable;

    // 反射API支持
    private final Class<?> namespacedKeyClass;
    private final Class<?> persistentDataTypeClass;
    private final Class<?> persistentDataContainerClass;
    private final Object stringDataType;
    private final Constructor<?> namespacedKeyConstructor;

    public ModernVersionAdapter(Plugin plugin) {
        String packageName = Bukkit.getServer().getClass().getPackage().getName();
        this.serverVersion = packageName.substring(packageName.lastIndexOf('.') + 1);
        this.plugin = plugin;

        // 尝试加载现代API类
        Class<?> namespacedKeyClass = null;
        Class<?> persistentDataTypeClass = null;
        Class<?> persistentDataContainerClass = null;
        Object stringDataType = null;
        Constructor<?> namespacedKeyConstructor = null;
        boolean apiAvailable = false;

        try {
            // 加载必要的类
            namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            persistentDataTypeClass = Class.forName("org.bukkit.persistence.PersistentDataType");
            persistentDataContainerClass = Class.forName("org.bukkit.persistence.PersistentDataContainer");

            // 获取STRING类型
            stringDataType = persistentDataTypeClass.getField("STRING").get(null);

            // 获取NamespacedKey构造器
            namespacedKeyConstructor = namespacedKeyClass.getConstructor(Plugin.class, String.class);

            // 测试是否可以创建NamespacedKey
            Object testKey = namespacedKeyConstructor.newInstance(plugin, "test");
            apiAvailable = true;

            plugin.getLogger().info("成功加载PersistentDataContainer API (反射模式): " + serverVersion);

        } catch (Exception e) {
            plugin.getLogger().warning("PersistentDataContainer API不可用，将使用降级模式: " + e.getMessage());
            apiAvailable = false;
        }

        // 设置final字段
        this.namespacedKeyClass = namespacedKeyClass;
        this.persistentDataTypeClass = persistentDataTypeClass;
        this.persistentDataContainerClass = persistentDataContainerClass;
        this.stringDataType = stringDataType;
        this.namespacedKeyConstructor = namespacedKeyConstructor;
        this.apiAvailable = apiAvailable;
    }

    @Override
    public String getVersion() {
        return this.serverVersion;
    }

    /**
     * 替代方案：使用物品显示名称存储数据
     */
    private ItemStack setItemDisplayName(ItemStack itemStack, String key, String value) {
        try {
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null) {
                // 使用隐藏的显示名称格式存储数据
                String hiddenData = "§r§0" + key + ":" + value;
                itemMeta.setDisplayName(hiddenData);
                itemStack.setItemMeta(itemMeta);
            }
        } catch (Exception e) {
            this.plugin.getLogger().warning("设置物品显示名称失败: " + e.getMessage());
        }
        return itemStack;
    }

    /**
     * 替代方案：从物品显示名称获取数据
     */
    private String getItemDisplayName(ItemStack itemStack, String key) {
        try {
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null && itemMeta.hasDisplayName()) {
                String displayName = itemMeta.getDisplayName();
                if (displayName.startsWith("§r§0" + key + ":")) {
                    return displayName.substring(("§r§0" + key + ":").length());
                }
            }
        } catch (Exception e) {
            this.plugin.getLogger().warning("获取物品显示名称失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 替代方案：检查物品显示名称是否包含数据
     */
    private boolean hasItemDisplayName(ItemStack itemStack, String key) {
        try {
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null && itemMeta.hasDisplayName()) {
                String displayName = itemMeta.getDisplayName();
                return displayName.startsWith("§r§0" + key + ":");
            }
        } catch (Exception e) {
            this.plugin.getLogger().warning("检查物品显示名称失败: " + e.getMessage());
        }
        return false;
    }

    @Override
    public ItemStack setItemNBT(ItemStack itemStack, String key, String value) {
        if (!apiAvailable) {
            // API不可用，使用物品显示名称作为替代方案
            return setItemDisplayName(itemStack, key, value);
        }

        try {
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null) {
                // 创建NamespacedKey
                Object namespacedKey = this.namespacedKeyConstructor.newInstance(this.plugin, key);

                // 获取PersistentDataContainer - 使用更安全的方法
                Object container = null;
                try {
                    // 尝试直接调用方法
                    Method getContainerMethod = ItemMeta.class.getMethod("getPersistentDataContainer");
                    container = getContainerMethod.invoke(itemMeta);
                } catch (Exception e) {
                    // 如果失败，尝试从具体实现类获取
                    Method getContainerMethod = itemMeta.getClass().getMethod("getPersistentDataContainer");
                    container = getContainerMethod.invoke(itemMeta);
                }

                if (container != null) {
                    // 设置数据
                    Method setMethod = this.persistentDataContainerClass.getMethod("set",
                            this.namespacedKeyClass, this.persistentDataTypeClass, Object.class);
                    setMethod.invoke(container, namespacedKey, this.stringDataType, value);

                    itemStack.setItemMeta(itemMeta);
                }
            }
        } catch (Exception e) {
            // 如果PersistentDataContainer失败，使用替代方案
            this.plugin.getLogger().warning("PersistentDataContainer设置失败，使用替代方案: " + e.getMessage());
            return setItemDisplayName(itemStack, key, value);
        }
        return itemStack;
    }

    @Override
    public String getItemNBT(ItemStack itemStack, String key) {
        if (!apiAvailable) {
            return getItemDisplayName(itemStack, key);
        }

        try {
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null) {
                // 创建NamespacedKey
                Object namespacedKey = this.namespacedKeyConstructor.newInstance(this.plugin, key);

                // 获取PersistentDataContainer - 使用更安全的方法
                Object container = null;
                try {
                    // 尝试直接调用方法
                    Method getContainerMethod = ItemMeta.class.getMethod("getPersistentDataContainer");
                    container = getContainerMethod.invoke(itemMeta);
                } catch (Exception e) {
                    // 如果失败，尝试从具体实现类获取
                    Method getContainerMethod = itemMeta.getClass().getMethod("getPersistentDataContainer");
                    container = getContainerMethod.invoke(itemMeta);
                }

                if (container != null) {
                    // 检查是否存在
                    Method hasMethod = this.persistentDataContainerClass.getMethod("has",
                            this.namespacedKeyClass, this.persistentDataTypeClass);
                    boolean hasData = (Boolean) hasMethod.invoke(container, namespacedKey, this.stringDataType);

                    if (hasData) {
                        // 获取数据
                        Method getMethod = this.persistentDataContainerClass.getMethod("get",
                                this.namespacedKeyClass, this.persistentDataTypeClass);
                        return (String) getMethod.invoke(container, namespacedKey, this.stringDataType);
                    }
                }
            }
        } catch (Exception e) {
            // 如果PersistentDataContainer失败，使用替代方案
            this.plugin.getLogger().warning("PersistentDataContainer获取失败，使用替代方案: " + e.getMessage());
            return getItemDisplayName(itemStack, key);
        }
        return null;
    }

    @Override
    public boolean hasItemNBT(ItemStack itemStack, String key) {
        if (!apiAvailable) {
            return hasItemDisplayName(itemStack, key);
        }

        try {
            ItemMeta itemMeta = itemStack.getItemMeta();
            if (itemMeta != null) {
                // 创建NamespacedKey
                Object namespacedKey = this.namespacedKeyConstructor.newInstance(this.plugin, key);

                // 获取PersistentDataContainer - 使用更安全的方法
                Object container = null;
                try {
                    // 尝试直接调用方法
                    Method getContainerMethod = ItemMeta.class.getMethod("getPersistentDataContainer");
                    container = getContainerMethod.invoke(itemMeta);
                } catch (Exception e) {
                    // 如果失败，尝试从具体实现类获取
                    Method getContainerMethod = itemMeta.getClass().getMethod("getPersistentDataContainer");
                    container = getContainerMethod.invoke(itemMeta);
                }

                if (container != null) {
                    // 检查是否存在
                    Method hasMethod = this.persistentDataContainerClass.getMethod("has",
                            this.namespacedKeyClass, this.persistentDataTypeClass);
                    return (Boolean) hasMethod.invoke(container, namespacedKey, this.stringDataType);
                }
            }
        } catch (Exception e) {
            // 如果PersistentDataContainer失败，使用替代方案
            this.plugin.getLogger().warning("PersistentDataContainer检查失败，使用替代方案: " + e.getMessage());
            return hasItemDisplayName(itemStack, key);
        }
        return false;
    }

    @Override
    public void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            // 尝试使用现代API
            Method sendTitleMethod = Player.class.getMethod("sendTitle", String.class, String.class, int.class,
                    int.class, int.class);
            sendTitleMethod.invoke(player, title, subtitle, fadeIn, stay, fadeOut);
        } catch (Exception e) {
            // 降级到基础API
            try {
                Method sendTitleMethod = Player.class.getMethod("sendTitle", String.class, String.class);
                sendTitleMethod.invoke(player, title, subtitle);
            } catch (Exception ex) {
                // 最后降级到聊天消息
                player.sendMessage(title);
                if (subtitle != null && !subtitle.isEmpty()) {
                    player.sendMessage(subtitle);
                }
            }
        }
    }

    @Override
    public void sendClickableLink(Player player, String url, String displayText) {
        try {
            // 使用反射创建可点击的文本组件
            Class<?> componentClass = Class.forName("net.md_5.bungee.api.chat.TextComponent");
            Class<?> clickEventClass = Class.forName("net.md_5.bungee.api.chat.ClickEvent");
            Class<?> clickActionClass = Class.forName("net.md_5.bungee.api.chat.ClickEvent$Action");
            Class<?> hoverEventClass = Class.forName("net.md_5.bungee.api.chat.HoverEvent");
            Class<?> hoverActionClass = Class.forName("net.md_5.bungee.api.chat.HoverEvent$Action");

            // 创建TextComponent
            Object textComponent = componentClass.getConstructor(String.class).newInstance(displayText);

            // 创建ClickEvent (使用OPEN_URL)
            Object openUrlAction = clickActionClass.getField("OPEN_URL").get(null);
            Object clickEvent = clickEventClass.getConstructor(clickActionClass, String.class)
                    .newInstance(openUrlAction, url);

            // 创建HoverEvent (显示提示文本)
            Object showTextAction = hoverActionClass.getField("SHOW_TEXT").get(null);
            Object hoverText = componentClass.getConstructor(String.class).newInstance("§a点击打开抽奖网站\n§7" + url);
            Object hoverEvent = hoverEventClass.getConstructor(hoverActionClass, componentClass)
                    .newInstance(showTextAction, hoverText);

            // 设置点击事件和悬停事件
            Method setClickEventMethod = componentClass.getMethod("setClickEvent", clickEventClass);
            Method setHoverEventMethod = componentClass.getMethod("setHoverEvent", hoverEventClass);
            setClickEventMethod.invoke(textComponent, clickEvent);
            setHoverEventMethod.invoke(textComponent, hoverEvent);

            // 发送消息
            Method spigotMethod = Player.class.getMethod("spigot");
            Object spigot = spigotMethod.invoke(player);
            Method sendMessageMethod = spigot.getClass().getMethod("sendMessage",
                    Class.forName("net.md_5.bungee.api.chat.BaseComponent"));
            sendMessageMethod.invoke(spigot, textComponent);

        } catch (Exception e) {
            // 如果失败，发送普通消息和复制命令
            player.sendMessage(displayText);
            player.sendMessage("§7如果链接无法点击，请手动复制上方链接到浏览器");
            plugin.getLogger().warning("发送可点击链接失败，已降级为普通消息: " + e.getMessage());
        }
    }
}
