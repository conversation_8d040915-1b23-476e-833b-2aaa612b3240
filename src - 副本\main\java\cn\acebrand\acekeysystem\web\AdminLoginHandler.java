package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 管理员登录处理器
 * 负责处理管理员登录验证和会话管理
 */
public class AdminLoginHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;

    // 存储活跃的会话
    private static final Map<String, AdminSession> activeSessions = new ConcurrentHashMap<>();

    // 会话过期时间（24小时）
    private static final long SESSION_TIMEOUT = 24 * 60 * 60 * 1000;

    public AdminLoginHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();

        try {
            if ("GET".equals(method)) {
                if ("/admin".equals(path)) {
                    handleAdminPage(exchange);
                } else if ("/admin/login".equals(path)) {
                    handleLoginPage(exchange);
                } else if ("/admin/online-users".equals(path)) {
                    handleOnlineUsersAPI(exchange);
                } else if ("/admin/session-check".equals(path)) {
                    handleSessionCheck(exchange);
                } else {
                    send404(exchange);
                }
            } else if ("POST".equals(method)) {
                if ("/admin/login".equals(path)) {
                    handleLoginSubmit(exchange);
                } else if ("/admin/logout".equals(path)) {
                    handleLogout(exchange);
                } else if ("/admin/kick-session".equals(path)) {
                    handleKickSession(exchange);
                } else {
                    send404(exchange);
                }
            } else {
                send405(exchange);
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理管理员请求时出错: " + e.getMessage());
            e.printStackTrace();
            send500(exchange, "服务器内部错误");
        }
    }

    /**
     * 处理管理员主页面访问
     */
    private void handleAdminPage(HttpExchange exchange) throws IOException {
        // 检查会话
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null && isValidSession(sessionId)) {
            // 会话有效，转发到AdminHandler处理
            AdminHandler adminHandler = new AdminHandler(plugin, webServer);
            adminHandler.handle(exchange);
        } else {
            // 会话无效，重定向到登录页面
            exchange.getResponseHeaders().set("Location", "/admin/login");
            sendResponse(exchange, 302, "text/plain", "Redirecting to login...");
        }
    }

    /**
     * 处理登录页面显示
     */
    private void handleLoginPage(HttpExchange exchange) throws IOException {
        String loginHtml = generateLoginPage();
        sendResponse(exchange, 200, "text/html", loginHtml);
    }

    /**
     * 处理登录提交
     */
    private void handleLoginSubmit(HttpExchange exchange) throws IOException {
        InputStream inputStream = exchange.getRequestBody();
        StringBuilder sb = new StringBuilder();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            sb.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
        }
        String requestBody = sb.toString();

        try {
            JSONParser parser = new JSONParser();
            JSONObject loginData = (JSONObject) parser.parse(requestBody);

            String loginType = (String) loginData.get("loginType");
            boolean isValid = false;
            String username = null;

            JSONObject response = new JSONObject();

            if ("apiKey".equals(loginType)) {
                // API密钥登录
                String apiKey = (String) loginData.get("apiKey");
                isValid = webServer.getAdminKey().equals(apiKey);
                username = "admin"; // API密钥登录使用默认用户名

                if (isValid && username != null) {
                    // 获取客户端IP地址
                    String clientIP = getClientIP(exchange);

                    // 创建会话，传入用户名和IP地址
                    String sessionId = createSession(username, clientIP);

                    // 更新账号的最后登录时间和IP
                    updateLastLogin(username, clientIP);

                    // 设置会话Cookie
                    String cookieValue = "ADMIN_SESSION=" + sessionId + "; Path=/; HttpOnly; Max-Age="
                            + (SESSION_TIMEOUT / 1000);
                    exchange.getResponseHeaders().add("Set-Cookie", cookieValue);

                    response.put("success", true);
                    response.put("message", "登录成功");
                    response.put("redirectUrl", "/admin");
                } else {
                    response.put("success", false);
                    response.put("message", "API密钥无效");
                }
            } else if ("password".equals(loginType)) {
                // 账号密码登录
                username = (String) loginData.get("username");
                String password = (String) loginData.get("password");

                plugin.getLogger().info("处理账号密码登录: " + username);
                AccountManagementHandler.LoginResult loginResult = validateUserPasswordWithDetails(username, password);
                plugin.getLogger().info("登录结果: success=" + loginResult.success + ", errorType=" + loginResult.errorType
                        + ", message=" + loginResult.message);

                if (loginResult.success) {
                    // 获取客户端IP地址
                    String clientIP = getClientIP(exchange);

                    // 创建会话，传入用户名和IP地址
                    String sessionId = createSession(username, clientIP);

                    // 更新账号的最后登录时间和IP
                    updateLastLogin(username, clientIP);

                    // 设置会话Cookie
                    String cookieValue = "ADMIN_SESSION=" + sessionId + "; Path=/; HttpOnly; Max-Age="
                            + (SESSION_TIMEOUT / 1000);
                    exchange.getResponseHeaders().add("Set-Cookie", cookieValue);

                    response.put("success", true);
                    response.put("message", loginResult.message);
                    response.put("redirectUrl", "/admin");
                } else {
                    response.put("success", false);
                    response.put("message", loginResult.message);
                    response.put("errorType", loginResult.errorType);

                    // 如果是封禁信息，添加详细的封禁信息
                    if ("ACCOUNT_BANNED".equals(loginResult.errorType) && loginResult.banInfo != null) {
                        response.put("banInfo", loginResult.banInfo);
                    }
                }
            } else {
                response.put("success", false);
                response.put("message", "无效的登录类型");
            }

            sendResponse(exchange, 200, "application/json", response.toString());

        } catch (Exception e) {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "登录请求处理失败");
            sendResponse(exchange, 400, "application/json", response.toString());
        }
    }

    /**
     * 处理登出
     */
    private void handleLogout(HttpExchange exchange) throws IOException {
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null) {
            activeSessions.remove(sessionId);
        }

        // 清除Cookie
        exchange.getResponseHeaders().add("Set-Cookie", "ADMIN_SESSION=; Path=/; HttpOnly; Max-Age=0");
        exchange.getResponseHeaders().set("Location", "/admin/login");
        sendResponse(exchange, 302, "text/plain", "Logged out");
    }

    /**
     * 处理在线用户查询API
     */
    private void handleOnlineUsersAPI(HttpExchange exchange) throws IOException {
        // 验证管理员权限
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId == null || !isValidSession(sessionId)) {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "未授权访问");
            sendResponse(exchange, 401, "application/json", response.toString());
            return;
        }

        JSONObject response = new JSONObject();
        JSONArray onlineUsers = new JSONArray();

        // 清理过期会话
        cleanExpiredSessions();

        // 获取当前时间
        long currentTime = System.currentTimeMillis();

        // 遍历所有活跃会话
        for (Map.Entry<String, AdminSession> entry : activeSessions.entrySet()) {
            AdminSession session = entry.getValue();
            JSONObject userInfo = new JSONObject();

            userInfo.put("sessionId", session.getSessionId());
            userInfo.put("username", session.getUsername());
            userInfo.put("clientIP", session.getClientIP());
            userInfo.put("loginTime", session.getCreatedTime());
            userInfo.put("lastAccessTime", session.getLastAccessTime());
            userInfo.put("onlineDuration", currentTime - session.getCreatedTime());
            userInfo.put("isCurrentSession", sessionId.equals(session.getSessionId()));

            onlineUsers.add(userInfo);
        }

        response.put("success", true);
        response.put("onlineUsers", onlineUsers);
        response.put("totalCount", onlineUsers.size());
        response.put("currentTime", currentTime);

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理会话状态检查API
     */
    private void handleSessionCheck(HttpExchange exchange) throws IOException {
        String sessionId = getSessionFromCookie(exchange);
        JSONObject response = new JSONObject();

        if (sessionId == null) {
            response.put("valid", false);
            response.put("reason", "no_session");
        } else if (!activeSessions.containsKey(sessionId)) {
            response.put("valid", false);
            response.put("reason", "kicked"); // 会话不存在，可能被踢出
        } else if (!isValidSession(sessionId)) {
            response.put("valid", false);
            response.put("reason", "expired"); // 会话过期
        } else {
            response.put("valid", true);
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理踢出会话API
     */
    private void handleKickSession(HttpExchange exchange) throws IOException {
        // 验证管理员权限
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId == null || !isValidSession(sessionId)) {
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "未授权访问");
            sendResponse(exchange, 401, "application/json", response.toString());
            return;
        }

        // 读取请求体
        InputStream inputStream = exchange.getRequestBody();
        StringBuilder sb = new StringBuilder();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            sb.append(new String(buffer, 0, bytesRead, StandardCharsets.UTF_8));
        }
        String requestBody = sb.toString();

        JSONObject response = new JSONObject();

        try {
            JSONParser parser = new JSONParser();
            JSONObject requestData = (JSONObject) parser.parse(requestBody);
            String targetSessionId = (String) requestData.get("sessionId");

            if (targetSessionId == null || targetSessionId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "会话ID不能为空");
            } else if (sessionId.equals(targetSessionId)) {
                response.put("success", false);
                response.put("message", "不能踢出自己的会话");
            } else if (!activeSessions.containsKey(targetSessionId)) {
                response.put("success", false);
                response.put("message", "会话不存在或已过期");
            } else {
                // 获取被踢出的用户信息
                AdminSession targetSession = activeSessions.get(targetSessionId);
                String targetUsername = targetSession.getUsername();

                // 移除会话
                activeSessions.remove(targetSessionId);

                response.put("success", true);
                response.put("message", "已成功踢出用户: " + targetUsername);

                plugin.getLogger().info("管理员踢出会话: " + targetUsername + " (会话ID: " + targetSessionId + ")");
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "请求处理失败: " + e.getMessage());
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 验证用户名密码
     */
    private boolean validateUserPassword(String username, String password) {
        // 首先尝试使用新的账号管理系统
        AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);
        if (accountHandler.validatePassword(username, password)) {
            return true;
        }

        // 如果新系统验证失败，回退到配置文件验证（向后兼容）
        String configUsername = plugin.getConfig().getString("admin.username", "admin");
        String configPassword = plugin.getConfig().getString("admin.password", "admin123");

        return configUsername.equals(username) && configPassword.equals(password);
    }

    /**
     * 验证用户名密码并返回详细信息
     */
    private AccountManagementHandler.LoginResult validateUserPasswordWithDetails(String username, String password) {
        // 首先尝试使用新的账号管理系统
        AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);
        AccountManagementHandler.LoginResult result = accountHandler.validatePasswordWithDetails(username, password);

        if (result.success) {
            return result;
        }

        // 如果是封禁或禁用等特殊错误类型，直接返回，不进行回退
        if ("ACCOUNT_BANNED".equals(result.errorType) ||
                "ACCOUNT_DISABLED".equals(result.errorType) ||
                "ACCOUNT_NOT_FOUND".equals(result.errorType)) {
            return result;
        }

        // 只有在密码错误的情况下才回退到配置文件验证（向后兼容）
        String configUsername = plugin.getConfig().getString("admin.username", "admin");
        String configPassword = plugin.getConfig().getString("admin.password", "admin123");

        AccountManagementHandler.LoginResult fallbackResult = new AccountManagementHandler.LoginResult();
        if (configUsername.equals(username) && configPassword.equals(password)) {
            fallbackResult.success = true;
            fallbackResult.message = "登录成功";
        } else {
            fallbackResult.success = false;
            fallbackResult.message = "用户名或密码错误";
            fallbackResult.errorType = "INVALID_CREDENTIALS";
        }

        return fallbackResult;
    }

    /**
     * 创建新会话
     */
    private String createSession(String username, String clientIP) {
        // 实现单点登录：踢出该用户的其他会话
        int kickedSessions = kickUserOtherSessions(username);
        if (kickedSessions > 0) {
            plugin.getLogger().info("用户 " + username + " 在新位置登录，已踢出 " + kickedSessions + " 个其他会话");
        }

        String sessionId = UUID.randomUUID().toString();
        AdminSession session = new AdminSession(sessionId, username, System.currentTimeMillis(), clientIP);
        activeSessions.put(sessionId, session);

        // 清理过期会话
        cleanExpiredSessions();

        plugin.getLogger().info("为用户 " + username + " 创建新会话: " + sessionId + " (IP: " + clientIP + ")");
        return sessionId;
    }

    /**
     * 从Cookie中获取会话ID
     */
    private String getSessionFromCookie(HttpExchange exchange) {
        String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
        if (cookieHeader != null) {
            String[] cookies = cookieHeader.split(";");
            for (String cookie : cookies) {
                String[] parts = cookie.trim().split("=", 2);
                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                    return parts[1];
                }
            }
        }
        return null;
    }

    /**
     * 清理过期会话
     */
    private void cleanExpiredSessions() {
        long currentTime = System.currentTimeMillis();
        activeSessions.entrySet().removeIf(entry -> currentTime - entry.getValue().getCreatedTime() > SESSION_TIMEOUT);
    }

    /**
     * 获取查询参数
     */
    private String getQueryParam(HttpExchange exchange, String paramName) {
        String query = exchange.getRequestURI().getQuery();
        if (query != null) {
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=", 2);
                if (keyValue.length == 2 && paramName.equals(keyValue[0])) {
                    return keyValue[1];
                }
            }
        }
        return null;
    }

    /**
     * 生成登录页面HTML
     */
    private String generateLoginPage() {
        // 获取logo设置
        String logoUrl = plugin.getConfig().getString("web-server.login-logo-url", "");
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>管理员登录 - AceKey系统</title>\n" +
                "    <style>\n" +
                generateLoginCSS() +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <!-- 动态粒子背景 -->\n" +
                "    <div class=\"particles-container\" id=\"particles\"></div>\n" +
                "    \n" +
                "    <!-- 光晕效果 -->\n" +
                "    <div class=\"aurora-effects\">\n" +
                "        <div class=\"aurora aurora-1\"></div>\n" +
                "        <div class=\"aurora aurora-2\"></div>\n" +
                "        <div class=\"aurora aurora-3\"></div>\n" +
                "    </div>\n" +
                "    \n" +
                "    <!-- 浮动装饰元素 -->\n" +
                "    <div class=\"floating-decorations\">\n" +
                "        <div class=\"decoration decoration-1\"></div>\n" +
                "        <div class=\"decoration decoration-2\"></div>\n" +
                "        <div class=\"decoration decoration-3\"></div>\n" +
                "        <div class=\"decoration decoration-4\"></div>\n" +
                "    </div>\n" +
                "    \n" +
                "    <div class=\"login-container\">\n" +
                "        <div class=\"login-card\">\n" +
                "            <div class=\"login-header\">\n" +
                "                <div class=\"logo-container\">\n" +
                "                    <div class=\"logo\">" +
                (logoUrl.isEmpty() ? "🔐"
                        : "<img src=\"" + logoUrl
                                + "\" alt=\"Logo\" onerror=\"this.style.display='none'; this.parentNode.innerHTML='🔐';\">")
                +
                "</div>\n" +
                "                </div>\n" +
                "                <h1>管理员登录</h1>\n" +
                "                <p>欢迎回来，请选择登录方式</p>\n" +
                "            </div>\n" +
                "            \n" +
                "            <div class=\"login-tabs\">\n" +
                "                <button class=\"tab-btn active\" onclick=\"switchTab('apiKey')\">API密钥登录</button>\n" +
                "                <button class=\"tab-btn\" onclick=\"switchTab('password')\">账号密码登录</button>\n" +
                "            </div>\n" +
                "            \n" +
                "            <form id=\"loginForm\">\n" +
                "                <!-- API密钥登录 -->\n" +
                "                <div id=\"apiKeyTab\" class=\"tab-content active\">\n" +
                "                    <div class=\"form-group\">\n" +
                "                        <label for=\"apiKey\">API密钥:</label>\n" +
                "                        <input type=\"password\" id=\"apiKey\" name=\"apiKey\" placeholder=\"请输入API密钥\" required>\n"
                +
                "                    </div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <!-- 账号密码登录 -->\n" +
                "                <div id=\"passwordTab\" class=\"tab-content\">\n" +
                "                    <div class=\"form-group\">\n" +
                "                        <label for=\"username\">用户名:</label>\n" +
                "                        <input type=\"text\" id=\"username\" name=\"username\" placeholder=\"请输入用户名\">\n"
                +
                "                    </div>\n" +
                "                    <div class=\"form-group\">\n" +
                "                        <label for=\"password\">密码:</label>\n" +
                "                        <input type=\"password\" id=\"password\" name=\"password\" placeholder=\"请输入密码\">\n"
                +
                "                    </div>\n" +
                "                </div>\n" +
                "                \n" +
                "                <button type=\"submit\" class=\"login-btn\">🚀 登录</button>\n" +
                "            </form>\n" +
                "            \n" +
                "            <div id=\"message\" class=\"message\"></div>\n" +
                "        </div>\n" +
                "    </div>\n" +
                "    \n" +
                "    <script>\n" +
                generateLoginJS() +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
    }

    // 辅助方法
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String response)
            throws IOException {
        exchange.getResponseHeaders().set("Content-Type", contentType + "; charset=UTF-8");
        exchange.sendResponseHeaders(statusCode, response.getBytes(StandardCharsets.UTF_8).length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(response.getBytes(StandardCharsets.UTF_8));
        }
    }

    private void send404(HttpExchange exchange) throws IOException {
        sendResponse(exchange, 404, "text/plain", "404 Not Found");
    }

    private void send405(HttpExchange exchange) throws IOException {
        sendResponse(exchange, 405, "text/plain", "405 Method Not Allowed");
    }

    private void send500(HttpExchange exchange, String message) throws IOException {
        sendResponse(exchange, 500, "text/plain", "500 " + message);
    }

    /**
     * 管理员会话类
     */
    private static class AdminSession {
        private final String sessionId;
        private final String username;
        private final long createdTime;
        private final String clientIP;
        private long lastAccessTime;

        public AdminSession(String sessionId, String username, long createdTime, String clientIP) {
            this.sessionId = sessionId;
            this.username = username;
            this.createdTime = createdTime;
            this.clientIP = clientIP;
            this.lastAccessTime = createdTime;
        }

        public String getSessionId() {
            return sessionId;
        }

        public String getUsername() {
            return username;
        }

        public long getCreatedTime() {
            return createdTime;
        }

        public long getLastAccessTime() {
            return lastAccessTime;
        }

        public void updateLastAccess() {
            this.lastAccessTime = System.currentTimeMillis();
        }

        public String getClientIP() {
            return clientIP;
        }
    }

    /**
     * 检查会话是否有效（供其他类使用）
     */
    public boolean isValidSession(String sessionId) {
        AdminSession session = activeSessions.get(sessionId);
        if (session == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() - session.getCreatedTime() > SESSION_TIMEOUT) {
            activeSessions.remove(sessionId);
            return false;
        }

        // 更新最后访问时间
        session.updateLastAccess();
        return true;
    }

    /**
     * 检查会话是否有效（静态方法，向后兼容）
     */
    public static boolean isValidAdminSession(String sessionId) {
        AdminSession session = activeSessions.get(sessionId);
        if (session == null) {
            return false;
        }

        // 检查是否过期
        if (System.currentTimeMillis() - session.getCreatedTime() > SESSION_TIMEOUT) {
            activeSessions.remove(sessionId);
            return false;
        }

        return true;
    }

    /**
     * 从会话ID获取用户名
     */
    public String getUsernameFromSession(String sessionId) {
        AdminSession session = activeSessions.get(sessionId);
        if (session == null) {
            return null;
        }

        // 检查是否过期
        if (System.currentTimeMillis() - session.getCreatedTime() > SESSION_TIMEOUT) {
            activeSessions.remove(sessionId);
            return null;
        }

        return session.getUsername();
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIP(HttpExchange exchange) {
        // 首先检查X-Forwarded-For头（代理服务器）
        String xForwardedFor = exchange.getRequestHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            // X-Forwarded-For可能包含多个IP，取第一个
            return xForwardedFor.split(",")[0].trim();
        }

        // 检查X-Real-IP头
        String xRealIP = exchange.getRequestHeaders().getFirst("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty()) {
            return xRealIP.trim();
        }

        // 获取远程地址
        String remoteAddress = exchange.getRemoteAddress().getAddress().getHostAddress();

        // 如果是IPv6的本地地址，转换为IPv4
        if ("0:0:0:0:0:0:0:1".equals(remoteAddress)) {
            return "127.0.0.1";
        }

        return remoteAddress;
    }

    /**
     * 更新账号最后登录时间和IP
     */
    private void updateLastLogin(String username, String clientIP) {
        try {
            AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);
            accountHandler.updateLastLogin(username, clientIP);
        } catch (Exception e) {
            plugin.getLogger().warning("更新最后登录信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户的活跃会话信息
     */
    public static Map<String, Object> getUserSessionInfo(String username) {
        Map<String, Object> sessionInfo = new HashMap<>();
        Map<String, Integer> ipCounts = new HashMap<>();
        int totalSessions = 0;

        for (AdminSession session : activeSessions.values()) {
            if (session.getUsername().equals(username)) {
                totalSessions++;
                String ip = session.getClientIP();
                ipCounts.put(ip, ipCounts.getOrDefault(ip, 0) + 1);
            }
        }

        sessionInfo.put("totalSessions", totalSessions);
        sessionInfo.put("ipCounts", ipCounts);
        sessionInfo.put("uniqueIPs", ipCounts.size());

        return sessionInfo;
    }

    /**
     * 踢出指定用户在指定IP上的会话
     * 如果ip为null，则踢出该用户的所有会话
     */
    public static int kickUserSessions(String username, String ip) {
        int kickedCount = 0;
        Iterator<Map.Entry<String, AdminSession>> iterator = activeSessions.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, AdminSession> entry = iterator.next();
            AdminSession session = entry.getValue();

            if (session.getUsername().equals(username)) {
                if (ip == null || session.getClientIP().equals(ip)) {
                    iterator.remove();
                    kickedCount++;
                }
            }
        }

        return kickedCount;
    }

    /**
     * 踢出指定IP的所有会话
     */
    public static int kickIPSessions(String ip) {
        int kickedCount = 0;
        Iterator<Map.Entry<String, AdminSession>> iterator = activeSessions.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, AdminSession> entry = iterator.next();
            AdminSession session = entry.getValue();

            if (session.getClientIP().equals(ip)) {
                iterator.remove();
                kickedCount++;
            }
        }

        return kickedCount;
    }

    /**
     * 生成登录页面CSS样式
     */
    private String generateLoginCSS() {
        // 获取背景图设置
        String backgroundImage = plugin.getConfig().getString("web-server.login-background-image", "");
        String backgroundStyle;

        if (backgroundImage.isEmpty()) {
            backgroundStyle = "background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);";
        } else {
            // 获取透明度设置
            int opacity = plugin.getConfig().getInt("web-server.login-background-opacity", 30);
            // 将透明度转换为0-1之间的值
            double opacityValue = opacity / 100.0;

            backgroundStyle = "background: linear-gradient(rgba(79, 70, 229, " + opacityValue + "), rgba(124, 58, 237, "
                    + (opacityValue + 0.1) + ")), url('" + backgroundImage
                    + "') center/cover fixed; background-size: cover;";
        }

        return "* { margin: 0; padding: 0; box-sizing: border-box; }\n" +
                "body { font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif; "
                + backgroundStyle
                + " min-height: 100vh; display: flex; align-items: center; justify-content: center; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; position: relative; overflow: hidden; }\n"
                +
                "body::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: "
                + (backgroundImage.isEmpty()
                        ? "radial-gradient(circle at 20% 80%, rgba(79, 70, 229, 0.15) 0%, transparent 60%), radial-gradient(circle at 80% 20%, rgba(124, 58, 237, 0.15) 0%, transparent 60%), radial-gradient(circle at 40% 40%, rgba(236, 72, 153, 0.15) 0%, transparent 60%)"
                        : "linear-gradient(135deg, rgba(0,0,0,0.2) 0%, rgba(0,0,0,0.1) 100%)")
                + "; z-index: 0; }\n"

                +
                "/* 粒子系统 */\n" +
                ".particles-container { position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1; }\n"
                +
                ".particle { position: absolute; width: 3px; height: 3px; background: rgba(255, 255, 255, 0.4); border-radius: 50%; animation: particleFloat 20s linear infinite; }\n"
                +
                "@keyframes particleFloat { 0% { transform: translateY(100vh) translateX(0) scale(0); opacity: 0; } 10% { opacity: 1; } 90% { opacity: 1; } 100% { transform: translateY(-10vh) translateX(50px) scale(1); opacity: 0; } }\n"
                +
                "/* 光晕效果 */\n" +
                ".aurora-effects { position: fixed; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1; }\n"
                +
                ".aurora { position: absolute; border-radius: 50%; filter: blur(40px); opacity: 0.3; animation: auroraMove 15s ease-in-out infinite; }\n"
                +
                ".aurora-1 { width: 400px; height: 400px; background: radial-gradient(circle, rgba(79, 70, 229, 0.3), transparent); top: -200px; left: -200px; animation-delay: 0s; }\n"
                +
                ".aurora-2 { width: 300px; height: 300px; background: radial-gradient(circle, rgba(236, 72, 153, 0.3), transparent); top: 50%; right: -150px; animation-delay: 5s; }\n"
                +
                ".aurora-3 { width: 350px; height: 350px; background: radial-gradient(circle, rgba(124, 58, 237, 0.3), transparent); bottom: -175px; left: 30%; animation-delay: 10s; }\n"
                +
                "@keyframes auroraMove { 0%, 100% { transform: translate(0, 0) scale(1); } 33% { transform: translate(100px, -50px) scale(1.2); } 66% { transform: translate(-50px, 100px) scale(0.8); } }\n"
                +
                "/* 浮动装饰元素 */\n" +
                ".floating-decorations { position: fixed; top: 0; left: 0; width: 100%; height: 100%; overflow: hidden; z-index: 1; pointer-events: none; }\n"
                +
                ".decoration { position: absolute; opacity: 0.1; animation: decorationFloat 12s ease-in-out infinite; }\n"
                +
                ".decoration-1 { top: 15%; left: 10%; width: 60px; height: 60px; background: linear-gradient(45deg, rgba(79, 70, 229, 0.3), rgba(124, 58, 237, 0.3)); border-radius: 50%; animation-delay: 0s; }\n"
                +
                ".decoration-2 { top: 60%; right: 15%; width: 80px; height: 80px; background: linear-gradient(45deg, rgba(236, 72, 153, 0.3), rgba(79, 70, 229, 0.3)); border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; animation-delay: 4s; }\n"
                +
                ".decoration-3 { bottom: 25%; left: 20%; width: 50px; height: 50px; background: linear-gradient(45deg, rgba(124, 58, 237, 0.3), rgba(236, 72, 153, 0.3)); transform: rotate(45deg); animation-delay: 8s; }\n"
                +
                ".decoration-4 { top: 30%; right: 30%; width: 70px; height: 70px; background: linear-gradient(45deg, rgba(79, 70, 229, 0.2), rgba(236, 72, 153, 0.2)); border-radius: 20px; animation-delay: 2s; }\n"
                +
                "@keyframes decorationFloat { 0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); opacity: 0.1; } 50% { transform: translateY(-20px) rotate(180deg) scale(1.1); opacity: 0.2; } }\n"
                +
                ".login-container { width: 100%; max-width: 450px; padding: 20px; position: relative; z-index: 10; animation: cardEntrance 1.2s cubic-bezier(0.4, 0, 0.2, 1); }\n"
                +
                "@keyframes cardEntrance { 0% { opacity: 0; transform: translateY(60px) scale(0.8) rotateX(15deg); } 100% { opacity: 1; transform: translateY(0) scale(1) rotateX(0deg); } }\n"
                +
                ".login-card { background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(25px); border-radius: 32px; box-shadow: 0 40px 100px rgba(0,0,0,0.2), 0 0 0 1px rgba(255,255,255,0.1), inset 0 1px 0 rgba(255,255,255,0.2); overflow: hidden; border: 1px solid rgba(255,255,255,0.15); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); position: relative; transform-style: preserve-3d; }\n"
                +
                ".login-card::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 50%, rgba(255,255,255,0.1) 100%); opacity: 0; transition: opacity 0.4s ease; border-radius: 32px; }\n"
                +
                ".login-card::after { content: ''; position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px; background: linear-gradient(135deg, rgba(79, 70, 229, 0.3), rgba(124, 58, 237, 0.3), rgba(236, 72, 153, 0.3)); border-radius: 34px; z-index: -1; opacity: 0; transition: opacity 0.4s ease; }\n"
                +
                ".login-card:hover { transform: translateY(-8px) rotateX(2deg) rotateY(2deg); box-shadow: 0 60px 120px rgba(0,0,0,0.25), 0 0 0 1px rgba(255,255,255,0.2), inset 0 1px 0 rgba(255,255,255,0.3); }\n"
                +
                ".login-card:hover::before { opacity: 1; }\n"
                +
                ".login-card:hover::after { opacity: 1; }\n"
                +
                ".login-header { background: linear-gradient(135deg, rgba(79, 70, 229, 0.15) 0%, rgba(124, 58, 237, 0.15) 50%, rgba(236, 72, 153, 0.15) 100%); color: white; padding: 50px 40px; text-align: center; position: relative; overflow: hidden; backdrop-filter: blur(20px); border-radius: 32px 32px 0 0; }\n"
                +
                ".login-header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.05) 100%); }\n"
                +
                ".login-header::after { content: ''; position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: conic-gradient(from 0deg, transparent, rgba(255,255,255,0.1), transparent); animation: headerRotate 8s linear infinite; }\n"
                +
                "@keyframes headerRotate { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }\n"
                +
                ".logo-container { margin-bottom: 30px; position: relative; z-index: 3; }\n"
                +
                ".logo { width: 100px; height: 100px; background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 25px; box-shadow: 0 20px 40px rgba(0,0,0,0.2), 0 0 0 3px rgba(255,255,255,0.2), inset 0 1px 0 rgba(255,255,255,0.3); font-size: 45px; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); overflow: hidden; position: relative; }\n"
                +
                ".logo::before { content: ''; position: absolute; top: -50%; left: -50%; width: 200%; height: 200%; background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent); transform: rotate(45deg); transition: transform 0.6s ease; }\n"
                +
                ".logo:hover { transform: translateY(-5px) scale(1.1) rotateY(10deg); box-shadow: 0 30px 60px rgba(0,0,0,0.3), 0 0 0 3px rgba(255,255,255,0.3), inset 0 1px 0 rgba(255,255,255,0.4); }\n"
                +
                ".logo:hover::before { transform: rotate(225deg); }\n"
                +
                ".logo img { width: 100%; height: 100%; border-radius: 50%; object-fit: cover; position: relative; z-index: 2; }\n"
                +
                ".login-header h1 { font-size: 32px; margin-bottom: 12px; font-weight: 700; position: relative; z-index: 3; text-shadow: 0 2px 10px rgba(0,0,0,0.3); }\n"
                +
                ".login-header p { opacity: 0.9; font-size: 18px; font-weight: 400; position: relative; z-index: 3; text-shadow: 0 1px 5px rgba(0,0,0,0.2); }\n"
                +
                ".login-tabs { display: flex; background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(15px); border-radius: 0; position: relative; }\n"
                +
                ".login-tabs::before { content: ''; position: absolute; bottom: 0; left: 0; right: 0; height: 1px; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); }\n"
                +
                ".tab-btn { flex: 1; padding: 20px; border: none; background: transparent; cursor: pointer; font-size: 16px; color: rgba(255,255,255,0.7); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); font-weight: 500; position: relative; }\n"
                +
                ".tab-btn::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(79, 70, 229, 0.1), rgba(236, 72, 153, 0.1)); opacity: 0; transition: opacity 0.3s ease; }\n"
                +
                ".tab-btn::after { content: ''; position: absolute; bottom: 0; left: 50%; width: 0; height: 3px; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform: translateX(-50%); border-radius: 2px; }\n"
                +
                ".tab-btn.active { background: rgba(255, 255, 255, 0.1); color: white; font-weight: 600; }\n"
                +
                ".tab-btn.active::before { opacity: 1; }\n"
                +
                ".tab-btn.active::after { width: 60%; }\n"
                +
                ".tab-btn:hover:not(.active) { background: rgba(255, 255, 255, 0.05); color: rgba(255,255,255,0.9); }\n"
                +
                ".tab-content { display: none; padding: 40px 35px; }\n"
                +
                ".tab-content.active { display: block; }\n"
                +
                ".form-group { margin-bottom: 28px; position: relative; }\n"
                +
                ".form-group label { display: block; margin-bottom: 12px; color: rgba(255,255,255,0.9); font-weight: 600; font-size: 15px; transition: all 0.3s ease; }\n"
                +
                ".form-group input { width: 100%; padding: 18px 24px; border: 2px solid rgba(255, 255, 255, 0.2); border-radius: 16px; font-size: 16px; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); background: rgba(255, 255, 255, 0.08); font-weight: 400; position: relative; backdrop-filter: blur(10px); color: white; }\n"
                +
                ".form-group input::placeholder { color: rgba(255, 255, 255, 0.5); }\n"
                +
                ".form-group input:focus { outline: none; border-color: rgba(79, 70, 229, 0.8); box-shadow: 0 0 0 4px rgba(79, 70, 229, 0.2), 0 10px 30px rgba(79, 70, 229, 0.2), inset 0 1px 0 rgba(255,255,255,0.2); background: rgba(255, 255, 255, 0.15); transform: translateY(-3px) scale(1.02); }\n"
                +
                ".form-group input:hover:not(:focus) { border-color: rgba(255, 255, 255, 0.4); transform: translateY(-1px); background: rgba(255, 255, 255, 0.12); }\n"
                +
                ".form-group::after { content: ''; position: absolute; bottom: -2px; left: 50%; width: 0; height: 3px; background: linear-gradient(90deg, #4f46e5, #7c3aed, #ec4899); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform: translateX(-50%); border-radius: 2px; }\n"
                +
                ".form-group:focus-within::after { width: 100%; }\n"
                +
                ".form-group:focus-within label { color: #4f46e5; transform: translateY(-3px) scale(1.05); text-shadow: 0 0 10px rgba(79, 70, 229, 0.5); }\n"
                +
                ".login-btn { width: 100%; padding: 20px; background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%); color: white; border: none; border-radius: 16px; font-size: 17px; font-weight: 700; cursor: pointer; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); margin-top: 25px; position: relative; overflow: hidden; text-transform: uppercase; letter-spacing: 1px; }\n"
                +
                ".login-btn::before { content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, transparent 50%, rgba(255,255,255,0.2) 100%); opacity: 0; transition: opacity 0.3s ease; }\n"
                +
                ".login-btn::after { content: ''; position: absolute; top: 50%; left: 50%; width: 0; height: 0; background: rgba(255,255,255,0.4); border-radius: 50%; transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1); transform: translate(-50%, -50%); }\n"
                +
                ".login-btn:hover { transform: translateY(-4px) scale(1.03); box-shadow: 0 20px 40px rgba(79, 70, 229, 0.4), 0 10px 20px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.3); background: linear-gradient(135deg, #5b52f0 0%, #8b46f5 50%, #f056a3 100%); }\n"
                +
                ".login-btn:hover::before { opacity: 1; }\n"
                +
                ".login-btn:active { transform: translateY(-2px) scale(1.01); }\n"
                +
                ".login-btn:active::after { width: 300px; height: 300px; }\n"
                +
                ".login-btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }\n"
                +
                ".message { margin-top: 30px; padding: 18px 24px; border-radius: 16px; text-align: left; font-size: 15px; font-weight: 500; line-height: 1.6; backdrop-filter: blur(10px); }\n"
                +
                ".message.success { background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); color: #155724; border: 1px solid #c3e6cb; text-align: center; }\n"
                +
                ".message.error { background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); color: #721c24; border: 1px solid #f5c6cb; }\n"
                +
                ".message.info { background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); color: #0c5460; border: 1px solid #bee5eb; text-align: center; }\n"
                +
                ".message strong { color: #dc3545; font-weight: 600; }\n" +
                ".message br { margin: 5px 0; }\n"
                +
                ".security-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); color: white; display: none; z-index: 9999; justify-content: center; align-items: center; font-size: 24px; text-align: center; }\n";
    }

    /**
     * 生成登录页面JavaScript
     */
    private String generateLoginJS() {
        return generateSecurityJS() + generateParticleJS() + generateObfuscatedLoginJS();
    }

    /**
     * 生成粒子效果JavaScript
     */
    private String generateParticleJS() {
        return "(function(){" +
                "function createParticles(){" +
                "var container=document.getElementById('particles');" +
                "var particleCount=40;" +
                "for(var i=0;i<particleCount;i++){" +
                "var particle=document.createElement('div');" +
                "particle.className='particle';" +
                "particle.style.left=Math.random()*100+'%';" +
                "particle.style.animationDelay=Math.random()*20+'s';" +
                "particle.style.animationDuration=(15+Math.random()*10)+'s';" +
                "var size=2+Math.random()*3;" +
                "particle.style.width=size+'px';" +
                "particle.style.height=size+'px';" +
                "var colors=['rgba(79,70,229,0.4)','rgba(124,58,237,0.4)','rgba(236,72,153,0.4)','rgba(255,255,255,0.6)'];"
                +
                "particle.style.background=colors[Math.floor(Math.random()*colors.length)];" +
                "particle.style.boxShadow='0 0 '+(size*3)+'px '+particle.style.background;" +
                "container.appendChild(particle);" +
                "}" +
                "}" +
                "function addAdvancedEffects(){" +
                "var inputs=document.querySelectorAll('input');" +
                "inputs.forEach(function(input){" +
                "input.addEventListener('focus',function(){" +
                "this.parentNode.style.transform='scale(1.02) translateY(-2px)';" +
                "this.style.boxShadow='0 0 0 4px rgba(79, 70, 229, 0.2), 0 10px 30px rgba(79, 70, 229, 0.2)';" +
                "});" +
                "input.addEventListener('blur',function(){" +
                "this.parentNode.style.transform='scale(1) translateY(0)';" +
                "});" +
                "});" +
                "}" +
                "function addMouseTracker(){" +
                "var card=document.querySelector('.login-card');" +
                "card.addEventListener('mousemove',function(e){" +
                "var rect=this.getBoundingClientRect();" +
                "var x=(e.clientX-rect.left)/rect.width;" +
                "var y=(e.clientY-rect.top)/rect.height;" +
                "var rotateX=(y-0.5)*10;" +
                "var rotateY=(x-0.5)*-10;" +
                "this.style.transform='translateY(-8px) rotateX('+rotateX+'deg) rotateY('+rotateY+'deg)';" +
                "});" +
                "card.addEventListener('mouseleave',function(){" +
                "this.style.transform='translateY(0) rotateX(0deg) rotateY(0deg)';" +
                "});" +
                "}" +
                "function addParallaxEffect(){" +
                "document.addEventListener('mousemove',function(e){" +
                "var decorations=document.querySelectorAll('.decoration');" +
                "var auroras=document.querySelectorAll('.aurora');" +
                "var mouseX=e.clientX/window.innerWidth;" +
                "var mouseY=e.clientY/window.innerHeight;" +
                "decorations.forEach(function(decoration,index){" +
                "var speed=0.5+index*0.2;" +
                "var x=(mouseX-0.5)*speed*20;" +
                "var y=(mouseY-0.5)*speed*20;" +
                "decoration.style.transform='translate('+x+'px, '+y+'px)';" +
                "});" +
                "auroras.forEach(function(aurora,index){" +
                "var speed=0.3+index*0.1;" +
                "var x=(mouseX-0.5)*speed*30;" +
                "var y=(mouseY-0.5)*speed*30;" +
                "aurora.style.transform='translate('+x+'px, '+y+'px)';" +
                "});" +
                "});" +
                "}" +
                "document.addEventListener('DOMContentLoaded',function(){" +
                "createParticles();" +
                "addAdvancedEffects();" +
                "addMouseTracker();" +
                "addParallaxEffect();" +
                "});" +
                "})();\n";
    }

    /**
     * 生成安全防护JavaScript
     */
    private String generateSecurityJS() {
        return "(function(){" +
                "var _0x1a2b=['keydown','F12','preventDefault','contextmenu','selectstart','dragstart','devtools','console','clear'];"
                +
                "function _0x3c4d(){return Math.random().toString(36).substr(2,9);}" +
                "var _0x5e6f=document.createElement('div');" +
                "_0x5e6f.className='security-overlay';" +
                "_0x5e6f.innerHTML='🚫 检测到开发者工具<br>请关闭后重新访问';" +
                "document.body.appendChild(_0x5e6f);" +
                "document.addEventListener(_0x1a2b[0],function(e){" +
                "if(e.key===_0x1a2b[1]||e.keyCode===123||" +
                "(e.ctrlKey&&e.shiftKey&&(e.keyCode===73||e.keyCode===74))||" +
                "(e.ctrlKey&&e.keyCode===85)){" +
                "e[_0x1a2b[2]]();_0x5e6f.style.display='flex';" +
                "setTimeout(function(){window.location.reload();},2000);" +
                "}});" +
                "document.addEventListener(_0x1a2b[3],function(e){e[_0x1a2b[2]]();});" +
                "document.addEventListener(_0x1a2b[4],function(e){e[_0x1a2b[2]]();});" +
                "document.addEventListener(_0x1a2b[5],function(e){e[_0x1a2b[2]]();});" +
                "setInterval(function(){" +
                "if(window.outerHeight-window.innerHeight>200||window.outerWidth-window.innerWidth>200){" +
                "_0x5e6f.style.display='flex';" +
                "setTimeout(function(){window.location.reload();},1000);" +
                "}},1000);" +
                "var _0x7g8h=0;" +
                "setInterval(function(){" +
                "var _0x9i0j=new Date().getTime();" +
                "debugger;" +
                "if(new Date().getTime()-_0x9i0j>100){" +
                "_0x5e6f.style.display='flex';" +
                "setTimeout(function(){window.location.reload();},500);" +
                "}},3000);" +
                "})();\n";
    }

    /**
     * 生成混淆的登录JavaScript
     */
    private String generateObfuscatedLoginJS() {
        return "(function(){" +
                "var _0xa1b2=['apiKey','password','username','loginType','submit','loginForm','tab-btn','active','tab-content','message','block','none','🔄 登录中...','🚀 登录','POST','application/json','/admin/login','success','✅ ','❌ 网络错误，请稍后重试','❌ '];"
                +
                "var _0xc3d4=_0xa1b2[0];" +
                "function _0xe5f6(_0xg7h8){" +
                "_0xc3d4=_0xg7h8;" +
                "document.querySelectorAll('.'+_0xa1b2[6]).forEach(function(_0xi9j0){_0xi9j0.classList.remove(_0xa1b2[7]);});"
                +
                "event.target.classList.add(_0xa1b2[7]);" +
                "document.querySelectorAll('.'+_0xa1b2[8]).forEach(function(_0xk1l2){_0xk1l2.classList.remove(_0xa1b2[7]);});"
                +
                "document.getElementById(_0xg7h8+'Tab').classList.add(_0xa1b2[7]);" +
                "document.getElementById(_0xa1b2[5]).reset();" +
                "_0xm3n4();" +
                "}" +
                "function _0xo5p6(_0xq7r8,_0xs9t0){" +
                "var _0xu1v2=document.getElementById(_0xa1b2[9]);" +
                "_0xu1v2.textContent=_0xq7r8;" +
                "_0xu1v2.className=_0xa1b2[9]+' '+_0xs9t0;" +
                "_0xu1v2.style.display=_0xa1b2[10];" +
                "}" +
                "function _0xm3n4(){" +
                "var _0xu1v2=document.getElementById(_0xa1b2[9]);" +
                "_0xu1v2.style.display=_0xa1b2[11];" +
                "}" +
                "window.switchTab=_0xe5f6;" +
                "window.showMessage=_0xo5p6;" +
                "window.clearMessage=_0xm3n4;" +
                "document.getElementById(_0xa1b2[5]).addEventListener(_0xa1b2[4],function(_0xw3x4){" +
                "_0xw3x4.preventDefault();" +
                "var _0xy5z6=document.querySelector('.login-btn');" +
                "_0xy5z6.disabled=true;" +
                "_0xy5z6.textContent=_0xa1b2[12];" +
                "var _0xa7b8={};" +
                "_0xa7b8[_0xa1b2[3]]=_0xc3d4;" +
                "if(_0xc3d4===_0xa1b2[0]){" +
                "_0xa7b8[_0xa1b2[0]]=document.getElementById(_0xa1b2[0]).value;" +
                "}else{" +
                "_0xa7b8[_0xa1b2[2]]=document.getElementById(_0xa1b2[2]).value;" +
                "_0xa7b8[_0xa1b2[1]]=document.getElementById(_0xa1b2[1]).value;" +
                "}" +
                "fetch(_0xa1b2[16],{" +
                "method:_0xa1b2[14]," +
                "headers:{'Content-Type':_0xa1b2[15]}," +
                "body:JSON.stringify(_0xa7b8)" +
                "})" +
                ".then(function(_0xc9d0){return _0xc9d0.json();})" +
                ".then(function(_0xe1f2){" +
                "if(_0xe1f2[_0xa1b2[17]]){" +
                "_0xo5p6(_0xa1b2[18]+_0xe1f2.message,_0xa1b2[17]);" +
                "setTimeout(function(){" +
                "window.location.href=_0xe1f2.redirectUrl||'/admin';" +
                "},1000);" +
                "}else{" +
                "var _0xerrMsg=_0xa1b2[20]+_0xe1f2.message;" +
                "if(_0xe1f2.errorType==='ACCOUNT_BANNED'&&_0xe1f2.banInfo){" +
                "var _0xbanInfo=_0xe1f2.banInfo;" +
                "var _0xbanDetails='<br><strong>封禁详情:</strong><br>';" +
                "_0xbanDetails+='原因: '+(_0xbanInfo.reason||'未知')+'<br>';" +
                "if(_0xbanInfo.custom_reason){" +
                "_0xbanDetails+='详细说明: '+_0xbanInfo.custom_reason+'<br>';" +
                "}" +
                "_0xbanDetails+='封禁时间: '+new Date(_0xbanInfo.ban_time).toLocaleString()+'<br>';" +
                "if(_0xbanInfo.ban_end_time===-1){" +
                "_0xbanDetails+='解封时间: 永不解封<br>';" +
                "}else{" +
                "_0xbanDetails+='解封时间: '+new Date(_0xbanInfo.ban_end_time).toLocaleString()+'<br>';" +
                "}" +
                "_0xbanDetails+='执行人: '+(_0xbanInfo.banned_by||'未知');" +
                "_0xerrMsg+=_0xbanDetails;" +
                "}" +
                "var _0xmsgEl=document.getElementById(_0xa1b2[9]);" +
                "_0xmsgEl.innerHTML=_0xerrMsg;" +
                "_0xmsgEl.className=_0xa1b2[9]+' error';" +
                "_0xmsgEl.style.display=_0xa1b2[10];" +
                "}" +
                "})" +
                ".catch(function(_0xg3h4){" +
                "_0xo5p6(_0xa1b2[19],'error');" +
                "})" +
                ".finally(function(){" +
                "_0xy5z6.disabled=false;" +
                "_0xy5z6.textContent=_0xa1b2[13];" +
                "});" +
                "});" +
                "})();\n";
    }

    /**
     * 踢出指定会话ID的会话
     */
    public static boolean kickSessionById(String sessionId) {
        return activeSessions.remove(sessionId) != null;
    }

    /**
     * 获取指定用户的在线会话数量
     */
    public static int getUserSessionCount(String username) {
        int count = 0;
        for (AdminSession session : activeSessions.values()) {
            if (username.equals(session.getUsername())) {
                count++;
            }
        }
        return count;
    }

    /**
     * 获取所有在线用户信息（静态方法，供其他类调用）
     */
    public static Map<String, AdminSession> getAllActiveSessions() {
        return new HashMap<>(activeSessions);
    }

    /**
     * 清理过期会话（静态方法，供其他类调用）
     */
    public static void cleanupExpiredSessions() {
        long currentTime = System.currentTimeMillis();
        activeSessions.entrySet().removeIf(entry -> currentTime - entry.getValue().getCreatedTime() > SESSION_TIMEOUT);
    }

    /**
     * 踢出指定用户的其他会话（单点登录）
     */
    private int kickUserOtherSessions(String username) {
        int kickedCount = 0;
        Iterator<Map.Entry<String, AdminSession>> iterator = activeSessions.entrySet().iterator();

        while (iterator.hasNext()) {
            Map.Entry<String, AdminSession> entry = iterator.next();
            AdminSession session = entry.getValue();

            // 如果用户名匹配，踢出该会话
            if (username.equals(session.getUsername())) {
                iterator.remove();
                kickedCount++;
                plugin.getLogger().info("踢出用户 " + username + " 的会话: " + session.getSessionId() + " (IP: "
                        + session.getClientIP() + ")");
            }
        }

        return kickedCount;
    }
}
