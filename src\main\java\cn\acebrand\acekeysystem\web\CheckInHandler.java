package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.checkin.CheckInManager;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 签到系统Web处理器
 * 处理签到相关的Web请求
 */
public class CheckInHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final CheckInPageGenerator pageGenerator;

    public CheckInHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
        this.pageGenerator = new CheckInPageGenerator();
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();

        try {
            if ("GET".equals(method)) {
                handleGetRequest(exchange, path);
            } else if ("POST".equals(method)) {
                handlePostRequest(exchange, path);
            } else {
                sendResponse(exchange, 405, "text/plain", "方法不允许");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理签到请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "text/plain", "服务器内部错误");
        }
    }

    /**
     * 处理GET请求
     */
    private void handleGetRequest(HttpExchange exchange, String path) throws IOException {
        if (path.equals("/checkin") || path.equals("/checkin/")) {
            // 显示签到页面
            String html = pageGenerator.generateCheckInPage();
            sendResponse(exchange, 200, "text/html; charset=utf-8", html);
        } else if (path.equals("/checkin/style.css")) {
            // 返回CSS样式
            String css = pageGenerator.generateCSS();
            sendResponse(exchange, 200, "text/css; charset=utf-8", css);
        } else if (path.equals("/checkin/script.js")) {
            // 返回JavaScript
            String js = pageGenerator.generateJS();
            sendResponse(exchange, 200, "application/javascript; charset=utf-8", js);
        } else {
            sendResponse(exchange, 404, "text/plain", "页面未找到");
        }
    }

    /**
     * 处理POST请求
     */
    private void handlePostRequest(HttpExchange exchange, String path) throws IOException {
        if (path.equals("/checkin/api")) {
            handleAPIRequest(exchange);
        } else {
            sendResponse(exchange, 404, "text/plain", "API未找到");
        }
    }

    /**
     * 处理API请求
     */
    private void handleAPIRequest(HttpExchange exchange) throws IOException {
        try {
            // 读取请求体
            JSONParser parser = new JSONParser();
            JSONObject request = (JSONObject) parser.parse(
                    new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8));

            String action = (String) request.get("action");
            JSONObject response = new JSONObject();

            switch (action) {
                case "checkin":
                    handleCheckInAction(request, response);
                    break;
                case "get_status":
                    handleGetStatusAction(request, response);
                    break;
                case "get_binding_status":
                    handleGetBindingStatusAction(request, response);
                    break;
                case "get_calendar":
                    handleGetCalendarAction(request, response);
                    break;
                case "buy_makeup_card":
                    handleBuyMakeupCardAction(request, response);
                    break;
                case "get_makeup_prices":
                    handleGetMakeupPricesAction(request, response);
                    break;
                case "get_makeup_cards":
                    handleGetMakeupCardsAction(request, response);
                    break;
                case "makeup_checkin":
                    handleMakeupCheckinAction(request, response);
                    break;
                case "get_user_balance":
                    handleGetUserBalanceAction(request, response);
                    break;
                default:
                    response.put("success", false);
                    response.put("message", "未知的操作");
            }

            sendResponse(exchange, 200, "application/json; charset=utf-8", response.toJSONString());

        } catch (Exception e) {
            plugin.getLogger().warning("处理签到API请求失败: " + e.getMessage());
            JSONObject errorResponse = new JSONObject();
            errorResponse.put("success", false);
            errorResponse.put("message", "请求处理失败");
            sendResponse(exchange, 500, "application/json; charset=utf-8", errorResponse.toJSONString());
        }
    }

    /**
     * 处理签到操作
     */
    @SuppressWarnings("unchecked")
    private void handleCheckInAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        try {
            plugin.getLogger().info("开始处理签到请求: " + playerName);

            CheckInManager.CheckInResult result = plugin.getCheckInManager().checkIn(playerName);

            plugin.getLogger().info("签到结果: 成功=" + result.isSuccess() +
                    ", 消息=" + result.getMessage() +
                    ", 连续天数=" + result.getConsecutiveDays() +
                    ", 获得积分=" + result.getPointsEarned());

            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("consecutive_days", result.getConsecutiveDays());
            response.put("points_earned", result.getPointsEarned());

        } catch (Exception e) {
            plugin.getLogger().severe("处理签到请求时发生错误: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "签到处理失败，请稍后重试");
        }
    }

    /**
     * 处理获取状态操作
     */
    @SuppressWarnings("unchecked")
    private void handleGetStatusAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        plugin.getLogger().info("获取玩家状态: " + playerName);

        boolean hasCheckedIn = plugin.getCheckInManager().hasCheckedInToday(playerName);
        int consecutiveDays = plugin.getCheckInManager().getConsecutiveDays(playerName);
        int totalDays = plugin.getCheckInManager().getTotalDays(playerName);

        plugin.getLogger().info("玩家 " + playerName + " 状态: 今日签到=" + hasCheckedIn +
                ", 连续天数=" + consecutiveDays + ", 总天数=" + totalDays);

        // 获取签到日历数据
        List<String> checkinDays = plugin.getCheckInManager().getPlayerCheckinDays(playerName);
        plugin.getLogger().info("玩家 " + playerName + " 签到日期: " + checkinDays);

        response.put("success", true);
        response.put("has_checked_in", hasCheckedIn);
        response.put("consecutive_days", consecutiveDays);
        response.put("total_days", totalDays);
        response.put("checkin_days", checkinDays);
    }

    /**
     * 处理获取绑定状态操作
     */
    @SuppressWarnings("unchecked")
    private void handleGetBindingStatusAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        CheckInManager.PlayerBindingStatus bindingStatus = plugin.getCheckInManager()
                .getPlayerBindingStatus(playerName);

        response.put("success", true);
        response.put("is_bound", bindingStatus.isBound());
        response.put("status", bindingStatus.getStatus());
        response.put("bind_time", bindingStatus.getBindTime());
        response.put("next_unbind_time", bindingStatus.getNextUnbindTime());

        // 如果已绑定，还可以提供更多信息
        if (bindingStatus.isBound()) {
            long currentTime = System.currentTimeMillis();
            long bindTime = bindingStatus.getBindTime();
            long nextUnbindTime = bindingStatus.getNextUnbindTime();

            // 计算绑定天数
            long bindDays = (currentTime - bindTime) / (24 * 60 * 60 * 1000);
            response.put("bind_days", bindDays);

            // 检查是否可以解绑
            boolean canUnbind = currentTime >= nextUnbindTime;
            response.put("can_unbind", canUnbind);

            if (!canUnbind) {
                long remainingTime = nextUnbindTime - currentTime;
                response.put("unbind_remaining_time", remainingTime);
            }
        }
    }

    /**
     * 处理获取日历操作
     */
    @SuppressWarnings("unchecked")
    private void handleGetCalendarAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        // 这里可以添加获取玩家签到日历的逻辑
        response.put("success", true);
        response.put("calendar", new JSONObject()); // 暂时返回空对象
    }

    /**
     * 处理购买补签卡操作
     */
    @SuppressWarnings("unchecked")
    private void handleBuyMakeupCardAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");
        String paymentType = (String) request.get("payment_type");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        if (paymentType == null || paymentType.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "支付方式不能为空");
            return;
        }

        // 检查玩家是否已绑定
        CheckInManager.PlayerBindingStatus bindingStatus = plugin.getCheckInManager()
                .getPlayerBindingStatus(playerName);

        if (!bindingStatus.isBound()) {
            response.put("success", false);
            response.put("message", "请先绑定账号后再购买补签卡");
            return;
        }

        // 获取购买数量
        int quantity = 1;
        if (request.containsKey("quantity")) {
            try {
                quantity = ((Number) request.get("quantity")).intValue();
                if (quantity <= 0 || quantity > 99) {
                    response.put("success", false);
                    response.put("message", "购买数量必须在1-99之间");
                    return;
                }
            } catch (Exception e) {
                response.put("success", false);
                response.put("message", "购买数量格式错误");
                return;
            }
        }

        // 检查支付方式是否启用
        String configPath = "makeup_cards." + paymentType;
        if (!plugin.getConfig().getBoolean(configPath + ".enabled", false)) {
            response.put("success", false);
            response.put("message", "该支付方式未启用");
            return;
        }

        // 获取价格配置
        int unitPrice = plugin.getConfig().getInt(configPath + ".price", 100);
        String currencyName = plugin.getConfig().getString(configPath + ".currency_name", "未知货币");
        String balanceVariable = plugin.getConfig().getString(configPath + ".balance_variable", "");
        String deductCommand = plugin.getConfig().getString(configPath + ".deduct_command", "");

        int totalPrice = unitPrice * quantity;

        // 执行购买逻辑
        boolean purchaseSuccess = false;
        String errorMessage = "";

        try {
            plugin.getLogger().info("开始处理购买补签卡 - 玩家: " + playerName + ", 支付方式: " + paymentType + ", 数量: " + quantity
                    + ", 总价: " + totalPrice);
            plugin.getLogger().info("扣除指令: '" + deductCommand + "', 余额变量: '" + balanceVariable + "'");
            plugin.getLogger().info("deductCommand是否为internal: " + "internal".equals(deductCommand));
            plugin.getLogger().info("balanceVariable是否为internal: " + "internal".equals(balanceVariable));

            // 检查是否是积分支付方式
            if ("score".equals(paymentType)) {
                // 直接使用积分商店的积分系统
                plugin.getLogger().info("检测到积分支付方式，使用内置积分系统");
                int currentPoints = plugin.getPointsManager().getPlayerPoints(playerName);
                plugin.getLogger()
                        .info("购买补签卡 - 玩家: " + playerName + ", 当前积分: " + currentPoints + ", 需要积分: " + totalPrice);

                if (currentPoints >= totalPrice) {
                    plugin.getLogger().info("积分足够，开始扣除积分...");

                    // 再次确认积分数量（防止并发问题）
                    int reconfirmPoints = plugin.getPointsManager().getPlayerPoints(playerName);
                    plugin.getLogger().info("再次确认积分数量: " + reconfirmPoints);

                    if (reconfirmPoints >= totalPrice) {
                        purchaseSuccess = plugin.getPointsManager().deductPlayerPoints(playerName, totalPrice);
                        plugin.getLogger().info("扣除积分结果: " + purchaseSuccess);

                        if (purchaseSuccess) {
                            // 验证扣除后的积分
                            int afterPoints = plugin.getPointsManager().getPlayerPoints(playerName);
                            plugin.getLogger()
                                    .info("扣除后积分: " + afterPoints + " (应该是: " + (reconfirmPoints - totalPrice) + ")");
                        } else {
                            errorMessage = "积分扣除失败，请稍后重试";
                            plugin.getLogger().warning(
                                    "积分扣除失败 - 玩家: " + playerName + ", 积分: " + reconfirmPoints + ", 需要: " + totalPrice);
                        }
                    } else {
                        purchaseSuccess = false;
                        errorMessage = currencyName + "不足，当前积分: " + reconfirmPoints + "，需要: " + totalPrice;
                        plugin.getLogger()
                                .warning("再次确认时积分不足 - 玩家: " + playerName + ", 当前: " + reconfirmPoints + ", 需要: "
                                        + totalPrice);
                    }
                } else {
                    purchaseSuccess = false;
                    errorMessage = currencyName + "不足，当前积分: " + currentPoints + "，需要: " + totalPrice;
                    plugin.getLogger()
                            .warning("积分不足 - 玩家: " + playerName + ", 当前: " + currentPoints + ", 需要: " + totalPrice);
                }
            } else if ("internal".equals(deductCommand) || "internal".equals(balanceVariable)) {
                // 备用方案：通过配置判断使用内部积分系统
                plugin.getLogger().info("通过配置判断使用内部积分系统");
                int currentPoints = plugin.getPointsManager().getPlayerPoints(playerName);
                plugin.getLogger()
                        .info("购买补签卡(备用) - 玩家: " + playerName + ", 当前积分: " + currentPoints + ", 需要积分: " + totalPrice);

                if (currentPoints >= totalPrice) {
                    purchaseSuccess = plugin.getPointsManager().deductPlayerPoints(playerName, totalPrice);
                    plugin.getLogger().info("内部积分系统扣除结果: " + purchaseSuccess);
                    if (!purchaseSuccess) {
                        errorMessage = "积分扣除失败，请稍后重试";
                    }
                } else {
                    purchaseSuccess = false;
                    errorMessage = currencyName + "不足，当前积分: " + currentPoints + "，需要: " + totalPrice;
                }
            } else {
                // 使用自定义指令扣除
                plugin.getLogger().warning("进入自定义指令扣除分支！这可能是错误的！");
                plugin.getLogger().warning("deductCommand: '" + deductCommand + "' (长度: "
                        + (deductCommand != null ? deductCommand.length() : "null") + ")");
                plugin.getLogger().warning("balanceVariable: '" + balanceVariable + "' (长度: "
                        + (balanceVariable != null ? balanceVariable.length() : "null") + ")");
                plugin.getLogger().warning("这表明配置可能有问题或者字符串比较失败");

                // 尝试手动检查字符串
                if (deductCommand != null) {
                    plugin.getLogger()
                            .warning("deductCommand字符: " + java.util.Arrays.toString(deductCommand.toCharArray()));
                }
                if (balanceVariable != null) {
                    plugin.getLogger()
                            .warning("balanceVariable字符: " + java.util.Arrays.toString(balanceVariable.toCharArray()));
                }

                plugin.getLogger().info("使用自定义指令扣除 - 指令: " + deductCommand);

                // 先检查余额是否足够
                double currentBalance = getPlayerBalance(playerName, paymentType, balanceVariable);
                plugin.getLogger()
                        .info("检查余额 - 玩家: " + playerName + ", 当前余额: " + currentBalance + ", 需要: " + totalPrice);

                if (currentBalance >= totalPrice) {
                    purchaseSuccess = executeDeductCommand(playerName, totalPrice, deductCommand, balanceVariable);
                    plugin.getLogger().info("自定义指令执行结果: " + purchaseSuccess);
                    if (!purchaseSuccess) {
                        errorMessage = "扣除失败，请稍后重试";
                    }
                } else {
                    purchaseSuccess = false;
                    errorMessage = currencyName + "不足，当前余额: " + (int) currentBalance + "，需要: " + totalPrice;
                    plugin.getLogger().info("余额不足，拒绝购买");
                }
            }

            if (purchaseSuccess) {
                // 给玩家添加补签卡
                plugin.getCheckInManager().addMakeupCards(playerName, quantity);

                response.put("success", true);
                response.put("message", String.format("成功购买 %d 张补签卡，花费 %d %s", quantity, totalPrice, currencyName));
            } else {
                response.put("success", false);
                response.put("message", errorMessage.isEmpty() ? "购买失败" : errorMessage);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("购买补签卡时发生错误: " + e.getMessage());
            response.put("success", false);
            response.put("message", "购买失败，请稍后重试");
        }
    }

    /**
     * 处理获取补签卡价格操作
     */
    @SuppressWarnings("unchecked")
    private void handleGetMakeupPricesAction(JSONObject request, JSONObject response) {
        try {
            plugin.getLogger().info("开始处理获取补签卡价格请求");
            JSONObject prices = new JSONObject();

            // 遍历所有支付方式
            String[] paymentTypes = { "gold", "points", "score", "custom" };
            for (String type : paymentTypes) {
                String configPath = "makeup_cards." + type;
                boolean enabled = plugin.getConfig().getBoolean(configPath + ".enabled", false);
                plugin.getLogger().info("检查支付方式: " + type + ", 启用状态: " + enabled);

                if (enabled) {
                    JSONObject typeInfo = new JSONObject();
                    int price = plugin.getConfig().getInt(configPath + ".price", 100);
                    String currencyName = plugin.getConfig().getString(configPath + ".currency_name", "未知货币");
                    String description = plugin.getConfig().getString(configPath + ".description", "使用货币购买");
                    String balanceVariable = plugin.getConfig().getString(configPath + ".balance_variable", "");

                    typeInfo.put("price", price);
                    typeInfo.put("currency_name", currencyName);
                    typeInfo.put("description", description);
                    typeInfo.put("balance_variable", balanceVariable);
                    prices.put(type, typeInfo);

                    plugin.getLogger().info("添加支付方式: " + type + ", 价格: " + price + ", 货币: " + currencyName);
                }
            }

            plugin.getLogger().info("补签卡价格数据: " + prices.toJSONString());
            response.put("success", true);
            response.put("prices", prices);
        } catch (Exception e) {
            plugin.getLogger().severe("获取补签卡价格时发生错误: " + e.getMessage());
            e.printStackTrace();
            response.put("success", false);
            response.put("message", "获取价格信息失败");
        }
    }

    /**
     * 执行扣除指令
     */
    private boolean executeDeductCommand(String playerName, int amount, String deductCommand, String balanceVariable) {
        try {
            // 首先检查余额是否足够（如果有余额变量）
            if (balanceVariable != null && !balanceVariable.isEmpty() && !balanceVariable.equals("internal")) {
                // 这里需要PlaceholderAPI来获取余额
                // 暂时跳过余额检查，直接执行扣除指令
                plugin.getLogger().info("检查余额变量: " + balanceVariable + " (暂未实现)");
            }

            // 替换指令中的占位符
            String command = deductCommand
                    .replace("{player}", playerName)
                    .replace("{amount}", String.valueOf(amount));

            // 执行指令（需要在主线程中执行）
            plugin.getLogger().info("调度扣除指令到主线程: " + command);

            // 使用同步方式执行，因为我们需要返回结果
            final boolean[] result = { false };
            final Object lock = new Object();

            plugin.getServer().getScheduler().runTask(plugin, () -> {
                synchronized (lock) {
                    try {
                        result[0] = plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(), command);
                        if (result[0]) {
                            plugin.getLogger().info("指令执行成功: " + command);
                        } else {
                            plugin.getLogger().warning("指令执行失败: " + command);
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("主线程执行指令失败: " + command + ", 错误: " + e.getMessage());
                        result[0] = false;
                    }
                    lock.notify();
                }
            });

            // 等待主线程执行完成
            synchronized (lock) {
                try {
                    lock.wait(5000); // 最多等待5秒
                } catch (InterruptedException e) {
                    plugin.getLogger().warning("等待指令执行被中断: " + e.getMessage());
                    Thread.currentThread().interrupt();
                    return false;
                }
            }

            return result[0];
        } catch (Exception e) {
            plugin.getLogger().warning("执行扣除指令时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 处理获取补签卡数量操作
     */
    @SuppressWarnings("unchecked")
    private void handleGetMakeupCardsAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        // 检查玩家是否已绑定
        CheckInManager.PlayerBindingStatus bindingStatus = plugin.getCheckInManager()
                .getPlayerBindingStatus(playerName);

        if (!bindingStatus.isBound()) {
            response.put("success", false);
            response.put("message", "请先绑定账号");
            return;
        }

        // 获取玩家的补签卡数量
        int cardCount = plugin.getCheckInManager().getMakeupCardCount(playerName);

        response.put("success", true);
        response.put("card_count", cardCount);
    }

    /**
     * 处理补签操作
     */
    @SuppressWarnings("unchecked")
    private void handleMakeupCheckinAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");
        Object dayObj = request.get("day");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        if (dayObj == null) {
            response.put("success", false);
            response.put("message", "补签日期不能为空");
            return;
        }

        int day;
        try {
            day = ((Number) dayObj).intValue();
            if (day < 1 || day > 31) {
                response.put("success", false);
                response.put("message", "补签日期必须在1-31之间");
                return;
            }
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "补签日期格式错误");
            return;
        }

        // 检查玩家是否已绑定
        CheckInManager.PlayerBindingStatus bindingStatus = plugin.getCheckInManager()
                .getPlayerBindingStatus(playerName);

        if (!bindingStatus.isBound()) {
            response.put("success", false);
            response.put("message", "请先绑定账号");
            return;
        }

        // 检查玩家是否有补签卡
        int cardCount = plugin.getCheckInManager().getMakeupCardCount(playerName);
        if (cardCount <= 0) {
            response.put("success", false);
            response.put("message", "您没有补签卡，请先购买");
            return;
        }

        // 执行补签
        CheckInManager.CheckInResult result = plugin.getCheckInManager().makeupCheckIn(playerName, day);

        response.put("success", result.isSuccess());
        response.put("message", result.getMessage());
        if (result.isSuccess()) {
            response.put("points_earned", result.getPointsEarned());
            response.put("remaining_cards", plugin.getCheckInManager().getMakeupCardCount(playerName));
        }
    }

    /**
     * 处理获取用户余额操作
     */
    @SuppressWarnings("unchecked")
    private void handleGetUserBalanceAction(JSONObject request, JSONObject response) {
        String playerName = (String) request.get("player");
        String currencyType = (String) request.get("currency_type");
        String balanceVariable = (String) request.get("balance_variable");

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            return;
        }

        if (currencyType == null || currencyType.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "货币类型不能为空");
            return;
        }

        // 检查玩家是否已绑定
        CheckInManager.PlayerBindingStatus bindingStatus = plugin.getCheckInManager()
                .getPlayerBindingStatus(playerName);

        if (!bindingStatus.isBound()) {
            response.put("success", false);
            response.put("message", "请先绑定账号");
            return;
        }

        try {
            double balance = 0.0;

            // 根据余额变量获取余额
            if (balanceVariable != null && !balanceVariable.isEmpty() && !balanceVariable.equals("internal")) {
                // 使用PlaceholderAPI获取余额
                if (plugin.getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
                    try {
                        // 先尝试获取在线玩家对象
                        org.bukkit.entity.Player player = plugin.getServer().getPlayer(playerName);
                        org.bukkit.OfflinePlayer offlinePlayer = null;

                        if (player == null) {
                            // 如果玩家不在线，获取离线玩家对象
                            offlinePlayer = plugin.getServer().getOfflinePlayer(playerName);
                        }

                        if (player != null || offlinePlayer != null) {
                            // 使用反射调用PlaceholderAPI
                            Class<?> placeholderAPIClass = Class.forName("me.clip.placeholderapi.PlaceholderAPI");
                            String balanceStr = null;

                            if (player != null) {
                                // 在线玩家
                                java.lang.reflect.Method setPlaceholdersMethod = placeholderAPIClass.getMethod(
                                        "setPlaceholders",
                                        org.bukkit.entity.Player.class, String.class);
                                balanceStr = (String) setPlaceholdersMethod.invoke(null, player, balanceVariable);
                            } else {
                                // 离线玩家
                                java.lang.reflect.Method setPlaceholdersMethod = placeholderAPIClass.getMethod(
                                        "setPlaceholders",
                                        org.bukkit.OfflinePlayer.class, String.class);
                                balanceStr = (String) setPlaceholdersMethod.invoke(null, offlinePlayer,
                                        balanceVariable);
                            }

                            plugin.getLogger().info("PlaceholderAPI变量 " + balanceVariable + " 返回值: " + balanceStr
                                    + " (玩家: " + playerName + ", 在线: " + (player != null) + ")");

                            // 尝试解析为数字
                            if (balanceStr != null && !balanceStr.trim().isEmpty()
                                    && !balanceStr.equals(balanceVariable)) {
                                try {
                                    balance = Double.parseDouble(balanceStr.trim());
                                } catch (NumberFormatException e) {
                                    plugin.getLogger()
                                            .warning("无法解析余额变量值为数字: " + balanceStr + ", 变量: " + balanceVariable);
                                    balance = 0.0;
                                }
                            } else {
                                plugin.getLogger().warning(
                                        "PlaceholderAPI变量返回空值或未解析: " + balanceStr + ", 变量: " + balanceVariable);
                                balance = 0.0;
                            }
                        } else {
                            plugin.getLogger()
                                    .warning("无法找到玩家 " + playerName + "，无法获取PlaceholderAPI变量: " + balanceVariable);
                            balance = 0.0;
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("使用PlaceholderAPI获取余额时出错: " + e.getMessage());
                        e.printStackTrace();
                        balance = 0.0;
                    }
                } else {
                    plugin.getLogger().warning("PlaceholderAPI插件未找到，无法获取余额变量: " + balanceVariable);
                    // 尝试使用内置系统作为备用方案
                    balance = getBalanceFromInternalSystems(playerName, currencyType, balanceVariable);
                }
            } else {
                // 使用内部系统获取余额
                switch (currencyType) {
                    case "score":
                        // 使用积分商店的积分系统
                        balance = plugin.getPointsManager().getPlayerPoints(playerName);
                        break;
                    default:
                        // 其他类型暂时返回0
                        balance = 0.0;
                        break;
                }
            }

            response.put("success", true);
            response.put("balance", balance);
        } catch (Exception e) {
            plugin.getLogger().warning("获取用户余额时发生错误: " + e.getMessage());
            response.put("success", false);
            response.put("message", "获取余额失败");
        }
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String response)
            throws IOException {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        exchange.getResponseBody().write(responseBytes);
        exchange.getResponseBody().close();
    }

    /**
     * 获取玩家余额
     */
    private double getPlayerBalance(String playerName, String currencyType, String balanceVariable) {
        plugin.getLogger().info("获取玩家余额 - 玩家: " + playerName + ", 类型: " + currencyType + ", 变量: " + balanceVariable);

        try {
            // 根据余额变量获取余额
            if (balanceVariable != null && !balanceVariable.isEmpty() && !balanceVariable.equals("internal")) {
                // 使用PlaceholderAPI获取余额
                if (plugin.getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
                    try {
                        // 先尝试获取在线玩家对象
                        org.bukkit.entity.Player player = plugin.getServer().getPlayer(playerName);
                        org.bukkit.OfflinePlayer offlinePlayer = null;

                        if (player == null) {
                            // 如果玩家不在线，获取离线玩家对象
                            offlinePlayer = plugin.getServer().getOfflinePlayer(playerName);
                        }

                        if (player != null || offlinePlayer != null) {
                            // 使用反射调用PlaceholderAPI
                            Class<?> placeholderAPIClass = Class.forName("me.clip.placeholderapi.PlaceholderAPI");
                            String balanceStr = null;

                            if (player != null) {
                                // 在线玩家
                                java.lang.reflect.Method setPlaceholdersMethod = placeholderAPIClass.getMethod(
                                        "setPlaceholders",
                                        org.bukkit.entity.Player.class, String.class);
                                balanceStr = (String) setPlaceholdersMethod.invoke(null, player, balanceVariable);
                            } else {
                                // 离线玩家
                                java.lang.reflect.Method setPlaceholdersMethod = placeholderAPIClass.getMethod(
                                        "setPlaceholders",
                                        org.bukkit.OfflinePlayer.class, String.class);
                                balanceStr = (String) setPlaceholdersMethod.invoke(null, offlinePlayer,
                                        balanceVariable);
                            }

                            plugin.getLogger().info("PlaceholderAPI变量 " + balanceVariable + " 返回值: " + balanceStr
                                    + " (玩家: " + playerName + ", 在线: " + (player != null) + ")");

                            // 尝试解析为数字
                            if (balanceStr != null && !balanceStr.trim().isEmpty()
                                    && !balanceStr.equals(balanceVariable)) {
                                try {
                                    return Double.parseDouble(balanceStr.trim());
                                } catch (NumberFormatException e) {
                                    plugin.getLogger()
                                            .warning("无法解析余额变量值为数字: " + balanceStr + ", 变量: " + balanceVariable);
                                    return 0.0;
                                }
                            } else {
                                plugin.getLogger().warning(
                                        "PlaceholderAPI变量返回空值或未解析: " + balanceStr + ", 变量: " + balanceVariable);
                                return 0.0;
                            }
                        } else {
                            plugin.getLogger()
                                    .warning("无法找到玩家 " + playerName + "，无法获取PlaceholderAPI变量: " + balanceVariable);
                            return 0.0;
                        }
                    } catch (Exception e) {
                        plugin.getLogger().warning("使用PlaceholderAPI获取余额时出错: " + e.getMessage());
                        e.printStackTrace();
                        return 0.0;
                    }
                } else {
                    plugin.getLogger().warning("PlaceholderAPI插件未找到，无法获取余额变量: " + balanceVariable);
                    // 尝试使用内置系统作为备用方案
                    return getBalanceFromInternalSystems(playerName, currencyType, balanceVariable);
                }
            } else {
                // 使用内部系统获取余额
                switch (currencyType) {
                    case "score":
                        // 使用积分商店的积分系统
                        double points = plugin.getPointsManager().getPlayerPoints(playerName);
                        plugin.getLogger().info("从内置积分系统获取余额: " + points);
                        return points;
                    case "gold":
                        // 使用Vault经济系统
                        if (plugin.getVaultIntegration() != null && plugin.getVaultIntegration().isEnabled()) {
                            double balance = plugin.getVaultIntegration().getBalance(playerName);
                            plugin.getLogger().info("从Vault经济系统获取金币余额: " + balance);
                            return balance;
                        } else {
                            plugin.getLogger().warning("Vault经济系统未启用，金币余额返回0");
                            return 0.0;
                        }
                    case "points":
                        // 点券系统，需要PlaceholderAPI或其他插件支持
                        plugin.getLogger().warning("点券系统需要配置PlaceholderAPI变量，当前返回0");
                        return 0.0;
                    default:
                        // 其他自定义类型，需要配置PlaceholderAPI变量
                        plugin.getLogger().warning("未知货币类型: " + currencyType + "，需要配置PlaceholderAPI变量，当前返回0");
                        return 0.0;
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("获取玩家余额时发生错误: " + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 从内置系统获取余额（备用方案）
     */
    private double getBalanceFromInternalSystems(String playerName, String currencyType, String balanceVariable) {
        plugin.getLogger()
                .info("尝试使用内置系统获取余额，玩家: " + playerName + ", 类型: " + currencyType + ", 变量: " + balanceVariable);

        // 根据占位符变量推断货币类型
        if (balanceVariable != null) {
            if (balanceVariable.contains("vault") || balanceVariable.contains("eco")) {
                // 尝试使用Vault经济系统
                if (plugin.getVaultIntegration() != null && plugin.getVaultIntegration().isEnabled()) {
                    double balance = plugin.getVaultIntegration().getBalance(playerName);
                    plugin.getLogger().info("从Vault获取到余额: " + balance);
                    return balance;
                }
            } else if (balanceVariable.contains("playerpoints") || balanceVariable.contains("points")) {
                // 点券系统需要通过PlaceholderAPI获取，这里作为备用返回0
                plugin.getLogger().warning("点券系统需要PlaceholderAPI支持，当前返回0");
                return 0.0;
            } else {
                // 其他自定义变量，尝试通过PlaceholderAPI获取
                plugin.getLogger().warning("自定义变量 " + balanceVariable + " 需要PlaceholderAPI支持，当前返回0");
                return 0.0;
            }
        }

        // 根据货币类型使用内置系统
        switch (currencyType) {
            case "score":
                double points = plugin.getPointsManager().getPlayerPoints(playerName);
                plugin.getLogger().info("从内置积分系统获取到积分: " + points);
                return points;
            case "gold":
                // 尝试使用Vault经济系统
                if (plugin.getVaultIntegration() != null && plugin.getVaultIntegration().isEnabled()) {
                    double balance = plugin.getVaultIntegration().getBalance(playerName);
                    plugin.getLogger().info("从Vault获取到金币余额: " + balance);
                    return balance;
                }
                break;
            default:
                plugin.getLogger().info("未知货币类型: " + currencyType + "，返回0");
                break;
        }

        return 0.0;
    }
}
