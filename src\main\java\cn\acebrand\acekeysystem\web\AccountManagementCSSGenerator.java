package cn.acebrand.acekeysystem.web;

/**
 * 账号管理CSS样式生成器
 * 负责生成账号管理相关的CSS样式
 */
public class AccountManagementCSSGenerator {

    /**
     * 生成账号管理CSS样式
     */
    public String generateAccountManagementCSS() {
        return generateBasicStyles() +
                generateTabStyles() +
                generateToolbarStyles() +
                generateAccountGridStyles() +
                generateAccountCardStyles() +
                generateRoleStyles() +
                generateModalStyles() +
                generateFormStyles() +
                generateLogoutStyles() +
                generateDarkThemeStyles();
    }

    /**
     * 生成基础样式
     */
    private String generateBasicStyles() {
        return "/* ==================== 基础样式 ==================== */\n" +
                "* {\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                "html {\n" +
                "    height: 100%;\n" +
                "    overflow-x: hidden;\n" +
                "}\n" +
                "\n" +
                "body {\n" +
                "    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n" +
                "    min-height: 100vh;\n" +
                "    height: 100%;\n" +
                "    overflow-x: hidden;\n" +
                "    overflow-y: auto;\n" +
                "    position: relative;\n" +
                "    color: #333;\n" +
                "    transition: background-color 0.3s ease, color 0.3s ease;\n" +
                "    /* 隐藏滚动条但保持滚动功能 */\n" +
                "    scrollbar-width: none; /* Firefox */\n" +
                "    -ms-overflow-style: none; /* IE and Edge */\n" +
                "}\n" +
                "\n" +
                "/* 隐藏 Webkit 浏览器的滚动条 */\n" +
                "body::-webkit-scrollbar {\n" +
                "    display: none;\n" +
                "}\n" +
                "\n" +
                "/* 当鼠标悬停在页面上时显示滚动条 */\n" +
                "body:hover {\n" +
                "    scrollbar-width: thin; /* Firefox */\n" +
                "    -ms-overflow-style: auto; /* IE and Edge */\n" +
                "}\n" +
                "\n" +
                "body:hover::-webkit-scrollbar {\n" +
                "    display: block;\n" +
                "    width: 8px;\n" +
                "}\n" +
                "\n" +
                "body:hover::-webkit-scrollbar-track {\n" +
                "    background: rgba(0, 0, 0, 0.1);\n" +
                "    border-radius: 4px;\n" +
                "}\n" +
                "\n" +
                "body:hover::-webkit-scrollbar-thumb {\n" +
                "    background: rgba(0, 0, 0, 0.3);\n" +
                "    border-radius: 4px;\n" +
                "}\n" +
                "\n" +
                "body:hover::-webkit-scrollbar-thumb:hover {\n" +
                "    background: rgba(0, 0, 0, 0.5);\n" +
                "}\n" +
                "\n" +
                ".container {\n" +
                "    max-width: 1400px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 20px;\n" +
                "    min-height: calc(100vh - 40px);\n" +
                "}\n\n";
    }

    /**
     * 生成标签页样式
     */
    private String generateTabStyles() {
        return "\n/* ==================== 标签页样式 ==================== */\n" +
                ".tab-navigation {\n" +
                "    display: flex;\n" +
                "    margin-bottom: 20px;\n" +
                "    border-bottom: 2px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".tab-btn {\n" +
                "    padding: 12px 24px;\n" +
                "    background: none;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "    font-size: 14px;\n" +
                "    font-weight: 500;\n" +
                "    color: #6c757d;\n" +
                "    border-bottom: 2px solid transparent;\n" +
                "    transition: all 0.3s ease;\n" +
                "    position: relative;\n" +
                "    margin-bottom: -2px;\n" +
                "}\n" +
                "\n" +
                ".tab-btn:hover {\n" +
                "    color: #007bff;\n" +
                "    background: rgba(0, 123, 255, 0.05);\n" +
                "}\n" +
                "\n" +
                ".tab-btn.active {\n" +
                "    color: #007bff;\n" +
                "    border-bottom-color: #007bff;\n" +
                "    background: rgba(0, 123, 255, 0.05);\n" +
                "}\n" +
                "\n" +
                ".account-tab-content {\n" +
                "    display: none;\n" +
                "}\n" +
                "\n" +
                ".account-tab-content.active {\n" +
                "    display: block;\n" +
                "}\n\n";
    }

    /**
     * 生成工具栏样式
     */
    private String generateToolbarStyles() {
        return "\n/* ==================== 账号管理工具栏样式 ==================== */\n" +
                ".toolbar {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    margin-bottom: 20px;\n" +
                "    padding: 15px;\n" +
                "    background: #f8f9fa;\n" +
                "    border-radius: 8px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".toolbar-left {\n" +
                "    display: flex;\n" +
                "    gap: 10px;\n" +
                "}\n" +
                "\n" +
                ".toolbar-right {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 15px;\n" +
                "}\n" +
                "\n" +
                ".account-count {\n" +
                "    font-size: 14px;\n" +
                "    color: #6c757d;\n" +
                "}\n" +
                "\n" +
                ".account-count strong {\n" +
                "    color: #007bff;\n" +
                "    font-weight: 600;\n" +
                "}\n\n";
    }

    /**
     * 生成账号网格样式
     */
    private String generateAccountGridStyles() {
        return "/* ==================== 账号网格样式 ==================== */\n" +
                ".accounts-container {\n" +
                "    min-height: 200px;\n" +
                "}\n" +
                "\n" +
                ".accounts-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n" +
                "    gap: 20px;\n" +
                "    margin-top: 20px;\n" +
                "}\n" +
                "\n" +
                ".no-data {\n" +
                "    text-align: center;\n" +
                "    padding: 40px;\n" +
                "    color: #6c757d;\n" +
                "    font-size: 16px;\n" +
                "}\n" +
                "\n" +
                ".loading-spinner {\n" +
                "    text-align: center;\n" +
                "    padding: 40px;\n" +
                "    color: #007bff;\n" +
                "    font-size: 16px;\n" +
                "}\n\n";
    }

    /**
     * 生成账号卡片样式
     */
    private String generateAccountCardStyles() {
        return "/* ==================== 账号卡片样式 ==================== */\n" +
                ".account-card {\n" +
                "    background: #fff;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    border-radius: 8px;\n" +
                "    padding: 20px;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n" +
                "}\n" +
                "\n" +
                ".account-card:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 8px rgba(0,0,0,0.15);\n" +
                "}\n" +
                "\n" +
                "/* 旧的禁用样式已移到下方的状态样式中 */\n" +
                ".account-header {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    margin-bottom: 15px;\n" +
                "    padding-bottom: 10px;\n" +
                "    border-bottom: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".account-header h4 {\n" +
                "    margin: 0;\n" +
                "    font-size: 18px;\n" +
                "    font-weight: 600;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".account-role {\n" +
                "    padding: 4px 8px;\n" +
                "    border-radius: 4px;\n" +
                "    font-size: 12px;\n" +
                "    font-weight: 500;\n" +
                "    text-transform: uppercase;\n" +
                "}\n" +
                "\n" +
                ".account-role.super_admin {\n" +
                "    background: #dc3545;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".account-role.admin {\n" +
                "    background: #007bff;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".account-info {\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".info-item {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    margin-bottom: 8px;\n" +
                "    font-size: 14px;\n" +
                "}\n" +
                "\n" +
                ".info-item .label {\n" +
                "    color: #6c757d;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                ".info-item .value {\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".status-enabled {\n" +
                "    color: #28a745 !important;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".status-disabled {\n" +
                "    color: #dc3545 !important;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".account-actions {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    gap: 8px;\n" +
                "    margin-top: 15px;\n" +
                "}\n" +
                "\n" +
                ".action-row {\n" +
                "    display: flex;\n" +
                "    gap: 8px;\n" +
                "    justify-content: space-between;\n" +
                "}\n" +
                "\n" +
                ".action-row .btn-sm {\n" +
                "    flex: 1;\n" +
                "    text-align: center;\n" +
                "    min-width: 0;\n" +
                "}\n" +
                "\n" +
                ".btn-sm {\n" +
                "    padding: 6px 12px;\n" +
                "    font-size: 12px;\n" +
                "    border-radius: 4px;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.2s ease;\n" +
                "    text-decoration: none;\n" +
                "    display: inline-block;\n" +
                "}\n" +
                "\n" +
                ".btn-sm:hover {\n" +
                "    opacity: 0.9;\n" +
                "}\n" +
                "\n" +
                "/* 账号卡片状态样式 */\n" +
                ".account-card.enabled {\n" +
                "    border-left: 4px solid #28a745;\n" +
                "}\n" +
                "\n" +
                ".account-card.disabled {\n" +
                "    border-left: 4px solid #dc3545;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                ".account-status {\n" +
                "    display: inline-block;\n" +
                "    padding: 2px 8px;\n" +
                "    border-radius: 12px;\n" +
                "    font-size: 11px;\n" +
                "    font-weight: 600;\n" +
                "    text-transform: uppercase;\n" +
                "}\n" +
                "\n" +
                ".account-status.enabled {\n" +
                "    background: rgba(40, 167, 69, 0.1);\n" +
                "    color: #28a745;\n" +
                "    border: 1px solid rgba(40, 167, 69, 0.3);\n" +
                "}\n" +
                "\n" +
                ".account-status.disabled {\n" +
                "    background: rgba(220, 53, 69, 0.1);\n" +
                "    color: #dc3545;\n" +
                "    border: 1px solid rgba(220, 53, 69, 0.3);\n" +
                "}\n" +
                "\n" +
                ".account-status.banned {\n" +
                "    background: rgba(108, 117, 125, 0.1);\n" +
                "    color: #6c757d;\n" +
                "    border: 1px solid rgba(108, 117, 125, 0.3);\n" +
                "    font-weight: bold;\n" +
                "}\n" +
                "\n" +
                ".account-card.banned {\n" +
                "    border-left: 4px solid #6c757d;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                ".ban-info {\n" +
                "    grid-column: 1 / -1;\n" +
                "    background: rgba(108, 117, 125, 0.05);\n" +
                "    padding: 10px;\n" +
                "    border-radius: 4px;\n" +
                "    border-left: 3px solid #6c757d;\n" +
                "}\n" +
                "\n" +
                ".ban-reason {\n" +
                "    font-weight: bold;\n" +
                "    color: #6c757d;\n" +
                "}\n" +
                "\n" +
                ".permission-count {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 8px;\n" +
                "}\n" +
                "\n" +
                ".permission-stats {\n" +
                "    font-weight: 600;\n" +
                "    color: #007bff;\n" +
                "}\n" +
                "\n" +
                ".permission-percentage {\n" +
                "    font-size: 12px;\n" +
                "    color: #6c757d;\n" +
                "    font-weight: 500;\n" +
                "}\n\n";
    }

    /**
     * 生成角色管理样式
     */
    private String generateRoleStyles() {
        return "/* ==================== 角色管理样式 ==================== */\n" +
                ".roles-container {\n" +
                "    min-height: 200px;\n" +
                "}\n" +
                "\n" +
                ".roles-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n" +
                "    gap: 20px;\n" +
                "    margin-top: 20px;\n" +
                "}\n" +
                "\n" +
                ".role-card {\n" +
                "    background: #fff;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    border-radius: 8px;\n" +
                "    padding: 20px;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n" +
                "    border-left: 4px solid #007bff;\n" +
                "}\n" +
                "\n" +
                ".role-card:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 8px rgba(0,0,0,0.15);\n" +
                "}\n" +
                "\n" +
                ".role-header {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    margin-bottom: 15px;\n" +
                "    padding-bottom: 10px;\n" +
                "    border-bottom: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".role-header h4 {\n" +
                "    margin: 0;\n" +
                "    font-size: 18px;\n" +
                "    font-weight: 600;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".role-id {\n" +
                "    padding: 4px 8px;\n" +
                "    background: #f8f9fa;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    border-radius: 4px;\n" +
                "    font-size: 12px;\n" +
                "    font-weight: 500;\n" +
                "    color: #6c757d;\n" +
                "    font-family: monospace;\n" +
                "}\n" +
                "\n" +
                ".role-description {\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".role-description p {\n" +
                "    margin: 0;\n" +
                "    color: #6c757d;\n" +
                "    font-size: 14px;\n" +
                "    line-height: 1.5;\n" +
                "}\n" +
                "\n" +
                ".role-permissions {\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".role-permissions strong {\n" +
                "    display: block;\n" +
                "    margin-bottom: 8px;\n" +
                "    color: #333;\n" +
                "    font-size: 14px;\n" +
                "}\n" +
                "\n" +
                ".permissions-tags {\n" +
                "    display: flex;\n" +
                "    flex-wrap: wrap;\n" +
                "    gap: 6px;\n" +
                "}\n" +
                "\n" +
                ".permission-tag {\n" +
                "    display: inline-block;\n" +
                "    padding: 3px 8px;\n" +
                "    background: rgba(0, 123, 255, 0.1);\n" +
                "    color: #007bff;\n" +
                "    border: 1px solid rgba(0, 123, 255, 0.3);\n" +
                "    border-radius: 12px;\n" +
                "    font-size: 11px;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                ".role-actions {\n" +
                "    display: flex;\n" +
                "    gap: 8px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".role-count {\n" +
                "    font-size: 14px;\n" +
                "    color: #6c757d;\n" +
                "}\n" +
                "\n" +
                ".role-count strong {\n" +
                "    color: #007bff;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".permissions-preview {\n" +
                "    padding: 10px;\n" +
                "    background: #f8f9fa;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    border-radius: 4px;\n" +
                "    min-height: 60px;\n" +
                "}\n" +
                "\n" +
                ".text-muted {\n" +
                "    color: #6c757d;\n" +
                "    font-style: italic;\n" +
                "}\n" +
                "\n" +
                ".form-text {\n" +
                "    font-weight: 600;\n" +
                "    color: #333;\n" +
                "    padding: 8px 0;\n" +
                "}\n\n";
    }

    /**
     * 生成弹窗样式
     */
    private String generateModalStyles() {
        return "/* ==================== 弹窗样式 ==================== */\n" +
                ".modal {\n" +
                "    position: fixed;\n" +
                "    z-index: 1000;\n" +
                "    left: 0;\n" +
                "    top: 0;\n" +
                "    width: 100%;\n" +
                "    height: 100%;\n" +
                "    background-color: rgba(0,0,0,0.5);\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".modal-content {\n" +
                "    background-color: #fff;\n" +
                "    border-radius: 8px;\n" +
                "    width: 90%;\n" +
                "    max-width: 500px;\n" +
                "    max-height: 90vh;\n" +
                "    overflow-y: auto;\n" +
                "    box-shadow: 0 4px 20px rgba(0,0,0,0.3);\n" +
                "}\n" +
                "\n" +
                ".modal-header {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    padding: 20px;\n" +
                "    border-bottom: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".modal-header h3 {\n" +
                "    margin: 0;\n" +
                "    font-size: 18px;\n" +
                "    font-weight: 600;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".close {\n" +
                "    font-size: 24px;\n" +
                "    font-weight: bold;\n" +
                "    color: #aaa;\n" +
                "    cursor: pointer;\n" +
                "    transition: color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".close:hover {\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".modal-body {\n" +
                "    padding: 20px;\n" +
                "}\n\n";
    }

    /**
     * 生成表单样式
     */
    private String generateFormStyles() {
        return "/* ==================== 表单样式 ==================== */\n" +
                ".form-group {\n" +
                "    margin-bottom: 20px;\n" +
                "}\n" +
                "\n" +
                ".form-group label {\n" +
                "    display: block;\n" +
                "    margin-bottom: 5px;\n" +
                "    font-weight: 500;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".form-input {\n" +
                "    width: 100%;\n" +
                "    padding: 10px;\n" +
                "    border: 1px solid #ddd;\n" +
                "    border-radius: 4px;\n" +
                "    font-size: 14px;\n" +
                "    transition: border-color 0.2s ease;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                ".form-input:focus {\n" +
                "    outline: none;\n" +
                "    border-color: #007bff;\n" +
                "    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);\n" +
                "}\n" +
                "\n" +
                ".form-actions {\n" +
                "    display: flex;\n" +
                "    justify-content: flex-end;\n" +
                "    gap: 10px;\n" +
                "    margin-top: 20px;\n" +
                "    padding-top: 20px;\n" +
                "    border-top: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".search-box {\n" +
                "    flex: 1;\n" +
                "    max-width: 300px;\n" +
                "}\n" +
                "\n" +
                ".search-box input {\n" +
                "    width: 100%;\n" +
                "    padding: 8px 12px;\n" +
                "    border: 1px solid #ddd;\n" +
                "    border-radius: 4px;\n" +
                "    font-size: 14px;\n" +
                "}\n" +
                "\n" +
                "/* 权限复选框样式 */\n" +
                ".permission-checkboxes {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n" +
                "    gap: 10px;\n" +
                "    margin-top: 10px;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    cursor: pointer;\n" +
                "    padding: 8px;\n" +
                "    border-radius: 4px;\n" +
                "    transition: background-color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label:hover {\n" +
                "    background-color: #f8f9fa;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label input[type=\"checkbox\"] {\n" +
                "    margin-right: 8px;\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "    cursor: pointer;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label span {\n" +
                "    font-size: 14px;\n" +
                "    color: #333;\n" +
                "    user-select: none;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label input[type=\"checkbox\"]:disabled {\n" +
                "    cursor: not-allowed;\n" +
                "    opacity: 0.6;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label:has(input:disabled) {\n" +
                "    cursor: not-allowed;\n" +
                "    opacity: 0.7;\n" +
                "}\n" +
                "\n" +
                ".checkbox-label:has(input:disabled):hover {\n" +
                "    background-color: transparent;\n" +
                "}\n" +
                "\n" +
                "/* IP管理样式 */\n" +
                ".ip-address {\n" +
                "    font-family: monospace;\n" +
                "    background: #f8f9fa;\n" +
                "    padding: 2px 6px;\n" +
                "    border-radius: 3px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".ip-tags {\n" +
                "    display: flex;\n" +
                "    flex-wrap: wrap;\n" +
                "    gap: 4px;\n" +
                "    margin-top: 4px;\n" +
                "}\n" +
                "\n" +
                ".ip-tag {\n" +
                "    display: inline-block;\n" +
                "    padding: 2px 6px;\n" +
                "    background: rgba(0, 123, 255, 0.1);\n" +
                "    color: #007bff;\n" +
                "    border: 1px solid rgba(0, 123, 255, 0.3);\n" +
                "    border-radius: 10px;\n" +
                "    font-size: 11px;\n" +
                "    font-weight: 500;\n" +
                "    font-family: monospace;\n" +
                "    cursor: pointer;\n" +
                "}\n" +
                "\n" +
                ".ip-tag:hover {\n" +
                "    background: rgba(0, 123, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".no-login-info {\n" +
                "    display: inline-block;\n" +
                "    padding: 2px 6px;\n" +
                "    background: rgba(108, 117, 125, 0.1);\n" +
                "    color: #6c757d;\n" +
                "    border: 1px solid rgba(108, 117, 125, 0.3);\n" +
                "    border-radius: 10px;\n" +
                "    font-size: 11px;\n" +
                "    font-weight: 500;\n" +
                "    font-style: italic;\n" +
                "}\n" +
                "\n" +
                ".multi-ip-warning {\n" +
                "    color: #ffc107;\n" +
                "    font-size: 16px;\n" +
                "    margin-left: 5px;\n" +
                "    cursor: help;\n" +
                "}\n" +
                "\n" +
                ".ip-list {\n" +
                "    grid-column: 1 / -1;\n" +
                "}\n" +
                "\n" +
                ".ip-management-item {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    padding: 10px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    border-radius: 4px;\n" +
                "    margin-bottom: 8px;\n" +
                "    background: #f8f9fa;\n" +
                "}\n" +
                "\n" +
                ".ip-info strong {\n" +
                "    font-family: monospace;\n" +
                "    color: #007bff;\n" +
                "}\n" +
                "\n" +
                ".ip-details {\n" +
                "    display: block;\n" +
                "    font-size: 12px;\n" +
                "    color: #6c757d;\n" +
                "    margin-top: 2px;\n" +
                "}\n" +
                "\n" +
                ".ip-actions {\n" +
                "    display: flex;\n" +
                "    gap: 5px;\n" +
                "}\n" +
                "\n" +
                ".ip-summary {\n" +
                "    background: #f8f9fa;\n" +
                "    padding: 15px;\n" +
                "    border-radius: 4px;\n" +
                "    margin-bottom: 20px;\n" +
                "    border-left: 4px solid #007bff;\n" +
                "}\n" +
                "\n" +
                ".ip-summary p {\n" +
                "    margin: 5px 0;\n" +
                "}\n\n";
    }

    /**
     * 生成登出按钮样式
     */
    private String generateLogoutStyles() {
        return "/* ==================== 登出按钮样式 ==================== */\n" +
                ".logout-btn {\n" +
                "    color: #dc3545 !important;\n" +
                "    transition: all 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".logout-btn:hover {\n" +
                "    background-color: #dc3545 !important;\n" +
                "    color: white !important;\n" +
                "}\n" +
                "\n" +
                ".sidebar .divider {\n" +
                "    height: 1px;\n" +
                "    background-color: #e9ecef;\n" +
                "    margin: 10px 0;\n" +
                "}\n\n";
    }

    /**
     * 生成夜间主题样式
     */
    private String generateDarkThemeStyles() {
        return "/* ==================== 账号管理夜间主题样式 ==================== */\n" +
                "/* 标签页夜间主题 */\n" +
                "body.theme-dark .tab-navigation {\n" +
                "    border-bottom: 2px solid #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .tab-btn {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .tab-btn:hover {\n" +
                "    color: #667eea;\n" +
                "    background: rgba(102, 126, 234, 0.1);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .tab-btn.active {\n" +
                "    color: #667eea;\n" +
                "    border-bottom-color: #667eea;\n" +
                "    background: rgba(102, 126, 234, 0.1);\n" +
                "}\n" +
                "\n" +
                "/* 工具栏夜间主题 */\n" +
                "body.theme-dark .toolbar {\n" +
                "    background: #2d2d2d;\n" +
                "    border: 1px solid #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-count {\n" +
                "    color: #cbd5e0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-count strong {\n" +
                "    color: #667eea;\n" +
                "}\n" +
                "\n" +
                "/* 账号卡片夜间主题 */\n" +
                "body.theme-dark .account-card {\n" +
                "    background: #2d2d2d;\n" +
                "    border: 1px solid #404040;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-card.disabled {\n" +
                "    background: #2d2d2d;\n" +
                "    border: 1px solid #404040;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-header {\n" +
                "    border-bottom: 1px solid #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-header h4 {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .info-item .label {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .info-item .value {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "/* 弹窗夜间主题 */\n" +
                "body.theme-dark .modal-content {\n" +
                "    background-color: #2d2d2d;\n" +
                "    border: 1px solid #404040;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .modal-header {\n" +
                "    border-bottom: 1px solid #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .modal-header h3 {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .close {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .close:hover {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "/* 表单夜间主题 */\n" +
                "body.theme-dark .form-group label {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .form-input {\n" +
                "    background: #2d2d2d;\n" +
                "    border: 1px solid #4a5568;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .form-input:focus {\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.25);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .form-actions {\n" +
                "    border-top: 1px solid #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .search-box input {\n" +
                "    background: #2d2d2d;\n" +
                "    border: 1px solid #4a5568;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .search-box input:focus {\n" +
                "    border-color: #667eea;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .no-data {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .loading-spinner {\n" +
                "    color: #667eea;\n" +
                "}\n" +
                "\n" +
                "/* 账号状态夜间主题 */\n" +
                "body.theme-dark .account-card.enabled {\n" +
                "    border-left: 4px solid #38a169;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-card.disabled {\n" +
                "    border-left: 4px solid #e53e3e;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-status.enabled {\n" +
                "    background: rgba(56, 161, 105, 0.2);\n" +
                "    color: #68d391;\n" +
                "    border: 1px solid rgba(56, 161, 105, 0.4);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-status.disabled {\n" +
                "    background: rgba(229, 62, 62, 0.2);\n" +
                "    color: #fc8181;\n" +
                "    border: 1px solid rgba(229, 62, 62, 0.4);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-status.banned {\n" +
                "    background: rgba(160, 174, 192, 0.2);\n" +
                "    color: #a0aec0;\n" +
                "    border: 1px solid rgba(160, 174, 192, 0.4);\n" +
                "    font-weight: bold;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .account-card.banned {\n" +
                "    border-left: 4px solid #a0aec0;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ban-info {\n" +
                "    background: rgba(160, 174, 192, 0.1);\n" +
                "    border-left: 3px solid #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ban-reason {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "/* 角色管理夜间主题 */\n" +
                "body.theme-dark .role-card {\n" +
                "    background: #2d2d2d;\n" +
                "    border: 1px solid #404040;\n" +
                "    color: #f7fafc;\n" +
                "    border-left: 4px solid #667eea;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-header {\n" +
                "    border-bottom: 1px solid #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-header h4 {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-id {\n" +
                "    background: #404040;\n" +
                "    border: 1px solid #4a5568;\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-description p {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-permissions strong {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .permission-tag {\n" +
                "    background: rgba(102, 126, 234, 0.2);\n" +
                "    color: #a3bffa;\n" +
                "    border: 1px solid rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-count {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .role-count strong {\n" +
                "    color: #667eea;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .permissions-preview {\n" +
                "    background: #404040;\n" +
                "    border: 1px solid #4a5568;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .text-muted {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .form-text {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "/* 权限复选框夜间主题 */\n" +
                "body.theme-dark .checkbox-label:hover {\n" +
                "    background-color: #404040;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .checkbox-label span {\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "/* IP管理夜间主题 */\n" +
                "body.theme-dark .ip-address {\n" +
                "    background: #404040;\n" +
                "    border: 1px solid #4a5568;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ip-tag {\n" +
                "    background: rgba(102, 126, 234, 0.2);\n" +
                "    color: #a3bffa;\n" +
                "    border: 1px solid rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ip-tag:hover {\n" +
                "    background: rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ip-management-item {\n" +
                "    background: #404040;\n" +
                "    border: 1px solid #4a5568;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ip-info strong {\n" +
                "    color: #667eea;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ip-details {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .ip-summary {\n" +
                "    background: #404040;\n" +
                "    border-left: 4px solid #667eea;\n" +
                "    color: #f7fafc;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .no-login-info {\n" +
                "    background: rgba(160, 174, 192, 0.1);\n" +
                "    color: #a0aec0;\n" +
                "    border: 1px solid rgba(160, 174, 192, 0.3);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .permission-stats {\n" +
                "    color: #667eea;\n" +
                "}\n" +
                "\n" +
                "body.theme-dark .permission-percentage {\n" +
                "    color: #a0aec0;\n" +
                "}\n" +
                "\n" +
                "/* 夜间主题滚动条样式 */\n" +
                "body.theme-dark:hover::-webkit-scrollbar-track {\n" +
                "    background: rgba(255, 255, 255, 0.1);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark:hover::-webkit-scrollbar-thumb {\n" +
                "    background: rgba(255, 255, 255, 0.3);\n" +
                "}\n" +
                "\n" +
                "body.theme-dark:hover::-webkit-scrollbar-thumb:hover {\n" +
                "    background: rgba(255, 255, 255, 0.5);\n" +
                "}\n\n";
    }
}
