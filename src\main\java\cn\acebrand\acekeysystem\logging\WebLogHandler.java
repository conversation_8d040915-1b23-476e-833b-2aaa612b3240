package cn.acebrand.acekeysystem.logging;

import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.logging.Handler;
import java.util.logging.LogRecord;

/**
 * 自定义日志处理器
 * 根据配置决定是否在控制台显示日志，同时将日志写入网站日志文件
 */
public class WebLogHandler extends Handler {

    private final AceKeySystem plugin;
    private final File webLogFile;
    private final SimpleDateFormat dateFormat;
    private final List<String> logBuffer;
    private static final int MAX_BUFFER_SIZE = 1000;
    private volatile boolean isProcessing = false;

    public WebLogHandler(AceKeySystem plugin) {
        this.plugin = plugin;
        this.webLogFile = new File(plugin.getDataFolder(), "web-logs.txt");
        this.dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        this.logBuffer = new ArrayList<>();

        // 确保日志文件目录存在
        if (!plugin.getDataFolder().exists()) {
            plugin.getDataFolder().mkdirs();
        }
    }

    @Override
    public void publish(LogRecord record) {
        // 防止递归调用
        if (isProcessing || !isLoggable(record)) {
            return;
        }

        // 只处理AceKeySystem相关的日志
        String loggerName = record.getLoggerName();
        if (loggerName == null || !loggerName.contains("AceKeySystem")) {
            return;
        }

        try {
            isProcessing = true;

            // 格式化日志消息
            String formattedMessage = formatLogRecord(record);

            // 添加到缓冲区
            synchronized (logBuffer) {
                logBuffer.add(formattedMessage);

                // 限制缓冲区大小
                if (logBuffer.size() > MAX_BUFFER_SIZE) {
                    logBuffer.remove(0);
                }
            }

            // 写入网站日志文件
            writeToWebLogFile(formattedMessage);
        } finally {
            isProcessing = false;
        }
    }

    /**
     * 格式化日志记录
     */
    private String formatLogRecord(LogRecord record) {
        StringBuilder sb = new StringBuilder();

        // 时间戳
        sb.append("[").append(dateFormat.format(new Date(record.getMillis()))).append("] ");

        // 日志级别
        sb.append("[").append(record.getLevel().getName()).append("] ");

        // 日志消息
        sb.append(record.getMessage());

        // 异常信息
        if (record.getThrown() != null) {
            sb.append(" - Exception: ").append(record.getThrown().getMessage());
        }

        return sb.toString();
    }

    /**
     * 写入网站日志文件
     */
    private void writeToWebLogFile(String message) {
        try (FileWriter writer = new FileWriter(webLogFile, true)) {
            writer.write(message + System.lineSeparator());
            writer.flush();
        } catch (IOException e) {
            // 避免递归日志错误
            System.err.println("Failed to write to web log file: " + e.getMessage());
        }
    }

    /**
     * 获取最近的日志记录
     */
    public List<String> getRecentLogs(int maxLines, String levelFilter) {
        List<String> result = new ArrayList<>();

        synchronized (logBuffer) {
            for (String logLine : logBuffer) {
                // 过滤日志级别
                if ("all".equals(levelFilter) || logLine.contains("[" + levelFilter + "]")) {
                    result.add(logLine);
                }
            }
        }

        // 只返回最后的指定行数
        int startIndex = Math.max(0, result.size() - maxLines);
        return result.subList(startIndex, result.size());
    }

    /**
     * 清空日志缓冲区和文件
     */
    public void clearLogs() {
        synchronized (logBuffer) {
            logBuffer.clear();
        }

        try {
            if (webLogFile.exists()) {
                new FileWriter(webLogFile, false).close();
            }
        } catch (IOException e) {
            System.err.println("Failed to clear web log file: " + e.getMessage());
        }
    }

    /**
     * 手动保存日志到logs文件夹
     * 供管理员随时调用
     */
    public void manualSaveToServerFolder() {
        saveLogsToServerFolder();
    }

    @Override
    public void flush() {
        // 不需要特殊的flush操作
    }

    @Override
    public void close() throws SecurityException {
        // 在关闭前保存日志到logs文件夹
        saveLogsToServerFolder();

        // 清理资源
        synchronized (logBuffer) {
            logBuffer.clear();
        }
    }

    /**
     * 将网站日志保存到插件logs文件夹
     */
    private void saveLogsToServerFolder() {
        try {
            // 获取插件数据文件夹内的logs文件夹
            File pluginLogsDir = new File(plugin.getDataFolder(), "logs");
            if (!pluginLogsDir.exists()) {
                pluginLogsDir.mkdirs();
            }

            // 生成带年月时间的文件名
            SimpleDateFormat fileNameFormat = new SimpleDateFormat("yyyy-MM");
            String fileName = "acekeysystem-web-logs_" + fileNameFormat.format(new Date()) + ".log";
            File targetFile = new File(pluginLogsDir, fileName);

            // 保存日志缓冲区内容
            synchronized (logBuffer) {
                if (!logBuffer.isEmpty()) {
                    // 检查文件是否已存在，决定是否添加文件头
                    boolean fileExists = targetFile.exists() && targetFile.length() > 0;

                    try (FileWriter writer = new FileWriter(targetFile, true)) { // 使用追加模式
                        if (!fileExists) {
                            // 如果是新文件，添加文件头
                            writer.write("# AceKeySystem 网站日志文件" + System.lineSeparator());
                            writer.write("# 文件创建时间: " + new Date() + System.lineSeparator());
                            writer.write("# ===========================================" + System.lineSeparator());
                            writer.write(System.lineSeparator());
                        }

                        // 添加本次保存的分隔符和时间戳
                        writer.write(System.lineSeparator());
                        writer.write("# ========== 保存时间: " + new Date() + " ==========" + System.lineSeparator());
                        writer.write("# 本次保存 " + logBuffer.size() + " 条日志记录" + System.lineSeparator());
                        writer.write(System.lineSeparator());

                        for (String logLine : logBuffer) {
                            writer.write(logLine + System.lineSeparator());
                        }

                        writer.flush();
                    }

                    plugin.getLogger()
                            .info("网站日志已保存到: " + targetFile.getAbsolutePath() + " (共 " + logBuffer.size() + " 条记录)");
                }
            }

            // 同时复制当前的web-logs.txt文件
            if (webLogFile.exists() && webLogFile.length() > 0) {
                // 使用更精确的时间戳避免文件名冲突
                SimpleDateFormat detailedFormat = new SimpleDateFormat("yyyy-MM-dd_HH-mm-ss");
                String webFileName = "acekeysystem-web-logs-backup_" + detailedFormat.format(new Date()) + ".log";
                File webTargetFile = new File(pluginLogsDir, webFileName);

                try (FileInputStream fis = new FileInputStream(webLogFile);
                        FileOutputStream fos = new FileOutputStream(webTargetFile)) {

                    // 添加文件头信息
                    String header = "# AceKeySystem 网站日志文件备份" + System.lineSeparator() +
                            "# 备份时间: " + new Date() + System.lineSeparator() +
                            "# 原文件: " + webLogFile.getAbsolutePath() + System.lineSeparator() +
                            "# ===========================================" + System.lineSeparator() +
                            System.lineSeparator();
                    fos.write(header.getBytes());

                    // 复制文件内容
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((length = fis.read(buffer)) > 0) {
                        fos.write(buffer, 0, length);
                    }
                }

                plugin.getLogger().info("网站日志文件已备份到: " + webTargetFile.getAbsolutePath());
            }

        } catch (Exception e) {
            // 使用System.err避免递归日志
            System.err.println("保存网站日志到logs文件夹时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
