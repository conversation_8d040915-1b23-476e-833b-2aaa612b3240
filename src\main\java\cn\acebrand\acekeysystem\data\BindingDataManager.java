package cn.acebrand.acekeysystem.data;

import cn.acebrand.acekeysystem.AceKeySystem;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;
import java.util.logging.Level;

/**
 * 绑定数据管理器
 * 专门管理玩家绑定相关的数据存储
 */
public class BindingDataManager {

    private final AceKeySystem plugin;
    private File bindingFile;
    private FileConfiguration bindingConfig;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public BindingDataManager(AceKeySystem plugin) {
        this.plugin = plugin;
        setupBindingFile();
    }

    /**
     * 设置绑定数据文件
     */
    private void setupBindingFile() {
        bindingFile = new File(plugin.getDataFolder(), "bindings.yml");

        if (!bindingFile.exists()) {
            try {
                bindingFile.createNewFile();
                plugin.getLogger().info("创建绑定数据文件: bindings.yml");
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建绑定数据文件: " + e.getMessage());
            }
        }

        bindingConfig = YamlConfiguration.loadConfiguration(bindingFile);

        // 添加文件头注释
        if (!bindingConfig.contains("info")) {
            bindingConfig.set("info.description", "AceKey系统绑定数据文件");
            bindingConfig.set("info.version", "1.0");
            bindingConfig.set("info.created", System.currentTimeMillis());
            saveBindingConfig();
        }
    }

    /**
     * 保存绑定配置文件
     */
    public void saveBindingConfig() {
        try {
            bindingConfig.save(bindingFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存绑定数据文件", e);
        }
    }

    /**
     * 重载绑定配置文件
     */
    public void reloadBindingConfig() {
        bindingConfig = YamlConfiguration.loadConfiguration(bindingFile);
    }

    /**
     * 获取绑定配置
     */
    public FileConfiguration getBindingConfig() {
        return bindingConfig;
    }

    // ==================== 时间格式化工具 ====================

    /**
     * 将时间戳转换为可读格式
     */
    private String formatTime(long timestamp) {
        return dateFormat.format(new Date(timestamp));
    }

    /**
     * 将可读时间格式转换为时间戳
     */
    private long parseTime(String timeString) {
        try {
            return dateFormat.parse(timeString).getTime();
        } catch (Exception e) {
            // 如果解析失败，可能是旧格式的数字，直接返回
            try {
                return Long.parseLong(timeString);
            } catch (NumberFormatException ex) {
                return 0;
            }
        }
    }

    /**
     * 获取解绑冷却时间（毫秒）
     */
    private long getUnbindCooldownMillis() {
        // 新配置格式
        if (plugin.getConfig().contains("binding.unbind-cooldown.value")) {
            int value = plugin.getConfig().getInt("binding.unbind-cooldown.value", 24);
            String unit = plugin.getConfig().getString("binding.unbind-cooldown.unit", "hours");

            if ("seconds".equalsIgnoreCase(unit)) {
                return value * 1000L; // 秒转毫秒
            } else {
                return value * 60 * 60 * 1000L; // 小时转毫秒
            }
        }

        // 兼容旧配置格式
        int unbindCooldownHours = plugin.getConfig().getInt("binding.unbind-cooldown-hours", 24);
        return unbindCooldownHours * 60 * 60 * 1000L;
    }

    /**
     * 获取解绑冷却时间配置信息（用于存储）
     */
    private String getUnbindCooldownConfig() {
        if (plugin.getConfig().contains("binding.unbind-cooldown.value")) {
            int value = plugin.getConfig().getInt("binding.unbind-cooldown.value", 24);
            String unit = plugin.getConfig().getString("binding.unbind-cooldown.unit", "hours");
            return value + " " + unit;
        }

        // 兼容旧配置格式
        int unbindCooldownHours = plugin.getConfig().getInt("binding.unbind-cooldown-hours", 24);
        return unbindCooldownHours + " hours";
    }

    // ==================== 绑定码管理 ====================

    /**
     * 存储绑定码
     */
    public void storeBindCode(String bindCode, String sessionId, long expiryTime) {
        String path = "bind_codes." + bindCode;
        long currentTime = System.currentTimeMillis();
        bindingConfig.set(path + ".created_time", formatTime(currentTime));
        bindingConfig.set(path + ".created_timestamp", currentTime);
        bindingConfig.set(path + ".expiry_time", formatTime(expiryTime));
        bindingConfig.set(path + ".expiry_timestamp", expiryTime);
        bindingConfig.set(path + ".session_id", sessionId);
        bindingConfig.set(path + ".used", false);
        saveBindingConfig();
    }

    /**
     * 检查绑定码是否存在
     */
    public boolean bindCodeExists(String bindCode) {
        return bindingConfig.contains("bind_codes." + bindCode);
    }

    /**
     * 检查绑定码是否已使用
     */
    public boolean isBindCodeUsed(String bindCode) {
        return bindingConfig.getBoolean("bind_codes." + bindCode + ".used", false);
    }

    /**
     * 检查绑定码是否过期
     */
    public boolean isBindCodeExpired(String bindCode) {
        // 优先使用timestamp，如果没有则尝试解析时间字符串
        long expiryTime = bindingConfig.getLong("bind_codes." + bindCode + ".expiry_timestamp", 0);
        if (expiryTime == 0) {
            String expiryTimeStr = bindingConfig.getString("bind_codes." + bindCode + ".expiry_time");
            if (expiryTimeStr != null) {
                expiryTime = parseTime(expiryTimeStr);
            }
        }
        return System.currentTimeMillis() > expiryTime;
    }

    /**
     * 标记绑定码为已使用
     */
    public void markBindCodeAsUsed(String bindCode, String username, UUID playerUuid) {
        String path = "bind_codes." + bindCode;
        long currentTime = System.currentTimeMillis();
        bindingConfig.set(path + ".used", true);
        bindingConfig.set(path + ".used_by", username);
        bindingConfig.set(path + ".used_time", formatTime(currentTime));
        bindingConfig.set(path + ".used_timestamp", currentTime);
        bindingConfig.set(path + ".player_uuid", playerUuid.toString());
        saveBindingConfig();
    }

    /**
     * 获取绑定码使用者
     */
    public String getBindCodeUser(String bindCode) {
        return bindingConfig.getString("bind_codes." + bindCode + ".used_by");
    }

    /**
     * 获取绑定码关联的玩家UUID
     */
    public String getBindCodePlayerUuid(String bindCode) {
        return bindingConfig.getString("bind_codes." + bindCode + ".player_uuid");
    }

    // ==================== 会话管理 ====================

    /**
     * 存储会话信息
     */
    public void storeSession(String sessionId, String bindCode) {
        String path = "sessions." + sessionId;
        long currentTime = System.currentTimeMillis();
        bindingConfig.set(path + ".bind_code", bindCode);
        bindingConfig.set(path + ".created_time", formatTime(currentTime));
        bindingConfig.set(path + ".created_timestamp", currentTime);
        saveBindingConfig();
    }

    /**
     * 获取会话关联的绑定码
     */
    public String getSessionBindCode(String sessionId) {
        return bindingConfig.getString("sessions." + sessionId + ".bind_code");
    }

    // ==================== 玩家绑定管理 ====================

    /**
     * 存储玩家绑定信息
     */
    public void storePlayerBinding(UUID playerUuid, String username, String bindCode) {
        String path = "player_bindings." + playerUuid.toString();
        long currentTime = System.currentTimeMillis();

        // 获取配置的冷却时间
        long unbindCooldownMillis = getUnbindCooldownMillis();
        long unbindAvailableTime = currentTime + unbindCooldownMillis;

        bindingConfig.set(path + ".username", username);
        bindingConfig.set(path + ".bind_time", formatTime(currentTime));
        bindingConfig.set(path + ".bind_timestamp", currentTime);
        bindingConfig.set(path + ".bind_code", bindCode);

        // 存储解绑相关时间信息
        bindingConfig.set(path + ".unbind_available_time", formatTime(unbindAvailableTime));
        bindingConfig.set(path + ".unbind_available_timestamp", unbindAvailableTime);
        bindingConfig.set(path + ".unbind_cooldown_config", getUnbindCooldownConfig());

        saveBindingConfig();
    }

    /**
     * 获取玩家绑定信息
     */
    public String getPlayerUsername(UUID playerUuid) {
        return bindingConfig.getString("player_bindings." + playerUuid.toString() + ".username");
    }

    /**
     * 获取玩家绑定时间
     */
    public long getPlayerBindTime(UUID playerUuid) {
        String path = "player_bindings." + playerUuid.toString();
        // 优先使用timestamp，如果没有则尝试解析时间字符串
        long bindTime = bindingConfig.getLong(path + ".bind_timestamp", 0);
        if (bindTime == 0) {
            String bindTimeStr = bindingConfig.getString(path + ".bind_time");
            if (bindTimeStr != null) {
                bindTime = parseTime(bindTimeStr);
            }
        }
        return bindTime;
    }

    /**
     * 检查玩家是否已绑定
     */
    public boolean isPlayerBound(UUID playerUuid) {
        return bindingConfig.contains("player_bindings." + playerUuid.toString());
    }

    /**
     * 解绑玩家账户
     */
    public void unbindPlayer(UUID playerUuid) {
        String path = "player_bindings." + playerUuid.toString();
        String username = getPlayerUsername(playerUuid);
        long currentTime = System.currentTimeMillis();

        // 获取绑定码信息用于清理
        String bindCode = bindingConfig.getString(path + ".bind_code");

        // 记录解绑历史
        String historyPath = "unbind_history." + playerUuid.toString() + "." + currentTime;
        bindingConfig.set(historyPath + ".username", username);
        bindingConfig.set(historyPath + ".unbind_time", formatTime(currentTime));
        bindingConfig.set(historyPath + ".unbind_timestamp", currentTime);
        if (bindCode != null) {
            bindingConfig.set(historyPath + ".bind_code", bindCode);
        }

        // 移除绑定信息
        bindingConfig.set(path, null);

        // 清理对应的绑定码记录
        if (bindCode != null) {
            cleanupBindCodeRecord(bindCode, username, playerUuid);
        }

        saveBindingConfig();

        plugin.getLogger().info("玩家 " + username + " (" + playerUuid + ") 已解绑，解绑时间: " + formatTime(currentTime));
    }

    /**
     * 清理绑定码记录
     */
    private void cleanupBindCodeRecord(String bindCode, String username, UUID playerUuid) {
        String bindCodePath = "bind_codes." + bindCode;

        if (bindingConfig.contains(bindCodePath)) {
            // 检查绑定码是否确实是这个玩家使用的
            String usedBy = bindingConfig.getString(bindCodePath + ".used_by");
            String playerUuidStr = bindingConfig.getString(bindCodePath + ".player_uuid");

            if (username.equals(usedBy) && playerUuid.toString().equals(playerUuidStr)) {
                // 移除绑定码记录
                bindingConfig.set(bindCodePath, null);
                plugin.getLogger().info("已清理玩家 " + username + " 的绑定码记录: " + bindCode);
            } else {
                plugin.getLogger().warning("绑定码 " + bindCode + " 的使用者信息不匹配，跳过清理");
            }
        }
    }

    /**
     * 检查玩家是否可以解绑（时间限制）
     */
    public boolean canUnbindPlayer(UUID playerUuid) {
        String path = "player_bindings." + playerUuid.toString();
        if (!bindingConfig.contains(path)) {
            return false; // 未绑定
        }

        // 优先使用存储的解绑可用时间
        long unbindAvailableTime = bindingConfig.getLong(path + ".unbind_available_timestamp", 0);
        if (unbindAvailableTime == 0) {
            String unbindAvailableTimeStr = bindingConfig.getString(path + ".unbind_available_time");
            if (unbindAvailableTimeStr != null) {
                unbindAvailableTime = parseTime(unbindAvailableTimeStr);
            } else {
                // 如果没有存储解绑时间，则根据绑定时间计算
                long bindTime = getPlayerBindTime(playerUuid);
                if (bindTime == 0) {
                    return false;
                }
                long unbindCooldownMillis = getUnbindCooldownMillis();
                unbindAvailableTime = bindTime + unbindCooldownMillis;
            }
        }

        return System.currentTimeMillis() >= unbindAvailableTime;
    }

    /**
     * 获取玩家下次可以解绑的时间
     */
    public long getNextUnbindTime(UUID playerUuid) {
        String path = "player_bindings." + playerUuid.toString();
        if (!bindingConfig.contains(path)) {
            return -1; // 未绑定
        }

        // 优先使用存储的解绑可用时间
        long unbindAvailableTime = bindingConfig.getLong(path + ".unbind_available_timestamp", 0);
        if (unbindAvailableTime == 0) {
            String unbindAvailableTimeStr = bindingConfig.getString(path + ".unbind_available_time");
            if (unbindAvailableTimeStr != null) {
                unbindAvailableTime = parseTime(unbindAvailableTimeStr);
            } else {
                // 如果没有存储解绑时间，则根据绑定时间计算
                long bindTime = getPlayerBindTime(playerUuid);
                if (bindTime == 0) {
                    return -1;
                }
                long unbindCooldownMillis = getUnbindCooldownMillis();
                unbindAvailableTime = bindTime + unbindCooldownMillis;
            }
        }

        return unbindAvailableTime;
    }

    /**
     * 根据用户名查找玩家UUID
     */
    public UUID findPlayerUuidByUsername(String username) {
        if (bindingConfig.contains("player_bindings")) {
            for (String uuidStr : bindingConfig.getConfigurationSection("player_bindings").getKeys(false)) {
                String boundUsername = bindingConfig.getString("player_bindings." + uuidStr + ".username");
                if (username.equals(boundUsername)) {
                    try {
                        return UUID.fromString(uuidStr);
                    } catch (IllegalArgumentException e) {
                        // 忽略无效的UUID
                    }
                }
            }
        }
        return null;
    }

    // ==================== 数据清理 ====================

    /**
     * 清理过期的绑定码
     */
    public void cleanupExpiredBindCodes() {
        if (!bindingConfig.contains("bind_codes")) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;

        for (String bindCode : bindingConfig.getConfigurationSection("bind_codes").getKeys(false)) {
            // 优先使用timestamp，如果没有则尝试解析时间字符串
            long expiryTime = bindingConfig.getLong("bind_codes." + bindCode + ".expiry_timestamp", 0);
            if (expiryTime == 0) {
                String expiryTimeStr = bindingConfig.getString("bind_codes." + bindCode + ".expiry_time");
                if (expiryTimeStr != null) {
                    expiryTime = parseTime(expiryTimeStr);
                }
            }

            // 清理超过24小时的过期绑定码
            if (expiryTime > 0 && currentTime - expiryTime > 24 * 60 * 60 * 1000) {
                bindingConfig.set("bind_codes." + bindCode, null);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            saveBindingConfig();
            plugin.getLogger().info("清理了 " + cleanedCount + " 个过期绑定码");
        }
    }

    /**
     * 清理过期的会话
     */
    public void cleanupExpiredSessions() {
        if (!bindingConfig.contains("sessions")) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;

        for (String sessionId : bindingConfig.getConfigurationSection("sessions").getKeys(false)) {
            // 优先使用timestamp，如果没有则尝试解析时间字符串
            long createdTime = bindingConfig.getLong("sessions." + sessionId + ".created_timestamp", 0);
            if (createdTime == 0) {
                String createdTimeStr = bindingConfig.getString("sessions." + sessionId + ".created_time");
                if (createdTimeStr != null) {
                    createdTime = parseTime(createdTimeStr);
                }
            }

            // 清理超过24小时的会话
            if (createdTime > 0 && currentTime - createdTime > 24 * 60 * 60 * 1000) {
                bindingConfig.set("sessions." + sessionId, null);
                cleanedCount++;
            }
        }

        if (cleanedCount > 0) {
            saveBindingConfig();
            plugin.getLogger().info("清理了 " + cleanedCount + " 个过期会话");
        }
    }

    /**
     * 获取统计信息
     */
    public void printStatistics() {
        int totalBindCodes = bindingConfig.contains("bind_codes")
                ? bindingConfig.getConfigurationSection("bind_codes").getKeys(false).size()
                : 0;
        int totalSessions = bindingConfig.contains("sessions")
                ? bindingConfig.getConfigurationSection("sessions").getKeys(false).size()
                : 0;
        int totalBindings = bindingConfig.contains("player_bindings")
                ? bindingConfig.getConfigurationSection("player_bindings").getKeys(false).size()
                : 0;

        plugin.getLogger().info("绑定数据统计 - 绑定码: " + totalBindCodes +
                ", 会话: " + totalSessions +
                ", 玩家绑定: " + totalBindings);
    }

    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        try {
            bindingConfig = YamlConfiguration.loadConfiguration(bindingFile);

            // 检查并更新解绑时间配置
            updateUnbindTimeConfiguration();

            plugin.getLogger().info("绑定数据配置文件已重新加载");
        } catch (Exception e) {
            plugin.getLogger().severe("重新加载绑定数据配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 更新解绑时间配置（配置重载时调用）
     */
    public void updateUnbindTimeConfiguration() {
        if (!bindingConfig.contains("player_bindings")) {
            return;
        }

        long currentTime = System.currentTimeMillis();
        long newUnbindCooldownMillis = getUnbindCooldownMillis();
        String newUnbindCooldownConfig = getUnbindCooldownConfig();

        int updatedCount = 0;

        for (String playerUuidStr : bindingConfig.getConfigurationSection("player_bindings").getKeys(false)) {
            String path = "player_bindings." + playerUuidStr;

            try {
                // 获取绑定时间
                long bindTime = getPlayerBindTime(UUID.fromString(playerUuidStr));
                if (bindTime == 0) {
                    continue;
                }

                // 计算新的解绑可用时间
                long newUnbindAvailableTime = bindTime + newUnbindCooldownMillis;

                // 更新配置
                bindingConfig.set(path + ".unbind_available_time", formatTime(newUnbindAvailableTime));
                bindingConfig.set(path + ".unbind_available_timestamp", newUnbindAvailableTime);
                bindingConfig.set(path + ".unbind_cooldown_config", newUnbindCooldownConfig);

                updatedCount++;

                // 记录日志
                String username = bindingConfig.getString(path + ".username", "未知");
                long timePassed = currentTime - bindTime;
                boolean canUnbindNow = currentTime >= newUnbindAvailableTime;

                plugin.getLogger().info(String.format(
                        "更新玩家 %s 的解绑配置: 绑定时间=%s, 新解绑时间=%s, 当前%s解绑",
                        username,
                        formatTime(bindTime),
                        formatTime(newUnbindAvailableTime),
                        canUnbindNow ? "可以" : "不能"));
            } catch (Exception e) {
                plugin.getLogger().warning("更新玩家 " + playerUuidStr + " 的解绑配置时出错: " + e.getMessage());
            }
        }

        if (updatedCount > 0) {
            saveBindingConfig();
            plugin.getLogger().info("已更新 " + updatedCount + " 个玩家的解绑时间配置");
        }
    }
}
