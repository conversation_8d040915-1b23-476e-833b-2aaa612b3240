package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.checkin.CheckInManager;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.bukkit.configuration.file.FileConfiguration;

import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理员签到统计处理器
 * 处理签到统计数据的获取和排行榜功能
 */
public class AdminCheckInStatsHandler implements HttpHandler {
    private final AceKeySystem plugin;
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private final SimpleDateFormat weekFormat = new SimpleDateFormat("yyyy-'W'ww");
    private final SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");

    public AdminCheckInStatsHandler(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();

        // 设置CORS头
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");

        if ("OPTIONS".equals(method)) {
            exchange.sendResponseHeaders(200, 0);
            exchange.close();
            return;
        }


        try {
            if (path.endsWith("/stats")) {
                handleGetStats(exchange);
            } else if (path.endsWith("/leaderboard")) {
                handleGetLeaderboard(exchange);
            } else if (path.endsWith("/player-records")) {
                handleGetPlayerRecords(exchange);
            } else {
                sendResponse(exchange, 404, "{\"success\": false, \"message\": \"接口不存在\"}");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理签到统计请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "{\"success\": false, \"message\": \"服务器内部错误\"}");
        }
    }

    /**
     * 处理获取统计数据请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetStats(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        JSONObject stats = new JSONObject();

        CheckInManager checkInManager = plugin.getCheckInManager();
        FileConfiguration config = checkInManager.getCheckInConfig();

        String today = dateFormat.format(new Date());
        String thisWeek = weekFormat.format(new Date());
        String thisMonth = monthFormat.format(new Date());

        // 统计今日签到人数
        int todayCount = 0;
        int weekCount = 0;
        int monthCount = 0;
        int maxConsecutive = 0;

        if (config.getConfigurationSection("players") != null) {
            for (String playerName : config.getConfigurationSection("players").getKeys(false)) {
                String playerPath = "players." + playerName;

                // 今日签到
                if (config.getBoolean(playerPath + ".dates." + today, false)) {
                    todayCount++;
                }

                // 本周签到（简化计算，检查最近7天）
                Calendar cal = Calendar.getInstance();
                for (int i = 0; i < 7; i++) {
                    String checkDate = dateFormat.format(cal.getTime());
                    if (config.getBoolean(playerPath + ".dates." + checkDate, false)) {
                        weekCount++;
                        break; // 每个玩家只计算一次
                    }
                    cal.add(Calendar.DAY_OF_MONTH, -1);
                }

                // 本月签到（检查本月所有日期）
                cal = Calendar.getInstance();
                cal.set(Calendar.DAY_OF_MONTH, 1);
                while (cal.get(Calendar.MONTH) == Calendar.getInstance().get(Calendar.MONTH)) {
                    String checkDate = dateFormat.format(cal.getTime());
                    if (config.getBoolean(playerPath + ".dates." + checkDate, false)) {
                        monthCount++;
                        break; // 每个玩家只计算一次
                    }
                    cal.add(Calendar.DAY_OF_MONTH, 1);
                }

                // 最高连续签到天数
                int consecutive = config.getInt(playerPath + ".consecutive_days", 0);
                if (consecutive > maxConsecutive) {
                    maxConsecutive = consecutive;
                }
            }
        }

        stats.put("today_checkins", todayCount);
        stats.put("week_checkins", weekCount);
        stats.put("month_checkins", monthCount);
        stats.put("max_consecutive", maxConsecutive);

        response.put("success", true);
        response.put("stats", stats);

        sendResponse(exchange, 200, response.toString());
    }

    /**
     * 处理获取排行榜请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetLeaderboard(HttpExchange exchange) throws IOException {
        String query = exchange.getRequestURI().getQuery();
        String type = "consecutive"; // 默认连续签到排行榜

        if (query != null && query.contains("type=")) {
            String[] params = query.split("&");
            for (String param : params) {
                if (param.startsWith("type=")) {
                    type = param.substring(5);
                    break;
                }
            }
        }

        JSONObject response = new JSONObject();
        JSONArray leaderboard = new JSONArray();

        CheckInManager checkInManager = plugin.getCheckInManager();
        FileConfiguration config = checkInManager.getCheckInConfig();

        List<PlayerStats> playerStatsList = new ArrayList<>();

        if (config.getConfigurationSection("players") != null) {
            for (String playerName : config.getConfigurationSection("players").getKeys(false)) {
                String playerPath = "players." + playerName;

                int consecutiveDays = config.getInt(playerPath + ".consecutive_days", 0);
                int totalDays = config.getInt(playerPath + ".total_days", 0);
                String lastCheckIn = config.getString(playerPath + ".last_checkin", "从未签到");

                playerStatsList.add(new PlayerStats(playerName, consecutiveDays, totalDays, lastCheckIn));
            }
        }

        // 根据类型排序
        if ("total".equals(type)) {
            playerStatsList.sort((a, b) -> Integer.compare(b.totalDays, a.totalDays));
        } else {
            playerStatsList.sort((a, b) -> Integer.compare(b.consecutiveDays, a.consecutiveDays));
        }

        // 取前20名
        for (int i = 0; i < Math.min(20, playerStatsList.size()); i++) {
            PlayerStats stats = playerStatsList.get(i);
            JSONObject playerData = new JSONObject();
            playerData.put("rank", i + 1);
            playerData.put("player", stats.playerName);
            playerData.put("consecutive_days", stats.consecutiveDays);
            playerData.put("total_days", stats.totalDays);
            playerData.put("last_checkin", stats.lastCheckIn);
            leaderboard.add(playerData);
        }

        response.put("success", true);
        response.put("type", type);
        response.put("leaderboard", leaderboard);

        sendResponse(exchange, 200, response.toString());
    }

    /**
     * 处理获取玩家签到记录请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetPlayerRecords(HttpExchange exchange) throws IOException {
        String query = exchange.getRequestURI().getQuery();
        String playerName = null;

        if (query != null && query.contains("player=")) {
            String[] params = query.split("&");
            for (String param : params) {
                if (param.startsWith("player=")) {
                    playerName = param.substring(7);
                    break;
                }
            }
        }

        JSONObject response = new JSONObject();

        if (playerName == null || playerName.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "玩家名不能为空");
            sendResponse(exchange, 400, response.toJSONString());
            return;
        }

        CheckInManager checkInManager = plugin.getCheckInManager();
        FileConfiguration config = checkInManager.getCheckInConfig();

        String playerPath = "players." + playerName;
        JSONObject playerData = new JSONObject();
        JSONArray checkInDates = new JSONArray();

        if (config.getConfigurationSection(playerPath) != null) {
            int consecutiveDays = config.getInt(playerPath + ".consecutive_days", 0);
            int totalDays = config.getInt(playerPath + ".total_days", 0);
            String lastCheckIn = config.getString(playerPath + ".last_checkin", "从未签到");
            int makeupCards = config.getInt(playerPath + ".makeup_cards", 0);

            playerData.put("player", playerName);
            playerData.put("consecutive_days", consecutiveDays);
            playerData.put("total_days", totalDays);
            playerData.put("last_checkin", lastCheckIn);
            playerData.put("makeup_cards", makeupCards);

            // 获取签到日期列表
            if (config.getConfigurationSection(playerPath + ".dates") != null) {
                Set<String> dates = config.getConfigurationSection(playerPath + ".dates").getKeys(false);
                List<String> sortedDates = dates.stream()
                        .filter(date -> config.getBoolean(playerPath + ".dates." + date, false))
                        .sorted()
                        .collect(Collectors.toList());

                for (String date : sortedDates) {
                    checkInDates.add(date);
                }
            }
        } else {
            playerData.put("player", playerName);
            playerData.put("consecutive_days", 0);
            playerData.put("total_days", 0);
            playerData.put("last_checkin", "从未签到");
            playerData.put("makeup_cards", 0);
        }

        playerData.put("checkin_dates", checkInDates);

        response.put("success", true);
        response.put("data", playerData);

        sendResponse(exchange, 200, response.toString());
    }


    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        OutputStream os = exchange.getResponseBody();
        os.write(responseBytes);
        os.close();
    }

    /**
     * 玩家统计数据类
     */
    private static class PlayerStats {
        final String playerName;
        final int consecutiveDays;
        final int totalDays;
        final String lastCheckIn;

        PlayerStats(String playerName, int consecutiveDays, int totalDays, String lastCheckIn) {
            this.playerName = playerName;
            this.consecutiveDays = consecutiveDays;
            this.totalDays = totalDays;
            this.lastCheckIn = lastCheckIn;
        }
    }
}
