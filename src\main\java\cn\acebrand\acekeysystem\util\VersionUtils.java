package cn.acebrand.acekeysystem.util;

/**
 * 版本工具类
 * 用于解析和比较Minecraft服务器版本
 */
public final class VersionUtils {

    /**
     * 从版本字符串中提取主版本号
     * 例如: "v1_16_R3" -> 16, "v1_12_R1" -> 12
     * 
     * @param versionString 版本字符串
     * @return 主版本号，如果解析失败则返回12
     */
    public static int extractMajorVersion(String versionString) {
        if (versionString.startsWith("v")) {
            String[] parts = versionString.split("_");
            if (parts.length >= 2) {
                try {
                    return Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    // 解析失败，返回默认值
                }
            }
        }
        return 12; // 默认返回1.12版本
    }

    /**
     * 检查版本是否支持现代API (1.13+)
     * 
     * @param versionString 版本字符串
     * @return 是否支持现代API
     */
    public static boolean supportsModernAPI(String versionString) {
        return extractMajorVersion(versionString) >= 13;
    }

    /**
     * 检查版本是否为旧版本 (1.12及以下)
     * 
     * @param versionString 版本字符串
     * @return 是否为旧版本
     */
    public static boolean isLegacyVersion(String versionString) {
        return extractMajorVersion(versionString) <= 12;
    }

    /**
     * 获取版本的友好显示名称
     * 
     * @param versionString 版本字符串
     * @return 友好显示名称
     */
    public static String getFriendlyVersionName(String versionString) {
        int majorVersion = extractMajorVersion(versionString);
        return "1." + majorVersion;
    }
}
