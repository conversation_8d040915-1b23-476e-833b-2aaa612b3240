package cn.acebrand.acekeysystem.ban;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 封禁记录数据模型
 */
public class BanRecord {
    
    private long id;
    private String uuid;
    private String playerName;
    private String ip;
    private String reason;
    private String bannedByUuid;
    private String bannedByName;
    private String removedByUuid;
    private String removedByName;
    private String removedByReason;
    private LocalDateTime removedByDate;
    private LocalDateTime time;
    private LocalDateTime until;
    private int template;
    private String serverScope;
    private String serverOrigin;
    private boolean silent;
    private boolean ipban;
    private boolean ipbanWildcard;
    private boolean active;
    
    // 构造函数
    public BanRecord() {}
    
    public BanRecord(long id, String uuid, String playerName, String reason, 
                    String bannedByName, LocalDateTime time, LocalDateTime until, boolean active) {
        this.id = id;
        this.uuid = uuid;
        this.playerName = playerName;
        this.reason = reason;
        this.bannedByName = bannedByName;
        this.time = time;
        this.until = until;
        this.active = active;
    }
    
    // Getter和Setter方法
    public long getId() { return id; }
    public void setId(long id) { this.id = id; }
    
    public String getUuid() { return uuid; }
    public void setUuid(String uuid) { this.uuid = uuid; }
    
    public String getPlayerName() { return playerName; }
    public void setPlayerName(String playerName) { this.playerName = playerName; }
    
    public String getIp() { return ip; }
    public void setIp(String ip) { this.ip = ip; }
    
    public String getReason() { return reason; }
    public void setReason(String reason) { this.reason = reason; }
    
    public String getBannedByUuid() { return bannedByUuid; }
    public void setBannedByUuid(String bannedByUuid) { this.bannedByUuid = bannedByUuid; }
    
    public String getBannedByName() { return bannedByName; }
    public void setBannedByName(String bannedByName) { this.bannedByName = bannedByName; }
    
    public String getRemovedByUuid() { return removedByUuid; }
    public void setRemovedByUuid(String removedByUuid) { this.removedByUuid = removedByUuid; }
    
    public String getRemovedByName() { return removedByName; }
    public void setRemovedByName(String removedByName) { this.removedByName = removedByName; }
    
    public String getRemovedByReason() { return removedByReason; }
    public void setRemovedByReason(String removedByReason) { this.removedByReason = removedByReason; }
    
    public LocalDateTime getRemovedByDate() { return removedByDate; }
    public void setRemovedByDate(LocalDateTime removedByDate) { this.removedByDate = removedByDate; }
    
    public LocalDateTime getTime() { return time; }
    public void setTime(LocalDateTime time) { this.time = time; }
    
    public LocalDateTime getUntil() { return until; }
    public void setUntil(LocalDateTime until) { this.until = until; }
    
    public int getTemplate() { return template; }
    public void setTemplate(int template) { this.template = template; }
    
    public String getServerScope() { return serverScope; }
    public void setServerScope(String serverScope) { this.serverScope = serverScope; }
    
    public String getServerOrigin() { return serverOrigin; }
    public void setServerOrigin(String serverOrigin) { this.serverOrigin = serverOrigin; }
    
    public boolean isSilent() { return silent; }
    public void setSilent(boolean silent) { this.silent = silent; }
    
    public boolean isIpban() { return ipban; }
    public void setIpban(boolean ipban) { this.ipban = ipban; }
    
    public boolean isIpbanWildcard() { return ipbanWildcard; }
    public void setIpbanWildcard(boolean ipbanWildcard) { this.ipbanWildcard = ipbanWildcard; }
    
    public boolean isActive() { return active; }
    public void setActive(boolean active) { this.active = active; }
    
    // 工具方法
    
    /**
     * 获取格式化的封禁时间
     */
    public String getFormattedTime() {
        if (time == null) return "未知";
        return time.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 获取格式化的到期时间
     */
    public String getFormattedUntil() {
        if (until == null) return "永久";
        // 检查是否为永久封禁（时间戳为0或很大的值）
        if (until.getYear() > 2100) return "永久";
        return until.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }
    
    /**
     * 检查封禁是否已过期
     */
    public boolean isExpired() {
        if (until == null || until.getYear() > 2100) return false; // 永久封禁不过期
        return LocalDateTime.now().isAfter(until);
    }
    
    /**
     * 获取封禁状态文本
     */
    public String getStatusText() {
        if (!active) return "已解封";
        if (isExpired()) return "已过期";
        return "生效中";
    }
    
    /**
     * 获取封禁状态CSS类
     */
    public String getStatusClass() {
        if (!active) return "status-unbanned";
        if (isExpired()) return "status-expired";
        return "status-active";
    }
    
    /**
     * 获取封禁类型文本
     */
    public String getBanTypeText() {
        if (ipban) return "IP封禁";
        return "玩家封禁";
    }
    
    /**
     * 获取玩家头像URL
     */
    public String getPlayerAvatarUrl(String provider) {
        if (uuid == null || uuid.isEmpty()) return "";
        
        switch (provider.toLowerCase()) {
            case "minotar":
                return "https://minotar.net/avatar/" + uuid + "/32";
            case "crafatar":
                return "https://crafatar.com/avatars/" + uuid + "?size=32";
            case "mc-heads":
                return "https://mc-heads.net/avatar/" + uuid + "/32";
            default:
                return "https://minotar.net/avatar/" + uuid + "/32";
        }
    }
    
    /**
     * 获取简短的封禁原因（用于列表显示）
     */
    public String getShortReason() {
        if (reason == null || reason.isEmpty()) return "无原因";
        if (reason.length() > 50) {
            return reason.substring(0, 47) + "...";
        }
        return reason;
    }
    
    @Override
    public String toString() {
        return "BanRecord{" +
                "id=" + id +
                ", playerName='" + playerName + '\'' +
                ", reason='" + reason + '\'' +
                ", bannedByName='" + bannedByName + '\'' +
                ", time=" + time +
                ", until=" + until +
                ", active=" + active +
                '}';
    }
}
