package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.IOException;

/**
 * 用户界面处理器
 * 提供普通用户功能界面
 */
public class UserHandler extends WebHandler implements HttpHandler {

    public UserHandler(AceKeySystem plugin, WebServer webServer) {
        super(plugin, webServer);
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        try {
            if ("GET".equals(method)) {
                handleUserPage(exchange);
            } else if ("POST".equals(method)) {
                handleUserAction(exchange);
            } else {
                sendResponse(exchange, 405, "text/plain", "方法不允许");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理用户请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "text/html; charset=utf-8", generateErrorPage("服务器内部错误"));
        }
    }

    /**
     * 处理用户页面
     */
    private void handleUserPage(HttpExchange exchange) throws IOException {
        String path = exchange.getRequestURI().getPath();
        String html;

        if ("/user".equals(path)) {
            html = generateUserPage();
        } else if ("/points-shop".equals(path)) {
            html = new PointsShopGenerator(plugin, webServer).generatePointsShopPage();
        } else {
            html = generateErrorPage("页面未找到");
        }

        sendResponse(exchange, 200, "text/html; charset=utf-8", html);
    }

    /**
     * 处理用户操作
     */
    private void handleUserAction(HttpExchange exchange) throws IOException {
        // TODO: 实现用户操作处理
        sendResponse(exchange, 200, "text/plain", "操作完成");
    }

    /**
     * 生成用户页面
     */
    private String generateUserPage() {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>游戏幸运抽奖</title>\n");
        html.append("    <style>\n");
        html.append(getUserCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");
        html.append("    <div class=\"container\">\n");
        html.append("        <div class=\"lottery-modal\">\n");
        html.append(
                "            <button class=\"leaderboard-btn-corner\" onclick=\"openLeaderboard()\" title=\"查看排行榜\">🏆</button>\n");
        html.append(
                "            <button class=\"punishment-btn-corner\" onclick=\"openPunishmentRecords()\" title=\"查看处罚记录\">⚖️</button>\n");
        html.append("            <button class=\"info-btn\" onclick=\"showPrizeInfo()\" title=\"查看奖品说明\">!</button>\n");
        html.append(
                "            <button class=\"points-shop-btn\" onclick=\"openPointsShop()\" title=\"积分商店\">🛒</button>\n");
        html.append("            \n");
        html.append("            <div class=\"modal-header\">\n");
        html.append("                <h1 class=\"modal-title\">🎁 游戏幸运抽奖</h1>\n");
        html.append("                <p class=\"modal-subtitle\">输入卡密抽奖品</p>\n");
        html.append("            </div>\n");
        html.append("            \n");
        html.append("            <div class=\"form-group\">\n");
        html.append("                <label for=\"keyInput\">🔑 卡密:</label>\n");
        html.append(
                "                <input type=\"text\" id=\"keyInput\" placeholder=\"请输入您的卡密\" oninput=\"checkKeyInput()\">\n");
        html.append("            </div>\n");
        html.append("            \n");
        html.append("            <div class=\"form-group\">\n");
        html.append("                <label for=\"username\">👤 游戏名:</label>\n");
        html.append("                <input type=\"text\" id=\"username\" placeholder=\"请输入您的游戏用户名\">\n");
        html.append("            </div>\n");
        html.append("            \n");
        html.append(
                "            <button id=\"lotteryBtn\" class=\"lottery-btn\" onclick=\"participateLottery()\" disabled>\n");
        html.append("                🎲 开始抽奖\n");
        html.append("            </button>\n");
        html.append("            \n");
        html.append("            <div id=\"result\" class=\"result\" style=\"display: none;\"></div>\n");
        html.append("            \n");
        html.append("            <div class=\"recent-winners\">\n");
        html.append("                <h3>🏆 最近中奖记录</h3>\n");
        html.append("                <div class=\"record-container\">\n");
        html.append("                    <div class=\"record-list\" id=\"winnerList\">\n");
        html.append("                        <div class=\"record-item\">\n");
        html.append("                            <span class=\"record-time\">05.30号(00:06)</span>\n");
        html.append("                            <span class=\"record-username\">AceBrand</span>\n");
        html.append("                            <span class=\"record-prize\">六等奖2</span>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"record-item\">\n");
        html.append("                            <span class=\"record-time\">05.29号(21:17)</span>\n");
        html.append("                            <span class=\"record-username\">AceBrand</span>\n");
        html.append("                            <span class=\"record-prize\">四等奖</span>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"record-item\">\n");
        html.append("                            <span class=\"record-time\">05.29号(00:03)</span>\n");
        html.append("                            <span class=\"record-username\">AceBrand</span>\n");
        html.append("                            <span class=\"record-prize\">四等奖</span>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"record-item\">\n");
        html.append("                            <span class=\"record-time\">05.28号(23:52)</span>\n");
        html.append("                            <span class=\"record-username\">AceBrand</span>\n");
        html.append("                            <span class=\"record-prize\">四等奖</span>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"record-item\">\n");
        html.append("                            <span class=\"record-time\">05.28号(23:47)</span>\n");
        html.append("                            <span class=\"record-username\">AceBrand</span>\n");
        html.append("                            <span class=\"record-prize\">一等奖</span>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <!-- 宝箱动画模态框 -->\n");
        html.append("    <div class=\"chest-modal-overlay\" id=\"chestModal\" style=\"display: none;\">\n");
        html.append("        <div class=\"chest-modal-container\">\n");
        html.append("            <div class=\"chest-modal-content\">\n");
        html.append("                <!-- 动画区域 -->\n");
        html.append("                <div class=\"animation-container\" id=\"animationContainer\">\n");

        // 获取动画配置
        String animationStyle = plugin.getConfig().getString("website.opening-animation.style", "classic");
        String customAnimationUrl = plugin.getConfig().getString("website.opening-animation.custom-url", "");
        double animationDuration = plugin.getConfig().getDouble("website.opening-animation.duration", 3.0);

        // 添加动画样式类
        html.append("                    <div class=\"animation-wrapper ").append(animationStyle)
                .append("-animation\" data-duration=\"").append(animationDuration).append("\">\n");

        if ("custom".equals(animationStyle) && customAnimationUrl != null && !customAnimationUrl.trim().isEmpty()) {
            // 使用自定义动画（添加时间戳避免缓存问题）
            String cacheBuster = "?t=" + System.currentTimeMillis();
            String animationUrlWithCache = customAnimationUrl + cacheBuster;
            String extension = getFileExtension(customAnimationUrl).toLowerCase();
            if (extension.equals(".mp4") || extension.equals(".webm")) {
                html.append(
                        "                        <video autoplay loop muted class=\"animation-media custom-video\" id=\"animationMedia\">\n");
                html.append("                            <source src=\"").append(animationUrlWithCache)
                        .append("\" type=\"video/").append(extension.substring(1)).append("\">\n");
                html.append("                        </video>\n");
            } else {
                html.append("                        <img src=\"").append(animationUrlWithCache)
                        .append("\" alt=\"自定义开箱动画\" class=\"animation-media custom-gif\" id=\"animationMedia\">\n");
            }
        } else {
            // 使用预设动画风格
            switch (animationStyle) {
                case "classic":
                    html.append("                        <div class=\"classic-chest\" id=\"animationMedia\">\n");
                    html.append("                            <div class=\"chest-base\"></div>\n");
                    html.append("                            <div class=\"chest-lid\"></div>\n");
                    html.append("                            <div class=\"chest-treasure\">💎</div>\n");
                    html.append("                        </div>\n");
                    break;
                case "modern":
                    html.append("                        <div class=\"modern-card\" id=\"animationMedia\">\n");
                    html.append("                            <div class=\"card-front\">🎁</div>\n");
                    html.append("                            <div class=\"card-back\">✨</div>\n");
                    html.append("                        </div>\n");
                    break;
                case "sparkle":
                    html.append("                        <div class=\"sparkle-effect\" id=\"animationMedia\">\n");
                    html.append("                            <div class=\"sparkle-center\">🌟</div>\n");
                    html.append("                            <div class=\"sparkle-particle\"></div>\n");
                    html.append("                            <div class=\"sparkle-particle\"></div>\n");
                    html.append("                            <div class=\"sparkle-particle\"></div>\n");
                    html.append("                            <div class=\"sparkle-particle\"></div>\n");
                    html.append("                            <div class=\"sparkle-particle\"></div>\n");
                    html.append("                            <div class=\"sparkle-particle\"></div>\n");
                    html.append("                        </div>\n");
                    break;
                case "rotate":
                    html.append("                        <div class=\"rotate-display\" id=\"animationMedia\">\n");
                    html.append("                            <div class=\"rotate-item\">🎁</div>\n");
                    html.append("                            <div class=\"rotate-glow\"></div>\n");
                    html.append("                        </div>\n");
                    break;
                case "luxury":
                    html.append("                        <div class=\"luxury-vault\" id=\"animationMedia\">\n");
                    html.append("                            <div class=\"vault-background\"></div>\n");
                    html.append("                            <div class=\"vault-door\">\n");
                    html.append("                                <div class=\"door-handle\"></div>\n");
                    html.append("                                <div class=\"door-lock\"></div>\n");
                    html.append("                                <div class=\"door-pattern\"></div>\n");
                    html.append("                            </div>\n");
                    html.append("                            <div class=\"vault-light\"></div>\n");
                    html.append("                            <div class=\"treasure-burst\">\n");
                    html.append("                                <div class=\"treasure-item\">💎</div>\n");
                    html.append("                                <div class=\"treasure-item\">👑</div>\n");
                    html.append("                                <div class=\"treasure-item\">💰</div>\n");
                    html.append("                                <div class=\"treasure-item\">⭐</div>\n");
                    html.append("                                <div class=\"treasure-item\">🏆</div>\n");
                    html.append("                            </div>\n");
                    html.append("                            <div class=\"golden-particles\"></div>\n");
                    html.append("                        </div>\n");
                    break;
                case "cosmic":
                    html.append("                        <div class=\"cosmic-portal\" id=\"animationMedia\">\n");
                    html.append("                            <div class=\"space-background\"></div>\n");
                    html.append("                            <div class=\"portal-ring portal-ring-1\"></div>\n");
                    html.append("                            <div class=\"portal-ring portal-ring-2\"></div>\n");
                    html.append("                            <div class=\"portal-ring portal-ring-3\"></div>\n");
                    html.append("                            <div class=\"portal-center\">\n");
                    html.append("                                <div class=\"energy-core\"></div>\n");
                    html.append("                                <div class=\"energy-pulse\"></div>\n");
                    html.append("                            </div>\n");
                    html.append("                            <div class=\"cosmic-particles\">\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                                <div class=\"star-particle\"></div>\n");
                    html.append("                            </div>\n");
                    html.append("                            <div class=\"prize-emergence\">\n");
                    html.append("                                <div class=\"emerged-prize\">🌟</div>\n");
                    html.append("                            </div>\n");
                    html.append("                        </div>\n");
                    break;
                default:
                    // 默认使用经典宝箱
                    html.append(
                            "                        <img src=\"/static/chest-open.gif\" alt=\"开箱动画\" class=\"animation-media classic-gif\" id=\"animationMedia\">\n");
                    break;
            }
        }

        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        html.append("                <!-- 状态文字 -->\n");
        html.append("                <div class=\"chest-status-text\" id=\"chestStatusText\">🎲 正在开启宝箱...</div>\n");
        html.append("                \n");
        html.append("                <!-- 音频元素 -->\n");
        html.append("                <audio id=\"chestAudio\" preload=\"auto\">\n");

        // 获取音效配置
        String openingSoundUrl = plugin.getConfig().getString("website.sound-effects.opening-sound", "");

        if (openingSoundUrl != null && !openingSoundUrl.trim().isEmpty()) {
            // 使用自定义音效
            String extension = getFileExtension(openingSoundUrl).toLowerCase();
            String mimeType = "audio/mpeg"; // 默认
            if (extension.equals(".wav")) {
                mimeType = "audio/wav";
            } else if (extension.equals(".ogg")) {
                mimeType = "audio/ogg";
            }
            html.append("                    <source src=\"").append(openingSoundUrl).append("\" type=\"")
                    .append(mimeType).append("\">\n");
        } else {
            // 使用默认音效
            html.append("                    <source src=\"/static/chest-open.mp3\" type=\"audio/mpeg\">\n");
            html.append("                    <source src=\"/static/chest-open.wav\" type=\"audio/wav\">\n");
            html.append("                    <source src=\"/static/chest-open.ogg\" type=\"audio/ogg\">\n");
        }

        html.append("                    您的浏览器不支持音频元素。\n");
        html.append("                </audio>\n");
        html.append("                \n");
        html.append("                <!-- 中奖音效元素 -->\n");
        html.append("                <audio id=\"winningAudio\" preload=\"auto\">\n");

        // 获取中奖音效配置
        String winningSoundUrl = plugin.getConfig().getString("website.sound-effects.winning-sound", "");

        if (winningSoundUrl != null && !winningSoundUrl.trim().isEmpty()) {
            // 使用自定义中奖音效
            String extension = getFileExtension(winningSoundUrl).toLowerCase();
            String mimeType = "audio/mpeg"; // 默认
            if (extension.equals(".wav")) {
                mimeType = "audio/wav";
            } else if (extension.equals(".ogg")) {
                mimeType = "audio/ogg";
            }
            html.append("                    <source src=\"").append(winningSoundUrl).append("\" type=\"")
                    .append(mimeType).append("\">\n");
        } else {
            // 使用默认中奖音效
            html.append("                    <source src=\"/static/winning-sound.mp3\" type=\"audio/mpeg\">\n");
            html.append("                    <source src=\"/static/winning-sound.wav\" type=\"audio/wav\">\n");
            html.append("                    <source src=\"/static/winning-sound.ogg\" type=\"audio/ogg\">\n");
        }

        html.append("                    您的浏览器不支持音频元素。\n");
        html.append("                </audio>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <!-- 奖品获得弹窗 -->\n");
        html.append("    <div id=\"prizeRewardModal\" class=\"prize-reward-modal\" style=\"display: none;\">\n");
        html.append("        <div class=\"prize-reward-content\">\n");
        html.append("            <div class=\"prize-reward-header\">\n");
        html.append(
                "                <span class=\"prize-reward-close\" onclick=\"hidePrizeReward()\">&times;</span>\n");
        html.append("                <h2>🎉 恭喜获得奖品</h2>\n");
        html.append("            </div>\n");
        html.append("            <div class=\"prize-reward-body\">\n");
        html.append("                <div class=\"prize-icon-container\">\n");
        html.append("                    <div id=\"prizeIcon\" class=\"prize-icon-large\">🪙</div>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"prize-info\">\n");
        html.append("                    <h3 id=\"prizeRewardName\" class=\"prize-reward-name\">五等奖2</h3>\n");
        html.append(
                "                    <p id=\"prizeRewardDesc\" class=\"prize-reward-desc\">恭喜您在24小时内获得奖励的幸运用户</p>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            <div class=\"prize-reward-footer\">\n");
        html.append("                <button class=\"prize-confirm-btn\" onclick=\"hidePrizeReward()\">确认</button>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <!-- 奖品说明弹窗 -->\n");
        html.append("    <div id=\"prizeInfoModal\" class=\"prize-modal\" style=\"display: none;\">\n");
        html.append("        <div class=\"prize-modal-content\">\n");
        html.append("            <div class=\"prize-modal-header\">\n");
        html.append("                <h2>🎁 奖品说明</h2>\n");
        html.append("                <button class=\"prize-close-btn\" onclick=\"hidePrizeInfo()\">×</button>\n");
        html.append("            </div>\n");
        html.append("            <div class=\"prize-modal-body\">\n");
        html.append("                <p class=\"prize-description\">参与抽奖可能获得以下奖励：</p>\n");
        html.append("                <div id=\"prizeList\" class=\"prize-list\">\n");
        html.append("                    <!-- 奖品列表将通过JavaScript动态加载 -->\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            <div class=\"prize-modal-footer\">\n");
        html.append("                <button class=\"understand-btn\" onclick=\"hidePrizeInfo()\">了解了</button>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("    \n");
        html.append("    <script>\n");
        html.append(generateSecurityJS()); // 添加安全防护
        html.append(getUserJS());
        html.append("    </script>\n");
        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null)
            return ".mp3";
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            return fileName.substring(lastDot);
        }
        return ".mp3";
    }

    /**
     * 生成错误页面
     */
    private String generateErrorPage(String message) {
        return "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"><title>错误</title></head>" +
                "<body style=\"font-family: Arial, sans-serif; text-align: center; padding: 50px;\">" +
                "<h1>发生错误</h1><p>" + message + "</p>" +
                "<a href=\"/user\">返回用户界面</a>" +
                "</body></html>";
    }

    /**
     * 获取用户界面CSS
     */
    private String getUserCSS() {
        // 获取界面主题配置
        String theme = plugin.getConfig().getString("website.interface.theme", "default");
        String primaryColor = plugin.getConfig().getString("website.interface.primary-color", "#667eea");
        String accentColor = plugin.getConfig().getString("website.interface.accent-color", "#764ba2");
        boolean enableAnimations = plugin.getConfig().getBoolean("website.interface.enable-animations", true);
        String loadAnimation = plugin.getConfig().getString("website.interface.load-animation", "fade");
        String animationSpeed = plugin.getConfig().getString("website.interface.animation-speed", "normal");

        // 获取背景图配置
        String backgroundImage = plugin.getConfig().getString("website.background-image", "");
        String backgroundStyle = getThemeBackgroundStyle(theme, backgroundImage, primaryColor, accentColor);

        return generateThemeCSS(theme, primaryColor, accentColor, enableAnimations, loadAnimation, animationSpeed) +
                "* { margin: 0; padding: 0; box-sizing: border-box; }\n" +
                "body { font-family: 'Microsoft YaHei', Arial, sans-serif; " + backgroundStyle
                + " min-height: 100vh; padding: 20px; position: relative; overflow-x: hidden; }\n"
                +

                ".container { max-width: 1200px; margin: 0 auto; position: relative; z-index: 1; }\n" +
                ".lottery-modal { background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 40px; box-shadow: 0 25px 50px rgba(0,0,0,0.5), inset 0 1px 0 rgba(255,255,255,0.1); border: 1px solid rgba(255,255,255,0.1); max-width: 700px; margin: 50px auto; position: relative; }\n"
                +
                ".lottery-modal::before { content: ''; position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px; background: linear-gradient(45deg, #3b82f6, #8b5cf6, #06b6d4, #10b981); border-radius: 22px; z-index: -1; opacity: 0.7; }\n"
                +
                ".modal-header { text-align: center; margin-bottom: 30px; }\n" +
                ".modal-title { color: #ffffff; font-size: 1.8em; font-weight: bold; margin-bottom: 10px; display: flex; align-items: center; justify-content: center; gap: 10px; }\n"
                +
                ".modal-subtitle { color: #94a3b8; font-size: 1em; }\n" +
                ".form-group { margin-bottom: 20px; }\n" +
                ".form-group label { display: block; margin-bottom: 8px; color: #e2e8f0; font-weight: 500; font-size: 0.9em; display: flex; align-items: center; gap: 8px; }\n"
                +
                ".form-group input { width: 100%; padding: 15px; background: rgba(15, 23, 42, 0.8); border: 2px solid rgba(71, 85, 105, 0.5); border-radius: 12px; color: #ffffff; font-size: 16px; transition: all 0.3s ease; }\n"
                +
                ".form-group input:focus { border-color: #3b82f6; outline: none; box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1); background: rgba(15, 23, 42, 0.9); }\n"
                +
                ".form-group input::placeholder { color: #64748b; }\n" +
                ".lottery-btn { width: 100%; padding: 18px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border: none; border-radius: 12px; color: white; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; margin-top: 10px; position: relative; overflow: hidden; }\n"
                +
                ".lottery-btn:hover:not(:disabled) { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4); }\n"
                +
                ".lottery-btn:disabled { background: linear-gradient(135deg, #64748b 0%, #475569 100%); cursor: not-allowed; transform: none; box-shadow: none; }\n"
                +
                ".lottery-btn::before { content: ''; position: absolute; top: 0; left: -100%; width: 100%; height: 100%; background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent); transition: left 0.5s; }\n"
                +
                ".lottery-btn:hover:not(:disabled)::before { left: 100%; }\n" +
                ".result { margin: 20px 0; padding: 20px; border-radius: 12px; display: none; text-align: center; }\n" +
                ".result.success { background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.2) 100%); color: #10b981; border: 1px solid rgba(16, 185, 129, 0.3); }\n"
                +
                ".result.error { background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.2) 100%); color: #ef4444; border: 1px solid rgba(239, 68, 68, 0.3); }\n"
                +
                ".prize-result { font-size: 1.3em; font-weight: bold; line-height: 1.6; }\n" +
                ".recent-winners { background: rgba(15, 23, 42, 0.6); border-radius: 15px; padding: 25px; margin-top: 30px; border: 1px solid rgba(71, 85, 105, 0.3); }\n"
                +
                ".recent-winners h3 { color: #fbbf24; font-size: 1.2em; margin-bottom: 15px; display: flex; align-items: center; justify-content: center; gap: 8px; text-align: center; }\n"
                +
                ".record-container { height: 200px; overflow: hidden; position: relative; border-radius: 10px; background: rgba(44, 62, 80, 0.7); backdrop-filter: blur(5px); box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5); }\n"
                +
                ".record-list { position: absolute; width: 100%; animation: scrollUp 30s linear infinite; }\n"
                +
                ".record-item { display: flex; justify-content: space-between; padding: 8px 15px; border-bottom: 1px solid #34495e; color: #e6e6e6; transition: all 0.3s; }\n"
                +
                ".record-item:hover { background: rgba(52, 152, 219, 0.1); }\n" +
                ".record-time { color: #9cb3cc; width: 30%; text-align: left; }\n" +
                ".record-username { color: #3a7bd5; width: 30%; text-align: center; font-weight: bold; }\n" +
                ".record-prize { color: #e94560; width: 40%; text-align: right; background: none !important; padding: 0 !important; border-radius: 0 !important; }\n"
                +
                "@keyframes scrollUp { 0% { transform: translateY(200px); } 100% { transform: translateY(-100%); } }\n"
                +
                ".record-container:hover .record-list { animation-play-state: paused; }\n"
                +
                "/* 宝箱动画弹窗样式 */\n" +
                ".chest-modal-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.9); z-index: 9999; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(5px); }\n"
                +
                ".chest-modal-container { position: relative; width: 90%; max-width: 600px; height: 70vh; max-height: 500px; }\n"
                +
                ".chest-modal-content { width: 100%; height: 100%; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); border-radius: 20px; border: 3px solid #FFD700; box-shadow: 0 0 50px rgba(255, 215, 0, 0.4), inset 0 0 30px rgba(255, 215, 0, 0.1); position: relative; overflow: hidden; display: flex; flex-direction: column; align-items: center; justify-content: center; }\n"
                +
                "\n" +
                "/* 动画容器 */\n" +
                ".animation-container { display: flex; align-items: center; justify-content: center; flex: 1; padding: 20px; position: relative; }\n"
                +
                ".animation-wrapper { position: relative; width: 400px; height: 400px; display: flex; align-items: center; justify-content: center; }\n"
                +
                ".animation-media { width: 100%; height: 100%; object-fit: contain; position: relative; z-index: 2; }\n"
                +
                "\n" +
                "/* 自定义动画样式 */\n" +
                ".custom-gif, .custom-video { width: 450px; height: 450px; object-fit: contain; filter: contrast(1.2) brightness(1.1); mix-blend-mode: screen; position: relative; z-index: 2; border: none; outline: none; background: transparent; }\n"
                +
                ".classic-gif { width: 450px; height: 450px; object-fit: contain; filter: contrast(1.2) brightness(1.1); mix-blend-mode: screen; position: relative; z-index: 2; border: none; outline: none; }\n"
                +
                "\n" +
                "/* 视频特殊处理 - 支持透明背景 */\n" +
                ".custom-video { background-color: transparent !important; }\n" +
                ".custom-video::-webkit-media-controls { display: none !important; }\n" +
                ".custom-video::-webkit-media-controls-enclosure { display: none !important; }\n" +
                "\n" +
                "/* 自定义动画容器发光效果 */\n" +
                ".custom-animation .animation-wrapper::before { content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 500px; height: 500px; background: radial-gradient(circle at center, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 40%, transparent 70%); z-index: 0; animation: customGlow 3s ease-in-out infinite; }\n"
                +
                "@keyframes customGlow { 0%, 100% { opacity: 0.4; transform: translate(-50%, -50%) scale(1); } 50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); } }\n"
                +
                "\n" +
                "/* 经典宝箱动画 */\n" +
                ".classic-chest { width: 300px; height: 300px; position: relative; }\n" +
                ".chest-base { width: 100%; height: 60%; background: linear-gradient(45deg, #8B4513, #A0522D); border-radius: 0 0 20px 20px; position: absolute; bottom: 0; border: 3px solid #654321; }\n"
                +
                ".chest-lid { width: 100%; height: 50%; background: linear-gradient(45deg, #A0522D, #CD853F); border-radius: 20px 20px 0 0; position: absolute; top: 0; border: 3px solid #654321; transform-origin: bottom; transition: transform 0.8s ease; }\n"
                +
                ".chest-treasure { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 4em; opacity: 0; transition: all 0.5s ease; z-index: 10; }\n"
                +
                ".classic-animation.active .chest-lid { transform: rotateX(-120deg); }\n" +
                ".classic-animation.active .chest-treasure { opacity: 1; animation: treasureBounce 1s ease 0.8s; }\n" +
                "@keyframes treasureBounce { 0%, 20%, 50%, 80%, 100% { transform: translate(-50%, -50%) translateY(0); } 40% { transform: translate(-50%, -50%) translateY(-30px); } 60% { transform: translate(-50%, -50%) translateY(-15px); } }\n"
                +
                "\n" +
                "/* 现代卡片动画 */\n" +
                ".modern-card { width: 250px; height: 350px; position: relative; perspective: 1000px; }\n" +
                ".card-front, .card-back { width: 100%; height: 100%; position: absolute; backface-visibility: hidden; border-radius: 20px; display: flex; align-items: center; justify-content: center; font-size: 6em; transition: transform 1s ease; }\n"
                +
                ".card-front { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }\n" +
                ".card-back { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; transform: rotateY(180deg); }\n"
                +
                ".modern-animation.active .card-front { transform: rotateY(-180deg); }\n" +
                ".modern-animation.active .card-back { transform: rotateY(0deg); }\n" +
                "\n" +
                "/* 闪光特效动画 */\n" +
                ".sparkle-effect { width: 300px; height: 300px; position: relative; }\n" +
                ".sparkle-center { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 6em; animation: sparkleCenter 2s ease-in-out infinite; }\n"
                +
                ".sparkle-particle { position: absolute; width: 20px; height: 20px; background: radial-gradient(circle, #FFD700, #FFA500); border-radius: 50%; opacity: 0; }\n"
                +
                ".sparkle-particle:nth-child(2) { top: 20%; left: 20%; animation: sparkleParticle 2s ease-in-out 0.2s infinite; }\n"
                +
                ".sparkle-particle:nth-child(3) { top: 20%; right: 20%; animation: sparkleParticle 2s ease-in-out 0.4s infinite; }\n"
                +
                ".sparkle-particle:nth-child(4) { bottom: 20%; left: 20%; animation: sparkleParticle 2s ease-in-out 0.6s infinite; }\n"
                +
                ".sparkle-particle:nth-child(5) { bottom: 20%; right: 20%; animation: sparkleParticle 2s ease-in-out 0.8s infinite; }\n"
                +
                ".sparkle-particle:nth-child(6) { top: 50%; left: 10%; animation: sparkleParticle 2s ease-in-out 1s infinite; }\n"
                +
                ".sparkle-particle:nth-child(7) { top: 50%; right: 10%; animation: sparkleParticle 2s ease-in-out 1.2s infinite; }\n"
                +
                "@keyframes sparkleCenter { 0%, 100% { transform: translate(-50%, -50%) scale(1); filter: brightness(1); } 50% { transform: translate(-50%, -50%) scale(1.2); filter: brightness(1.5); } }\n"
                +
                "@keyframes sparkleParticle { 0%, 100% { opacity: 0; transform: scale(0); } 50% { opacity: 1; transform: scale(1); } }\n"
                +
                "\n" +
                "/* 旋转展示动画 */\n" +
                ".rotate-display { width: 300px; height: 300px; position: relative; }\n" +
                ".rotate-item { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 6em; animation: rotateItem 3s linear infinite; }\n"
                +
                ".rotate-glow { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 350px; height: 350px; border: 3px solid #FFD700; border-radius: 50%; opacity: 0.6; animation: rotateGlow 3s linear infinite reverse; }\n"
                +
                "@keyframes rotateItem { 0% { transform: translate(-50%, -50%) rotateY(0deg); } 100% { transform: translate(-50%, -50%) rotateY(360deg); } }\n"
                +
                "@keyframes rotateGlow { 0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); opacity: 0.6; } 50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.1); opacity: 0.8; } 100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); opacity: 0.6; } }\n"
                +
                "\n" +
                "/* 豪华保险库动画 */\n" +
                ".luxury-vault { width: 400px; height: 400px; position: relative; perspective: 1000px; }\n" +
                ".vault-background { position: absolute; width: 100%; height: 100%; background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%); border-radius: 20px; box-shadow: inset 0 0 50px rgba(0,0,0,0.5), 0 0 30px rgba(255,215,0,0.3); }\n"
                +
                ".vault-door { position: absolute; width: 80%; height: 80%; top: 10%; left: 10%; background: linear-gradient(45deg, #bdc3c7, #ecf0f1, #bdc3c7); border-radius: 15px; border: 5px solid #34495e; transform-origin: left center; transition: transform 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94); box-shadow: inset 0 0 30px rgba(0,0,0,0.3); }\n"
                +
                ".door-handle { position: absolute; right: 20px; top: 50%; transform: translateY(-50%); width: 30px; height: 60px; background: linear-gradient(45deg, #f39c12, #e67e22); border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); }\n"
                +
                ".door-lock { position: absolute; right: 60px; top: 50%; transform: translateY(-50%); width: 40px; height: 40px; background: radial-gradient(circle, #2c3e50, #34495e); border-radius: 50%; border: 3px solid #f39c12; }\n"
                +
                ".door-pattern { position: absolute; width: 60%; height: 60%; top: 20%; left: 20%; background: repeating-linear-gradient(45deg, transparent, transparent 10px, rgba(52,73,94,0.1) 10px, rgba(52,73,94,0.1) 20px); }\n"
                +
                ".vault-light { position: absolute; top: -20px; left: 50%; transform: translateX(-50%); width: 100px; height: 20px; background: linear-gradient(90deg, transparent, #f1c40f, transparent); opacity: 0; transition: opacity 0.5s ease; }\n"
                +
                ".treasure-burst { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 200px; height: 200px; opacity: 0; }\n"
                +
                ".treasure-item { position: absolute; font-size: 3em; opacity: 0; }\n" +
                ".treasure-item:nth-child(1) { top: 20%; left: 50%; transform: translateX(-50%); }\n" +
                ".treasure-item:nth-child(2) { top: 50%; right: 20%; }\n" +
                ".treasure-item:nth-child(3) { bottom: 20%; left: 50%; transform: translateX(-50%); }\n" +
                ".treasure-item:nth-child(4) { top: 50%; left: 20%; }\n" +
                ".treasure-item:nth-child(5) { top: 30%; right: 30%; }\n" +
                ".golden-particles { position: absolute; width: 100%; height: 100%; pointer-events: none; }\n" +
                ".golden-particles::before, .golden-particles::after { content: ''; position: absolute; width: 6px; height: 6px; background: #f1c40f; border-radius: 50%; opacity: 0; }\n"
                +
                ".luxury-animation.active .vault-door { transform: rotateY(-120deg); }\n" +
                ".luxury-animation.active .vault-light { opacity: 1; animation: vaultLightSweep 2s ease-in-out; }\n" +
                ".luxury-animation.active .treasure-burst { opacity: 1; animation: treasureExplode 1.5s ease-out 1s; }\n"
                +
                ".luxury-animation.active .treasure-item { animation: treasureFloat 2s ease-out 1.2s; }\n" +
                ".luxury-animation.active .golden-particles::before { animation: goldenParticle1 3s ease-out 0.8s; }\n"
                +
                ".luxury-animation.active .golden-particles::after { animation: goldenParticle2 3s ease-out 1s; }\n" +
                "@keyframes vaultLightSweep { 0% { transform: translateX(-50%) scaleX(0); } 50% { transform: translateX(-50%) scaleX(2); } 100% { transform: translateX(-50%) scaleX(0); } }\n"
                +
                "@keyframes treasureExplode { 0% { transform: translate(-50%, -50%) scale(0); opacity: 0; } 50% { transform: translate(-50%, -50%) scale(1.2); opacity: 1; } 100% { transform: translate(-50%, -50%) scale(1); opacity: 1; } }\n"
                +
                "@keyframes treasureFloat { 0% { opacity: 0; transform: translateY(20px); } 100% { opacity: 1; transform: translateY(0); } }\n"
                +
                "@keyframes goldenParticle1 { 0% { opacity: 0; transform: translate(0, 0); } 50% { opacity: 1; transform: translate(100px, -50px); } 100% { opacity: 0; transform: translate(200px, -100px); } }\n"
                +
                "@keyframes goldenParticle2 { 0% { opacity: 0; transform: translate(0, 0); } 50% { opacity: 1; transform: translate(-80px, -60px); } 100% { opacity: 0; transform: translate(-160px, -120px); } }\n"
                +
                "\n" +
                "/* 宇宙传送门动画 */\n" +
                ".cosmic-portal { width: 400px; height: 400px; position: relative; }\n" +
                ".space-background { position: absolute; width: 100%; height: 100%; background: radial-gradient(circle at center, #0f0f23 0%, #000000 70%); border-radius: 50%; overflow: hidden; }\n"
                +
                ".space-background::before { content: ''; position: absolute; width: 200%; height: 200%; background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"20\" cy=\"20\" r=\"1\" fill=\"white\" opacity=\"0.8\"/><circle cx=\"80\" cy=\"30\" r=\"0.5\" fill=\"white\" opacity=\"0.6\"/><circle cx=\"40\" cy=\"70\" r=\"1.5\" fill=\"white\" opacity=\"0.9\"/><circle cx=\"70\" cy=\"80\" r=\"0.8\" fill=\"white\" opacity=\"0.7\"/><circle cx=\"10\" cy=\"60\" r=\"0.3\" fill=\"white\" opacity=\"0.5\"/></svg>'); animation: starField 20s linear infinite; }\n"
                +
                "@keyframes starField { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }\n" +
                ".portal-ring { position: absolute; border-radius: 50%; border: 3px solid; top: 50%; left: 50%; transform: translate(-50%, -50%); }\n"
                +
                ".portal-ring-1 { width: 300px; height: 300px; border-color: rgba(74, 144, 226, 0.8); animation: portalRotate1 4s linear infinite; }\n"
                +
                ".portal-ring-2 { width: 200px; height: 200px; border-color: rgba(155, 89, 182, 0.6); animation: portalRotate2 3s linear infinite reverse; }\n"
                +
                ".portal-ring-3 { width: 100px; height: 100px; border-color: rgba(52, 152, 219, 0.9); animation: portalRotate3 2s linear infinite; }\n"
                +
                "@keyframes portalRotate1 { 0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); border-color: rgba(74, 144, 226, 0.8); } 50% { transform: translate(-50%, -50%) rotate(180deg) scale(1.1); border-color: rgba(155, 89, 182, 0.8); } 100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); border-color: rgba(74, 144, 226, 0.8); } }\n"
                +
                "@keyframes portalRotate2 { 0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); } 100% { transform: translate(-50%, -50%) rotate(-360deg) scale(1); } }\n"
                +
                "@keyframes portalRotate3 { 0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); } 100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); } }\n"
                +
                ".portal-center { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 80px; height: 80px; }\n"
                +
                ".energy-core { width: 100%; height: 100%; background: radial-gradient(circle, #3498db 0%, #2980b9 50%, #1abc9c 100%); border-radius: 50%; animation: energyPulse 2s ease-in-out infinite; }\n"
                +
                ".energy-pulse { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 120px; height: 120px; border: 2px solid rgba(52, 152, 219, 0.5); border-radius: 50%; animation: energyWave 3s ease-out infinite; }\n"
                +
                "@keyframes energyPulse { 0%, 100% { transform: translate(-50%, -50%) scale(1); box-shadow: 0 0 20px rgba(52, 152, 219, 0.5); } 50% { transform: translate(-50%, -50%) scale(1.2); box-shadow: 0 0 40px rgba(52, 152, 219, 0.8); } }\n"
                +
                "@keyframes energyWave { 0% { transform: translate(-50%, -50%) scale(0.5); opacity: 1; } 100% { transform: translate(-50%, -50%) scale(3); opacity: 0; } }\n"
                +
                ".cosmic-particles { position: absolute; width: 100%; height: 100%; }\n" +
                ".star-particle { position: absolute; width: 4px; height: 4px; background: white; border-radius: 50%; opacity: 0; }\n"
                +
                ".star-particle:nth-child(1) { top: 20%; left: 30%; animation: starTwinkle 2s ease-in-out 0.2s infinite; }\n"
                +
                ".star-particle:nth-child(2) { top: 40%; right: 20%; animation: starTwinkle 2s ease-in-out 0.4s infinite; }\n"
                +
                ".star-particle:nth-child(3) { bottom: 30%; left: 25%; animation: starTwinkle 2s ease-in-out 0.6s infinite; }\n"
                +
                ".star-particle:nth-child(4) { top: 60%; right: 30%; animation: starTwinkle 2s ease-in-out 0.8s infinite; }\n"
                +
                ".star-particle:nth-child(5) { top: 25%; left: 60%; animation: starTwinkle 2s ease-in-out 1s infinite; }\n"
                +
                ".star-particle:nth-child(6) { bottom: 40%; right: 40%; animation: starTwinkle 2s ease-in-out 1.2s infinite; }\n"
                +
                ".star-particle:nth-child(7) { top: 70%; left: 40%; animation: starTwinkle 2s ease-in-out 1.4s infinite; }\n"
                +
                ".star-particle:nth-child(8) { top: 35%; left: 80%; animation: starTwinkle 2s ease-in-out 1.6s infinite; }\n"
                +
                "@keyframes starTwinkle { 0%, 100% { opacity: 0; transform: scale(0); } 50% { opacity: 1; transform: scale(1); } }\n"
                +
                ".prize-emergence { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); opacity: 0; }\n"
                +
                ".emerged-prize { font-size: 4em; animation: prizeEmerge 2s ease-out; }\n" +
                ".cosmic-animation.active .prize-emergence { opacity: 1; }\n" +
                ".cosmic-animation.active .emerged-prize { animation: prizeEmerge 2s ease-out 2s; }\n" +
                "@keyframes prizeEmerge { 0% { opacity: 0; transform: scale(0) rotateY(0deg); filter: brightness(0.5); } 50% { opacity: 1; transform: scale(1.3) rotateY(180deg); filter: brightness(1.5); } 100% { opacity: 1; transform: scale(1) rotateY(360deg); filter: brightness(1); } }\n"
                +
                "\n" +
                "/* 通用发光效果 */\n" +
                ".animation-container::before { content: ''; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); width: 500px; height: 500px; background: radial-gradient(circle at center, rgba(255, 215, 0, 0.15) 0%, rgba(255, 215, 0, 0.05) 40%, transparent 70%); z-index: 0; animation: containerGlow 3s ease-in-out infinite; }\n"
                +
                "@keyframes containerGlow { 0%, 100% { opacity: 0.4; transform: translate(-50%, -50%) scale(1); } 50% { opacity: 0.8; transform: translate(-50%, -50%) scale(1.1); } }\n"
                +
                "\n" +
                "/* 状态文字样式 */\n" +
                ".chest-status-text { position: absolute; bottom: 30px; left: 50%; transform: translateX(-50%); color: #FFD700; font-size: 1.3em; font-weight: bold; text-align: center; text-shadow: 0 0 15px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.4); animation: textGlow 2s ease-in-out infinite; }\n"
                +
                "@keyframes textGlow { 0%, 100% { text-shadow: 0 0 15px rgba(255, 215, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.4); } 50% { text-shadow: 0 0 25px rgba(255, 215, 0, 1), 0 0 50px rgba(255, 215, 0, 0.6); } }\n"
                +
                "\n" +
                "/* 奖品获得弹窗样式 */\n" +
                ".prize-reward-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); backdrop-filter: blur(10px); display: flex; align-items: center; justify-content: center; z-index: 20000; }\n"
                +
                ".prize-reward-content { width: 90%; max-width: 500px; background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%); border-radius: 20px; border: 3px solid #FFD700; box-shadow: 0 0 50px rgba(255, 215, 0, 0.4), inset 0 0 30px rgba(255, 215, 0, 0.1); position: relative; overflow: hidden; animation: prizeModalShow 0.5s ease-out; }\n"
                +
                "@keyframes prizeModalShow { from { opacity: 0; transform: scale(0.8) translateY(-50px); } to { opacity: 1; transform: scale(1) translateY(0); } }\n"
                +
                ".prize-reward-header { padding: 20px; text-align: center; border-bottom: 2px solid rgba(255, 215, 0, 0.3); position: relative; }\n"
                +
                ".prize-reward-header h2 { color: #FFD700; margin: 0; font-size: 1.8em; font-weight: bold; text-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }\n"
                +
                ".prize-reward-close { position: absolute; top: 15px; right: 20px; color: #FFD700; font-size: 2em; font-weight: bold; cursor: pointer; transition: all 0.3s ease; }\n"
                +
                ".prize-reward-close:hover { color: #FFF; text-shadow: 0 0 10px rgba(255, 215, 0, 0.8); transform: scale(1.2); }\n"
                +
                ".prize-reward-body { padding: 30px; text-align: center; }\n" +
                ".prize-icon-container { margin-bottom: 20px; }\n" +
                ".prize-icon-large { font-size: 6em; margin-bottom: 15px; filter: drop-shadow(0 0 25px rgba(255, 215, 0, 0.8)); animation: prizeIconFloat 3s ease-in-out infinite; line-height: 1; width: 1.2em; height: 1.2em; display: flex; align-items: center; justify-content: center; margin: 0 auto 15px auto; }\n"
                +
                "@keyframes prizeIconFloat { 0%, 100% { transform: translateY(0px) scale(1); } 50% { transform: translateY(-10px) scale(1.1); } }\n"
                +
                ".prize-reward-name { color: #FFD700; font-size: 2.2em; font-weight: bold; margin: 15px 0; text-shadow: 0 0 15px rgba(255, 215, 0, 0.8); }\n"
                +
                ".prize-reward-desc { color: #E0E0E0; font-size: 1.1em; margin: 10px 0; line-height: 1.5; }\n" +
                ".prize-reward-footer { padding: 20px; text-align: center; border-top: 2px solid rgba(255, 215, 0, 0.3); }\n"
                +
                ".prize-confirm-btn { background: linear-gradient(45deg, #FFD700, #FFA500); border: none; color: #000; font-size: 1.2em; font-weight: bold; padding: 12px 40px; border-radius: 25px; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3); }\n"
                +
                ".prize-confirm-btn:hover { background: linear-gradient(45deg, #FFA500, #FFD700); transform: translateY(-2px); box-shadow: 0 6px 20px rgba(255, 215, 0, 0.5); }\n"
                +
                ".leaderboard-btn-corner { position: absolute; top: 15px; left: 15px; background: #f093fb; border: none; border-radius: 50%; width: 30px; height: 30px; color: white; cursor: pointer; font-size: 16px; font-weight: bold; display: flex; align-items: center; justify-content: center; animation: leaderboardPulse 1.5s infinite; transition: all 0.3s; }\n"
                +
                ".leaderboard-btn-corner:hover { transform: scale(1.2); }\n" +
                "@keyframes leaderboardPulse { 0% { transform: scale(1); box-shadow: 0 2px 10px rgba(240, 147, 251, 0.5); } 50% { transform: scale(1.1); box-shadow: 0 5px 15px rgba(240, 147, 251, 0.7); } 100% { transform: scale(1); box-shadow: 0 2px 10px rgba(240, 147, 251, 0.5); } }\n"
                +
                ".punishment-btn-corner { position: absolute; top: 15px; left: 55px; background: #ff6b6b; border: none; border-radius: 50%; width: 30px; height: 30px; color: white; cursor: pointer; font-size: 16px; font-weight: bold; display: flex; align-items: center; justify-content: center; animation: punishmentPulse 1.5s infinite; transition: all 0.3s; }\n"
                +
                ".punishment-btn-corner:hover { transform: scale(1.2); }\n" +
                "@keyframes punishmentPulse { 0% { transform: scale(1); box-shadow: 0 2px 10px rgba(255, 107, 107, 0.5); } 50% { transform: scale(1.1); box-shadow: 0 5px 15px rgba(255, 107, 107, 0.7); } 100% { transform: scale(1); box-shadow: 0 2px 10px rgba(255, 107, 107, 0.5); } }\n"
                +
                ".info-btn { position: absolute; top: 15px; right: 15px; background: #e94560; border: none; border-radius: 50%; width: 30px; height: 30px; color: white; cursor: pointer; font-size: 18px; font-weight: bold; display: flex; align-items: center; justify-content: center; animation: pulse 1.5s infinite; transition: all 0.3s; }\n"
                +
                ".info-btn:hover { transform: scale(1.2); }\n" +
                "@keyframes pulse { 0% { transform: scale(1); box-shadow: 0 2px 10px rgba(233, 69, 96, 0.5); } 50% { transform: scale(1.1); box-shadow: 0 5px 15px rgba(233, 69, 96, 0.7); } 100% { transform: scale(1); box-shadow: 0 2px 10px rgba(233, 69, 96, 0.5); } }\n"
                +
                ".points-shop-btn { position: absolute; top: 15px; right: 55px; background: #10b981; border: none; border-radius: 50%; width: 30px; height: 30px; color: white; cursor: pointer; font-size: 16px; font-weight: bold; display: flex; align-items: center; justify-content: center; animation: shopPulse 1.5s infinite; transition: all 0.3s; }\n"
                +
                ".points-shop-btn:hover { transform: scale(1.2); }\n" +
                "@keyframes shopPulse { 0% { transform: scale(1); box-shadow: 0 2px 10px rgba(16, 185, 129, 0.5); } 50% { transform: scale(1.1); box-shadow: 0 5px 15px rgba(16, 185, 129, 0.7); } 100% { transform: scale(1); box-shadow: 0 2px 10px rgba(16, 185, 129, 0.5); } }\n"
                +
                ".prize-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 1000; display: flex; align-items: center; justify-content: center; }\n"
                +
                ".prize-modal-content { background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; max-width: 600px; width: 90%; max-height: 80vh; overflow-y: auto; border: 1px solid rgba(71, 85, 105, 0.3); }\n"
                +
                ".prize-modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }\n"
                +
                ".prize-modal-header h2 { color: #fbbf24; font-size: 1.5em; margin: 0; }\n" +
                ".prize-close-btn { background: rgba(239, 68, 68, 0.2); border: none; border-radius: 50%; width: 30px; height: 30px; color: #ef4444; cursor: pointer; font-size: 18px; display: flex; align-items: center; justify-content: center; transition: all 0.3s; }\n"
                +
                ".prize-close-btn:hover { background: rgba(239, 68, 68, 0.3); transform: scale(1.1); }\n" +
                ".prize-description { color: #e2e8f0; text-align: center; margin-bottom: 20px; font-size: 1.1em; }\n" +
                ".prize-list { display: flex; flex-direction: column; gap: 12px; }\n" +
                ".prize-item { background: rgba(15, 23, 42, 0.6); border-radius: 12px; padding: 15px; border: 1px solid rgba(71, 85, 105, 0.3); display: flex; justify-content: space-between; align-items: center; }\n"
                +
                ".prize-name { color: #ffffff; font-weight: bold; font-size: 1.1em; }\n" +
                ".prize-desc { color: #94a3b8; font-size: 0.9em; margin-top: 4px; }\n" +
                ".prize-probability { color: #ef4444; font-weight: bold; font-size: 1.2em; }\n" +
                ".prize-modal-footer { text-align: center; margin-top: 20px; }\n" +
                ".understand-btn { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border: none; border-radius: 8px; padding: 12px 24px; color: white; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; }\n"
                +
                ".understand-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4); }\n"
                +
                "::-webkit-scrollbar { width: 6px; }\n" +
                "::-webkit-scrollbar-track { background: rgba(71, 85, 105, 0.2); border-radius: 3px; }\n" +
                "::-webkit-scrollbar-thumb { background: rgba(59, 130, 246, 0.5); border-radius: 3px; }\n" +
                "::-webkit-scrollbar-thumb:hover { background: rgba(59, 130, 246, 0.7); }\n" +
                "\n" +
                "/* ==================== 移动端响应式设计 ==================== */\n" +
                "@media (max-width: 768px) {\n" +
                "    body { padding: 10px; }\n" +
                "    \n" +
                "    .container { max-width: 100%; }\n" +
                "    \n" +
                "    /* 抽奖主界面 */\n" +
                "    .lottery-modal {\n" +
                "        padding: 25px 20px;\n" +
                "        margin: 20px auto;\n" +
                "        max-width: 100%;\n" +
                "        border-radius: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .modal-title {\n" +
                "        font-size: 1.5em;\n" +
                "        flex-direction: column;\n" +
                "        gap: 5px;\n" +
                "    }\n" +
                "    \n" +
                "    .modal-subtitle {\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "    \n" +
                "    /* 表单元素 */\n" +
                "    .form-group {\n" +
                "        margin-bottom: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .form-group input {\n" +
                "        padding: 12px;\n" +
                "        font-size: 16px;\n" +
                "        border-radius: 10px;\n" +
                "    }\n" +
                "    \n" +
                "    .lottery-btn {\n" +
                "        padding: 15px;\n" +
                "        font-size: 16px;\n" +
                "        border-radius: 10px;\n" +
                "    }\n" +
                "    \n" +
                "    /* 结果显示 */\n" +
                "    .result {\n" +
                "        padding: 15px;\n" +
                "        border-radius: 10px;\n" +
                "        font-size: 0.95em;\n" +
                "    }\n" +
                "    \n" +
                "    /* 中奖记录 */\n" +
                "    .recent-winners {\n" +
                "        padding: 20px 15px;\n" +
                "        margin-top: 25px;\n" +
                "        border-radius: 12px;\n" +
                "    }\n" +
                "    \n" +
                "    .recent-winners h3 {\n" +
                "        font-size: 1.1em;\n" +
                "        margin-bottom: 12px;\n" +
                "    }\n" +
                "    \n" +
                "    .record-container {\n" +
                "        height: 150px;\n" +
                "        border-radius: 8px;\n" +
                "    }\n" +
                "    \n" +
                "    .record-item {\n" +
                "        padding: 6px 10px;\n" +
                "        font-size: 0.85em;\n" +
                "        flex-direction: column;\n" +
                "        text-align: center;\n" +
                "        gap: 2px;\n" +
                "    }\n" +
                "    \n" +
                "    .record-time,\n" +
                "    .record-username,\n" +
                "    .record-prize {\n" +
                "        width: 100%;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .record-time {\n" +
                "        font-size: 0.8em;\n" +
                "        color: #9cb3cc;\n" +
                "    }\n" +
                "    \n" +
                "    .record-username {\n" +
                "        font-weight: bold;\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "    \n" +
                "    .record-prize {\n" +
                "        font-size: 0.85em;\n" +
                "        margin-top: 2px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* 开箱动画移动端优化 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .chest-modal-container {\n" +
                "        width: 95%;\n" +
                "        height: 60vh;\n" +
                "        max-height: 400px;\n" +
                "    }\n" +
                "    \n" +
                "    .chest-modal-content {\n" +
                "        border-radius: 15px;\n" +
                "        border-width: 2px;\n" +
                "    }\n" +
                "    \n" +
                "    .animation-container {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .animation-wrapper {\n" +
                "        width: 280px;\n" +
                "        height: 280px;\n" +
                "    }\n" +
                "    \n" +
                "    .custom-gif,\n" +
                "    .custom-video,\n" +
                "    .classic-gif {\n" +
                "        width: 320px;\n" +
                "        height: 320px;\n" +
                "    }\n" +
                "    \n" +
                "    /* 各种动画的移动端适配 */\n" +
                "    .classic-chest {\n" +
                "        width: 200px;\n" +
                "        height: 200px;\n" +
                "    }\n" +
                "    \n" +
                "    .modern-card {\n" +
                "        width: 180px;\n" +
                "        height: 250px;\n" +
                "    }\n" +
                "    \n" +
                "    .sparkle-effect,\n" +
                "    .rotate-display {\n" +
                "        width: 200px;\n" +
                "        height: 200px;\n" +
                "    }\n" +
                "    \n" +
                "    .luxury-vault {\n" +
                "        width: 280px;\n" +
                "        height: 280px;\n" +
                "    }\n" +
                "    \n" +
                "    .cosmic-portal {\n" +
                "        width: 280px;\n" +
                "        height: 280px;\n" +
                "    }\n" +
                "    \n" +
                "    .chest-status-text {\n" +
                "        bottom: 20px;\n" +
                "        font-size: 1.1em;\n" +
                "        padding: 0 10px;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* 奖品弹窗移动端优化 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .prize-reward-content {\n" +
                "        width: 95%;\n" +
                "        max-width: none;\n" +
                "        margin: 10px;\n" +
                "        border-radius: 15px;\n" +
                "        border-width: 2px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-header {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-header h2 {\n" +
                "        font-size: 1.5em;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-close {\n" +
                "        top: 10px;\n" +
                "        right: 15px;\n" +
                "        font-size: 1.8em;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-body {\n" +
                "        padding: 20px 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-icon-large {\n" +
                "        font-size: 4em;\n" +
                "        margin-bottom: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-name {\n" +
                "        font-size: 1.3em;\n" +
                "        margin-bottom: 10px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-desc {\n" +
                "        font-size: 0.95em;\n" +
                "        line-height: 1.5;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-footer {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-confirm-btn {\n" +
                "        width: 100%;\n" +
                "        padding: 12px;\n" +
                "        font-size: 16px;\n" +
                "        border-radius: 10px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* 奖品说明弹窗移动端优化 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .prize-modal-content {\n" +
                "        width: 95%;\n" +
                "        max-width: none;\n" +
                "        margin: 10px;\n" +
                "        border-radius: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-modal-header {\n" +
                "        padding: 15px;\n" +
                "        flex-direction: column;\n" +
                "        gap: 10px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-modal-header h2 {\n" +
                "        font-size: 1.4em;\n" +
                "        margin: 0;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-close-btn {\n" +
                "        position: static;\n" +
                "        width: 40px;\n" +
                "        height: 40px;\n" +
                "        font-size: 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-modal-body {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-description {\n" +
                "        font-size: 0.95em;\n" +
                "        margin-bottom: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-list {\n" +
                "        max-height: 250px;\n" +
                "        overflow-y: auto;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-modal-footer {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .understand-btn {\n" +
                "        width: 100%;\n" +
                "        padding: 12px;\n" +
                "        font-size: 16px;\n" +
                "        border-radius: 10px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* 小屏幕设备优化 */\n" +
                "@media (max-width: 480px) {\n" +
                "    body { padding: 5px; }\n" +
                "    \n" +
                "    .lottery-modal {\n" +
                "        padding: 20px 15px;\n" +
                "        margin: 10px auto;\n" +
                "    }\n" +
                "    \n" +
                "    .modal-title {\n" +
                "        font-size: 1.3em;\n" +
                "    }\n" +
                "    \n" +
                "    .form-group input {\n" +
                "        padding: 10px;\n" +
                "        font-size: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .lottery-btn {\n" +
                "        padding: 12px;\n" +
                "        font-size: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .recent-winners {\n" +
                "        padding: 15px 10px;\n" +
                "    }\n" +
                "    \n" +
                "    .record-container {\n" +
                "        height: 120px;\n" +
                "    }\n" +
                "    \n" +
                "    .record-item {\n" +
                "        padding: 4px 8px;\n" +
                "        font-size: 0.8em;\n" +
                "    }\n" +
                "    \n" +
                "    /* 开箱动画 */\n" +
                "    .chest-modal-container {\n" +
                "        width: 98%;\n" +
                "        height: 50vh;\n" +
                "        max-height: 300px;\n" +
                "    }\n" +
                "    \n" +
                "    .animation-wrapper {\n" +
                "        width: 200px;\n" +
                "        height: 200px;\n" +
                "    }\n" +
                "    \n" +
                "    .custom-gif,\n" +
                "    .custom-video,\n" +
                "    .classic-gif {\n" +
                "        width: 240px;\n" +
                "        height: 240px;\n" +
                "    }\n" +
                "    \n" +
                "    .chest-status-text {\n" +
                "        font-size: 1em;\n" +
                "        bottom: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    /* 奖品弹窗 */\n" +
                "    .prize-reward-content {\n" +
                "        width: 98%;\n" +
                "        margin: 5px;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-header h2 {\n" +
                "        font-size: 1.3em;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-icon-large {\n" +
                "        font-size: 3.5em;\n" +
                "    }\n" +
                "    \n" +
                "    .prize-reward-name {\n" +
                "        font-size: 1.2em;\n" +
                "    }\n" +
                "}\n";
    }

    /**
     * 获取用户界面JavaScript
     */
    private String getUserJS() {
        return "function showResult(message, type = 'info') {\n" +
                "    const resultDiv = document.getElementById('result');\n" +
                "    resultDiv.className = 'result ' + type;\n" +
                "    resultDiv.innerHTML = message;\n" +
                "    resultDiv.style.display = 'block';\n" +
                "    setTimeout(() => { resultDiv.style.display = 'none'; }, 8000);\n" +
                "}\n" +
                "\n" +
                "function getUsername() {\n" +
                "    const username = document.getElementById('username').value.trim();\n" +
                "    if (!username) {\n" +
                "        showResult('请先输入您的游戏用户名！', 'error');\n" +
                "        return null;\n" +
                "    }\n" +
                "    return username;\n" +
                "}\n" +
                "\n" +
                "function checkKeyInput() {\n" +
                "    const key = document.getElementById('keyInput').value.trim();\n" +
                "    const lotteryBtn = document.getElementById('lotteryBtn');\n" +
                "    \n" +
                "    if (key.length > 0) {\n" +
                "        lotteryBtn.disabled = false;\n" +
                "    } else {\n" +
                "        lotteryBtn.disabled = true;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "function addWinnerRecord(username, prize) {\n" +
                "    const winnerList = document.getElementById('winnerList');\n" +
                "    const now = new Date();\n" +
                "    const timeStr = String(now.getMonth() + 1).padStart(2, '0') + '.' + String(now.getDate()).padStart(2, '0') + '号(' + String(now.getHours()).padStart(2, '0') + ':' + String(now.getMinutes()).padStart(2, '0') + ')';\n"
                +
                "    \n" +
                "    const newWinner = document.createElement('div');\n" +
                "    newWinner.className = 'record-item new-winner';\n" +
                "    newWinner.innerHTML = '<span class=\"record-time\">' + timeStr + '</span><span class=\"record-username\">' + username + '</span><span class=\"record-prize\">' + prize + '</span>';\n"
                +
                "    \n" +
                "    // 设置奖品颜色\n" +
                "    const prizeElement = newWinner.querySelector('.record-prize');\n" +
                "    setPrizeColor(prizeElement, prize);\n" +
                "    \n" +
                "    // 添加新记录到顶部\n" +
                "    winnerList.insertBefore(newWinner, winnerList.firstChild);\n" +
                "    \n" +
                "    // 移除动画类，准备下次使用\n" +
                "    setTimeout(() => {\n" +
                "        newWinner.classList.remove('new-winner');\n" +
                "    }, 800);\n" +
                "    \n" +
                "    // 保持最多显示8条记录，超出的添加淡出动画后移除\n" +
                "    if (winnerList.children.length > 8) {\n" +
                "        const itemsToRemove = [];\n" +
                "        for (let i = 8; i < winnerList.children.length; i++) {\n" +
                "            const item = winnerList.children[i];\n" +
                "            item.classList.add('fade-out');\n" +
                "            itemsToRemove.push(item);\n" +
                "        }\n" +
                "        \n" +
                "        // 延迟移除元素，等待动画完成\n" +
                "        setTimeout(() => {\n" +
                "            itemsToRemove.forEach(item => {\n" +
                "                if (item.parentNode) {\n" +
                "                    item.parentNode.removeChild(item);\n" +
                "                }\n" +
                "            });\n" +
                "        }, 500);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 设置奖品颜色\n" +
                "function setPrizeColor(element, prize) {\n" +
                "    if (prize.includes('一等奖')) {\n" +
                "        element.style.color = '#ef4444';\n" +
                "\n" +
                "    } else if (prize.includes('二等奖')) {\n" +
                "        element.style.color = '#f97316';\n" +
                "\n" +
                "    } else if (prize.includes('三等奖')) {\n" +
                "        element.style.color = '#eab308';\n" +
                "\n" +
                "    } else if (prize.includes('四等奖')) {\n" +
                "        element.style.color = '#10b981';\n" +
                "\n" +
                "    } else if (prize.includes('五等奖')) {\n" +
                "        element.style.color = '#3b82f6';\n" +
                "\n" +
                "    } else if (prize.includes('六等奖')) {\n" +
                "        element.style.color = '#8b5cf6';\n" +
                "    } else {\n" +
                "        element.style.color = '#e94560';\n" +
                "    }\n" +
                "    // 确保没有背景色和边框\n" +
                "    element.style.background = 'none';\n" +
                "    element.style.padding = '0';\n" +
                "    element.style.borderRadius = '0';\n" +
                "}\n" +
                "\n" +
                "// 自动滚动功能\n" +
                "function startAutoScroll() {\n" +
                "    const winnerList = document.getElementById('winnerList');\n" +
                "    if (winnerList.children.length > 3) {\n" +
                "        // 创建无缝滚动效果\n" +
                "        createSeamlessScroll(winnerList);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "function stopAutoScroll() {\n" +
                "    const winnerList = document.getElementById('winnerList');\n" +
                "    winnerList.classList.remove('auto-scrolling');\n" +
                "    // 移除复制的内容\n" +
                "    const clonedItems = winnerList.querySelectorAll('.cloned-item');\n" +
                "    clonedItems.forEach(item => item.remove());\n" +
                "}\n" +
                "\n" +
                "function createSeamlessScroll(winnerList) {\n" +
                "    // 先移除之前的克隆项\n" +
                "    const existingClones = winnerList.querySelectorAll('.cloned-item');\n" +
                "    existingClones.forEach(item => item.remove());\n" +
                "    \n" +
                "    // 克隆所有原始项目到末尾，实现无缝滚动\n" +
                "    const originalItems = winnerList.querySelectorAll('.record-item:not(.cloned-item)');\n" +
                "    originalItems.forEach(item => {\n" +
                "        const clone = item.cloneNode(true);\n" +
                "        clone.classList.add('cloned-item');\n" +
                "        winnerList.appendChild(clone);\n" +
                "    });\n" +
                "    \n" +
                "    // 添加一些额外的空白项目确保滚动边界\n" +
                "    for (let i = 0; i < 2; i++) {\n" +
                "        const spacer = document.createElement('div');\n" +
                "        spacer.className = 'record-item cloned-item';\n" +
                "        spacer.style.opacity = '0';\n" +
                "        spacer.style.height = '20px';\n" +
                "        winnerList.appendChild(spacer);\n" +
                "    }\n" +
                "    \n" +
                "    winnerList.classList.add('auto-scrolling');\n" +
                "}\n" +
                "\n" +
                "function participateLottery() {\n" +
                "    console.log('开始抽奖流程');\n" +
                "    const username = getUsername();\n" +
                "    console.log('用户名:', username);\n" +
                "    if (!username) return;\n" +
                "    \n" +
                "    const key = document.getElementById('keyInput').value.trim();\n" +
                "    console.log('卡密:', key);\n" +
                "    if (!key) {\n" +
                "        showResult('请先输入卡密！', 'error');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    // 禁用按钮防止重复点击\n" +
                "    const lotteryBtn = document.getElementById('lotteryBtn');\n" +
                "    lotteryBtn.disabled = true;\n" +
                "    lotteryBtn.textContent = '🎲 抽奖中...';\n" +
                "    \n" +
                "    showResult('🎲 正在验证卡密...', 'info');\n" +
                "    \n" +
                "    console.log('发送卡密验证请求');\n" +
                "    fetch('/api', {\n" +
                "        method: 'POST',\n" +
                "        headers: { 'Content-Type': 'application/json' },\n" +
                "        body: JSON.stringify({\n" +
                "            action: 'participate_lottery',\n" +
                "            api_key: '" + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE")
                + "',\n" +
                "            key: key,\n" +
                "            username: username\n" +
                "        })\n" +
                "    })\n" +
                "    .then(response => {\n" +
                "        console.log('卡密验证响应状态:', response.status);\n" +
                "        return response.json();\n" +
                "    })\n" +
                "    .then(data => {\n" +
                "        console.log('卡密验证结果:', data);\n" +
                "        if (data.success) {\n" +
                "            // 卡密验证成功，显示宝箱动画\n" +
                "            console.log('卡密验证成功，开始抽奖动画');\n" +
                "            showResult('🎲 正在使用卡密进行抽奖...', 'info');\n" +
                "            showChestAnimation();\n" +
                "            \n" +
                "            // 延迟显示结果，让宝箱动画播放完成\n" +
                "            setTimeout(() => {\n" +
                "                console.log('抽奖成功，奖品:', data.prize, '奖励ID:', data.reward_id);\n" +
                "                // 先关闭开箱动画，然后立即显示奖励弹窗\n" +
                "                hideChestAnimationAndShowPrize(data.prize, data.reward_id, username);\n" +
                "                addWinnerRecord(username, data.prize);\n" +
                "                // 重新创建滚动效果\n" +
                "                stopAutoScroll();\n" +
                "                setTimeout(startAutoScroll, 500);\n" +
                "                document.getElementById('keyInput').value = '';\n" +
                "                checkKeyInput(); // 重新检查输入状态\n" +
                "            }, 3000); // 3秒后显示结果\n" +
                "        } else {\n" +
                "            // 卡密验证失败，直接显示错误信息，不播放动画和音效\n" +
                "            console.log('卡密验证失败:', data.message);\n" +
                "            showResult('❌ ' + data.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        // 错误时不需要隐藏动画，因为失败时根本没有显示动画\n" +
                "        showResult('❌ ' + error.message, 'error');\n" +
                "        console.error('Error:', error);\n" +
                "    })\n" +
                "    .finally(() => {\n" +
                "        // 恢复按钮状态\n" +
                "        lotteryBtn.textContent = '🎲 开始抽奖';\n" +
                "        checkKeyInput(); // 重新检查按钮状态\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 显示开箱动画\n" +
                "function showChestAnimation() {\n" +
                "    const chestModal = document.getElementById('chestModal');\n" +
                "    chestModal.style.display = 'flex';\n" +
                "    \n" +
                "    // 获取动画配置\n" +
                "    const animationWrapper = document.querySelector('.animation-wrapper');\n" +
                "    const animationDuration = parseFloat(animationWrapper.getAttribute('data-duration')) * 1000 || 3000;\n"
                +
                "    const animationStyle = animationWrapper.className.split(' ')[1].replace('-animation', '');\n" +
                "    \n" +
                "    console.log('动画风格:', animationStyle, '持续时间:', animationDuration + 'ms');\n" +
                "    \n" +
                "    // 启动对应的动画\n" +
                "    startAnimation(animationStyle, animationWrapper);\n" +
                "    \n" +
                "    // 播放音效\n" +
                "    setTimeout(() => {\n" +
                "        playChestSound();\n" +
                "    }, 200);\n" +
                "    \n" +
                "    // 更新状态文字\n" +
                "    const statusText = document.getElementById('chestStatusText');\n" +
                "    const statusMessages = {\n" +
                "        classic: ['🎲 正在开启宝箱...', '✨ 宝箱正在开启...', '🎉 发现宝藏！'],\n" +
                "        modern: ['🎲 正在翻转卡片...', '✨ 卡片正在翻转...', '🎉 发现惊喜！'],\n" +
                "        sparkle: ['🎲 正在施展魔法...', '✨ 魔法正在生效...', '🎉 魔法成功！'],\n" +
                "        rotate: ['🎲 正在旋转展示...', '✨ 物品正在旋转...', '🎉 展示完成！'],\n" +
                "        luxury: ['🎲 正在开启保险库...', '✨ 保险库门正在开启...', '🎉 发现珍宝！'],\n" +
                "        cosmic: ['🎲 正在开启传送门...', '✨ 宇宙能量正在汇聚...', '🎉 神秘奖励降临！'],\n" +
                "        custom: ['🎲 正在播放动画...', '✨ 动画正在进行...', '🎉 动画完成！']\n" +
                "    };\n" +
                "    \n" +
                "    const messages = statusMessages[animationStyle] || statusMessages.classic;\n" +
                "    \n" +
                "    statusText.textContent = messages[0];\n" +
                "    setTimeout(() => {\n" +
                "        statusText.textContent = messages[1];\n" +
                "    }, animationDuration * 0.3);\n" +
                "    \n" +
                "    setTimeout(() => {\n" +
                "        statusText.textContent = messages[2];\n" +
                "    }, animationDuration * 0.7);\n" +
                "}\n" +
                "\n" +
                "// 启动对应的动画\n" +
                "function startAnimation(style, wrapper) {\n" +
                "    // 移除之前的active类\n" +
                "    wrapper.classList.remove('active');\n" +
                "    \n" +
                "    // 强制重排，确保类被移除\n" +
                "    wrapper.offsetHeight;\n" +
                "    \n" +
                "    switch(style) {\n" +
                "        case 'classic':\n" +
                "            // 经典宝箱动画\n" +
                "            setTimeout(() => {\n" +
                "                wrapper.classList.add('active');\n" +
                "            }, 100);\n" +
                "            break;\n" +
                "        case 'modern':\n" +
                "            // 现代卡片动画\n" +
                "            setTimeout(() => {\n" +
                "                wrapper.classList.add('active');\n" +
                "            }, 100);\n" +
                "            break;\n" +
                "        case 'sparkle':\n" +
                "            // 闪光特效动画（自动循环）\n" +
                "            wrapper.classList.add('active');\n" +
                "            break;\n" +
                "        case 'rotate':\n" +
                "            // 旋转展示动画（自动循环）\n" +
                "            wrapper.classList.add('active');\n" +
                "            break;\n" +
                "        case 'luxury':\n" +
                "            // 豪华保险库动画\n" +
                "            setTimeout(() => {\n" +
                "                wrapper.classList.add('active');\n" +
                "            }, 100);\n" +
                "            break;\n" +
                "        case 'cosmic':\n" +
                "            // 宇宙传送门动画\n" +
                "            wrapper.classList.add('active');\n" +
                "            break;\n" +
                "        case 'custom':\n" +
                "            // 自定义动画（GIF/视频自动播放）\n" +
                "            const media = wrapper.querySelector('#animationMedia');\n" +
                "            if (media.tagName === 'VIDEO') {\n" +
                "                media.currentTime = 0;\n" +
                "                media.play();\n" +
                "            } else if (media.tagName === 'IMG') {\n" +
                "                // 重新加载GIF确保从头播放\n" +
                "                const src = media.src;\n" +
                "                media.src = '';\n" +
                "                media.src = src;\n" +
                "            }\n" +
                "            break;\n" +
                "        default:\n" +
                "            // 默认处理\n" +
                "            const defaultMedia = wrapper.querySelector('#animationMedia');\n" +
                "            if (defaultMedia && defaultMedia.tagName === 'IMG') {\n" +
                "                const src = defaultMedia.src;\n" +
                "                defaultMedia.src = '';\n" +
                "                defaultMedia.src = src;\n" +
                "            }\n" +
                "            break;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 隐藏开箱动画\n" +
                "function hideChestAnimation() {\n" +
                "    const chestModal = document.getElementById('chestModal');\n" +
                "    chestModal.style.display = 'none';\n" +
                "    \n" +
                "    // 重置动画状态\n" +
                "    const animationWrapper = document.querySelector('.animation-wrapper');\n" +
                "    if (animationWrapper) {\n" +
                "        animationWrapper.classList.remove('active');\n" +
                "        \n" +
                "        // 停止视频播放\n" +
                "        const video = animationWrapper.querySelector('video');\n" +
                "        if (video) {\n" +
                "            video.pause();\n" +
                "            video.currentTime = 0;\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    // 重置状态文字\n" +
                "    setTimeout(() => {\n" +
                "        const statusText = document.getElementById('chestStatusText');\n" +
                "        if (statusText) {\n" +
                "            statusText.textContent = '🎲 正在开启宝箱...';\n" +
                "        }\n" +
                "    }, 300);\n" +
                "}\n" +
                "\n" +
                "// 隐藏开箱动画并显示奖励弹窗\n" +
                "function hideChestAnimationAndShowPrize(prize, rewardId, username) {\n" +
                "    console.log('关闭开箱动画并显示奖励:', prize, '奖励ID:', rewardId, '用户:', username);\n" +
                "    \n" +
                "    // 调用隐藏动画函数（包含重置逻辑）\n" +
                "    hideChestAnimation();\n" +
                "    \n" +
                "    // 立即显示奖励弹窗\n" +
                "    setTimeout(() => {\n" +
                "        showPrizeModal(prize, rewardId, username);\n" +
                "    }, 100); // 很短的延迟，确保开箱动画完全关闭\n" +
                "}\n" +
                "\n" +
                "// 播放宝箱音效\n" +
                "function playChestSound() {\n" +
                "    // 检查音效是否启用\n" +
                "    const soundEnabled = " + plugin.getConfig().getBoolean("website.sound-effects.enabled", true)
                + ";\n" +
                "    if (!soundEnabled) {\n" +
                "        console.log('音效已禁用');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    const audio = document.getElementById('chestAudio');\n" +
                "    if (audio) {\n" +
                "        audio.currentTime = 0;\n" +
                "        audio.volume = " + (plugin.getConfig().getInt("website.sound-effects.volume", 50) / 100.0)
                + "; // 从配置读取音量\n" +
                "        \n" +
                "        // 尝试播放音效\n" +
                "        const playPromise = audio.play();\n" +
                "        if (playPromise !== undefined) {\n" +
                "            playPromise\n" +
                "                .then(() => {\n" +
                "                    console.log('宝箱音效播放成功');\n" +
                "                })\n" +
                "                .catch(error => {\n" +
                "                    console.log('宝箱音效播放失败:', error);\n" +
                "                    // 如果自动播放失败，可以尝试用户交互后播放\n" +
                "                });\n" +
                "        }\n" +
                "    } else {\n" +
                "        console.log('未找到开箱音频元素');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 播放中奖音效\n" +
                "function playWinningSound() {\n" +
                "    // 检查音效是否启用\n" +
                "    const soundEnabled = " + plugin.getConfig().getBoolean("website.sound-effects.enabled", true)
                + ";\n" +
                "    if (!soundEnabled) {\n" +
                "        console.log('音效已禁用');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    const audio = document.getElementById('winningAudio');\n" +
                "    if (audio) {\n" +
                "        audio.currentTime = 0;\n" +
                "        audio.volume = " + (plugin.getConfig().getInt("website.sound-effects.volume", 50) / 100.0)
                + "; // 从配置读取音量\n" +
                "        \n" +
                "        // 尝试播放音效\n" +
                "        const playPromise = audio.play();\n" +
                "        if (playPromise !== undefined) {\n" +
                "            playPromise\n" +
                "                .then(() => {\n" +
                "                    console.log('中奖音效播放成功');\n" +
                "                })\n" +
                "                .catch(error => {\n" +
                "                    console.log('中奖音效播放失败:', error);\n" +
                "                    // 如果自动播放失败，可以尝试用户交互后播放\n" +
                "                });\n" +
                "        }\n" +
                "    } else {\n" +
                "        console.log('未找到中奖音频元素');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 显示奖品获得弹窗\n" +
                "function showPrizeModal(prize, rewardId, username) {\n" +
                "    console.log('显示奖品弹窗，奖品:', prize, '奖励ID:', rewardId, '用户:', username);\n" +
                "    \n" +
                "    // 播放中奖音效\n" +
                "    playWinningSound();\n" +
                "    \n" +
                "    // 根据奖品名称设置图标和描述\n" +
                "    const prizeInfo = getPrizeInfo(prize);\n" +
                "    console.log('奖品信息:', prizeInfo);\n" +
                "    \n" +
                "    const prizeIcon = document.getElementById('prizeIcon');\n" +
                "    const prizeRewardName = document.getElementById('prizeRewardName');\n" +
                "    const prizeRewardDesc = document.getElementById('prizeRewardDesc');\n" +
                "    const modal = document.getElementById('prizeRewardModal');\n" +
                "    \n" +
                "    if (!prizeIcon || !prizeRewardName || !prizeRewardDesc || !modal) {\n" +
                "        console.error('奖品弹窗元素未找到:', {\n" +
                "            prizeIcon: !!prizeIcon,\n" +
                "            prizeRewardName: !!prizeRewardName,\n" +
                "            prizeRewardDesc: !!prizeRewardDesc,\n" +
                "            modal: !!modal\n" +
                "        });\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    prizeIcon.textContent = prizeInfo.icon;\n" +
                "    prizeRewardName.textContent = prize;\n" +
                "    prizeRewardDesc.textContent = prizeInfo.description;\n" +
                "    \n" +
                "    modal.style.display = 'flex';\n" +
                "    console.log('奖品弹窗已显示');\n" +
                "    \n" +
                "    // 弹窗显示后，立即发放奖励\n" +
                "    setTimeout(() => {\n" +
                "        claimReward(rewardId, username);\n" +
                "    }, 500); // 延迟500ms让用户看到弹窗\n" +
                "}\n" +
                "\n" +
                "// 隐藏奖品获得弹窗\n" +
                "function hidePrizeReward() {\n" +
                "    const modal = document.getElementById('prizeRewardModal');\n" +
                "    modal.style.display = 'none';\n" +
                "}\n" +
                "\n" +
                "// 领取奖励\n" +
                "function claimReward(rewardId, username) {\n" +
                "    console.log('开始领取奖励，奖励ID:', rewardId, '用户:', username);\n" +
                "    \n" +
                "    fetch('/api', {\n" +
                "        method: 'POST',\n" +
                "        headers: { 'Content-Type': 'application/json' },\n" +
                "        body: JSON.stringify({\n" +
                "            action: 'claim_reward',\n" +
                "            api_key: '" + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE")
                + "',\n" +
                "            reward_id: rewardId,\n" +
                "            username: username\n" +
                "        })\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        console.log('奖励领取结果:', data);\n" +
                "        if (data.success) {\n" +
                "            console.log('✅ 奖励发放成功');\n" +
                "        } else {\n" +
                "            console.error('❌ 奖励发放失败:', data.message);\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('奖励领取请求失败:', error);\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 获取奖品信息（图标和描述）\n" +
                "function getPrizeInfo(prizeName) {\n" +
                "    const prizeMap = {\n" +
                "        '一等奖': { icon: '👑', description: '恭喜您获得一等奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '二等奖': { icon: '🏆', description: '恭喜您获得二等奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '三等奖': { icon: '🥉', description: '恭喜您获得三等奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '四等奖': { icon: '🎖️', description: '恭喜您获得四等奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '五等奖': { icon: '🪙', description: '恭喜您获得五等奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '六等奖': { icon: '🎁', description: '恭喜您获得六等奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '超级大奖': { icon: '🎁', description: '恭喜您获得超级大奖！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '钻石礼包': { icon: '💎', description: '恭喜您获得钻石礼包！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '金锭大礼包': { icon: '🥇', description: '恭喜您获得金锭大礼包！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '经验宝瓶': { icon: '🧪', description: '恭喜您获得经验宝瓶！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '绿宝石珍藏': { icon: '💚', description: '恭喜您获得绿宝石珍藏！奖品将在24小时内发放到您的游戏账户' },\n" +
                "        '谢谢参与': { icon: '🌟', description: '感谢您的参与！下次再来试试运气吧！' }\n" +
                "    };\n" +
                "    \n" +
                "    // 检查是否包含等奖关键词\n" +
                "    for (let key in prizeMap) {\n" +
                "        if (prizeName.includes(key)) {\n" +
                "            return prizeMap[key];\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    // 默认返回\n" +
                "    return { icon: '🎁', description: '恭喜您获得奖品！奖品将在24小时内发放到您的游戏账户' };\n" +
                "}\n" +
                "\n" +
                "// 测试奖励弹窗（调试用）\n" +
                "function testPrizeModal() {\n" +
                "    console.log('测试奖励弹窗');\n" +
                "    showPrizeModal('五等奖2', 'test_reward', 'test_user');\n" +
                "}\n" +
                "\n" +
                "// 将测试函数暴露到全局，方便在控制台调用\n" +
                "window.testPrizeModal = testPrizeModal;\n" +
                "\n" +
                "// 显示奖品说明弹窗\n" +
                "function showPrizeInfo() {\n" +
                "    const modal = document.getElementById('prizeInfoModal');\n" +
                "    modal.style.display = 'flex';\n" +
                "    \n" +
                "    // 加载奖品信息\n" +
                "    loadPrizeInfo();\n" +
                "}\n" +
                "\n" +
                "// 隐藏奖品说明弹窗\n" +
                "function hidePrizeInfo() {\n" +
                "    const modal = document.getElementById('prizeInfoModal');\n" +
                "    modal.style.display = 'none';\n" +
                "}\n" +
                "\n" +
                "// 加载奖品信息\n" +
                "function loadPrizeInfo() {\n" +
                "    fetch('/api', {\n" +
                "        method: 'POST',\n" +
                "        headers: { 'Content-Type': 'application/json' },\n" +
                "        body: JSON.stringify({\n" +
                "            action: 'get_rewards',\n" +
                "            api_key: '" + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE")
                + "'\n" +
                "        })\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success && data.rewards) {\n" +
                "            displayPrizeList(data.rewards);\n" +
                "        } else {\n" +
                "            document.getElementById('prizeList').innerHTML = '<p style=\"color: #ef4444; text-align: center;\">加载奖品信息失败</p>';\n"
                +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('Error loading prize info:', error);\n" +
                "        document.getElementById('prizeList').innerHTML = '<p style=\"color: #ef4444; text-align: center;\">加载奖品信息失败</p>';\n"
                +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 显示奖品列表\n" +
                "function displayPrizeList(rewards) {\n" +
                "    const prizeList = document.getElementById('prizeList');\n" +
                "    let html = '';\n" +
                "    \n" +
                "    rewards.forEach(reward => {\n" +
                "        html += '<div class=\"prize-item\">';\n" +
                "        html += '<div>';\n" +
                "        html += '<div class=\"prize-name\">' + reward.name + '</div>';\n" +
                "        html += '<div class=\"prize-desc\">' + reward.description + '</div>';\n" +
                "        html += '</div>';\n" +
                "        html += '<div class=\"prize-probability\">' + reward.probability + '%</div>';\n" +
                "        html += '</div>';\n" +
                "    });\n" +
                "    \n" +
                "    prizeList.innerHTML = html;\n" +
                "}\n" +
                "\n" +
                "// 打开排行榜页面\n" +
                "function openLeaderboard() {\n" +
                "    window.location.href = '/leaderboard';\n" +
                "}\n" +
                "\n" +
                "// 打开处罚记录页面\n" +
                "function openPunishmentRecords() {\n" +
                "    window.location.href = '/punishments';\n" +
                "}\n" +
                "\n" +
                "// 页面加载完成后的初始化\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    checkKeyInput(); // 初始化按钮状态\n" +
                "    \n" +
                "    // 为现有奖品设置颜色\n" +
                "    const existingPrizes = document.querySelectorAll('.record-prize');\n" +
                "    existingPrizes.forEach(function(element) {\n" +
                "        setPrizeColor(element, element.textContent);\n" +
                "    });\n" +
                "    \n" +
                "    // 启动自动滚动\n" +
                "    setTimeout(startAutoScroll, 1000);\n" +
                "    \n" +
                "    // 根据记录数量调整滚动动画\n" +
                "    const recordList = document.querySelector('.record-list');\n" +
                "    if (recordList) {\n" +
                "        const recordCount = document.querySelectorAll('.record-item').length;\n" +
                "        if (recordCount > 0) {\n" +
                "            const animationDuration = Math.max(30, recordCount * 2);\n" +
                "            recordList.style.animationDuration = animationDuration + 's';\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    // 获取URL参数中的key值\n" +
                "    const urlParams = new URLSearchParams(window.location.search);\n" +
                "    const key = urlParams.get('key');\n" +
                "    \n" +
                "    // 如果存在key参数，自动填入卡密输入框\n" +
                "    if (key) {\n" +
                "        document.getElementById('keyInput').value = key;\n" +
                "        checkKeyInput(); // 重新检查按钮状态\n" +
                "    }\n" +
                "    \n" +
                "    // 点击弹窗外部关闭弹窗\n" +
                "    const prizeModal = document.getElementById('prizeInfoModal');\n" +
                "    if (prizeModal) {\n" +
                "        prizeModal.addEventListener('click', function(e) {\n" +
                "            if (e.target === prizeModal) {\n" +
                "                hidePrizeInfo();\n" +
                "            }\n" +
                "        });\n" +
                "    }\n" +
                "    \n" +
                "    // 点击宝箱弹窗外部关闭弹窗\n" +
                "    const chestModal = document.getElementById('chestModal');\n" +
                "    if (chestModal) {\n" +
                "        chestModal.addEventListener('click', function(e) {\n" +
                "            if (e.target === chestModal) {\n" +
                "                hideChestAnimation();\n" +
                "            }\n" +
                "        });\n" +
                "    }\n" +
                "    \n" +
                "    // 点击奖励弹窗外部关闭弹窗\n" +
                "    const prizeRewardModal = document.getElementById('prizeRewardModal');\n" +
                "    if (prizeRewardModal) {\n" +
                "        prizeRewardModal.addEventListener('click', function(e) {\n" +
                "            if (e.target === prizeRewardModal) {\n" +
                "                hidePrizeReward();\n" +
                "            }\n" +
                "        });\n" +
                "    }\n" +
                "    \n" +
                "    // 添加表单提交事件监听\n" +
                "    const drawForm = document.getElementById('drawForm');\n" +
                "    if (drawForm) {\n" +
                "        drawForm.addEventListener('submit', function() {\n" +
                "            // 创建一个临时的音频元素来预加载音效\n" +
                "            const tempAudio = new Audio('/static/chest-open.mp3');\n" +
                "            tempAudio.preload = 'auto';\n" +
                "            \n" +
                "            // 尝试预加载音效（这样在显示宝箱时可以立即播放）\n" +
                "            try {\n" +
                "                tempAudio.load();\n" +
                "            } catch (e) {\n" +
                "                console.log('预加载音频出错:', e);\n" +
                "            }\n" +
                "        });\n" +
                "    }\n" +
                "});\n" +
                "\n" +
                "// 积分商店相关函数\n" +
                "function openPointsShop() {\n" +
                "    window.location.href = '/points-shop';\n" +
                "}\n" +
                "\n" +
                "</script>\n" +
                "<script src=\"https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js\"></script>\n"
                +
                "</body>\n" +
                "</html>";
    }

    /**
     * 获取主题背景样式
     */
    private String getThemeBackgroundStyle(String theme, String backgroundImage, String primaryColor,
            String accentColor) {
        if (backgroundImage != null && !backgroundImage.trim().isEmpty()) {
            // 获取透明度设置
            int opacity = plugin.getConfig().getInt("website.background-opacity", 30);
            // 将透明度转换为0-1之间的值
            double opacityValue = opacity / 100.0;

            // 如果配置了背景图，使用背景图（支持GIF动图）
            return "background: linear-gradient(rgba(30, 41, 59, " + opacityValue + "), rgba(71, 85, 105, "
                    + (opacityValue + 0.1) + ")), url('"
                    + backgroundImage + "'); " +
                    "background-size: cover; background-position: center; background-attachment: fixed; background-repeat: no-repeat;";
        }

        // 根据主题返回不同的背景样式
        switch (theme) {
            case "dark":
                return "background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #404040 100%);";
            case "light":
                return "background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);";
            case "blue":
                return "background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);";
            case "purple":
                return "background: linear-gradient(135deg, #581c87 0%, #8b5cf6 50%, #a78bfa 100%);";
            case "green":
                return "background: linear-gradient(135deg, #14532d 0%, #22c55e 50%, #4ade80 100%);";
            default:
                return "background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 50%, rgba(71, 85, 105, 0.8) 100%);";
        }
    }

    /**
     * 生成主题CSS
     */
    private String generateThemeCSS(String theme, String primaryColor, String accentColor, boolean enableAnimations,
            String loadAnimation, String animationSpeed) {
        StringBuilder css = new StringBuilder();

        // CSS变量定义
        css.append(":root {\n");
        css.append("  --primary-color: ").append(primaryColor).append(";\n");
        css.append("  --accent-color: ").append(accentColor).append(";\n");
        css.append("  --animation-duration: ").append(getAnimationDuration(animationSpeed)).append(";\n");
        css.append("}\n\n");

        // 页面加载动画
        if (enableAnimations && !"none".equals(loadAnimation)) {
            css.append(generateLoadAnimationCSS(loadAnimation));
        }

        // 主题特定样式
        css.append(generateThemeSpecificCSS(theme));

        return css.toString();
    }

    /**
     * 获取动画持续时间
     */
    private String getAnimationDuration(String speed) {
        switch (speed) {
            case "slow":
                return "0.8s";
            case "fast":
                return "0.3s";
            default:
                return "0.5s";
        }
    }

    /**
     * 生成页面加载动画CSS
     */
    private String generateLoadAnimationCSS(String animation) {
        StringBuilder css = new StringBuilder();

        css.append("@keyframes pageLoad {\n");
        switch (animation) {
            case "fade":
                css.append("  from { opacity: 0; }\n");
                css.append("  to { opacity: 1; }\n");
                break;
            case "slide":
                css.append("  from { transform: translateY(30px); opacity: 0; }\n");
                css.append("  to { transform: translateY(0); opacity: 1; }\n");
                break;
            case "zoom":
                css.append("  from { transform: scale(0.9); opacity: 0; }\n");
                css.append("  to { transform: scale(1); opacity: 1; }\n");
                break;
            case "bounce":
                css.append("  0% { transform: translateY(-30px); opacity: 0; }\n");
                css.append("  50% { transform: translateY(10px); opacity: 0.8; }\n");
                css.append("  100% { transform: translateY(0); opacity: 1; }\n");
                break;
        }
        css.append("}\n\n");

        css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");

        return css.toString();
    }

    /**
     * 生成主题特定CSS
     */
    private String generateThemeSpecificCSS(String theme) {
        StringBuilder css = new StringBuilder();

        switch (theme) {
            case "dark":
                css.append(".lottery-modal { background: rgba(15, 15, 15, 0.95); color: #ffffff; }\n");
                css.append(".form-input { background: rgba(40, 40, 40, 0.8); color: #ffffff; border-color: #555; }\n");
                css.append(
                        ".lottery-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                break;
            case "light":
                css.append(
                        ".lottery-modal { background: rgba(255, 255, 255, 0.95); color: #1a1a1a; box-shadow: 0 25px 50px rgba(0,0,0,0.1); }\n");
                css.append(
                        ".form-input { background: rgba(255, 255, 255, 0.9); color: #1a1a1a; border-color: #e2e8f0; }\n");
                css.append(
                        ".lottery-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                break;
            default:
                // 使用默认样式，通过CSS变量应用自定义颜色
                css.append(
                        ".lottery-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                css.append(
                        ".points-shop-btn, .leaderboard-btn-corner, .punishment-btn-corner, .info-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                break;
        }

        return css.toString();
    }

    /**
     * 生成安全防护JavaScript
     */
    private String generateSecurityJS() {
        StringBuilder js = new StringBuilder();

        // 添加安全防护样式
        js.append("// 安全防护样式\n");
        js.append("const securityStyle = document.createElement('style');\n");
        js.append("securityStyle.textContent = `\n");
        js.append("    .security-overlay {\n");
        js.append("        position: fixed;\n");
        js.append("        top: 0;\n");
        js.append("        left: 0;\n");
        js.append("        width: 100%;\n");
        js.append("        height: 100%;\n");
        js.append("        background: rgba(0,0,0,0.9);\n");
        js.append("        color: white;\n");
        js.append("        display: none;\n");
        js.append("        z-index: 9999;\n");
        js.append("        justify-content: center;\n");
        js.append("        align-items: center;\n");
        js.append("        font-size: 24px;\n");
        js.append("        text-align: center;\n");
        js.append("        font-family: Arial, sans-serif;\n");
        js.append("    }\n");
        js.append("`;\n");
        js.append("document.head.appendChild(securityStyle);\n");
        js.append("\n");

        // 添加智能安全防护代码
        js.append("(function(){\n");
        js.append("var _0x1a2b=['keydown','F12','preventDefault','contextmenu','selectstart','dragstart'];\n");
        js.append("var _0x5e6f=document.createElement('div');\n");
        js.append("_0x5e6f.className='security-overlay';\n");
        js.append("_0x5e6f.innerHTML='🚫 检测到开发者工具<br>请关闭后重新访问<br><br>为保护系统安全，禁止使用开发者工具';\n");
        js.append("document.body.appendChild(_0x5e6f);\n");
        js.append("var _0xDetected = false;\n");

        // 键盘事件监听 - 只阻止开发者工具快捷键
        js.append("document.addEventListener(_0x1a2b[0],function(e){\n");
        js.append("if(e.key===_0x1a2b[1]||e.keyCode===123||\n");
        js.append("(e.ctrlKey&&e.shiftKey&&(e.keyCode===73||e.keyCode===74))||\n");
        js.append("(e.ctrlKey&&e.keyCode===85)){\n");
        js.append("e[_0x1a2b[2]]();showWarning();\n");
        js.append("}});\n");

        // 右键菜单禁用
        js.append("document.addEventListener(_0x1a2b[3],function(e){e[_0x1a2b[2]]();});\n");

        // 选择和拖拽禁用 - 但不影响正常点击
        js.append("document.addEventListener(_0x1a2b[4],function(e){\n");
        js.append("if(e.target.tagName!=='INPUT'&&e.target.tagName!=='TEXTAREA'&&e.target.tagName!=='BUTTON'){\n");
        js.append("e[_0x1a2b[2]]();\n");
        js.append("}});\n");
        js.append("document.addEventListener(_0x1a2b[5],function(e){e[_0x1a2b[2]]();});\n");

        // 智能窗口大小检测 - 更宽松的检测
        js.append("var _0xLastCheck = Date.now();\n");
        js.append("setInterval(function(){\n");
        js.append("var now = Date.now();\n");
        js.append("if(now - _0xLastCheck > 5000) {\n"); // 5秒检测一次
        js.append("if(window.outerHeight-window.innerHeight>250||window.outerWidth-window.innerWidth>250){\n");
        js.append("if(!_0xDetected) showWarning();\n");
        js.append("}\n");
        js.append("_0xLastCheck = now;\n");
        js.append("}},2000);\n");

        // 智能debugger检测 - 降低频率
        js.append("setInterval(function(){\n");
        js.append("if(_0xDetected) return;\n");
        js.append("var _0x9i0j=new Date().getTime();\n");
        js.append("debugger;\n");
        js.append("if(new Date().getTime()-_0x9i0j>150){\n");
        js.append("showWarning();\n");
        js.append("}},5000);\n"); // 5秒检测一次

        // 显示警告函数
        js.append("function showWarning(){\n");
        js.append("if(_0xDetected) return;\n");
        js.append("_0xDetected = true;\n");
        js.append("_0x5e6f.style.display='flex';\n");
        js.append("setTimeout(function(){\n");
        js.append("_0x5e6f.style.display='none';\n");
        js.append("_0xDetected = false;\n");
        js.append("},3000);\n"); // 3秒后自动隐藏，不刷新页面
        js.append("}\n");

        js.append("})();\n");
        js.append("\n");

        return js.toString();
    }

}
