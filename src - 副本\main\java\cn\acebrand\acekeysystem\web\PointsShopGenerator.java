package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 积分商店页面生成器
 * 统一管理积分商店的HTML、CSS和JavaScript代码
 */
public class PointsShopGenerator {

        private final AceKeySystem plugin;
        private final WebServer webServer;

        public PointsShopGenerator(AceKeySystem plugin, WebServer webServer) {
                this.plugin = plugin;
                this.webServer = webServer;
        }

        /**
         * 生成完整的积分商店页面
         */
        public String generatePointsShopPage() {
                StringBuilder html = new StringBuilder();

                html.append("<!DOCTYPE html>\n");
                html.append("<html lang=\"zh-CN\">\n");
                html.append("<head>\n");
                html.append("    <meta charset=\"UTF-8\">\n");
                html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
                html.append("    <title>积分商店</title>\n");
                html.append("    <style>\n");
                html.append(generatePointsShopCSS());
                html.append("    </style>\n");
                html.append("</head>\n");
                html.append("<body>\n");
                html.append(generatePointsShopHTML());
                html.append("    <script>\n");
                html.append(generateSecurityJS()); // 添加安全防护
                html.append(generatePointsShopJavaScript());
                html.append("    </script>\n");
                html.append("</body>\n");
                html.append("</html>\n");

                return html.toString();
        }

        /**
         * 生成积分商店HTML结构 - 和原来UserHandler中的结构完全一致
         */
        private String generatePointsShopHTML() {
                StringBuilder html = new StringBuilder();

                html.append("    <div class=\"container\">\n");
                html.append("        <div class=\"shop-header\">\n");
                html.append("            <h1 class=\"shop-title\">🛒 "
                                + plugin.getConfig().getString("website.shop-title", "积分商店") + "</h1>\n");
                html.append("            <p class=\"shop-subtitle\">"
                                + plugin.getConfig().getString("website.shop-description", "使用积分购买各种游戏道具和特权")
                                + "</p>\n");
                html.append("            <div class=\"user-points\">\n");
                html.append("                <span class=\"points-label\">我的积分:</span>\n");
                html.append("                <span class=\"points-value\" id=\"userPoints\">加载中...</span>\n");
                html.append(
                                "                <button class=\"refresh-btn\" onclick=\"refreshPoints()\" title=\"刷新积分\">🔄</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");
                html.append("        <div class=\"bind-section\">\n");
                html.append("            <div class=\"bind-status\" id=\"bindStatus\">\n");
                html.append("                <div class=\"bind-info\">\n");
                html.append("                    <h3>🔗 账户绑定</h3>\n");
                html.append("                    <p>请先绑定您的游戏账户才能使用积分商店</p>\n");
                html.append(
                                "                    <button class=\"bind-btn\" onclick=\"generateBindCode()\">🎯 生成绑定码</button>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");
                html.append("        <div class=\"shop-items\" id=\"shopItems\">\n");
                html.append("            <!-- 商店物品将通过JavaScript动态加载 -->\n");
                html.append("        </div>\n");
                html.append("        \n");
                html.append("        <!-- 分页控件 -->\n");
                html.append("        <div class=\"pagination-container\" id=\"paginationContainer\" style=\"display: none;\">\n");
                html.append("            <div class=\"pagination\">\n");
                html.append("                <button class=\"page-btn\" id=\"firstPageBtn\" onclick=\"goToPage(1)\" title=\"首页\">⏮️</button>\n");
                html.append("                <button class=\"page-btn\" id=\"prevPageBtn\" onclick=\"goToPrevPage()\" title=\"上一页\">⬅️</button>\n");
                html.append("                <div class=\"page-numbers\" id=\"pageNumbers\"></div>\n");
                html.append("                <button class=\"page-btn\" id=\"nextPageBtn\" onclick=\"goToNextPage()\" title=\"下一页\">➡️</button>\n");
                html.append("                <button class=\"page-btn\" id=\"lastPageBtn\" onclick=\"goToLastPage()\" title=\"末页\">⏭️</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"pagination-info\">\n");
                html.append("                <span id=\"paginationInfo\">第 1 页，共 1 页</span>\n");
                html.append("                <span class=\"items-info\">共 <span id=\"totalItemsCount\">0</span> 个商品</span>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("        \n");
                html.append("        <div class=\"back-to-lottery\">\n");
                html.append("            <button class=\"back-btn\" onclick=\"goBackToLottery()\">🎲 返回抽奖页面</button>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");
                html.append("    \n");
                html.append("    <!-- 购买确认弹窗 -->\n");
                html.append("    <div id=\"purchaseModal\" class=\"purchase-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"purchase-modal-content\">\n");
                html.append("            <div class=\"purchase-modal-header\">\n");
                html.append("                <h2>🛒 确认购买</h2>\n");
                html.append(
                                "                <button class=\"purchase-close-btn\" onclick=\"closePurchaseModal()\">×</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"purchase-modal-body\">\n");
                html.append("                <div class=\"item-preview\">\n");
                html.append("                    <div class=\"item-icon\" id=\"previewIcon\">📦</div>\n");
                html.append("                    <div class=\"item-info\">\n");
                html.append("                        <h3 class=\"item-name\" id=\"previewName\">物品名称</h3>\n");
                html.append("                        <p class=\"item-desc\" id=\"previewDesc\">物品描述</p>\n");
                html.append("                        <p class=\"item-cost\">单价: <span id=\"previewCost\">0</span> 积分</p>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 数量选择 -->\n");
                html.append("                <div class=\"quantity-selector\">\n");
                html.append("                    <label for=\"purchaseQuantity\">购买数量:</label>\n");
                html.append("                    <div class=\"quantity-controls\">\n");
                html.append(
                                "                        <button type=\"button\" class=\"quantity-btn\" onclick=\"changeQuantity(-1)\">-</button>\n");
                html.append(
                                "                        <input type=\"number\" id=\"purchaseQuantity\" value=\"1\" min=\"1\" max=\"99\" onchange=\"updateTotalCost()\" oninput=\"updateTotalCost()\">\n");
                html.append(
                                "                        <button type=\"button\" class=\"quantity-btn\" onclick=\"changeQuantity(1)\">+</button>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"quantity-presets\">\n");
                html.append(
                                "                        <button type=\"button\" class=\"preset-btn\" onclick=\"setQuantity(1)\">1</button>\n");
                html.append(
                                "                        <button type=\"button\" class=\"preset-btn\" onclick=\"setQuantity(5)\">5</button>\n");
                html.append(
                                "                        <button type=\"button\" class=\"preset-btn\" onclick=\"setQuantity(10)\">10</button>\n");
                html.append(
                                "                        <button type=\"button\" class=\"preset-btn\" onclick=\"setQuantity(99)\">最大</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 总价显示 -->\n");
                html.append("                <div class=\"total-cost\">\n");
                html.append("                    <p class=\"total-text\">总计: <span id=\"totalCost\">0</span> 积分</p>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"purchase-modal-footer\">\n");
                html.append("                <button class=\"cancel-btn\" onclick=\"closePurchaseModal()\">取消</button>\n");
                html.append("                <button class=\"confirm-btn\" onclick=\"confirmPurchase()\">确认购买</button>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");
                html.append("    \n");
                html.append("    <!-- 绑定码弹窗 -->\n");
                html.append("    <div id=\"bindModal\" class=\"bind-modal\" style=\"display: none;\">\n");
                html.append("        <div class=\"bind-modal-content\">\n");
                html.append("            <div class=\"bind-modal-header\">\n");
                html.append("                <h2>🔗 账户绑定</h2>\n");
                html.append("                <button class=\"bind-close-btn\" onclick=\"closeBindModal()\">×</button>\n");
                html.append("            </div>\n");
                html.append("            <div class=\"bind-modal-body\">\n");
                html.append("                <div class=\"bind-step\">\n");
                html.append("                    <h3>步骤 1: 复制绑定码</h3>\n");
                html.append("                    <div class=\"bind-code-container\">\n");
                html.append(
                                "                        <input type=\"text\" id=\"bindCodeInput\" readonly class=\"bind-code-input\">\n");
                html.append(
                                "                        <button class=\"copy-btn\" onclick=\"copyBindCode(event)\">📋 复制</button>\n");
                html.append("                    </div>\n");
                html.append("                    <p class=\"bind-code-note\">绑定码有效期：5分钟</p>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"bind-step\">\n");
                html.append("                    <h3>步骤 2: 游戏内绑定</h3>\n");
                html.append("                    <p>在游戏中输入以下命令：</p>\n");
                html.append("                    <div class=\"command-container\">\n");
                html.append("                        <code>/acebind <span id=\"bindCodeDisplay\">XXXXXX</span></code>\n");
                html.append(
                                "                        <button class=\"copy-btn\" onclick=\"copyCommand(event)\">📋 复制命令</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"bind-status-check\">\n");
                html.append("                    <p id=\"bindStatusText\">等待绑定...</p>\n");
                html.append("                    <div class=\"bind-progress\">\n");
                html.append("                        <div class=\"bind-progress-bar\" id=\"bindProgressBar\"></div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("        </div>\n");
                html.append("    </div>\n");

                return html.toString();
        }

        /**
         * 生成积分商店CSS样式 - 支持主题和动画
         */
        private String generatePointsShopCSS() {
                // 获取界面主题配置
                String theme = plugin.getConfig().getString("website.interface.theme", "default");
                String primaryColor = plugin.getConfig().getString("website.interface.primary-color", "#667eea");
                String accentColor = plugin.getConfig().getString("website.interface.accent-color", "#764ba2");
                boolean enableAnimations = plugin.getConfig().getBoolean("website.interface.enable-animations", true);
                String loadAnimation = plugin.getConfig().getString("website.interface.load-animation", "fade");
                String animationSpeed = plugin.getConfig().getString("website.interface.animation-speed", "normal");

                // 获取积分商店背景图配置
                String backgroundImage = plugin.getConfig().getString("website.shop-background-image", "");
                String backgroundStyle = getThemeBackgroundStyle(theme, backgroundImage, primaryColor, accentColor);

                // 获取动画效果配置
                boolean shopAnimations = plugin.getConfig().getBoolean("website.shop-animations", true);
                String animationStyle = (enableAnimations && shopAnimations)
                                ? "transition: all 0.3s ease; transform: translateY(0);"
                                : "transition: none; transform: none;";

                return generateThemeCSS(theme, primaryColor, accentColor, enableAnimations, loadAnimation,
                                animationSpeed) +
                                "* { margin: 0; padding: 0; box-sizing: border-box; }\n" +
                                "body { font-family: 'Microsoft YaHei', Arial, sans-serif; " + backgroundStyle
                                + " min-height: 100vh; padding: 20px; }\n" +
                                ".container { max-width: 1200px; margin: 0 auto; }\n" +
                                ".shop-header { text-align: center; margin-bottom: 30px; background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; border: 1px solid rgba(255,255,255,0.1); }\n"
                                +
                                ".shop-title { color: #ffffff; font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }\n"
                                +
                                ".shop-subtitle { color: #94a3b8; font-size: 1.2em; margin-bottom: 20px; }\n" +
                                ".user-points { display: flex; align-items: center; justify-content: center; gap: 10px; background: rgba(15, 23, 42, 0.8); border-radius: 15px; padding: 15px; border: 2px solid rgba(16, 185, 129, 0.3); }\n"
                                +
                                ".points-label { color: #e2e8f0; font-size: 1.1em; font-weight: 500; }\n" +
                                ".points-value { color: #10b981; font-size: 1.5em; font-weight: bold; }\n" +
                                ".refresh-btn { background: #10b981; border: none; border-radius: 50%; width: 35px; height: 35px; color: white; cursor: pointer; font-size: 16px; transition: all 0.3s ease; }\n"
                                +
                                ".refresh-btn:hover { background: #059669; transform: rotate(180deg); }\n" +
                                ".bind-section { margin-bottom: 30px; background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 15px; padding: 20px; border: 1px solid rgba(255,255,255,0.1); }\n"
                                +
                                ".bind-info { text-align: center; }\n" +
                                ".bind-info h3 { color: #ffffff; font-size: 1.5em; margin-bottom: 10px; }\n" +
                                ".bind-info p { color: #94a3b8; margin-bottom: 20px; }\n" +
                                ".bind-btn { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border: none; border-radius: 12px; padding: 15px 30px; color: white; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; }\n"
                                +
                                ".bind-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4); }\n"
                                +
                                ".bind-actions { margin-top: 15px; display: flex; gap: 10px; justify-content: center; }\n"
                                +
                                ".unbind-btn { background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease; }\n"
                                +
                                ".unbind-btn:hover { background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%); transform: translateY(-1px); }\n"
                                +
                                ".unbind-timer { color: #f59e0b; font-size: 0.9em; margin: 10px 0; }\n" +
                                ".bind-actions.disabled { opacity: 0.5; pointer-events: none; }\n" +
                                ".bind-success { background: rgba(16, 185, 129, 0.1); border: 2px solid rgba(16, 185, 129, 0.3); }\n"
                                +
                                ".bind-success h3 { color: #10b981; }\n" +
                                ".bind-success p { color: #10b981; }\n" +
                                ".bind-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 1000; display: flex; align-items: center; justify-content: center; }\n"
                                +
                                ".bind-modal-content { background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; max-width: 600px; width: 90%; border: 1px solid rgba(71, 85, 105, 0.3); }\n"
                                +
                                ".bind-modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }\n"
                                +
                                ".bind-modal-header h2 { color: #ffffff; font-size: 1.5em; margin: 0; }\n" +
                                ".bind-close-btn { background: rgba(239, 68, 68, 0.2); border: none; border-radius: 50%; width: 30px; height: 30px; color: #ef4444; cursor: pointer; font-size: 18px; transition: all 0.3s; }\n"
                                +
                                ".bind-close-btn:hover { background: rgba(239, 68, 68, 0.3); transform: scale(1.1); }\n"
                                +
                                ".bind-step { margin-bottom: 25px; }\n" +
                                ".bind-step h3 { color: #ffffff; font-size: 1.2em; margin-bottom: 10px; }\n" +
                                ".bind-step p { color: #94a3b8; margin-bottom: 10px; }\n" +
                                ".bind-code-container, .command-container { display: flex; gap: 10px; align-items: center; margin-bottom: 10px; }\n"
                                +
                                ".bind-code-input { flex: 1; padding: 12px; background: rgba(15, 23, 42, 0.8); border: 2px solid rgba(71, 85, 105, 0.5); border-radius: 8px; color: #ffffff; font-size: 18px; font-weight: bold; text-align: center; letter-spacing: 2px; }\n"
                                +
                                ".command-container code { flex: 1; background: rgba(15, 23, 42, 0.8); border: 2px solid rgba(71, 85, 105, 0.5); border-radius: 8px; padding: 12px; color: #10b981; font-family: monospace; font-size: 16px; }\n"
                                +
                                ".copy-btn { background: #10b981; border: none; border-radius: 8px; padding: 10px 15px; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s; white-space: nowrap; }\n"
                                +
                                ".copy-btn:hover { background: #059669; transform: translateY(-2px); }\n" +
                                ".bind-code-note { color: #f59e0b; font-size: 0.9em; font-weight: 500; }\n" +
                                ".bind-status-check { text-align: center; }\n" +
                                ".bind-progress { width: 100%; height: 6px; background: rgba(71, 85, 105, 0.3); border-radius: 3px; margin-top: 10px; overflow: hidden; }\n"
                                +
                                ".bind-progress-bar { height: 100%; background: linear-gradient(90deg, #3b82f6, #10b981); border-radius: 3px; width: 0%; transition: width 0.3s ease; animation: pulse 2s infinite; }\n"
                                +
                                "@keyframes pulse { 0%, 100% { opacity: 1; } 50% { opacity: 0.7; } }\n" +
                                "@keyframes pointsUpdate { 0% { transform: scale(1); color: #10b981; } 50% { transform: scale(1.1); color: #fbbf24; } 100% { transform: scale(1); color: #10b981; } }\n"
                                +
                                generateShopLayoutCSS() +
                                ".shop-item { background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 15px; padding: 20px; border: 1px solid rgba(255,255,255,0.1); "
                                + animationStyle
                                + " position: relative; overflow: hidden; display: flex; flex-direction: column; }\n"
                                +
                                ".shop-item:hover { "
                                + (enableAnimations
                                                ? "transform: translateY(-5px); box-shadow: 0 10px 30px rgba(0,0,0,0.3);"
                                                : "")
                                + " border-color: rgba(59, 130, 246, 0.5); }\n"
                                +
                                ".shop-item.disabled { opacity: 0.6; cursor: not-allowed; }\n" +
                                ".shop-item.disabled:hover { transform: none; box-shadow: none; }\n" +
                                ".item-header { display: flex; align-items: center; gap: 15px; margin-bottom: 15px; }\n"
                                +
                                ".item-icon { font-size: 3em; line-height: 1; }\n" +
                                ".item-title { flex: 1; }\n" +
                                ".item-name { color: #ffffff; font-size: 1.3em; font-weight: bold; margin-bottom: 5px; }\n"
                                +
                                ".item-cost { color: #10b981; font-size: 1.2em; font-weight: bold; }\n" +
                                ".item-description { color: #94a3b8; font-size: 1em; line-height: 1.5; margin-bottom: 15px; }\n"
                                +
                                ".item-meta { margin-bottom: 15px; font-size: 0.9em; }\n" +
                                ".meta-row { display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px; }\n"
                                +
                                ".meta-row:last-child { margin-bottom: 0; }\n" +
                                ".reset-row { min-height: 28px; justify-content: center; }\n" +
                                ".item-stock { color: #64748b; }\n" +
                                ".item-limit { color: #64748b; }\n" +
                                ".reset-timer { color: #f59e0b; font-weight: 500; text-align: center; padding: 4px 8px; background: rgba(245, 158, 11, 0.1); border-radius: 6px; }\n"
                                +
                                ".reset-placeholder { height: 28px; }\n"
                                +
                                ".purchase-btn { width: 100%; padding: 12px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border: none; border-radius: 10px; color: white; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; margin-top: auto; }\n"
                                +
                                ".purchase-btn:hover:not(:disabled) { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4); }\n"
                                +
                                ".purchase-btn:disabled { background: linear-gradient(135deg, #64748b 0%, #475569 100%); cursor: not-allowed; transform: none; box-shadow: none; }\n"
                                +
                                ".purchase-btn.bind-required { background: linear-gradient(135deg, #10b981 0%, #059669 100%); }\n"
                                +
                                ".purchase-btn.bind-required:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4); }\n"
                                +
                                ".back-to-lottery { text-align: center; }\n" +
                                ".back-btn { background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); border: none; border-radius: 12px; padding: 15px 30px; color: white; font-size: 16px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; }\n"
                                +
                                ".back-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(139, 92, 246, 0.4); }\n"
                                +
                                ".purchase-modal { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); z-index: 1000; display: flex; align-items: center; justify-content: center; }\n"
                                +
                                ".purchase-modal-content { background: rgba(30, 41, 59, 0.95); backdrop-filter: blur(10px); border-radius: 20px; padding: 30px; max-width: 500px; width: 90%; border: 1px solid rgba(71, 85, 105, 0.3); }\n"
                                +
                                ".purchase-modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }\n"
                                +
                                ".purchase-modal-header h2 { color: #ffffff; font-size: 1.5em; margin: 0; }\n" +
                                ".purchase-close-btn { background: rgba(239, 68, 68, 0.2); border: none; border-radius: 50%; width: 30px; height: 30px; color: #ef4444; cursor: pointer; font-size: 18px; transition: all 0.3s; }\n"
                                +
                                ".purchase-close-btn:hover { background: rgba(239, 68, 68, 0.3); transform: scale(1.1); }\n"
                                +
                                ".item-preview { display: flex; gap: 20px; align-items: center; margin-bottom: 20px; }\n"
                                +
                                ".item-preview .item-icon { font-size: 4em; }\n" +
                                ".item-preview .item-info { flex: 1; }\n" +
                                ".item-preview .item-name { color: #ffffff; font-size: 1.4em; font-weight: bold; margin-bottom: 8px; }\n"
                                +
                                ".item-preview .item-desc { color: #94a3b8; margin-bottom: 8px; }\n" +
                                ".item-preview .item-cost { color: #10b981; font-size: 1.2em; font-weight: bold; }\n" +
                                ".purchase-modal-footer { display: flex; gap: 15px; justify-content: flex-end; }\n" +
                                ".cancel-btn { background: rgba(71, 85, 105, 0.5); border: none; border-radius: 8px; padding: 10px 20px; color: #e2e8f0; cursor: pointer; transition: all 0.3s; }\n"
                                +
                                ".cancel-btn:hover { background: rgba(71, 85, 105, 0.7); }\n" +
                                ".confirm-btn { background: linear-gradient(135deg, #10b981 0%, #059669 100%); border: none; border-radius: 8px; padding: 10px 20px; color: white; font-weight: bold; cursor: pointer; transition: all 0.3s; }\n"
                                +
                                ".confirm-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4); }\n"
                                +
                                ".loading { text-align: center; padding: 40px; color: #94a3b8; font-size: 1.2em; }\n" +
                                ".error-message { text-align: center; padding: 40px; color: #ef4444; font-size: 1.2em; background: rgba(239, 68, 68, 0.1); border-radius: 15px; border: 1px solid rgba(239, 68, 68, 0.3); }\n"
                                +
                                ".success-message { position: fixed; top: 20px; right: 20px; background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 15px 20px; border-radius: 10px; box-shadow: 0 8px 20px rgba(16, 185, 129, 0.4); z-index: 2000; animation: slideIn 0.3s ease; }\n"
                                +
                                "@keyframes slideIn { from { transform: translateX(100%); opacity: 0; } to { transform: translateX(0); opacity: 1; } }\n"
                                +
                                ".error-toast { position: fixed; top: 20px; right: 20px; background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%); color: white; padding: 15px 20px; border-radius: 10px; box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4); z-index: 2000; animation: slideIn 0.3s ease; }\n"
                                +

                                // 数量选择器样式
                                ".quantity-selector { margin: 20px 0; padding: 20px; background: rgba(15, 23, 42, 0.5); border-radius: 12px; border: 1px solid rgba(255,255,255,0.1); }\n"
                                +
                                ".quantity-selector label { display: block; margin-bottom: 10px; color: #e2e8f0; font-weight: 500; }\n"
                                +
                                ".quantity-controls { display: flex; align-items: center; gap: 10px; margin-bottom: 15px; }\n"
                                +
                                ".quantity-btn { width: 40px; height: 40px; border: none; border-radius: 8px; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; font-size: 18px; font-weight: bold; cursor: pointer; transition: all 0.3s ease; }\n"
                                +
                                ".quantity-btn:hover { background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%); transform: scale(1.05); }\n"
                                +
                                ".quantity-btn:active { transform: scale(0.95); }\n" +
                                "#purchaseQuantity { width: 80px; height: 40px; text-align: center; border: 2px solid rgba(255,255,255,0.2); border-radius: 8px; background: rgba(30, 41, 59, 0.8); color: white; font-size: 16px; font-weight: bold; }\n"
                                +
                                "#purchaseQuantity:focus { outline: none; border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }\n"
                                +
                                ".quantity-presets { display: flex; gap: 8px; }\n" +
                                ".preset-btn { padding: 8px 12px; border: none; border-radius: 6px; background: rgba(59, 130, 246, 0.2); color: #3b82f6; font-weight: 500; cursor: pointer; transition: all 0.3s ease; }\n"
                                +
                                ".preset-btn:hover { background: rgba(59, 130, 246, 0.3); transform: translateY(-1px); }\n"
                                +
                                ".preset-btn:active { transform: translateY(0); }\n" +
                                ".total-cost { margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%); border-radius: 8px; border: 1px solid rgba(16, 185, 129, 0.3); }\n"
                                +
                                ".total-text { margin: 0; color: #10b981; font-size: 18px; font-weight: bold; text-align: center; }\n"
                                +

                                // 分页控件样式
                                ".pagination-container { margin: 30px 0; text-align: center; }\n" +
                                ".pagination { display: flex; justify-content: center; align-items: center; gap: 10px; margin-bottom: 15px; }\n"
                                +
                                ".page-btn { background: rgba(30, 41, 59, 0.95); border: 2px solid rgba(59, 130, 246, 0.3); border-radius: 8px; padding: 10px 15px; color: #3b82f6; font-size: 16px; cursor: pointer; transition: all 0.3s ease; min-width: 45px; }\n"
                                +
                                ".page-btn:hover:not(:disabled) { background: rgba(59, 130, 246, 0.1); border-color: #3b82f6; transform: translateY(-2px); }\n"
                                +
                                ".page-btn:disabled { opacity: 0.5; cursor: not-allowed; transform: none; }\n" +
                                ".page-numbers { display: flex; gap: 5px; }\n" +
                                ".page-number { background: rgba(30, 41, 59, 0.95); border: 2px solid rgba(71, 85, 105, 0.3); border-radius: 8px; padding: 10px 15px; color: #e2e8f0; font-size: 16px; cursor: pointer; transition: all 0.3s ease; min-width: 45px; text-align: center; }\n"
                                +
                                ".page-number:hover { background: rgba(59, 130, 246, 0.1); border-color: #3b82f6; color: #3b82f6; transform: translateY(-2px); }\n"
                                +
                                ".page-number.active { background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); border-color: #3b82f6; color: white; font-weight: bold; }\n"
                                +
                                ".page-number.active:hover { transform: translateY(-2px); box-shadow: 0 8px 20px rgba(59, 130, 246, 0.4); }\n"
                                +
                                ".pagination-info { display: flex; justify-content: center; gap: 20px; color: #94a3b8; font-size: 14px; }\n"
                                +
                                ".items-info { color: #64748b; }\n" +
                                generateMobileResponsiveCSS();
        }

        /**
         * 生成积分商店JavaScript代码
         */
        private String generatePointsShopJavaScript() {
                StringBuilder js = new StringBuilder();

                js.append("        // 全局变量\n");
                js.append("        let currentUsername = '';\n");
                js.append("        let userPoints = 0;\n");
                js.append("        let shopItems = [];\n");
                js.append("        let selectedItem = null;\n");
                js.append("        let playerPurchaseInfo = {};\n");
                js.append("        let bindCheckInterval = null;\n");
                js.append("        let progressInterval = null;\n");
                js.append("        let currentBindCode = '';\n");
                js.append("        let sessionId = '';\n");
                js.append("        \n");
                js.append("        // 分页相关变量\n");
                js.append("        let currentPage = 1;\n");
                js.append("        let totalPages = 1;\n");
                js.append("        let itemsPerPage = " + plugin.getConfig().getInt("website.shop-items-per-page", 12)
                                + ";\n");
                js.append("        let filteredItems = [];\n");
                js.append("\n");
                js.append("        // 页面加载完成后初始化\n");
                js.append("        document.addEventListener('DOMContentLoaded', function() {\n");
                js.append("            sessionId = generateSessionId();\n");
                js.append("            checkBindStatus();\n");
                js.append("            loadShopItems();\n");
                js.append("            loadPlayerPurchaseInfo();\n");
                js.append("            startAutoUpdate(); // 启动自动检测更新\n");
                js.append("        });\n");
                js.append("\n");
                js.append("        // 生成会话ID\n");
                js.append("        function generateSessionId() {\n");
                js.append("            return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 检查绑定状态\n");
                js.append("        function checkBindStatus() {\n");
                js.append("            // 这里可以检查本地存储或cookie中是否有绑定信息\n");
                js.append("            const savedBinding = localStorage.getItem('player_binding');\n");
                js.append("            if (savedBinding) {\n");
                js.append("                try {\n");
                js.append("                    const binding = JSON.parse(savedBinding);\n");
                js.append("                    if (binding.username && binding.bind_time) {\n");
                js.append("                        // 检查绑定是否仍然有效（24小时内）\n");
                js.append("                        const bindTime = new Date(binding.bind_time);\n");
                js.append("                        const now = new Date();\n");
                js.append("                        const hoursDiff = (now - bindTime) / (1000 * 60 * 60);\n");
                js.append("                        \n");
                js.append("                        if (hoursDiff < 24) {\n");
                js.append("                            // 验证服务器端绑定状态\n");
                js.append("                            verifyServerBinding(binding.username);\n");
                js.append("                            return;\n");
                js.append("                        }\n");
                js.append("                    }\n");
                js.append("                } catch (e) {\n");
                js.append("                    // 解析失败，清除本地存储\n");
                js.append("                    localStorage.removeItem('player_binding');\n");
                js.append("                }\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 显示绑定界面\n");
                js.append("            showBindInterface();\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 验证服务器端绑定状态\n");
                js.append("        function verifyServerBinding(username) {\n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'verify_binding',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    username: username\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success && data.is_bound) {\n");
                js.append("                    // 服务器确认绑定有效，更新本地存储\n");
                js.append("                    currentUsername = username;\n");
                js.append("                    \n");
                js.append("                    // 更新本地存储的绑定信息\n");
                js.append("                    if (data.bind_time) {\n");
                js.append("                        const bindingInfo = {\n");
                js.append("                            username: username,\n");
                js.append("                            bind_time: new Date(data.bind_time).toISOString()\n");
                js.append("                        };\n");
                js.append("                        localStorage.setItem('player_binding', JSON.stringify(bindingInfo));\n");
                js.append("                    }\n");
                js.append("                    \n");
                js.append("                    showBindSuccess();\n");
                js.append("                    loadUserPoints();\n");
                js.append("                    loadPlayerPurchaseInfo();\n");
                js.append("                    startAutoUpdate(); // 启动自动检测\n");
                js.append("                    \n");
                js.append("                    // 启动解绑倒计时\n");
                js.append("                    if (data.bind_time) {\n");
                js.append("                        checkUnbindTimeLimit(data.bind_time);\n");
                js.append("                    }\n");
                js.append("                } else {\n");
                js.append("                    // 服务器端绑定无效，清除本地存储\n");
                js.append("                    localStorage.removeItem('player_binding');\n");
                js.append("                    showBindInterface();\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                // 网络错误，清除本地存储\n");
                js.append("                localStorage.removeItem('player_binding');\n");
                js.append("                showBindInterface();\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 刷新积分\n");
                js.append("        function refreshPoints() {\n");
                js.append("            if (currentUsername) {\n");
                js.append("                loadUserPoints();\n");
                js.append("                loadPlayerPurchaseInfo();\n");
                js.append("            } else {\n");
                js.append("                showErrorToast('请先输入用户名');\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 加载用户积分\n");
                js.append("        function loadUserPoints() {\n");
                js.append("            if (!currentUsername) return;\n");
                js.append("\n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_user_points',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    username: currentUsername\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    userPoints = data.points;\n");
                js.append("                    document.getElementById('userPoints').textContent = userPoints + ' 积分';\n");
                js.append("                } else {\n");
                js.append("                    document.getElementById('userPoints').textContent = '获取失败';\n");
                js.append("                    showErrorToast('获取积分失败: ' + data.message);\n");
                js.append("                }\n");
                js.append("                updateShopItemsDisplay();\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                document.getElementById('userPoints').textContent = '获取失败';\n");
                js.append("                showErrorToast('网络错误: ' + error.message);\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 加载玩家购买信息\n");
                js.append("        function loadPlayerPurchaseInfo() {\n");
                js.append("            if (!currentUsername) {\n");
                js.append("                playerPurchaseInfo = {};\n");
                js.append("                return;\n");
                js.append("            }\n");
                js.append("\n");
                js.append("            \n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_player_purchase_info',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    username: currentUsername\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    playerPurchaseInfo = data.purchase_info || {};\n");
                js.append("                    updateShopItemsDisplay();\n");
                js.append("                } else {\n");
                js.append("                    playerPurchaseInfo = {};\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                playerPurchaseInfo = {};\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 加载商店物品\n");
                js.append("        function loadShopItems() {\n");
                js.append("            const shopItemsContainer = document.getElementById('shopItems');\n");
                js.append("            shopItemsContainer.innerHTML = '<div class=\"loading\">正在加载商店物品...</div>';\n");
                js.append("            \n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_shop_items',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "'\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    shopItems = data.items;\n");
                js.append("                    // 设置初始哈希值用于自动检测\n");
                js.append("                    lastShopItemsHash = JSON.stringify(data.items);\n");
                js.append("                    displayShopItems();\n");
                js.append("                } else {\n");
                js.append(
                                "                    shopItemsContainer.innerHTML = '<div class=\"error-message\">加载商店物品失败: ' + data.message + '</div>';\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append(
                                "                shopItemsContainer.innerHTML = '<div class=\"error-message\">网络错误: ' + error.message + '</div>';\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 显示商店物品\n");
                js.append("        function displayShopItems() {\n");
                js.append("            filteredItems = shopItems.slice(); // 复制所有商品\n");
                js.append("            currentPage = 1; // 重置到第一页\n");
                js.append("            updatePagination();\n");
                js.append("            updateShopItemsDisplay();\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 更新商店物品显示\n");
                js.append("        function updateShopItemsDisplay() {\n");
                js.append("            const container = document.getElementById('shopItems');\n");
                js.append("            \n");
                js.append("            if (filteredItems.length === 0) {\n");
                js.append("                container.innerHTML = '<div class=\"loading\">暂无商店物品</div>';\n");
                js.append("                document.getElementById('paginationContainer').style.display = 'none';\n");
                js.append("                return;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 计算分页\n");
                js.append("            const startIndex = (currentPage - 1) * itemsPerPage;\n");
                js.append("            const endIndex = startIndex + itemsPerPage;\n");
                js.append("            const pageItems = filteredItems.slice(startIndex, endIndex);\n");
                js.append("            \n");
                js.append("            let html = '';\n");
                js.append("            pageItems.forEach(item => {\n");
                js.append("                const canAfford = userPoints >= item.cost;\n");
                js.append("                const hasStock = item.stock === -1 || item.stock > 0;\n");
                js.append("                \n");
                js.append("                // 获取购买信息\n");
                js.append("                const purchaseInfo = playerPurchaseInfo[item.id] || {};\n");
                js.append("                const remainingPurchases = purchaseInfo.hasOwnProperty('remaining_purchases') ? purchaseInfo.remaining_purchases : -1;\n");
                js.append("                const resetCountdown = purchaseInfo.reset_countdown_seconds || -1;\n");
                js.append("                \n");
                js.append("                // 检查是否可以购买\n");
                js.append(
                                "                const hasRemainingPurchases = remainingPurchases === -1 || remainingPurchases > 0;\n");
                js.append(
                                "                const canPurchase = canAfford && hasStock && hasRemainingPurchases && currentUsername;\n");
                js.append("                \n");
                js.append("                let stockText = item.stock === -1 ? '无限' : item.stock;\n");
                js.append("                \n");
                js.append("                // 构建限购文本\n");
                js.append("                let limitText = '';\n");
                js.append("                if (item.maxPurchasePerPlayer === -1) {\n");
                js.append("                    limitText = '无限制';\n");
                js.append("                } else {\n");
                js.append("                    // 计算已购买次数\n");
                js.append(
                                "                    const purchasedCount = item.maxPurchasePerPlayer - (remainingPurchases === -1 ? item.maxPurchasePerPlayer : remainingPurchases);\n");
                js.append("                    \n");
                js.append("                    if (remainingPurchases === -1) {\n");
                js.append("                        // 还没有购买记录，显示总限购次数\n");
                js.append("                        limitText = '限购 ' + item.maxPurchasePerPlayer + ' 次';\n");
                js.append("                    } else if (remainingPurchases > 0) {\n");
                js.append("                        // 还有剩余购买次数\n");
                js.append(
                                "                        limitText = '已购 ' + purchasedCount + ' 次，剩余 ' + remainingPurchases + ' 次';\n");
                js.append("                    } else {\n");
                js.append("                        // 已达到限购次数，只显示已达限购\n");
                js.append("                        limitText = '已达到限购次数';\n");
                js.append("                    }\n");
                js.append("                    \n");
                js.append("                }\n");
                js.append("                \n");
                js.append("                // 构建重置时间文本\n");
                js.append("                let resetText = '';\n");
                js.append("                if (item.resetIntervalHours > 0) {\n");
                js.append("                    if (resetCountdown > 0) {\n");
                js.append("                        const hours = Math.floor(resetCountdown / 3600);\n");
                js.append("                        const minutes = Math.floor((resetCountdown % 3600) / 60);\n");
                js.append("                        const seconds = resetCountdown % 60;\n");
                js.append("                        \n");
                js.append("                        if (hours > 0) {\n");
                js.append("                            resetText = hours + '时' + minutes + '分后重置';\n");
                js.append("                        } else if (minutes > 0) {\n");
                js.append("                            resetText = minutes + '分' + seconds + '秒后重置';\n");
                js.append("                        } else {\n");
                js.append("                            resetText = seconds + '秒后重置';\n");
                js.append("                        }\n");
                js.append("                    } else if (remainingPurchases !== -1) {\n");
                js.append("                        resetText = '每' + item.resetIntervalHours + '小时重置';\n");
                js.append("                    }\n");
                js.append("                }\n");
                js.append("                \n");
                js.append("                // 构建购买按钮文本\n");
                js.append("                let buttonText = '购买';\n");
                js.append("                let buttonClass = 'purchase-btn';\n");
                js.append("                let buttonDisabled = '';\n");
                js.append("                \n");
                js.append("                if (!currentUsername) {\n");
                js.append("                    buttonText = '🎮 绑定账户购买';\n");
                js.append("                    buttonClass = 'purchase-btn bind-required';\n");
                js.append("                    buttonDisabled = '';\n");
                js.append("                } else if (!canAfford) {\n");
                js.append("                    buttonText = '积分不足';\n");
                js.append("                    buttonDisabled = 'disabled';\n");
                js.append("                } else if (!hasStock) {\n");
                js.append("                    buttonText = '库存不足';\n");
                js.append("                    buttonDisabled = 'disabled';\n");
                js.append("                } else if (!hasRemainingPurchases) {\n");
                js.append("                    buttonText = '已达限购';\n");
                js.append("                    buttonDisabled = 'disabled';\n");
                js.append("                }\n");
                js.append("                \n");
                js.append("                const itemClass = canPurchase ? '' : 'disabled';\n");
                js.append("                \n");
                js.append("                html += `\n");
                js.append("                    <div class=\"shop-item ${itemClass}\">\n");
                js.append("                        <div class=\"item-header\">\n");
                js.append("                            <div class=\"item-icon\">${item.icon}</div>\n");
                js.append("                            <div class=\"item-title\">\n");
                js.append("                                <div class=\"item-name\">${item.name}</div>\n");
                js.append("                                <div class=\"item-cost\">${item.cost} 积分</div>\n");
                js.append("                            </div>\n");
                js.append("                        </div>\n");
                js.append("                        <div class=\"item-description\">${item.description}</div>\n");
                js.append("                        <div class=\"item-meta\">\n");
                js.append("                            <div class=\"meta-row\">\n");
                js.append("                                <span class=\"item-stock\">库存: ${stockText}</span>\n");
                js.append("                                <span class=\"item-limit\">${limitText}</span>\n");
                js.append("                            </div>\n");
                js.append("                            <div class=\"meta-row reset-row\">\n");
                js.append(
                                "                                ${resetCountdown > 0 ? `<div class=\"reset-timer\" data-countdown=\"${resetCountdown}\" data-item-id=\"${item.id}\">⏰ ${resetText}</div>` : `<div class=\"reset-placeholder\">&nbsp;</div>`}\n");
                js.append("                            </div>\n");
                js.append("                        </div>\n");
                js.append(
                                "                        <button class=\"${buttonClass}\" ${buttonDisabled} onclick=\"openPurchaseModal('${item.id}')\">\n");
                js.append("                            ${buttonText}\n");
                js.append("                        </button>\n");
                js.append("                    </div>\n");
                js.append("                `;\n");
                js.append("            });\n");
                js.append("            \n");
                js.append("            container.innerHTML = html;\n");
                js.append("            \n");
                js.append("            // 显示分页控件\n");
                js.append("            if (filteredItems.length > itemsPerPage) {\n");
                js.append("                document.getElementById('paginationContainer').style.display = 'block';\n");
                js.append("                updatePaginationInfo();\n");
                js.append("            } else {\n");
                js.append("                document.getElementById('paginationContainer').style.display = 'none';\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 打开购买确认弹窗\n");
                js.append("        function openPurchaseModal(itemId) {\n");
                js.append("            // 检查是否已绑定账户\n");
                js.append("            if (!currentUsername) {\n");
                js.append("                showErrorToast('请先绑定您的游戏账户才能购买物品！');\n");
                js.append("                // 滚动到绑定区域\n");
                js.append("                document.getElementById('bindStatus').scrollIntoView({ behavior: 'smooth' });\n");
                js.append("                return;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            const item = shopItems.find(i => i.id === itemId);\n");
                js.append("            if (!item) return;\n");
                js.append("            \n");
                js.append("            selectedItem = item;\n");
                js.append("            \n");
                js.append("            // 更新弹窗内容\n");
                js.append("            document.getElementById('previewIcon').textContent = item.icon;\n");
                js.append("            document.getElementById('previewName').textContent = item.name;\n");
                js.append("            document.getElementById('previewDesc').textContent = item.description;\n");
                js.append("            document.getElementById('previewCost').textContent = item.cost;\n");
                js.append("            \n");
                js.append("            // 重置数量为1并更新总价\n");
                js.append("            document.getElementById('purchaseQuantity').value = 1;\n");
                js.append("            updateTotalCost();\n");
                js.append("            \n");
                js.append("            document.getElementById('purchaseModal').style.display = 'flex';\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 关闭购买确认弹窗\n");
                js.append("        function closePurchaseModal() {\n");
                js.append("            document.getElementById('purchaseModal').style.display = 'none';\n");
                js.append("            selectedItem = null;\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 确认购买\n");
                js.append("        function confirmPurchase() {\n");
                js.append("            if (!selectedItem || !currentUsername) return;\n");
                js.append("            \n");
                js.append("            const quantity = parseInt(document.getElementById('purchaseQuantity').value) || 1;\n");
                js.append("            \n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'purchase_item',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    username: currentUsername,\n");
                js.append("                    item_id: selectedItem.id,\n");
                js.append("                    quantity: quantity\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    showSuccessToast('购买成功！');\n");
                js.append("                    closePurchaseModal();\n");
                js.append("                    loadUserPoints();\n");
                js.append("                    loadShopItems();\n");
                js.append("                    loadPlayerPurchaseInfo();\n");
                js.append("                } else {\n");
                js.append("                    showErrorToast('购买失败: ' + data.message);\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                showErrorToast('网络错误: ' + error.message);\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 显示成功提示\n");
                js.append("        function showSuccessToast(message) {\n");
                js.append("            const toast = document.createElement('div');\n");
                js.append("            toast.className = 'success-message';\n");
                js.append("            toast.textContent = message;\n");
                js.append("            document.body.appendChild(toast);\n");
                js.append("            \n");
                js.append("            setTimeout(() => {\n");
                js.append("                if (toast.parentNode) {\n");
                js.append("                    toast.parentNode.removeChild(toast);\n");
                js.append("                }\n");
                js.append("            }, 3000);\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 显示错误提示\n");
                js.append("        function showErrorToast(message) {\n");
                js.append("            const toast = document.createElement('div');\n");
                js.append("            toast.className = 'error-message';\n");
                js.append("            toast.style.position = 'fixed';\n");
                js.append("            toast.style.top = '20px';\n");
                js.append("            toast.style.right = '20px';\n");
                js.append("            toast.style.zIndex = '1001';\n");
                js.append("            toast.textContent = message;\n");
                js.append("            document.body.appendChild(toast);\n");
                js.append("            \n");
                js.append("            setTimeout(() => {\n");
                js.append("                if (toast.parentNode) {\n");
                js.append("                    toast.parentNode.removeChild(toast);\n");
                js.append("                }\n");
                js.append("            }, 5000);\n");
                js.append("        }\n");
                js.append("\n");
                js.append("        // 点击弹窗外部关闭弹窗\n");
                js.append("        window.onclick = function(event) {\n");
                js.append("            const modal = document.getElementById('purchaseModal');\n");
                js.append("            if (event.target === modal) {\n");
                js.append("                closePurchaseModal();\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 返回抽奖页面\n");
                js.append("        function goBackToLottery() {\n");
                js.append("            window.location.href = '/user';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示错误提示\n");
                js.append("        function showErrorToast(message) {\n");
                js.append("            const toast = document.createElement('div');\n");
                js.append("            toast.className = 'error-toast';\n");
                js.append("            toast.textContent = message;\n");
                js.append("            document.body.appendChild(toast);\n");
                js.append("            \n");
                js.append("            setTimeout(() => {\n");
                js.append("                if (toast.parentNode) {\n");
                js.append("                    toast.parentNode.removeChild(toast);\n");
                js.append("                }\n");
                js.append("            }, 5000);\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示绑定界面\n");
                js.append("        function showBindInterface() {\n");
                js.append("            const bindStatus = document.getElementById('bindStatus');\n");
                js.append("            bindStatus.innerHTML = `\n");
                js.append("                <div class=\"bind-info\">\n");
                js.append("                    <h3>🔗 账户绑定</h3>\n");
                js.append("                    <p>您可以先浏览下方的商品，绑定账户后即可购买</p>\n");
                js.append("                    <button class=\"bind-btn\" onclick=\"generateBindCode()\">🎯 生成绑定码</button>\n");
                js.append("                </div>\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 显示商店物品（未绑定状态）\n");
                js.append("            updateShopItemsDisplay();\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示绑定成功\n");
                js.append("        function showBindSuccess() {\n");
                js.append("            const bindStatus = document.getElementById('bindStatus');\n");
                js.append("            bindStatus.innerHTML = `\n");
                js.append("                <div class=\"bind-info bind-success\">\n");
                js.append("                    <h3>✅ 已绑定账户</h3>\n");
                js.append("                    <p>🎮 玩家: <strong>` + currentUsername + `</strong></p>\n");
                js.append("                    <p id=\"unbindTimer\" class=\"unbind-timer\">⏰ 检查解绑时间限制...</p>\n");
                js.append("                    <div class=\"bind-actions\" id=\"bindActions\">\n");
                js.append("                        <button class=\"unbind-btn\" onclick=\"unbindAccount()\" title=\"解除当前账户绑定\">🔓 解绑</button>\n");
                js.append("                    </div>\n");
                js.append("                </div>\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 检查解绑时间限制\n");
                js.append("            checkUnbindTimeLimit();\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 生成绑定码\n");
                js.append("        function generateBindCode() {\n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'generate_bind_code',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    session_id: sessionId\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    currentBindCode = data.bind_code;\n");
                js.append("                    sessionId = data.session_id; // 更新会话ID\n");
                js.append("                    showBindModal(data.bind_code);\n");
                js.append("                    startBindStatusCheck();\n");
                js.append("                } else {\n");
                js.append("                    showErrorToast('生成绑定码失败: ' + data.message);\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                showErrorToast('生成绑定码失败，请重试');\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示绑定弹窗\n");
                js.append("        function showBindModal(bindCode) {\n");
                js.append("            // 先清理之前的定时器\n");
                js.append("            if (bindCheckInterval) {\n");
                js.append("                clearInterval(bindCheckInterval);\n");
                js.append("                bindCheckInterval = null;\n");
                js.append("            }\n");
                js.append("            if (progressInterval) {\n");
                js.append("                clearInterval(progressInterval);\n");
                js.append("                progressInterval = null;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 更新绑定码显示\n");
                js.append("            document.getElementById('bindCodeInput').value = bindCode;\n");
                js.append("            document.getElementById('bindCodeDisplay').textContent = bindCode;\n");
                js.append("            document.getElementById('bindStatusText').textContent = '等待绑定...';\n");
                js.append("            document.getElementById('bindProgressBar').style.width = '0%';\n");
                js.append("            document.getElementById('bindModal').style.display = 'flex';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 关闭绑定弹窗\n");
                js.append("        function closeBindModal() {\n");
                js.append("            document.getElementById('bindModal').style.display = 'none';\n");
                js.append("            \n");
                js.append("            // 清理所有定时器\n");
                js.append("            if (bindCheckInterval) {\n");
                js.append("                clearInterval(bindCheckInterval);\n");
                js.append("                bindCheckInterval = null;\n");
                js.append("            }\n");
                js.append("            if (progressInterval) {\n");
                js.append("                clearInterval(progressInterval);\n");
                js.append("                progressInterval = null;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 重置进度条\n");
                js.append("            document.getElementById('bindProgressBar').style.width = '0%';\n");
                js.append("            document.getElementById('bindStatusText').textContent = '等待绑定...';\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 复制绑定码\n");
                js.append("        function copyBindCode(event) {\n");
                js.append("            const bindCodeInput = document.getElementById('bindCodeInput');\n");
                js.append("            const copyBtn = event.target;\n");
                js.append("            const originalText = copyBtn.textContent;\n");
                js.append("            \n");
                js.append("            try {\n");
                js.append("                // 尝试使用现代API\n");
                js.append("                navigator.clipboard.writeText(bindCodeInput.value).then(() => {\n");
                js.append("                    copyBtn.textContent = '✅ 已复制';\n");
                js.append("                    setTimeout(() => {\n");
                js.append("                        copyBtn.textContent = originalText;\n");
                js.append("                    }, 2000);\n");
                js.append("                }).catch(() => {\n");
                js.append("                    // 降级到旧方法\n");
                js.append("                    fallbackCopy();\n");
                js.append("                });\n");
                js.append("            } catch (err) {\n");
                js.append("                // 降级到旧方法\n");
                js.append("                fallbackCopy();\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            function fallbackCopy() {\n");
                js.append("                try {\n");
                js.append("                    bindCodeInput.select();\n");
                js.append("                    document.execCommand('copy');\n");
                js.append("                    copyBtn.textContent = '✅ 已复制';\n");
                js.append("                    setTimeout(() => {\n");
                js.append("                        copyBtn.textContent = originalText;\n");
                js.append("                    }, 2000);\n");
                js.append("                } catch (err) {\n");
                js.append("                    copyBtn.textContent = '❌ 复制失败';\n");
                js.append("                    setTimeout(() => {\n");
                js.append("                        copyBtn.textContent = originalText;\n");
                js.append("                    }, 2000);\n");
                js.append("                }\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 复制命令\n");
                js.append("        function copyCommand(event) {\n");
                js.append("            const command = '/acebind ' + currentBindCode;\n");
                js.append("            \n");
                js.append("            const copyBtn = event.target;\n");
                js.append("            const originalText = copyBtn.textContent;\n");
                js.append("            \n");
                js.append("            // 检查绑定码是否有效\n");
                js.append("            if (!currentBindCode || currentBindCode === '') {\n");
                js.append("                copyBtn.textContent = '❌ 无绑定码';\n");
                js.append("                setTimeout(() => {\n");
                js.append("                    copyBtn.textContent = originalText;\n");
                js.append("                }, 2000);\n");
                js.append("                return;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            navigator.clipboard.writeText(command).then(() => {\n");
                js.append("                copyBtn.textContent = '✅ 已复制';\n");
                js.append("                setTimeout(() => {\n");
                js.append("                    copyBtn.textContent = originalText;\n");
                js.append("                }, 2000);\n");
                js.append("            }).catch(() => {\n");
                js.append("                // 降级方案\n");
                js.append("                try {\n");
                js.append("                    const textArea = document.createElement('textarea');\n");
                js.append("                    textArea.value = command;\n");
                js.append("                    document.body.appendChild(textArea);\n");
                js.append("                    textArea.select();\n");
                js.append("                    document.execCommand('copy');\n");
                js.append("                    document.body.removeChild(textArea);\n");
                js.append("                    \n");
                js.append("                    copyBtn.textContent = '✅ 已复制';\n");
                js.append("                    setTimeout(() => {\n");
                js.append("                        copyBtn.textContent = originalText;\n");
                js.append("                    }, 2000);\n");
                js.append("                } catch (err) {\n");
                js.append("                    copyBtn.textContent = '❌ 复制失败';\n");
                js.append("                    setTimeout(() => {\n");
                js.append("                        copyBtn.textContent = originalText;\n");
                js.append("                    }, 2000);\n");
                js.append("                }\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 开始检查绑定状态\n");
                js.append("        function startBindStatusCheck() {\n");
                js.append("            // 先清理之前的定时器\n");
                js.append("            if (bindCheckInterval) {\n");
                js.append("                clearInterval(bindCheckInterval);\n");
                js.append("                bindCheckInterval = null;\n");
                js.append("            }\n");
                js.append("            if (progressInterval) {\n");
                js.append("                clearInterval(progressInterval);\n");
                js.append("                progressInterval = null;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            let progress = 0;\n");
                js.append("            const progressBar = document.getElementById('bindProgressBar');\n");
                js.append("            const statusText = document.getElementById('bindStatusText');\n");
                js.append("            \n");
                js.append("            // 进度条动画\n");
                js.append("            progressInterval = setInterval(() => {\n");
                js.append("                progress += 1;\n");
                js.append("                if (progress > 100) progress = 0;\n");
                js.append("                progressBar.style.width = progress + '%';\n");
                js.append("            }, 100);\n");
                js.append("            \n");
                js.append("            // 检查绑定状态\n");
                js.append("            bindCheckInterval = setInterval(() => {\n");
                js.append("                fetch('/api', {\n");
                js.append("                    method: 'POST',\n");
                js.append("                    headers: { 'Content-Type': 'application/json' },\n");
                js.append("                    body: JSON.stringify({\n");
                js.append("                        action: 'get_bind_status',\n");
                js.append("                        api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                        session_id: sessionId\n");
                js.append("                    })\n");
                js.append("                })\n");
                js.append("                .then(response => response.json())\n");
                js.append("                .then(data => {\n");
                js.append("                    if (data.success && data.is_bound) {\n");
                js.append("                        // 清理定时器\n");
                js.append("                        if (bindCheckInterval) {\n");
                js.append("                            clearInterval(bindCheckInterval);\n");
                js.append("                            bindCheckInterval = null;\n");
                js.append("                        }\n");
                js.append("                        if (progressInterval) {\n");
                js.append("                            clearInterval(progressInterval);\n");
                js.append("                            progressInterval = null;\n");
                js.append("                        }\n");
                js.append("                        \n");
                js.append("                        currentUsername = data.username;\n");
                js.append("                        \n");
                js.append("                        // 保存绑定信息到本地存储\n");
                js.append("                        const bindingInfo = {\n");
                js.append("                            username: data.username,\n");
                js.append("                            player_uuid: data.player_uuid,\n");
                js.append("                            bind_time: new Date().toISOString()\n");
                js.append("                        };\n");
                js.append("                        localStorage.setItem('player_binding', JSON.stringify(bindingInfo));\n");
                js.append("                        \n");
                js.append("                        // 显示成功状态\n");
                js.append("                        statusText.textContent = '✅ 绑定成功！';\n");
                js.append("                        progressBar.style.width = '100%';\n");
                js.append("                        \n");
                js.append("                        setTimeout(() => {\n");
                js.append("                            closeBindModal();\n");
                js.append("                            showBindSuccess();\n");
                js.append("                            loadUserPoints();\n");
                js.append("                            loadPlayerPurchaseInfo();\n");
                js.append("                            updateShopItemsDisplay();\n");
                js.append("                            startAutoUpdate(); // 启动自动检测\n");
                js.append("                        }, 2000);\n");
                js.append("                    }\n");
                js.append("                })\n");
                js.append("                .catch(error => {\n");
                js.append("                });\n");
                js.append("            }, 2000); // 每2秒检查一次\n");
                js.append("            \n");
                js.append("            // 5分钟后自动停止检查\n");
                js.append("            setTimeout(() => {\n");
                js.append("                if (bindCheckInterval) {\n");
                js.append("                    clearInterval(bindCheckInterval);\n");
                js.append("                    bindCheckInterval = null;\n");
                js.append("                }\n");
                js.append("                if (progressInterval) {\n");
                js.append("                    clearInterval(progressInterval);\n");
                js.append("                    progressInterval = null;\n");
                js.append("                }\n");
                js.append("                statusText.textContent = '⏰ 绑定码已过期，请重新生成';\n");
                js.append("                progressBar.style.width = '0%';\n");
                js.append("            }, 5 * 60 * 1000);\n");
                js.append("        }\n");
                js.append("        \n");

                // 数量控制函数
                js.append("        // 更新总价\n");
                js.append("        function updateTotalCost() {\n");
                js.append("            if (!selectedItem) return;\n");
                js.append("            \n");
                js.append("            const quantity = parseInt(document.getElementById('purchaseQuantity').value) || 1;\n");
                js.append("            const totalCost = selectedItem.cost * quantity;\n");
                js.append("            \n");
                js.append("            document.getElementById('totalCost').textContent = totalCost;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 改变数量\n");
                js.append("        function changeQuantity(delta) {\n");
                js.append("            const quantityInput = document.getElementById('purchaseQuantity');\n");
                js.append("            let currentQuantity = parseInt(quantityInput.value) || 1;\n");
                js.append("            let newQuantity = currentQuantity + delta;\n");
                js.append("            \n");
                js.append("            // 限制数量范围\n");
                js.append("            newQuantity = Math.max(1, Math.min(99, newQuantity));\n");
                js.append("            \n");
                js.append("            quantityInput.value = newQuantity;\n");
                js.append("            updateTotalCost();\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 设置数量\n");
                js.append("        function setQuantity(quantity) {\n");
                js.append("            const quantityInput = document.getElementById('purchaseQuantity');\n");
                js.append("            \n");
                js.append("            // 限制数量范围\n");
                js.append("            quantity = Math.max(1, Math.min(99, quantity));\n");
                js.append("            \n");
                js.append("            quantityInput.value = quantity;\n");
                js.append("            updateTotalCost();\n");
                js.append("        }\n");
                js.append("        \n");

                js.append("        // 动态更新倒计时\n");
                js.append("        function startCountdownTimer() {\n");
                js.append("            setInterval(() => {\n");
                js.append("                const timers = document.querySelectorAll('.reset-timer[data-countdown]');\n");
                js.append("                timers.forEach(timer => {\n");
                js.append("                    let countdown = parseInt(timer.getAttribute('data-countdown'));\n");
                js.append("                    if (countdown > 0) {\n");
                js.append("                        countdown--;\n");
                js.append("                        timer.setAttribute('data-countdown', countdown);\n");
                js.append("                        \n");
                js.append("                        const hours = Math.floor(countdown / 3600);\n");
                js.append("                        const minutes = Math.floor((countdown % 3600) / 60);\n");
                js.append("                        const seconds = countdown % 60;\n");
                js.append("                        \n");
                js.append("                        let timeText = '';\n");
                js.append("                        if (hours > 0) {\n");
                js.append("                            timeText = hours + '时' + minutes + '分后重置';\n");
                js.append("                        } else if (minutes > 0) {\n");
                js.append("                            timeText = minutes + '分' + seconds + '秒后重置';\n");
                js.append("                        } else {\n");
                js.append("                            timeText = seconds + '秒后重置';\n");
                js.append("                        }\n");
                js.append("                        \n");
                js.append("                        timer.textContent = '⏰ ' + timeText;\n");
                js.append("                    } else {\n");
                js.append("                        // 倒计时结束，重新加载购买信息\n");
                js.append("                        timer.remove();\n");
                js.append("                        loadPlayerPurchaseInfo();\n");
                js.append("                    }\n");
                js.append("                });\n");
                js.append("            }, 1000);\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 检查解绑时间限制\n");
                js.append("        function checkUnbindTimeLimit() {\n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_bind_status',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    session_id: sessionId\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success && data.is_bound && data.bind_time) {\n");
                js.append("                    const bindTime = new Date(data.bind_time).getTime();\n");
                js.append("                    const currentTime = new Date().getTime();\n");
                js.append("                    const timeDiff = currentTime - bindTime;\n");
                js.append("                    \n");
                js.append("                    // 解析冷却时间配置\n");
                js.append("                    const unbindCooldownConfig = data.unbind_cooldown_config || '24 小时';\n");
                js.append("                    let minBindDuration;\n");
                js.append("                    if (unbindCooldownConfig.includes('秒')) {\n");
                js.append("                        const seconds = parseInt(unbindCooldownConfig.replace('秒', ''));\n");
                js.append("                        minBindDuration = seconds * 1000;\n");
                js.append("                    } else {\n");
                js.append("                        const hours = parseInt(unbindCooldownConfig.replace('小时', ''));\n");
                js.append("                        minBindDuration = hours * 60 * 60 * 1000;\n");
                js.append("                    }\n");
                js.append("                    \n");
                js.append("                    const timerElement = document.getElementById('unbindTimer');\n");
                js.append("                    const actionsElement = document.getElementById('bindActions');\n");
                js.append("                    \n");
                js.append("                    if (timeDiff < minBindDuration) {\n");
                js.append("                        // 还不能解绑\n");
                js.append("                        const remainingTime = minBindDuration - timeDiff;\n");
                js.append("                        \n");
                js.append("                        // 根据配置显示时间\n");
                js.append("                        let timeText = '';\n");
                js.append("                        if (unbindCooldownConfig.includes('秒')) {\n");
                js.append("                            const remainingSeconds = Math.ceil(remainingTime / 1000);\n");
                js.append("                            timeText = remainingSeconds + '秒';\n");
                js.append("                            timerElement.textContent = '⏰ 绑定后' + unbindCooldownConfig + '内不能解绑，还需等待 ' + timeText;\n");
                js.append("                        } else {\n");
                js.append("                            const remainingHours = Math.ceil(remainingTime / (60 * 60 * 1000));\n");
                js.append("                            timeText = remainingHours + '小时';\n");
                js.append("                            timerElement.textContent = '⏰ 绑定后' + unbindCooldownConfig + '内不能解绑，还需等待 ' + timeText;\n");
                js.append("                        }\n");
                js.append("                        \n");
                js.append("                        timerElement.style.color = '#ef4444';\n");
                js.append("                        actionsElement.classList.add('disabled');\n");
                js.append("                        \n");
                js.append("                        // 启动倒计时\n");
                js.append("                        startUnbindCountdown(remainingTime, unbindCooldownConfig);\n");
                js.append("                    } else {\n");
                js.append("                        // 可以解绑\n");
                js.append("                        timerElement.textContent = '✅ 可以解绑账户';\n");
                js.append("                        timerElement.style.color = '#10b981';\n");
                js.append("                        actionsElement.classList.remove('disabled');\n");
                js.append("                    }\n");
                js.append("                } else {\n");
                js.append("                    // 未绑定或获取信息失败\n");
                js.append("                    const timerElement = document.getElementById('unbindTimer');\n");
                js.append("                    if (timerElement) {\n");
                js.append("                        timerElement.style.display = 'none';\n");
                js.append("                    }\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                console.error('检查解绑时间限制失败:', error);\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 启动解绑倒计时\n");
                js.append("        function startUnbindCountdown(remainingTime, unbindCooldownConfig) {\n");
                js.append("            const timerElement = document.getElementById('unbindTimer');\n");
                js.append("            const actionsElement = document.getElementById('bindActions');\n");
                js.append("            \n");
                js.append("            const countdownInterval = setInterval(() => {\n");
                js.append("                remainingTime -= 1000;\n");
                js.append("                \n");
                js.append("                if (remainingTime <= 0) {\n");
                js.append("                    // 倒计时结束\n");
                js.append("                    clearInterval(countdownInterval);\n");
                js.append("                    timerElement.textContent = '✅ 可以解绑账户';\n");
                js.append("                    timerElement.style.color = '#10b981';\n");
                js.append("                    actionsElement.classList.remove('disabled');\n");
                js.append("                } else {\n");
                js.append("                    // 更新倒计时显示\n");
                js.append("                    const totalSeconds = Math.ceil(remainingTime / 1000);\n");
                js.append("                    const hours = Math.floor(totalSeconds / 3600);\n");
                js.append("                    const minutes = Math.floor((totalSeconds % 3600) / 60);\n");
                js.append("                    const seconds = totalSeconds % 60;\n");
                js.append("                    \n");
                js.append("                    let timeText = '';\n");
                js.append("                    if (hours > 0) {\n");
                js.append("                        timeText = hours + '小时' + minutes + '分钟' + seconds + '秒';\n");
                js.append("                    } else if (minutes > 0) {\n");
                js.append("                        timeText = minutes + '分钟' + seconds + '秒';\n");
                js.append("                    } else {\n");
                js.append("                        timeText = seconds + '秒';\n");
                js.append("                    }\n");
                js.append("                    \n");
                js.append("                    timerElement.textContent = '⏰ 绑定后' + unbindCooldownConfig + '内不能解绑，还需等待 ' + timeText;\n");
                js.append("                }\n");
                js.append("            }, 1000); // 每秒更新一次\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 解绑账户\n");
                js.append("        function unbindAccount() {\n");
                js.append("            showUnbindConfirmModal();\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示解绑确认弹窗\n");
                js.append("        function showUnbindConfirmModal() {\n");
                js.append("            // 创建弹窗元素\n");
                js.append("            const modal = document.createElement('div');\n");
                js.append("            modal.style.cssText = `\n");
                js.append("                position: fixed;\n");
                js.append("                top: 0;\n");
                js.append("                left: 0;\n");
                js.append("                width: 100%;\n");
                js.append("                height: 100%;\n");
                js.append("                background: rgba(0, 0, 0, 0.6);\n");
                js.append("                display: flex;\n");
                js.append("                justify-content: center;\n");
                js.append("                align-items: center;\n");
                js.append("                z-index: 10000;\n");
                js.append("                backdrop-filter: blur(5px);\n");
                js.append("                animation: fadeIn 0.3s ease-out;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const modalContent = document.createElement('div');\n");
                js.append("            modalContent.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #ffffff 0%, #fef3c7 100%);\n");
                js.append("                padding: 40px 35px;\n");
                js.append("                border-radius: 20px;\n");
                js.append("                text-align: center;\n");
                js.append("                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                js.append("                max-width: 450px;\n");
                js.append("                width: 90%;\n");
                js.append("                border: 1px solid rgba(245, 158, 11, 0.2);\n");
                js.append("                position: relative;\n");
                js.append("                animation: slideIn 0.3s ease-out;\n");
                js.append("                transform-origin: center;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 添加装饰性图标\n");
                js.append("            const iconContainer = document.createElement('div');\n");
                js.append("            iconContainer.style.cssText = `\n");
                js.append("                width: 80px;\n");
                js.append("                height: 80px;\n");
                js.append("                background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);\n");
                js.append("                border-radius: 50%;\n");
                js.append("                display: flex;\n");
                js.append("                align-items: center;\n");
                js.append("                justify-content: center;\n");
                js.append("                margin: 0 auto 25px auto;\n");
                js.append("                box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3);\n");
                js.append("                animation: warningPulse 2s infinite;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const icon = document.createElement('div');\n");
                js.append("            icon.textContent = '⚠️';\n");
                js.append("            icon.style.cssText = 'font-size: 36px; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));';\n");
                js.append("            iconContainer.appendChild(icon);\n");
                js.append("            \n");
                js.append("            const title = document.createElement('h3');\n");
                js.append("            title.textContent = '确定要解绑当前账户吗？';\n");
                js.append("            title.style.cssText = `\n");
                js.append("                margin: 0 0 15px 0;\n");
                js.append("                color: #d97706;\n");
                js.append("                font-size: 22px;\n");
                js.append("                font-weight: 700;\n");
                js.append("                text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const messageText = document.createElement('p');\n");
                js.append("            messageText.textContent = '解绑后需要重新绑定才能使用积分商店。';\n");
                js.append("            messageText.style.cssText = `\n");
                js.append("                margin: 0 0 25px 0;\n");
                js.append("                color: #6b7280;\n");
                js.append("                font-size: 16px;\n");
                js.append("                line-height: 1.5;\n");
                js.append("                font-weight: 500;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 按钮容器\n");
                js.append("            const buttonContainer = document.createElement('div');\n");
                js.append("            buttonContainer.style.cssText = `\n");
                js.append("                display: flex;\n");
                js.append("                gap: 15px;\n");
                js.append("                justify-content: center;\n");
                js.append("                margin-top: 20px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 取消按钮\n");
                js.append("            const cancelBtn = document.createElement('button');\n");
                js.append("            cancelBtn.textContent = '取消';\n");
                js.append("            cancelBtn.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);\n");
                js.append("                color: white;\n");
                js.append("                border: none;\n");
                js.append("                padding: 12px 25px;\n");
                js.append("                border-radius: 25px;\n");
                js.append("                cursor: pointer;\n");
                js.append("                font-size: 16px;\n");
                js.append("                font-weight: 600;\n");
                js.append("                box-shadow: 0 4px 15px rgba(107, 114, 128, 0.3);\n");
                js.append("                transition: all 0.3s ease;\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 确认按钮\n");
                js.append("            const confirmBtn = document.createElement('button');\n");
                js.append("            confirmBtn.textContent = '确定';\n");
                js.append("            confirmBtn.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n");
                js.append("                color: white;\n");
                js.append("                border: none;\n");
                js.append("                padding: 12px 25px;\n");
                js.append("                border-radius: 25px;\n");
                js.append("                cursor: pointer;\n");
                js.append("                font-size: 16px;\n");
                js.append("                font-weight: 600;\n");
                js.append("                box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);\n");
                js.append("                transition: all 0.3s ease;\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 按钮悬停效果\n");
                js.append("            cancelBtn.onmouseenter = () => {\n");
                js.append("                cancelBtn.style.transform = 'translateY(-2px)';\n");
                js.append("                cancelBtn.style.boxShadow = '0 6px 20px rgba(107, 114, 128, 0.4)';\n");
                js.append("            };\n");
                js.append("            cancelBtn.onmouseleave = () => {\n");
                js.append("                cancelBtn.style.transform = 'translateY(0)';\n");
                js.append("                cancelBtn.style.boxShadow = '0 4px 15px rgba(107, 114, 128, 0.3)';\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            confirmBtn.onmouseenter = () => {\n");
                js.append("                confirmBtn.style.transform = 'translateY(-2px)';\n");
                js.append("                confirmBtn.style.boxShadow = '0 6px 20px rgba(245, 158, 11, 0.4)';\n");
                js.append("            };\n");
                js.append("            confirmBtn.onmouseleave = () => {\n");
                js.append("                confirmBtn.style.transform = 'translateY(0)';\n");
                js.append("                confirmBtn.style.boxShadow = '0 4px 15px rgba(245, 158, 11, 0.3)';\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            // 取消按钮事件\n");
                js.append("            cancelBtn.onclick = () => {\n");
                js.append("                modal.style.animation = 'fadeOut 0.3s ease-out';\n");
                js.append("                modalContent.style.animation = 'slideOut 0.3s ease-out';\n");
                js.append("                setTimeout(() => {\n");
                js.append("                    if (document.body.contains(modal)) {\n");
                js.append("                        document.body.removeChild(modal);\n");
                js.append("                    }\n");
                js.append("                }, 300);\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            // 确认按钮事件\n");
                js.append("            confirmBtn.onclick = () => {\n");
                js.append("                // 关闭弹窗\n");
                js.append("                modal.style.animation = 'fadeOut 0.3s ease-out';\n");
                js.append("                modalContent.style.animation = 'slideOut 0.3s ease-out';\n");
                js.append("                setTimeout(() => {\n");
                js.append("                    if (document.body.contains(modal)) {\n");
                js.append("                        document.body.removeChild(modal);\n");
                js.append("                    }\n");
                js.append("                }, 300);\n");
                js.append("                \n");
                js.append("                // 执行解绑\n");
                js.append("                performUnbind();\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            // 组装弹窗\n");
                js.append("            buttonContainer.appendChild(cancelBtn);\n");
                js.append("            buttonContainer.appendChild(confirmBtn);\n");
                js.append("            modalContent.appendChild(iconContainer);\n");
                js.append("            modalContent.appendChild(title);\n");
                js.append("            modalContent.appendChild(messageText);\n");
                js.append("            modalContent.appendChild(buttonContainer);\n");
                js.append("            modal.appendChild(modalContent);\n");
                js.append("            \n");
                js.append("            // 添加CSS动画\n");
                js.append("            const style = document.createElement('style');\n");
                js.append("            style.textContent = `\n");
                js.append("                @keyframes warningPulse {\n");
                js.append("                    0%, 100% { transform: scale(1); box-shadow: 0 8px 25px rgba(245, 158, 11, 0.3); }\n");
                js.append("                    50% { transform: scale(1.05); box-shadow: 0 12px 35px rgba(245, 158, 11, 0.4); }\n");
                js.append("                }\n");
                js.append("            `;\n");
                js.append("            document.head.appendChild(style);\n");
                js.append("            document.body.appendChild(modal);\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 执行解绑操作\n");
                js.append("        function performUnbind() {\n");
                js.append("            \n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'unbind_account',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    session_id: sessionId,\n");
                js.append("                    username: currentUsername\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    showUnbindSuccessModal(data.message);\n");
                js.append("                    currentUsername = null;\n");
                js.append("                    showBindInterface();\n");
                js.append("                } else {\n");
                js.append("                    // 检查是否是时间限制错误\n");
                js.append("                    if (data.bind_time && data.unbind_cooldown_config) {\n");
                js.append("                        showUnbindTimeModal(data.bind_time, data.unbind_cooldown_config, data.message);\n");
                js.append("                    } else {\n");
                js.append("                        alert('❌ ' + data.message);\n");
                js.append("                    }\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                alert('❌ 网络错误: ' + error.message);\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示解绑时间限制弹窗（带动态倒计时）\n");
                js.append("        function showUnbindTimeModal(bindTime, unbindCooldownConfig, message) {\n");
                js.append("            const currentTime = new Date().getTime();\n");
                js.append("            \n");
                js.append("            // 解析冷却时间配置\n");
                js.append("            let minBindDuration;\n");
                js.append("            if (unbindCooldownConfig.includes('秒')) {\n");
                js.append("                const seconds = parseInt(unbindCooldownConfig.replace('秒', ''));\n");
                js.append("                minBindDuration = seconds * 1000;\n");
                js.append("            } else {\n");
                js.append("                const hours = parseInt(unbindCooldownConfig.replace('小时', ''));\n");
                js.append("                minBindDuration = hours * 60 * 60 * 1000;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            let remainingTime = (bindTime + minBindDuration) - currentTime;\n");
                js.append("            \n");
                js.append("            if (remainingTime <= 0) {\n");
                js.append("                alert('❌ ' + message);\n");
                js.append("                return;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 创建弹窗元素\n");
                js.append("            const modal = document.createElement('div');\n");
                js.append("            modal.style.cssText = `\n");
                js.append("                position: fixed;\n");
                js.append("                top: 0;\n");
                js.append("                left: 0;\n");
                js.append("                width: 100%;\n");
                js.append("                height: 100%;\n");
                js.append("                background: rgba(0, 0, 0, 0.6);\n");
                js.append("                display: flex;\n");
                js.append("                justify-content: center;\n");
                js.append("                align-items: center;\n");
                js.append("                z-index: 10000;\n");
                js.append("                backdrop-filter: blur(5px);\n");
                js.append("                animation: fadeIn 0.3s ease-out;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const modalContent = document.createElement('div');\n");
                js.append("            modalContent.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);\n");
                js.append("                padding: 40px 35px;\n");
                js.append("                border-radius: 20px;\n");
                js.append("                text-align: center;\n");
                js.append("                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                js.append("                max-width: 450px;\n");
                js.append("                width: 90%;\n");
                js.append("                border: 1px solid rgba(255, 255, 255, 0.2);\n");
                js.append("                position: relative;\n");
                js.append("                animation: slideIn 0.3s ease-out;\n");
                js.append("                transform-origin: center;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 添加装饰性图标\n");
                js.append("            const iconContainer = document.createElement('div');\n");
                js.append("            iconContainer.style.cssText = `\n");
                js.append("                width: 80px;\n");
                js.append("                height: 80px;\n");
                js.append("                background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);\n");
                js.append("                border-radius: 50%;\n");
                js.append("                display: flex;\n");
                js.append("                align-items: center;\n");
                js.append("                justify-content: center;\n");
                js.append("                margin: 0 auto 25px auto;\n");
                js.append("                box-shadow: 0 8px 25px rgba(239, 68, 68, 0.2);\n");
                js.append("                animation: pulse 2s infinite;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const icon = document.createElement('div');\n");
                js.append("            icon.textContent = '🔒';\n");
                js.append("            icon.style.cssText = 'font-size: 36px; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));';\n");
                js.append("            iconContainer.appendChild(icon);\n");
                js.append("            \n");
                js.append("            const title = document.createElement('h3');\n");
                js.append("            title.textContent = '解绑受限';\n");
                js.append("            title.style.cssText = `\n");
                js.append("                margin: 0 0 15px 0;\n");
                js.append("                color: #dc2626;\n");
                js.append("                font-size: 24px;\n");
                js.append("                font-weight: 700;\n");
                js.append("                text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const subtitle = document.createElement('p');\n");
                js.append("            subtitle.textContent = '为了账户安全，绑定后需要等待一段时间才能解绑';\n");
                js.append("            subtitle.style.cssText = `\n");
                js.append("                margin: 0 0 25px 0;\n");
                js.append("                color: #6b7280;\n");
                js.append("                font-size: 14px;\n");
                js.append("                line-height: 1.5;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const timeDisplay = document.createElement('div');\n");
                js.append("            timeDisplay.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);\n");
                js.append("                padding: 20px;\n");
                js.append("                border-radius: 15px;\n");
                js.append("                margin: 20px 0 30px 0;\n");
                js.append("                border: 2px solid #f59e0b;\n");
                js.append("                position: relative;\n");
                js.append("                overflow: hidden;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 添加装饰性背景\n");
                js.append("            const bgDecor = document.createElement('div');\n");
                js.append("            bgDecor.style.cssText = `\n");
                js.append("                position: absolute;\n");
                js.append("                top: -50%;\n");
                js.append("                right: -50%;\n");
                js.append("                width: 100%;\n");
                js.append("                height: 100%;\n");
                js.append("                background: linear-gradient(45deg, transparent 30%, rgba(245, 158, 11, 0.1) 50%, transparent 70%);\n");
                js.append("                animation: shimmer 3s infinite;\n");
                js.append("            `;\n");
                js.append("            timeDisplay.appendChild(bgDecor);\n");
                js.append("            \n");
                js.append("            const timeText = document.createElement('p');\n");
                js.append("            timeText.style.cssText = `\n");
                js.append("                margin: 0;\n");
                js.append("                font-size: 18px;\n");
                js.append("                color: #374151;\n");
                js.append("                font-weight: 600;\n");
                js.append("                position: relative;\n");
                js.append("                z-index: 1;\n");
                js.append("            `;\n");
                js.append("            timeDisplay.appendChild(timeText);\n");
                js.append("            \n");
                js.append("            const closeBtn = document.createElement('button');\n");
                js.append("            closeBtn.textContent = '我知道了';\n");
                js.append("            closeBtn.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);\n");
                js.append("                color: white;\n");
                js.append("                border: none;\n");
                js.append("                padding: 12px 30px;\n");
                js.append("                border-radius: 25px;\n");
                js.append("                cursor: pointer;\n");
                js.append("                font-size: 16px;\n");
                js.append("                font-weight: 600;\n");
                js.append("                margin-top: 10px;\n");
                js.append("                box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);\n");
                js.append("                transition: all 0.3s ease;\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 按钮悬停效果\n");
                js.append("            closeBtn.onmouseenter = () => {\n");
                js.append("                closeBtn.style.transform = 'translateY(-2px)';\n");
                js.append("                closeBtn.style.boxShadow = '0 6px 20px rgba(59, 130, 246, 0.4)';\n");
                js.append("            };\n");
                js.append("            closeBtn.onmouseleave = () => {\n");
                js.append("                closeBtn.style.transform = 'translateY(0)';\n");
                js.append("                closeBtn.style.boxShadow = '0 4px 15px rgba(59, 130, 246, 0.3)';\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            closeBtn.onclick = () => {\n");
                js.append("                modal.style.animation = 'fadeOut 0.3s ease-out';\n");
                js.append("                modalContent.style.animation = 'slideOut 0.3s ease-out';\n");
                js.append("                setTimeout(() => {\n");
                js.append("                    if (document.body.contains(modal)) {\n");
                js.append("                        document.body.removeChild(modal);\n");
                js.append("                    }\n");
                js.append("                    if (countdownInterval) {\n");
                js.append("                        clearInterval(countdownInterval);\n");
                js.append("                    }\n");
                js.append("                }, 300);\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            // 组装弹窗\n");
                js.append("            modalContent.appendChild(iconContainer);\n");
                js.append("            modalContent.appendChild(title);\n");
                js.append("            modalContent.appendChild(subtitle);\n");
                js.append("            modalContent.appendChild(timeDisplay);\n");
                js.append("            modalContent.appendChild(closeBtn);\n");
                js.append("            modal.appendChild(modalContent);\n");
                js.append("            \n");
                js.append("            // 添加CSS动画\n");
                js.append("            const style = document.createElement('style');\n");
                js.append("            style.textContent = `\n");
                js.append("                @keyframes fadeIn {\n");
                js.append("                    from { opacity: 0; }\n");
                js.append("                    to { opacity: 1; }\n");
                js.append("                }\n");
                js.append("                @keyframes fadeOut {\n");
                js.append("                    from { opacity: 1; }\n");
                js.append("                    to { opacity: 0; }\n");
                js.append("                }\n");
                js.append("                @keyframes slideIn {\n");
                js.append("                    from { transform: scale(0.8) translateY(-20px); opacity: 0; }\n");
                js.append("                    to { transform: scale(1) translateY(0); opacity: 1; }\n");
                js.append("                }\n");
                js.append("                @keyframes slideOut {\n");
                js.append("                    from { transform: scale(1) translateY(0); opacity: 1; }\n");
                js.append("                    to { transform: scale(0.8) translateY(-20px); opacity: 0; }\n");
                js.append("                }\n");
                js.append("                @keyframes pulse {\n");
                js.append("                    0%, 100% { transform: scale(1); }\n");
                js.append("                    50% { transform: scale(1.05); }\n");
                js.append("                }\n");
                js.append("                @keyframes shimmer {\n");
                js.append("                    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }\n");
                js.append("                    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }\n");
                js.append("                }\n");
                js.append("            `;\n");
                js.append("            document.head.appendChild(style);\n");
                js.append("            document.body.appendChild(modal);\n");
                js.append("            \n");
                js.append("            // 启动倒计时\n");
                js.append("            const countdownInterval = setInterval(() => {\n");
                js.append("                remainingTime -= 1000;\n");
                js.append("                \n");
                js.append("                if (remainingTime <= 0) {\n");
                js.append("                    clearInterval(countdownInterval);\n");
                js.append("                    timeText.innerHTML = `\n");
                js.append("                        <div style=\"color: #10b981; font-size: 20px; font-weight: 700;\">\n");
                js.append("                            🎉 现在可以解绑账户了！\n");
                js.append("                        </div>\n");
                js.append("                    `;\n");
                js.append("                    timeDisplay.style.borderColor = '#10b981';\n");
                js.append("                    timeDisplay.style.background = 'linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%)';\n");
                js.append("                    icon.textContent = '🔓';\n");
                js.append("                    iconContainer.style.background = 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)';\n");
                js.append("                    iconContainer.style.boxShadow = '0 8px 25px rgba(16, 185, 129, 0.2)';\n");
                js.append("                } else {\n");
                js.append("                    const totalSeconds = Math.ceil(remainingTime / 1000);\n");
                js.append("                    const hours = Math.floor(totalSeconds / 3600);\n");
                js.append("                    const minutes = Math.floor((totalSeconds % 3600) / 60);\n");
                js.append("                    const seconds = totalSeconds % 60;\n");
                js.append("                    \n");
                js.append("                    let timeString = '';\n");
                js.append("                    if (hours > 0) {\n");
                js.append("                        timeString = `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${hours}</span><span style=\"color: #6b7280; font-size: 14px;\">小时</span> ` +\n");
                js.append("                                   `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${minutes}</span><span style=\"color: #6b7280; font-size: 14px;\">分钟</span> ` +\n");
                js.append("                                   `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${seconds}</span><span style=\"color: #6b7280; font-size: 14px;\">秒</span>`;\n");
                js.append("                    } else if (minutes > 0) {\n");
                js.append("                        timeString = `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${minutes}</span><span style=\"color: #6b7280; font-size: 14px;\">分钟</span> ` +\n");
                js.append("                                   `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${seconds}</span><span style=\"color: #6b7280; font-size: 14px;\">秒</span>`;\n");
                js.append("                    } else {\n");
                js.append("                        timeString = `<span style=\"color: #dc2626; font-size: 24px; font-weight: 700;\">${seconds}</span><span style=\"color: #6b7280; font-size: 14px;\">秒</span>`;\n");
                js.append("                    }\n");
                js.append("                    \n");
                js.append("                    timeText.innerHTML = `\n");
                js.append("                        <div style=\"margin-bottom: 8px; color: #6b7280; font-size: 14px;\">还需等待</div>\n");
                js.append("                        <div>${timeString}</div>\n");
                js.append("                    `;\n");
                js.append("                }\n");
                js.append("            }, 1000);\n");
                js.append("            \n");
                js.append("            // 初始显示\n");
                js.append("            const totalSeconds = Math.ceil(remainingTime / 1000);\n");
                js.append("            const hours = Math.floor(totalSeconds / 3600);\n");
                js.append("            const minutes = Math.floor((totalSeconds % 3600) / 60);\n");
                js.append("            const seconds = totalSeconds % 60;\n");
                js.append("            \n");
                js.append("            let initialTimeString = '';\n");
                js.append("            if (hours > 0) {\n");
                js.append("                initialTimeString = `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${hours}</span><span style=\"color: #6b7280; font-size: 14px;\">小时</span> ` +\n");
                js.append("                               `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${minutes}</span><span style=\"color: #6b7280; font-size: 14px;\">分钟</span> ` +\n");
                js.append("                               `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${seconds}</span><span style=\"color: #6b7280; font-size: 14px;\">秒</span>`;\n");
                js.append("            } else if (minutes > 0) {\n");
                js.append("                initialTimeString = `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${minutes}</span><span style=\"color: #6b7280; font-size: 14px;\">分钟</span> ` +\n");
                js.append("                               `<span style=\"color: #dc2626; font-size: 22px; font-weight: 700;\">${seconds}</span><span style=\"color: #6b7280; font-size: 14px;\">秒</span>`;\n");
                js.append("            } else {\n");
                js.append("                initialTimeString = `<span style=\"color: #dc2626; font-size: 24px; font-weight: 700;\">${seconds}</span><span style=\"color: #6b7280; font-size: 14px;\">秒</span>`;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            timeText.innerHTML = `\n");
                js.append("                <div style=\"margin-bottom: 8px; color: #6b7280; font-size: 14px;\">还需等待</div>\n");
                js.append("                <div>${initialTimeString}</div>\n");
                js.append("            `;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 显示解绑成功弹窗\n");
                js.append("        function showUnbindSuccessModal(message) {\n");
                js.append("            // 创建弹窗元素\n");
                js.append("            const modal = document.createElement('div');\n");
                js.append("            modal.style.cssText = `\n");
                js.append("                position: fixed;\n");
                js.append("                top: 0;\n");
                js.append("                left: 0;\n");
                js.append("                width: 100%;\n");
                js.append("                height: 100%;\n");
                js.append("                background: rgba(0, 0, 0, 0.6);\n");
                js.append("                display: flex;\n");
                js.append("                justify-content: center;\n");
                js.append("                align-items: center;\n");
                js.append("                z-index: 10000;\n");
                js.append("                backdrop-filter: blur(5px);\n");
                js.append("                animation: fadeIn 0.3s ease-out;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const modalContent = document.createElement('div');\n");
                js.append("            modalContent.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #ffffff 0%, #f0fdf4 100%);\n");
                js.append("                padding: 40px 35px;\n");
                js.append("                border-radius: 20px;\n");
                js.append("                text-align: center;\n");
                js.append("                box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 25px rgba(0, 0, 0, 0.1);\n");
                js.append("                max-width: 450px;\n");
                js.append("                width: 90%;\n");
                js.append("                border: 1px solid rgba(16, 185, 129, 0.2);\n");
                js.append("                position: relative;\n");
                js.append("                animation: slideIn 0.3s ease-out;\n");
                js.append("                transform-origin: center;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 添加装饰性图标\n");
                js.append("            const iconContainer = document.createElement('div');\n");
                js.append("            iconContainer.style.cssText = `\n");
                js.append("                width: 80px;\n");
                js.append("                height: 80px;\n");
                js.append("                background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);\n");
                js.append("                border-radius: 50%;\n");
                js.append("                display: flex;\n");
                js.append("                align-items: center;\n");
                js.append("                justify-content: center;\n");
                js.append("                margin: 0 auto 25px auto;\n");
                js.append("                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3);\n");
                js.append("                animation: successPulse 2s infinite;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const icon = document.createElement('div');\n");
                js.append("            icon.textContent = '🎉';\n");
                js.append("            icon.style.cssText = 'font-size: 36px; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));';\n");
                js.append("            iconContainer.appendChild(icon);\n");
                js.append("            \n");
                js.append("            const title = document.createElement('h3');\n");
                js.append("            title.textContent = '解绑成功';\n");
                js.append("            title.style.cssText = `\n");
                js.append("                margin: 0 0 15px 0;\n");
                js.append("                color: #059669;\n");
                js.append("                font-size: 24px;\n");
                js.append("                font-weight: 700;\n");
                js.append("                text-shadow: 0 2px 4px rgba(0,0,0,0.1);\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            const messageText = document.createElement('p');\n");
                js.append("            messageText.textContent = message || '账户已成功解绑，您可以重新绑定其他账户';\n");
                js.append("            messageText.style.cssText = `\n");
                js.append("                margin: 0 0 25px 0;\n");
                js.append("                color: #374151;\n");
                js.append("                font-size: 16px;\n");
                js.append("                line-height: 1.5;\n");
                js.append("                font-weight: 500;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 添加装饰性成功标识\n");
                js.append("            const successBadge = document.createElement('div');\n");
                js.append("            successBadge.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                js.append("                color: white;\n");
                js.append("                padding: 8px 20px;\n");
                js.append("                border-radius: 20px;\n");
                js.append("                font-size: 14px;\n");
                js.append("                font-weight: 600;\n");
                js.append("                margin: 20px auto;\n");
                js.append("                display: inline-block;\n");
                js.append("                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n");
                js.append("                animation: bounce 1s ease-out;\n");
                js.append("            `;\n");
                js.append("            successBadge.textContent = '✓ 操作完成';\n");
                js.append("            \n");
                js.append("            const closeBtn = document.createElement('button');\n");
                js.append("            closeBtn.textContent = '确定';\n");
                js.append("            closeBtn.style.cssText = `\n");
                js.append("                background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n");
                js.append("                color: white;\n");
                js.append("                border: none;\n");
                js.append("                padding: 12px 30px;\n");
                js.append("                border-radius: 25px;\n");
                js.append("                cursor: pointer;\n");
                js.append("                font-size: 16px;\n");
                js.append("                font-weight: 600;\n");
                js.append("                margin-top: 10px;\n");
                js.append("                box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);\n");
                js.append("                transition: all 0.3s ease;\n");
                js.append("                letter-spacing: 0.5px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 按钮悬停效果\n");
                js.append("            closeBtn.onmouseenter = () => {\n");
                js.append("                closeBtn.style.transform = 'translateY(-2px)';\n");
                js.append("                closeBtn.style.boxShadow = '0 6px 20px rgba(16, 185, 129, 0.4)';\n");
                js.append("            };\n");
                js.append("            closeBtn.onmouseleave = () => {\n");
                js.append("                closeBtn.style.transform = 'translateY(0)';\n");
                js.append("                closeBtn.style.boxShadow = '0 4px 15px rgba(16, 185, 129, 0.3)';\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            closeBtn.onclick = () => {\n");
                js.append("                modal.style.animation = 'fadeOut 0.3s ease-out';\n");
                js.append("                modalContent.style.animation = 'slideOut 0.3s ease-out';\n");
                js.append("                setTimeout(() => {\n");
                js.append("                    if (document.body.contains(modal)) {\n");
                js.append("                        document.body.removeChild(modal);\n");
                js.append("                    }\n");
                js.append("                }, 300);\n");
                js.append("            };\n");
                js.append("            \n");
                js.append("            // 添加庆祝粒子效果\n");
                js.append("            const particleContainer = document.createElement('div');\n");
                js.append("            particleContainer.style.cssText = `\n");
                js.append("                position: absolute;\n");
                js.append("                top: 0;\n");
                js.append("                left: 0;\n");
                js.append("                width: 100%;\n");
                js.append("                height: 100%;\n");
                js.append("                pointer-events: none;\n");
                js.append("                overflow: hidden;\n");
                js.append("                border-radius: 20px;\n");
                js.append("            `;\n");
                js.append("            \n");
                js.append("            // 创建粒子\n");
                js.append("            for (let i = 0; i < 6; i++) {\n");
                js.append("                const particle = document.createElement('div');\n");
                js.append("                particle.textContent = ['✨', '🎊', '🎉', '⭐', '💫', '🌟'][i];\n");
                js.append("                particle.style.cssText = `\n");
                js.append("                    position: absolute;\n");
                js.append("                    font-size: 20px;\n");
                js.append("                    animation: float ${2 + Math.random() * 2}s ease-in-out infinite;\n");
                js.append("                    animation-delay: ${Math.random() * 2}s;\n");
                js.append("                    top: ${Math.random() * 80}%;\n");
                js.append("                    left: ${Math.random() * 80}%;\n");
                js.append("                    opacity: 0.7;\n");
                js.append("                `;\n");
                js.append("                particleContainer.appendChild(particle);\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 组装弹窗\n");
                js.append("            modalContent.appendChild(particleContainer);\n");
                js.append("            modalContent.appendChild(iconContainer);\n");
                js.append("            modalContent.appendChild(title);\n");
                js.append("            modalContent.appendChild(messageText);\n");
                js.append("            modalContent.appendChild(successBadge);\n");
                js.append("            modalContent.appendChild(closeBtn);\n");
                js.append("            modal.appendChild(modalContent);\n");
                js.append("            \n");
                js.append("            // 添加CSS动画\n");
                js.append("            const style = document.createElement('style');\n");
                js.append("            style.textContent = `\n");
                js.append("                @keyframes successPulse {\n");
                js.append("                    0%, 100% { transform: scale(1); box-shadow: 0 8px 25px rgba(16, 185, 129, 0.3); }\n");
                js.append("                    50% { transform: scale(1.05); box-shadow: 0 12px 35px rgba(16, 185, 129, 0.4); }\n");
                js.append("                }\n");
                js.append("                @keyframes bounce {\n");
                js.append("                    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }\n");
                js.append("                    40% { transform: translateY(-10px); }\n");
                js.append("                    60% { transform: translateY(-5px); }\n");
                js.append("                }\n");
                js.append("                @keyframes float {\n");
                js.append("                    0%, 100% { transform: translateY(0px) rotate(0deg); }\n");
                js.append("                    33% { transform: translateY(-10px) rotate(120deg); }\n");
                js.append("                    66% { transform: translateY(5px) rotate(240deg); }\n");
                js.append("                }\n");
                js.append("            `;\n");
                js.append("            document.head.appendChild(style);\n");
                js.append("            document.body.appendChild(modal);\n");
                js.append("            \n");
                js.append("            // 3秒后自动关闭\n");
                js.append("            setTimeout(() => {\n");
                js.append("                if (document.body.contains(modal)) {\n");
                js.append("                    closeBtn.click();\n");
                js.append("                }\n");
                js.append("            }, 3000);\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 页面加载完成后启动倒计时\n");
                js.append("        document.addEventListener('DOMContentLoaded', () => {\n");
                js.append("            startCountdownTimer();\n");
                js.append("        });\n");
                js.append("        \n");

                // 添加分页相关函数
                js.append("        // 更新分页信息\n");
                js.append("        function updatePagination() {\n");
                js.append("            totalPages = Math.ceil(filteredItems.length / itemsPerPage);\n");
                js.append("            \n");
                js.append("            // 确保当前页在有效范围内\n");
                js.append("            if (currentPage > totalPages) {\n");
                js.append("                currentPage = Math.max(1, totalPages);\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            updatePaginationButtons();\n");
                js.append("            updatePaginationInfo();\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 更新分页按钮\n");
                js.append("        function updatePaginationButtons() {\n");
                js.append("            const firstBtn = document.getElementById('firstPageBtn');\n");
                js.append("            const prevBtn = document.getElementById('prevPageBtn');\n");
                js.append("            const nextBtn = document.getElementById('nextPageBtn');\n");
                js.append("            const lastBtn = document.getElementById('lastPageBtn');\n");
                js.append("            const pageNumbers = document.getElementById('pageNumbers');\n");
                js.append("            \n");
                js.append("            // 更新导航按钮状态\n");
                js.append("            firstBtn.disabled = currentPage === 1;\n");
                js.append("            prevBtn.disabled = currentPage === 1;\n");
                js.append("            nextBtn.disabled = currentPage === totalPages;\n");
                js.append("            lastBtn.disabled = currentPage === totalPages;\n");
                js.append("            \n");
                js.append("            // 生成页码按钮\n");
                js.append("            let pageNumbersHtml = '';\n");
                js.append("            const maxVisiblePages = 5;\n");
                js.append("            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));\n");
                js.append("            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);\n");
                js.append("            \n");
                js.append("            // 调整起始页\n");
                js.append("            if (endPage - startPage + 1 < maxVisiblePages) {\n");
                js.append("                startPage = Math.max(1, endPage - maxVisiblePages + 1);\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            for (let i = startPage; i <= endPage; i++) {\n");
                js.append("                const activeClass = i === currentPage ? ' active' : '';\n");
                js.append("                pageNumbersHtml += `<span class=\"page-number${activeClass}\" onclick=\"goToPage(${i})\">${i}</span>`;\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            pageNumbers.innerHTML = pageNumbersHtml;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 更新分页信息\n");
                js.append("        function updatePaginationInfo() {\n");
                js.append("            const paginationInfo = document.getElementById('paginationInfo');\n");
                js.append("            const totalItemsCount = document.getElementById('totalItemsCount');\n");
                js.append("            \n");
                js.append("            paginationInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页`;\n");
                js.append("            totalItemsCount.textContent = filteredItems.length;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 跳转到指定页\n");
                js.append("        function goToPage(page) {\n");
                js.append("            if (page >= 1 && page <= totalPages && page !== currentPage) {\n");
                js.append("                currentPage = page;\n");
                js.append("                updatePaginationButtons();\n");
                js.append("                updateShopItemsDisplay();\n");
                js.append("                \n");
                js.append("                // 滚动到商品区域顶部\n");
                js.append("                document.getElementById('shopItems').scrollIntoView({ behavior: 'smooth' });\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 上一页\n");
                js.append("        function goToPrevPage() {\n");
                js.append("            if (currentPage > 1) {\n");
                js.append("                goToPage(currentPage - 1);\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 下一页\n");
                js.append("        function goToNextPage() {\n");
                js.append("            if (currentPage < totalPages) {\n");
                js.append("                goToPage(currentPage + 1);\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        // 跳转到最后一页\n");
                js.append("        function goToLastPage() {\n");
                js.append("            if (totalPages > 0) {\n");
                js.append("                goToPage(totalPages);\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");

                // 添加自动检测更新功能
                js.append(generateAutoUpdateFunctions());

                return js.toString();
        }

        /**
         * 生成商店布局CSS
         */
        private String generateShopLayoutCSS() {
                String layout = plugin.getConfig().getString("website.shop-layout", "grid");

                if ("list".equals(layout)) {
                        // 列表布局
                        return ".shop-items { display: flex; flex-direction: column; gap: 15px; margin-bottom: 30px; }\n"
                                        +
                                        ".shop-item { max-width: 100%; }\n" +
                                        ".item-header { flex-direction: row; }\n" +
                                        ".item-icon { font-size: 2.5em; }\n" +
                                        ".item-title { flex: 1; }\n" +
                                        ".item-meta { display: flex; justify-content: space-between; align-items: center; }\n"
                                        +
                                        ".purchase-btn { max-width: 200px; margin-left: auto; }\n";
                } else {
                        // 网格布局（默认）
                        return ".shop-items { display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 20px; margin-bottom: 30px; }\n"
                                        +
                                        "/* 移动端响应式设计 */\n" +
                                        "@media (max-width: 768px) {\n" +
                                        "    .shop-items { grid-template-columns: 1fr; gap: 15px; }\n" +
                                        "    .shop-item { margin: 0; }\n" +
                                        "}\n";
                }
        }

        /**
         * 获取主题背景样式
         */
        private String getThemeBackgroundStyle(String theme, String backgroundImage, String primaryColor,
                        String accentColor) {
                if (backgroundImage != null && !backgroundImage.trim().isEmpty()) {
                        // 获取透明度设置
                        int opacity = plugin.getConfig().getInt("website.shop-background-opacity", 30);
                        // 将透明度转换为0-1之间的值
                        double opacityValue = opacity / 100.0;

                        // 如果配置了背景图，使用背景图（支持GIF动图）
                        return "background: linear-gradient(rgba(30, 41, 59, " + opacityValue + "), rgba(71, 85, 105, "
                                        + (opacityValue + 0.1) + ")), url('"
                                        + backgroundImage + "'); " +
                                        "background-size: cover; background-position: center; background-attachment: fixed; background-repeat: no-repeat;";
                }

                // 根据主题返回不同的背景样式
                switch (theme) {
                        case "dark":
                                return "background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 50%, #404040 100%);";
                        case "light":
                                return "background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);";
                        case "blue":
                                return "background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);";
                        case "purple":
                                return "background: linear-gradient(135deg, #581c87 0%, #8b5cf6 50%, #a78bfa 100%);";
                        case "green":
                                return "background: linear-gradient(135deg, #14532d 0%, #22c55e 50%, #4ade80 100%);";
                        default:
                                return "background: linear-gradient(135deg, rgba(30, 41, 59, 0.8) 0%, rgba(51, 65, 85, 0.8) 50%, rgba(71, 85, 105, 0.8) 100%);";
                }
        }

        /**
         * 生成主题CSS
         */
        private String generateThemeCSS(String theme, String primaryColor, String accentColor, boolean enableAnimations,
                        String loadAnimation, String animationSpeed) {
                StringBuilder css = new StringBuilder();

                // CSS变量定义
                css.append(":root {\n");
                css.append("  --primary-color: ").append(primaryColor).append(";\n");
                css.append("  --accent-color: ").append(accentColor).append(";\n");
                css.append("  --animation-duration: ").append(getAnimationDuration(animationSpeed)).append(";\n");
                css.append("}\n\n");

                // 页面加载动画
                if (enableAnimations && !"none".equals(loadAnimation)) {
                        css.append(generateLoadAnimationCSS(loadAnimation));
                }

                // 主题特定样式
                css.append(generateThemeSpecificCSS(theme));

                return css.toString();
        }

        /**
         * 获取动画持续时间
         */
        private String getAnimationDuration(String speed) {
                switch (speed) {
                        case "slow":
                                return "0.8s";
                        case "fast":
                                return "0.3s";
                        default:
                                return "0.5s";
                }
        }

        /**
         * 生成页面加载动画CSS
         */
        private String generateLoadAnimationCSS(String animation) {
                StringBuilder css = new StringBuilder();

                switch (animation) {
                        case "fade":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  from { opacity: 0; }\n");
                                css.append("  to { opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "slide":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  from { transform: translateY(30px); opacity: 0; }\n");
                                css.append("  to { transform: translateY(0); opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "zoom":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  from { transform: scale(0.9); opacity: 0; }\n");
                                css.append("  to { transform: scale(1); opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "bounce":
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { transform: translateY(-30px); opacity: 0; }\n");
                                css.append("  50% { transform: translateY(10px); opacity: 0.8; }\n");
                                css.append("  100% { transform: translateY(0); opacity: 1; }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n\n");
                                break;
                        case "matrix":
                                // 黑客帝国风格矩阵效果
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: scale(0.8) rotateX(90deg); filter: hue-rotate(0deg) brightness(0.5); }\n");
                                css.append("  25% { opacity: 0.3; transform: scale(0.9) rotateX(45deg); filter: hue-rotate(90deg) brightness(0.7); }\n");
                                css.append("  50% { opacity: 0.6; transform: scale(1.05) rotateX(0deg); filter: hue-rotate(180deg) brightness(1.2); }\n");
                                css.append("  75% { opacity: 0.8; transform: scale(1.02) rotateX(-10deg); filter: hue-rotate(270deg) brightness(1.1); }\n");
                                css.append("  100% { opacity: 1; transform: scale(1) rotateX(0deg); filter: hue-rotate(360deg) brightness(1); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94); }\n");
                                css.append("body::before { animation: matrixGlow var(--animation-duration) ease-out; }\n");
                                css.append("@keyframes matrixGlow {\n");
                                css.append("  0% { box-shadow: inset 0 0 100px #00ff00; }\n");
                                css.append("  100% { box-shadow: inset 0 0 0px #00ff00; }\n");
                                css.append("}\n\n");
                                break;
                        case "portal":
                                // 传送门效果
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: scale(0) rotate(0deg); border-radius: 50%; filter: blur(20px); }\n");
                                css.append("  25% { opacity: 0.4; transform: scale(0.3) rotate(90deg); border-radius: 30%; filter: blur(15px); }\n");
                                css.append("  50% { opacity: 0.7; transform: scale(0.7) rotate(180deg); border-radius: 20%; filter: blur(10px); }\n");
                                css.append("  75% { opacity: 0.9; transform: scale(0.95) rotate(270deg); border-radius: 10%; filter: blur(5px); }\n");
                                css.append("  100% { opacity: 1; transform: scale(1) rotate(360deg); border-radius: 0%; filter: blur(0px); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) cubic-bezier(0.68, -0.55, 0.265, 1.55); overflow: hidden; }\n");
                                css.append("body::after { content: ''; position: fixed; top: 50%; left: 50%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(102,126,234,0.3) 0%, transparent 70%); transform: translate(-50%, -50%); animation: portalRing var(--animation-duration) ease-out; z-index: -1; }\n");
                                css.append("@keyframes portalRing {\n");
                                css.append("  0% { width: 0px; height: 0px; opacity: 1; }\n");
                                css.append("  50% { width: 400px; height: 400px; opacity: 0.8; }\n");
                                css.append("  100% { width: 800px; height: 800px; opacity: 0; }\n");
                                css.append("}\n\n");
                                break;
                        case "glitch":
                                // 故障风格效果
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: translateX(-100px) skewX(10deg); filter: hue-rotate(0deg); }\n");
                                css.append("  10% { opacity: 0.2; transform: translateX(-50px) skewX(-5deg); filter: hue-rotate(45deg); }\n");
                                css.append("  20% { opacity: 0.1; transform: translateX(-80px) skewX(15deg); filter: hue-rotate(90deg); }\n");
                                css.append("  30% { opacity: 0.4; transform: translateX(-20px) skewX(-10deg); filter: hue-rotate(135deg); }\n");
                                css.append("  40% { opacity: 0.2; transform: translateX(-60px) skewX(8deg); filter: hue-rotate(180deg); }\n");
                                css.append("  50% { opacity: 0.6; transform: translateX(-10px) skewX(-3deg); filter: hue-rotate(225deg); }\n");
                                css.append("  60% { opacity: 0.3; transform: translateX(-30px) skewX(12deg); filter: hue-rotate(270deg); }\n");
                                css.append("  70% { opacity: 0.7; transform: translateX(-5px) skewX(-2deg); filter: hue-rotate(315deg); }\n");
                                css.append("  80% { opacity: 0.5; transform: translateX(-15px) skewX(5deg); filter: hue-rotate(360deg); }\n");
                                css.append("  90% { opacity: 0.9; transform: translateX(-2px) skewX(-1deg); filter: hue-rotate(0deg); }\n");
                                css.append("  100% { opacity: 1; transform: translateX(0px) skewX(0deg); filter: hue-rotate(0deg); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) steps(10, end); }\n\n");
                                break;
                        case "hologram":
                                // 全息投影效果
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: perspective(1000px) rotateY(90deg) rotateX(30deg); filter: brightness(0.3) contrast(2); }\n");
                                css.append("  25% { opacity: 0.3; transform: perspective(1000px) rotateY(45deg) rotateX(15deg); filter: brightness(0.6) contrast(1.8); }\n");
                                css.append("  50% { opacity: 0.6; transform: perspective(1000px) rotateY(0deg) rotateX(0deg); filter: brightness(1.2) contrast(1.5); }\n");
                                css.append("  75% { opacity: 0.8; transform: perspective(1000px) rotateY(-10deg) rotateX(-5deg); filter: brightness(1.1) contrast(1.2); }\n");
                                css.append("  100% { opacity: 1; transform: perspective(1000px) rotateY(0deg) rotateX(0deg); filter: brightness(1) contrast(1); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94); }\n");
                                css.append("body::before { animation: holoScan var(--animation-duration) ease-out; }\n");
                                css.append("@keyframes holoScan {\n");
                                css.append("  0% { background: linear-gradient(90deg, transparent 0%, rgba(0,255,255,0.3) 50%, transparent 100%); transform: translateX(-100%); }\n");
                                css.append("  100% { background: linear-gradient(90deg, transparent 0%, rgba(0,255,255,0.3) 50%, transparent 100%); transform: translateX(100%); }\n");
                                css.append("}\n\n");
                                break;
                        case "quantum":
                                // 量子传输效果
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: scale(0.1) rotate(0deg); filter: blur(20px) saturate(0); }\n");
                                css.append("  20% { opacity: 0.2; transform: scale(0.3) rotate(72deg); filter: blur(15px) saturate(0.5); }\n");
                                css.append("  40% { opacity: 0.4; transform: scale(0.6) rotate(144deg); filter: blur(10px) saturate(1); }\n");
                                css.append("  60% { opacity: 0.6; transform: scale(0.8) rotate(216deg); filter: blur(5px) saturate(1.5); }\n");
                                css.append("  80% { opacity: 0.8; transform: scale(0.95) rotate(288deg); filter: blur(2px) saturate(1.2); }\n");
                                css.append("  100% { opacity: 1; transform: scale(1) rotate(360deg); filter: blur(0px) saturate(1); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) cubic-bezier(0.68, -0.55, 0.265, 1.55); }\n");
                                css.append("body::after { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle at center, rgba(138,43,226,0.1) 0%, transparent 50%), conic-gradient(from 0deg, transparent, rgba(138,43,226,0.2), transparent); animation: quantumField var(--animation-duration) ease-out; z-index: -1; }\n");
                                css.append("@keyframes quantumField {\n");
                                css.append("  0% { opacity: 1; transform: rotate(0deg) scale(2); }\n");
                                css.append("  100% { opacity: 0; transform: rotate(360deg) scale(0.5); }\n");
                                css.append("}\n\n");
                                break;
                        case "cyberpunk":
                                // 赛博朋克风格
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: translateY(50px) skewY(5deg); filter: hue-rotate(0deg) brightness(0.5) contrast(2); }\n");
                                css.append("  25% { opacity: 0.3; transform: translateY(30px) skewY(3deg); filter: hue-rotate(90deg) brightness(0.8) contrast(1.8); }\n");
                                css.append("  50% { opacity: 0.6; transform: translateY(10px) skewY(1deg); filter: hue-rotate(180deg) brightness(1.2) contrast(1.5); }\n");
                                css.append("  75% { opacity: 0.8; transform: translateY(5px) skewY(0.5deg); filter: hue-rotate(270deg) brightness(1.1) contrast(1.2); }\n");
                                css.append("  100% { opacity: 1; transform: translateY(0px) skewY(0deg); filter: hue-rotate(360deg) brightness(1) contrast(1); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94); }\n");
                                css.append("body::before { animation: cyberGrid var(--animation-duration) ease-out; }\n");
                                css.append("@keyframes cyberGrid {\n");
                                css.append("  0% { background-image: linear-gradient(rgba(0,255,255,0.3) 1px, transparent 1px), linear-gradient(90deg, rgba(255,0,255,0.3) 1px, transparent 1px); background-size: 20px 20px; opacity: 1; }\n");
                                css.append("  100% { background-image: linear-gradient(rgba(0,255,255,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(255,0,255,0.1) 1px, transparent 1px); background-size: 40px 40px; opacity: 0; }\n");
                                css.append("}\n\n");
                                break;
                        case "neon":
                                // 霓虹灯效果
                                css.append("@keyframes pageLoad {\n");
                                css.append("  0% { opacity: 0; transform: scale(0.8); filter: brightness(0) drop-shadow(0 0 0px #ff00ff); }\n");
                                css.append("  25% { opacity: 0.3; transform: scale(0.9); filter: brightness(0.5) drop-shadow(0 0 10px #ff00ff); }\n");
                                css.append("  50% { opacity: 0.6; transform: scale(1.05); filter: brightness(1.2) drop-shadow(0 0 20px #00ffff); }\n");
                                css.append("  75% { opacity: 0.8; transform: scale(1.02); filter: brightness(1.1) drop-shadow(0 0 15px #ffff00); }\n");
                                css.append("  100% { opacity: 1; transform: scale(1); filter: brightness(1) drop-shadow(0 0 5px rgba(255,255,255,0.5)); }\n");
                                css.append("}\n");
                                css.append("body { animation: pageLoad var(--animation-duration) ease-out; }\n");
                                css.append("body::after { content: ''; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: radial-gradient(circle at 25% 25%, rgba(255,0,255,0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(0,255,255,0.1) 0%, transparent 50%); animation: neonPulse var(--animation-duration) ease-out; z-index: -1; }\n");
                                css.append("@keyframes neonPulse {\n");
                                css.append("  0% { opacity: 1; }\n");
                                css.append("  50% { opacity: 0.5; }\n");
                                css.append("  100% { opacity: 0; }\n");
                                css.append("}\n\n");
                                break;
                }

                return css.toString();
        }

        /**
         * 生成主题特定CSS
         */
        private String generateThemeSpecificCSS(String theme) {
                StringBuilder css = new StringBuilder();

                switch (theme) {
                        case "dark":
                                css.append(".shop-header, .bind-section, .shop-item { background: rgba(15, 15, 15, 0.95); color: #ffffff; }\n");
                                css.append(".bind-code-input, #purchaseQuantity { background: rgba(40, 40, 40, 0.8); color: #ffffff; border-color: #555; }\n");
                                css.append(".purchase-btn, .bind-btn, .back-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                break;
                        case "light":
                                css.append(".shop-header, .bind-section, .shop-item { background: rgba(255, 255, 255, 0.95); color: #1a1a1a; box-shadow: 0 25px 50px rgba(0,0,0,0.1); }\n");
                                css.append(".bind-code-input, #purchaseQuantity { background: rgba(255, 255, 255, 0.9); color: #1a1a1a; border-color: #e2e8f0; }\n");
                                css.append(".purchase-btn, .bind-btn, .back-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                break;
                        default:
                                // 使用默认样式，通过CSS变量应用自定义颜色
                                css.append(".purchase-btn, .bind-btn, .back-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                css.append(".refresh-btn, .copy-btn, .confirm-btn { background: linear-gradient(135deg, var(--primary-color), var(--accent-color)); }\n");
                                break;
                }

                return css.toString();
        }

        /**
         * 生成安全防护JavaScript
         */
        private String generateSecurityJS() {
                StringBuilder js = new StringBuilder();

                // 添加安全防护样式
                js.append("// 安全防护样式\n");
                js.append("const securityStyle = document.createElement('style');\n");
                js.append("securityStyle.textContent = `\n");
                js.append("    .security-overlay {\n");
                js.append("        position: fixed;\n");
                js.append("        top: 0;\n");
                js.append("        left: 0;\n");
                js.append("        width: 100%;\n");
                js.append("        height: 100%;\n");
                js.append("        background: rgba(0,0,0,0.9);\n");
                js.append("        color: white;\n");
                js.append("        display: none;\n");
                js.append("        z-index: 9999;\n");
                js.append("        justify-content: center;\n");
                js.append("        align-items: center;\n");
                js.append("        font-size: 24px;\n");
                js.append("        text-align: center;\n");
                js.append("        font-family: Arial, sans-serif;\n");
                js.append("    }\n");
                js.append("`;\n");
                js.append("document.head.appendChild(securityStyle);\n");
                js.append("\n");

                // 添加智能安全防护代码
                js.append("(function(){\n");
                js.append("var _0x1a2b=['keydown','F12','preventDefault','contextmenu','selectstart','dragstart'];\n");
                js.append("var _0x5e6f=document.createElement('div');\n");
                js.append("_0x5e6f.className='security-overlay';\n");
                js.append("_0x5e6f.innerHTML='🚫 检测到开发者工具<br>请关闭后重新访问<br><br>为保护系统安全，禁止使用开发者工具';\n");
                js.append("document.body.appendChild(_0x5e6f);\n");
                js.append("var _0xDetected = false;\n");

                // 键盘事件监听 - 只阻止开发者工具快捷键
                js.append("document.addEventListener(_0x1a2b[0],function(e){\n");
                js.append("if(e.key===_0x1a2b[1]||e.keyCode===123||\n");
                js.append("(e.ctrlKey&&e.shiftKey&&(e.keyCode===73||e.keyCode===74))||\n");
                js.append("(e.ctrlKey&&e.keyCode===85)){\n");
                js.append("e[_0x1a2b[2]]();showWarning();\n");
                js.append("}});\n");

                // 右键菜单禁用
                js.append("document.addEventListener(_0x1a2b[3],function(e){e[_0x1a2b[2]]();});\n");

                // 选择和拖拽禁用 - 但不影响正常点击
                js.append("document.addEventListener(_0x1a2b[4],function(e){\n");
                js.append("if(e.target.tagName!=='INPUT'&&e.target.tagName!=='TEXTAREA'&&e.target.tagName!=='BUTTON'){\n");
                js.append("e[_0x1a2b[2]]();\n");
                js.append("}});\n");
                js.append("document.addEventListener(_0x1a2b[5],function(e){e[_0x1a2b[2]]();});\n");

                // 智能窗口大小检测 - 更宽松的检测
                js.append("var _0xLastCheck = Date.now();\n");
                js.append("setInterval(function(){\n");
                js.append("var now = Date.now();\n");
                js.append("if(now - _0xLastCheck > 5000) {\n"); // 5秒检测一次
                js.append("if(window.outerHeight-window.innerHeight>250||window.outerWidth-window.innerWidth>250){\n");
                js.append("if(!_0xDetected) showWarning();\n");
                js.append("}\n");
                js.append("_0xLastCheck = now;\n");
                js.append("}},2000);\n");

                // 智能debugger检测 - 降低频率
                js.append("setInterval(function(){\n");
                js.append("if(_0xDetected) return;\n");
                js.append("var _0x9i0j=new Date().getTime();\n");
                js.append("debugger;\n");
                js.append("if(new Date().getTime()-_0x9i0j>150){\n");
                js.append("showWarning();\n");
                js.append("}},5000);\n"); // 5秒检测一次

                // 显示警告函数
                js.append("function showWarning(){\n");
                js.append("if(_0xDetected) return;\n");
                js.append("_0xDetected = true;\n");
                js.append("_0x5e6f.style.display='flex';\n");
                js.append("setTimeout(function(){\n");
                js.append("_0x5e6f.style.display='none';\n");
                js.append("_0xDetected = false;\n");
                js.append("},3000);\n"); // 3秒后自动隐藏，不刷新页面
                js.append("}\n");

                js.append("})();\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成自动检测更新的JavaScript函数
         */
        private String generateAutoUpdateFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("        // 自动检测更新相关变量\n");
                js.append("        let autoUpdateInterval = null;\n");
                js.append("        let lastPointsHash = null;\n");
                js.append("        let lastShopItemsHash = null;\n");
                js.append("        let isAutoUpdateEnabled = true;\n");
                js.append("        \n");

                js.append("        // 启动自动检测更新\n");
                js.append("        function startAutoUpdate() {\n");
                js.append("            if (autoUpdateInterval) {\n");
                js.append("                clearInterval(autoUpdateInterval);\n");
                js.append("            }\n");
                js.append("            \n");
                js.append("            // 每1秒检测一次数据变化\n");
                js.append("            autoUpdateInterval = setInterval(() => {\n");
                js.append("                if (isAutoUpdateEnabled && currentUsername) {\n");
                js.append("                    checkForUpdates();\n");
                js.append("                }\n");
                js.append("            }, 1000);\n");
                js.append("        }\n");
                js.append("        \n");

                js.append("        // 停止自动检测更新\n");
                js.append("        function stopAutoUpdate() {\n");
                js.append("            if (autoUpdateInterval) {\n");
                js.append("                clearInterval(autoUpdateInterval);\n");
                js.append("                autoUpdateInterval = null;\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");

                js.append("        // 检测数据更新\n");
                js.append("        function checkForUpdates() {\n");
                js.append("            // 检测积分变化\n");
                js.append("            checkPointsUpdate();\n");
                js.append("            \n");
                js.append("            // 检测商店物品变化\n");
                js.append("            checkShopItemsUpdate();\n");
                js.append("        }\n");
                js.append("        \n");

                js.append("        // 检测积分更新\n");
                js.append("        function checkPointsUpdate() {\n");
                js.append("            if (!currentUsername) return;\n");
                js.append("            \n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_user_points',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "',\n");
                js.append("                    username: currentUsername\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    const currentHash = JSON.stringify(data.points);\n");
                js.append("                    if (lastPointsHash && lastPointsHash !== currentHash) {\n");
                js.append("                        // 积分发生变化，更新显示\n");
                js.append("                        const pointsElement = document.getElementById('userPoints');\n");
                js.append("                        if (pointsElement) {\n");
                js.append("                            pointsElement.textContent = data.points + ' 积分';\n");
                js.append("                            // 添加闪烁效果提示更新\n");
                js.append("                            pointsElement.style.animation = 'pointsUpdate 0.5s ease-in-out';\n");
                js.append("                            setTimeout(() => {\n");
                js.append("                                pointsElement.style.animation = '';\n");
                js.append("                            }, 500);\n");
                js.append("                        }\n");
                js.append("                        \n");
                js.append("                        // 更新当前积分变量\n");
                js.append("                        userPoints = data.points;\n");
                js.append("                        \n");
                js.append("                        // 重新渲染商店物品以更新购买按钮状态\n");
                js.append("                        updateShopItemsDisplay();\n");
                js.append("                        \n");
                js.append("                        // 显示积分更新提示\n");
                js.append("                        showSuccessToast('💰 积分已更新: ' + data.points + ' 积分');\n");
                js.append("                    }\n");
                js.append("                    lastPointsHash = currentHash;\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                // 静默处理错误，避免干扰用户体验\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("        \n");

                js.append("        // 检测商店物品更新\n");
                js.append("        function checkShopItemsUpdate() {\n");
                js.append("            fetch('/api', {\n");
                js.append("                method: 'POST',\n");
                js.append("                headers: { 'Content-Type': 'application/json' },\n");
                js.append("                body: JSON.stringify({\n");
                js.append("                    action: 'get_shop_items',\n");
                js.append("                    api_key: '"
                                + plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE") + "'\n");
                js.append("                })\n");
                js.append("            })\n");
                js.append("            .then(response => response.json())\n");
                js.append("            .then(data => {\n");
                js.append("                if (data.success) {\n");
                js.append("                    const currentHash = JSON.stringify(data.items);\n");
                js.append("                    if (lastShopItemsHash && lastShopItemsHash !== currentHash) {\n");
                js.append("                        // 商店物品发生变化，重新加载\n");
                js.append("                        shopItems = data.items || [];\n");
                js.append("                        filteredItems = shopItems.slice();\n");
                js.append("                        currentPage = 1;\n");
                js.append("                        updateShopItemsDisplay();\n");
                js.append("                        \n");
                js.append("                        // 显示更新提示\n");
                js.append("                        showSuccessToast('🔄 商店物品已更新');\n");
                js.append("                    }\n");
                js.append("                    lastShopItemsHash = currentHash;\n");
                js.append("                }\n");
                js.append("            })\n");
                js.append("            .catch(error => {\n");
                js.append("                // 静默处理错误，避免干扰用户体验\n");
                js.append("            });\n");
                js.append("        }\n");
                js.append("        \n");

                js.append("        // 页面可见性变化时控制自动检测\n");
                js.append("        document.addEventListener('visibilitychange', function() {\n");
                js.append("            if (document.hidden) {\n");
                js.append("                // 页面隐藏时停止自动检测\n");
                js.append("                stopAutoUpdate();\n");
                js.append("            } else {\n");
                js.append("                // 页面显示时重新启动自动检测\n");
                js.append("                if (currentUsername) {\n");
                js.append("                    startAutoUpdate();\n");
                js.append("                }\n");
                js.append("            }\n");
                js.append("        });\n");
                js.append("        \n");

                js.append("        // 页面卸载时停止自动检测\n");
                js.append("        window.addEventListener('beforeunload', function() {\n");
                js.append("            stopAutoUpdate();\n");
                js.append("        });\n");
                js.append("        \n");

                return js.toString();
        }

        /**
         * 生成移动端响应式CSS
         */
        private String generateMobileResponsiveCSS() {
                return "\n/* ==================== 移动端响应式设计 ==================== */\n" +
                                "@media (max-width: 768px) {\n" +
                                "    .container { padding: 15px 10px; }\n" +
                                "    \n" +
                                "    /* 头部区域 */\n" +
                                "    .header { padding: 20px 15px; }\n" +
                                "    .header h1 { font-size: 1.8em; }\n" +
                                "    .user-info { flex-direction: column; gap: 15px; text-align: center; }\n" +
                                "    .points-display { font-size: 1.1em; }\n" +
                                "    .refresh-btn { padding: 10px 20px; font-size: 0.9em; }\n" +
                                "    \n" +
                                "    /* 绑定界面 */\n" +
                                "    .bind-container { padding: 20px 15px; }\n" +
                                "    .bind-card { padding: 25px 20px; }\n" +
                                "    .bind-title { font-size: 1.5em; }\n" +
                                "    .bind-input { font-size: 16px; padding: 12px 15px; }\n" +
                                "    .bind-btn { padding: 12px 25px; font-size: 16px; }\n" +
                                "    \n" +
                                "    /* 商店物品网格 */\n" +
                                "    .shop-items { \n" +
                                "        grid-template-columns: 1fr !important; \n" +
                                "        gap: 15px; \n" +
                                "        padding: 0 5px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 商店物品卡片 */\n" +
                                "    .shop-item { \n" +
                                "        padding: 15px; \n" +
                                "        margin: 0;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .item-header { \n" +
                                "        flex-direction: column; \n" +
                                "        text-align: center; \n" +
                                "        gap: 10px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .item-icon { font-size: 2.5em; }\n" +
                                "    .item-name { font-size: 1.2em; }\n" +
                                "    .item-cost { font-size: 1.1em; }\n" +
                                "    .item-description { font-size: 0.95em; text-align: center; }\n" +
                                "    \n" +
                                "    /* 元数据行 */\n" +
                                "    .meta-row { \n" +
                                "        flex-direction: column; \n" +
                                "        gap: 8px; \n" +
                                "        text-align: center;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .item-stock, .item-limit { font-size: 0.9em; }\n" +
                                "    .reset-timer { font-size: 0.85em; }\n" +
                                "    \n" +
                                "    /* 购买按钮 */\n" +
                                "    .purchase-btn { \n" +
                                "        padding: 15px; \n" +
                                "        font-size: 16px; \n" +
                                "        margin-top: 15px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 购买模态框 */\n" +
                                "    .purchase-modal-content { \n" +
                                "        width: 95%; \n" +
                                "        max-width: none; \n" +
                                "        padding: 20px 15px;\n" +
                                "        margin: 10px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .purchase-modal-header h2 { font-size: 1.3em; }\n" +
                                "    \n" +
                                "    .item-preview { \n" +
                                "        flex-direction: column; \n" +
                                "        text-align: center; \n" +
                                "        gap: 15px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .item-preview .item-icon { font-size: 3em; }\n" +
                                "    .item-preview .item-name { font-size: 1.2em; }\n" +
                                "    \n" +
                                "    /* 数量选择器 */\n" +
                                "    .quantity-controls { \n" +
                                "        justify-content: center; \n" +
                                "        flex-wrap: wrap;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .quantity-presets { \n" +
                                "        justify-content: center; \n" +
                                "        flex-wrap: wrap;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 模态框按钮 */\n" +
                                "    .purchase-modal-footer { \n" +
                                "        flex-direction: column; \n" +
                                "        gap: 10px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .cancel-btn, .confirm-btn { \n" +
                                "        width: 100%; \n" +
                                "        padding: 12px;\n" +
                                "        font-size: 16px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 分页控件 */\n" +
                                "    .pagination { \n" +
                                "        flex-wrap: wrap; \n" +
                                "        gap: 5px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .page-btn, .page-number { \n" +
                                "        min-width: 40px; \n" +
                                "        padding: 8px 12px; \n" +
                                "        font-size: 14px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    .pagination-info { \n" +
                                "        flex-direction: column; \n" +
                                "        gap: 10px; \n" +
                                "        text-align: center;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 返回按钮 */\n" +
                                "    .back-btn { \n" +
                                "        padding: 12px 25px; \n" +
                                "        font-size: 16px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 消息提示 */\n" +
                                "    .success-message, .error-toast { \n" +
                                "        right: 10px; \n" +
                                "        left: 10px; \n" +
                                "        width: auto;\n" +
                                "        font-size: 14px;\n" +
                                "    }\n" +
                                "    \n" +
                                "    /* 加载和错误消息 */\n" +
                                "    .loading, .error-message { \n" +
                                "        padding: 30px 20px; \n" +
                                "        font-size: 1.1em;\n" +
                                "    }\n" +
                                "}\n" +
                                "\n" +
                                "/* 小屏幕设备优化 */\n" +
                                "@media (max-width: 480px) {\n" +
                                "    .container { padding: 10px 5px; }\n" +
                                "    \n" +
                                "    .header { padding: 15px 10px; }\n" +
                                "    .header h1 { font-size: 1.6em; }\n" +
                                "    \n" +
                                "    .shop-items { gap: 10px; }\n" +
                                "    .shop-item { padding: 12px; }\n" +
                                "    \n" +
                                "    .item-icon { font-size: 2em !important; }\n" +
                                "    .item-name { font-size: 1.1em; }\n" +
                                "    .item-cost { font-size: 1em; }\n" +
                                "    \n" +
                                "    .purchase-btn { padding: 12px; font-size: 15px; }\n" +
                                "    \n" +
                                "    .purchase-modal-content { padding: 15px 10px; }\n" +
                                "    .item-preview .item-icon { font-size: 2.5em !important; }\n" +
                                "}\n";
        }
}
