package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 奖品管理页面生成器 - 与卡密管理页面保持一致的样式
 */
public class RewardPageGenerator {

        private final AceKeySystem plugin;
        private final WebServer webServer;

        public RewardPageGenerator(AceKeySystem plugin, WebServer webServer) {
                this.plugin = plugin;
                this.webServer = webServer;
        }

        /**
         * 生成奖品管理页面HTML
         */
        public String generateRewardPage() {
                StringBuilder html = new StringBuilder();

                html.append("            <div class=\"page-header\">\n");
                html.append("                <h1>🎁 奖品管理</h1>\n");
                html.append("                <p>配置抽奖奖品和概率设置</p>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 操作工具栏 -->\n");
                html.append("            <div class=\"toolbar\">\n");
                html.append("                <div class=\"toolbar-left\">\n");
                html.append("                    <div class=\"form-group inline\">\n");
                html.append(
                                "                        <button onclick=\"addNewReward()\" class=\"btn btn-primary\">+ 新增奖品</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"toolbar-right\">\n");
                html.append(
                                "                    <span class=\"key-count\">总计: <strong id=\"totalRewardsCount\">0</strong> 个奖品</span>\n");
                html.append("                    <div class=\"sort-controls\">\n");
                html.append("                        <label for=\"rewardSortBy\">排序:</label>\n");
                html.append(
                                "                        <select id=\"rewardSortBy\" class=\"form-input small\" onchange=\"sortRewards()\">\n");
                html.append("                            <option value=\"name\">按名称</option>\n");
                html.append("                            <option value=\"probability\">按概率</option>\n");
                html.append("                            <option value=\"id\">按ID</option>\n");
                html.append("                        </select>\n");
                html.append(
                                "                        <select id=\"rewardSortOrder\" class=\"form-input small\" onchange=\"sortRewards()\">\n");
                html.append("                            <option value=\"asc\">升序</option>\n");
                html.append("                            <option value=\"desc\">降序</option>\n");
                html.append("                        </select>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"sort-controls\">\n");
                html.append("                        <label for=\"rewardPageSize\">每页:</label>\n");
                html.append(
                                "                        <select id=\"rewardPageSize\" class=\"form-input small\" onchange=\"updateRewardsPerPage()\">\n");
                html.append("                            <option value=\"10\">10条</option>\n");
                html.append("                            <option value=\"20\">20条</option>\n");
                html.append("                            <option value=\"50\">50条</option>\n");
                html.append("                            <option value=\"100\">100条</option>\n");
                html.append("                        </select>\n");
                html.append("                    </div>\n");
                html.append(
                                "                    <button onclick=\"selectAllRewards()\" class=\"btn btn-secondary\" id=\"selectAllRewardsBtn\">全选</button>\n");
                html.append(
                                "                    <button onclick=\"batchDeleteRewards()\" class=\"btn btn-danger\" id=\"deleteSelectedRewardsBtn\" disabled>🗑️ 删除选中</button>\n");
                html.append("                    <button onclick=\"refreshRewards()\" class=\"btn btn-info\">🔄 刷新</button>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 奖品列表 -->\n");
                html.append("            <div class=\"content-card\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <h3>奖品列表</h3>\n");
                html.append("                    <div class=\"search-box\">\n");
                html.append(
                                "                        <input type=\"text\" id=\"rewardSearchInput\" placeholder=\"搜索奖品名称、描述或ID...\" class=\"form-input\" onkeyup=\"filterRewards()\">\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 全选复选框 -->\n");
                html.append("                <div class=\"select-all-container\" style=\"margin-bottom: 15px;\">\n");
                html.append("                    <label class=\"checkbox-label\">\n");
                html.append("                        <input type=\"checkbox\" id=\"selectAllCheckbox\" onchange=\"toggleSelectAll()\">\n");
                html.append("                        <span>全选当前页</span>\n");
                html.append("                    </label>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <div class=\"shop-cards-container\" id=\"rewardContainer\">\n");
                html.append("                    <div class=\"loading-spinner\">正在加载奖品列表...</div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 分页控件 -->\n");
                html.append(
                                "                <div class=\"pagination-container\" id=\"rewardPaginationContainer\" style=\"display: none;\">\n");
                html.append("                    <div class=\"pagination\">\n");
                html.append(
                                "                        <button class=\"page-btn\" id=\"rewardFirstPageBtn\" onclick=\"goToRewardPage(1)\" title=\"首页\">⏮️</button>\n");
                html.append(
                                "                        <button class=\"page-btn\" id=\"rewardPrevPageBtn\" onclick=\"goToPrevRewardPage()\" title=\"上一页\">⬅️</button>\n");
                html.append("                        <div class=\"page-numbers\" id=\"rewardPageNumbers\"></div>\n");
                html.append(
                                "                        <button class=\"page-btn\" id=\"rewardNextPageBtn\" onclick=\"goToNextRewardPage()\" title=\"下一页\">➡️</button>\n");
                html.append(
                                "                        <button class=\"page-btn\" id=\"rewardLastPageBtn\" onclick=\"goToLastRewardPage()\" title=\"末页\">⏭️</button>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"pagination-info\">\n");
                html.append("                        <span id=\"rewardPaginationInfo\">第 1 页，共 1 页</span>\n");
                html.append(
                                "                        <span class=\"items-info\">共 <span id=\"totalRewardsDisplay\">0</span> 个奖品</span>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                // 添加编辑弹窗
                html.append(generateEditModal());

                return html.toString();
        }

        /**
         * 生成编辑奖品弹窗HTML
         */
        private String generateEditModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 编辑奖品弹窗 -->\n");
                html.append("            <div id=\"editRewardModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3 id=\"editRewardModalTitle\">编辑奖品</h3>\n");
                html.append(
                                "                        <span class=\"close\" onclick=\"closeEditRewardModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"rewardForm\" onsubmit=\"saveReward(event)\">\n");
                html.append("                            <input type=\"hidden\" id=\"rewardId\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"rewardName\">奖品名称</label>\n");
                html.append("                                <div class=\"input-with-icon\">\n");
                html.append(
                                "                                    <input type=\"text\" id=\"rewardName\" placeholder=\"例如：💎 钻石剑\" required class=\"form-input\">\n");
                html.append(
                                "                                    <button type=\"button\" class=\"icon-btn\" onclick=\"showIconSelector('rewardName')\" title=\"选择图标插入到名称\">🎨</button>\n");
                html.append("                                </div>\n");
                html.append("                                <small>奖品名称可以包含emoji图标</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"rewardDescription\">奖品描述</label>\n");
                html.append(
                                "                                <input type=\"text\" id=\"rewardDescription\" placeholder=\"锋利的钻石剑，战斗必备\" required class=\"form-input\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"rewardProbability\">中奖概率 (%)</label>\n");
                html.append(
                                "                                <input type=\"number\" id=\"rewardProbability\" placeholder=\"10\" min=\"0\" max=\"100\" step=\"0.01\" required class=\"form-input\">\n");
                html.append("                                <small>输入0-100之间的数值，支持小数</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"rewardCommands\">执行指令</label>\n");
                html.append(
                                "                                <textarea id=\"rewardCommands\" rows=\"6\" placeholder=\"give {player} diamond_sword 1&#10;tellraw {player} {&quot;text&quot;:&quot;恭喜获得钻石剑！&quot;,&quot;color&quot;:&quot;green&quot;}\" required class=\"form-input\"></textarea>\n");
                html.append("                                <small>使用 {player} 来代表玩家名称，每行一个指令</small>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"form-actions\">\n");
                html.append(
                                "                        <button type=\"button\" onclick=\"closeEditRewardModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append(
                                "                        <button type=\"button\" onclick=\"document.getElementById('rewardForm').dispatchEvent(new Event('submit', {cancelable: true, bubbles: true}))\" class=\"btn btn-primary\">保存</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                // 图标选择器弹窗已在AdminHandler中定义

                return html.toString();
        }



        /**
         * 生成奖品管理相关的JavaScript代码
         */
        public String generateRewardJavaScript() {
                StringBuilder js = new StringBuilder();

                js.append("// 奖品管理相关函数\n");
                js.append("let allRewards = [];\n");
                js.append("let filteredRewards = [];\n");
                js.append("let currentEditingReward = null;\n");
                js.append("let selectedRewards = new Set();\n");
                js.append("\n");
                js.append("// 奖品分页相关变量\n");
                js.append("let rewardCurrentPage = 1;\n");
                js.append("let rewardTotalPages = 1;\n");
                js.append("let rewardsPerPage = 10; // 每页显示10个奖品\n");
                js.append("\n");

                // 添加加载奖品函数
                js.append(generateLoadRewardsFunction());

                // 添加显示奖品函数
                js.append(generateDisplayRewardsFunction());

                // 添加其他管理函数
                js.append(generateRewardManagementFunctions());

                return js.toString();
        }

        /**
         * 生成加载奖品的JavaScript函数
         */
        private String generateLoadRewardsFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 加载奖品列表\n");
                js.append("function loadRewards() {\n");
                js.append("    const container = document.getElementById('rewardContainer');\n");
                js.append("    if (!container) return;\n");
                js.append("    \n");
                js.append("    container.innerHTML = '<div class=\"loading-spinner\">正在加载奖品列表...</div>';\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_lottery_rewards',\n");
                js.append("            api_key: '").append(webServer.getAdminKey()).append("'\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            allRewards = data.rewards || [];\n");
                js.append("            filteredRewards = allRewards.slice(); // 复制所有奖品\n");
                js.append("            rewardCurrentPage = 1; // 重置到第一页\n");
                js.append("            updateRewardPagination();\n");
                js.append("            displayRewards();\n");
                js.append("            updateRewardsCount();\n");
                js.append("            // 初始化按钮状态\n");
                js.append("            updateSelectionDisplay();\n");
                js.append("        } else {\n");
                js.append(
                                "            container.innerHTML = '<div class=\"error-message\">加载失败: ' + data.message + '</div>';\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        container.innerHTML = '<div class=\"error-message\">网络错误: ' + error.message + '</div>';\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成显示奖品的JavaScript函数
         */
        private String generateDisplayRewardsFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 显示奖品列表\n");
                js.append("function displayRewards() {\n");
                js.append("    const container = document.getElementById('rewardContainer');\n");
                js.append("    if (!container) return;\n");
                js.append("    \n");
                js.append("    if (filteredRewards.length === 0) {\n");
                js.append("        container.innerHTML = '<div class=\"no-items\">暂无奖品配置，点击上方按钮添加奖品</div>';\n");
                js.append("        document.getElementById('rewardPaginationContainer').style.display = 'none';\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 计算分页\n");
                js.append("    const startIndex = (rewardCurrentPage - 1) * rewardsPerPage;\n");
                js.append("    const endIndex = startIndex + rewardsPerPage;\n");
                js.append("    const pageRewards = filteredRewards.slice(startIndex, endIndex);\n");
                js.append("    \n");
                js.append("    let html = '';\n");
                js.append("    \n");

                js.append("    pageRewards.forEach(reward => {\n");
                js.append("        const itemClasses = ['shop-card'];\n");
                js.append("        if (selectedRewards.has(reward.id)) itemClasses.push('selected');\n");
                js.append("        html += '<div class=\"' + itemClasses.join(' ') + '\" data-reward-id=\"' + reward.id + '\">';\n");
                js.append("        html += '  <div class=\"card-header\">';\n");
                js.append("        html += '    <input type=\"checkbox\" class=\"card-checkbox\" value=\"' + reward.id + '\" onchange=\"updateSelection()\" ' + (selectedRewards.has(reward.id) ? 'checked' : '') + '>';\n");
                js.append("        html += '    <div class=\"card-actions\">';\n");
                js.append("        html += '      <button class=\"action-btn edit\" onclick=\"editReward(\\'' + reward.id + '\\')\" title=\"编辑\">✏️</button>';\n");
                js.append("        html += '      <button class=\"action-btn delete\" onclick=\"deleteReward(\\'' + reward.id + '\\')\" title=\"删除\">🗑️</button>';\n");
                js.append("        html += '    </div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '  <div class=\"card-content\">';\n");
                js.append("        html += '    <div class=\"item-icon\">🎁</div>';\n");
                js.append("        html += '    <div class=\"item-info\">';\n");
                js.append("        html += '      <h3 class=\"item-name\">' + reward.name + '</h3>';\n");
                js.append("        html += '      <div class=\"item-cost\">' + reward.probability + '% 概率</div>';\n");
                js.append("        html += '    </div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '  <div class=\"card-body\">';\n");
                js.append("        html += '    <div class=\"item-description\">' + reward.description + '</div>';\n");
                js.append("        html += '    <div class=\"item-details\">';\n");
                js.append("        html += '      <div class=\"detail-row\">';\n");
                js.append("        html += '        <span class=\"detail-label\">指令数量:</span>';\n");
                js.append("        html += '        <span class=\"detail-value\">' + (reward.commands ? reward.commands.length : 0) + ' 条</span>';\n");
                js.append("        html += '      </div>';\n");
                js.append("        html += '    </div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '  <div class=\"card-footer\">';\n");
                js.append("        html += '    <div class=\"status-indicator status-enabled\">奖品</div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '</div>';\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    container.innerHTML = html;\n");
                js.append("    \n");
                js.append("    // 显示分页控件\n");
                js.append("    if (filteredRewards.length > rewardsPerPage) {\n");
                js.append("        document.getElementById('rewardPaginationContainer').style.display = 'block';\n");
                js.append("        updateRewardPaginationInfo();\n");
                js.append("    } else {\n");
                js.append("        document.getElementById('rewardPaginationContainer').style.display = 'none';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 更新选择状态\n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成奖品管理相关的JavaScript函数
         */
        private String generateRewardManagementFunctions() {
                StringBuilder js = new StringBuilder();

                // 更新奖品数量
                js.append("// 更新奖品数量\n");
                js.append("function updateRewardsCount() {\n");
                js.append("    const countElement = document.getElementById('totalRewardsCount');\n");
                js.append("    if (countElement) {\n");
                js.append("        countElement.textContent = allRewards.length;\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                // 刷新奖品
                js.append("// 刷新奖品列表\n");
                js.append("function refreshRewards() {\n");
                js.append("    loadRewards();\n");
                js.append("}\n");
                js.append("\n");

                // 新增奖品
                js.append("// 新增奖品\n");
                js.append("function addNewReward() {\n");
                js.append("    currentEditingReward = null;\n");
                js.append("    document.getElementById('editRewardModalTitle').textContent = '新增奖品';\n");
                js.append("    document.getElementById('rewardForm').reset();\n");
                js.append("    document.getElementById('rewardId').value = '';\n");
                js.append("    document.getElementById('editRewardModal').style.display = 'block';\n");
                js.append("}\n");
                js.append("\n");

                // 编辑奖品
                js.append("// 编辑奖品\n");
                js.append("function editReward(rewardId) {\n");
                js.append("    const reward = allRewards.find(r => r.id === rewardId);\n");
                js.append("    if (!reward) return;\n");
                js.append("    \n");
                js.append("    currentEditingReward = reward;\n");
                js.append("    document.getElementById('editRewardModalTitle').textContent = '编辑奖品';\n");
                js.append("    document.getElementById('rewardId').value = reward.id;\n");
                js.append("    document.getElementById('rewardName').value = reward.name;\n");
                js.append("    document.getElementById('rewardDescription').value = reward.description;\n");
                js.append("    document.getElementById('rewardProbability').value = reward.probability;\n");
                js.append(
                                "    document.getElementById('rewardCommands').value = reward.commands ? reward.commands.join('\\n') : '';\n");
                js.append("    document.getElementById('editRewardModal').style.display = 'block';\n");
                js.append("}\n");
                js.append("\n");

                // 关闭编辑弹窗
                js.append("// 关闭编辑弹窗\n");
                js.append("function closeEditRewardModal() {\n");
                js.append("    document.getElementById('editRewardModal').style.display = 'none';\n");
                js.append("}\n");
                js.append("\n");

                // 保存奖品
                js.append("// 保存奖品\n");
                js.append("function saveReward(event) {\n");
                js.append("    event.preventDefault();\n");
                js.append("    \n");
                js.append("    const rewardData = {\n");
                js.append("        action: 'save_lottery_reward',\n");
                js.append("        api_key: '").append(webServer.getAdminKey()).append("',\n");
                js.append("        id: document.getElementById('rewardId').value.trim(),\n");
                js.append("        name: document.getElementById('rewardName').value.trim(),\n");
                js.append("        description: document.getElementById('rewardDescription').value.trim(),\n");
                js.append("        probability: parseFloat(document.getElementById('rewardProbability').value),\n");
                js.append(
                                "        commands: document.getElementById('rewardCommands').value.trim().split('\\n').filter(cmd => cmd.trim() !== '')\n");
                js.append("    };\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify(rewardData)\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            showResult('✅ 奖品保存成功！', 'success');\n");
                js.append("            closeEditRewardModal();\n");
                js.append("            loadRewards();\n");
                js.append("        } else {\n");
                js.append("            showResult('❌ 保存失败: ' + data.message, 'error');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                // 删除奖品
                js.append("// 删除奖品\n");
                js.append("function deleteReward(rewardId) {\n");
                js.append("    const reward = allRewards.find(r => r.id === rewardId);\n");
                js.append("    const rewardName = reward ? reward.name : rewardId;\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '🗑️ 删除奖品',\n");
                js.append("        `确定要删除奖品 <strong>\"${rewardName}\"</strong> 吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可撤销！</span>`,\n");
                js.append("        () => {\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'delete_lottery_reward',\n");
                js.append("            api_key: '").append(webServer.getAdminKey()).append("',\n");
                js.append("            reward_id: rewardId\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            showResult('✅ 奖品删除成功！', 'success');\n");
                js.append("            loadRewards();\n");
                js.append("        } else {\n");
                js.append("            showResult('❌ 删除失败: ' + data.message, 'error');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("    });\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                // 添加分页、排序、搜索和批量操作函数
                js.append(generateRewardPaginationFunctions());
                js.append(generateRewardSortingFunctions());
                js.append(generateRewardSearchFunctions());
                js.append(generateRewardBatchOperationFunctions());

                return js.toString();
        }

        /**
         * 生成奖品分页相关的JavaScript函数
         */
        private String generateRewardPaginationFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 奖品分页相关函数\n");
                js.append("function updateRewardPagination() {\n");
                js.append("    rewardTotalPages = Math.ceil(filteredRewards.length / rewardsPerPage);\n");
                js.append("    \n");
                js.append("    // 确保当前页在有效范围内\n");
                js.append("    if (rewardCurrentPage > rewardTotalPages) {\n");
                js.append("        rewardCurrentPage = Math.max(1, rewardTotalPages);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    updateRewardPaginationButtons();\n");
                js.append("    updateRewardPaginationInfo();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updateRewardPaginationButtons() {\n");
                js.append("    const firstBtn = document.getElementById('rewardFirstPageBtn');\n");
                js.append("    const prevBtn = document.getElementById('rewardPrevPageBtn');\n");
                js.append("    const nextBtn = document.getElementById('rewardNextPageBtn');\n");
                js.append("    const lastBtn = document.getElementById('rewardLastPageBtn');\n");
                js.append("    const pageNumbers = document.getElementById('rewardPageNumbers');\n");
                js.append("    \n");
                js.append("    if (!firstBtn) return;\n");
                js.append("    \n");
                js.append("    // 更新导航按钮状态\n");
                js.append("    firstBtn.disabled = rewardCurrentPage === 1;\n");
                js.append("    prevBtn.disabled = rewardCurrentPage === 1;\n");
                js.append("    nextBtn.disabled = rewardCurrentPage === rewardTotalPages;\n");
                js.append("    lastBtn.disabled = rewardCurrentPage === rewardTotalPages;\n");
                js.append("    \n");
                js.append("    // 生成页码按钮\n");
                js.append("    let pageNumbersHtml = '';\n");
                js.append("    const maxVisiblePages = 5;\n");
                js.append("    let startPage = Math.max(1, rewardCurrentPage - Math.floor(maxVisiblePages / 2));\n");
                js.append("    let endPage = Math.min(rewardTotalPages, startPage + maxVisiblePages - 1);\n");
                js.append("    \n");
                js.append("    // 调整起始页\n");
                js.append("    if (endPage - startPage + 1 < maxVisiblePages) {\n");
                js.append("        startPage = Math.max(1, endPage - maxVisiblePages + 1);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    for (let i = startPage; i <= endPage; i++) {\n");
                js.append("        const activeClass = i === rewardCurrentPage ? ' active' : '';\n");
                js.append(
                                "        pageNumbersHtml += `<span class=\"page-number${activeClass}\" onclick=\"goToRewardPage(${i})\">${i}</span>`;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    pageNumbers.innerHTML = pageNumbersHtml;\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updateRewardPaginationInfo() {\n");
                js.append("    const paginationInfo = document.getElementById('rewardPaginationInfo');\n");
                js.append("    const totalRewardsDisplay = document.getElementById('totalRewardsDisplay');\n");
                js.append("    \n");
                js.append("    if (paginationInfo) {\n");
                js.append("        paginationInfo.textContent = `第 ${rewardCurrentPage} 页，共 ${rewardTotalPages} 页`;\n");
                js.append("    }\n");
                js.append("    if (totalRewardsDisplay) {\n");
                js.append("        totalRewardsDisplay.textContent = filteredRewards.length;\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToRewardPage(page) {\n");
                js.append("    if (page >= 1 && page <= rewardTotalPages && page !== rewardCurrentPage) {\n");
                js.append("        rewardCurrentPage = page;\n");
                js.append("        updateRewardPaginationButtons();\n");
                js.append("        displayRewards();\n");
                js.append("        \n");
                js.append("        // 滚动到奖品区域顶部\n");
                js.append("        const container = document.getElementById('rewardContainer');\n");
                js.append("        if (container) {\n");
                js.append("            container.scrollIntoView({ behavior: 'smooth' });\n");
                js.append("        }\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToPrevRewardPage() {\n");
                js.append("    if (rewardCurrentPage > 1) {\n");
                js.append("        goToRewardPage(rewardCurrentPage - 1);\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToNextRewardPage() {\n");
                js.append("    if (rewardCurrentPage < rewardTotalPages) {\n");
                js.append("        goToRewardPage(rewardCurrentPage + 1);\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToLastRewardPage() {\n");
                js.append("    if (rewardTotalPages > 0) {\n");
                js.append("        goToRewardPage(rewardTotalPages);\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updateRewardsPerPage() {\n");
                js.append("    const pageSize = document.getElementById('rewardPageSize').value;\n");
                js.append("    rewardsPerPage = parseInt(pageSize);\n");
                js.append("    rewardCurrentPage = 1; // 重置到第一页\n");
                js.append("    updateRewardPagination();\n");
                js.append("    displayRewards();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成奖品排序相关的JavaScript函数
         */
        private String generateRewardSortingFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 奖品排序相关函数\n");
                js.append("function sortRewards() {\n");
                js.append("    const sortBy = document.getElementById('rewardSortBy').value;\n");
                js.append("    const sortOrder = document.getElementById('rewardSortOrder').value;\n");
                js.append("    \n");
                js.append("    filteredRewards.sort((a, b) => {\n");
                js.append("        let valueA, valueB;\n");
                js.append("        \n");
                js.append("        switch (sortBy) {\n");
                js.append("            case 'name':\n");
                js.append("                valueA = a.name.toLowerCase();\n");
                js.append("                valueB = b.name.toLowerCase();\n");
                js.append("                break;\n");
                js.append("            case 'probability':\n");
                js.append("                valueA = a.probability;\n");
                js.append("                valueB = b.probability;\n");
                js.append("                break;\n");
                js.append("            case 'id':\n");
                js.append("            default:\n");
                js.append("                valueA = a.id;\n");
                js.append("                valueB = b.id;\n");
                js.append("                break;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        if (sortOrder === 'desc') {\n");
                js.append("            return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;\n");
                js.append("        } else {\n");
                js.append("            return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    rewardCurrentPage = 1; // 重置到第一页\n");
                js.append("    updateRewardPagination();\n");
                js.append("    displayRewards();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成奖品搜索相关的JavaScript函数
         */
        private String generateRewardSearchFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 奖品搜索相关函数\n");
                js.append("function filterRewards() {\n");
                js.append("    const searchTerm = document.getElementById('rewardSearchInput').value.toLowerCase();\n");
                js.append("    \n");
                js.append("    if (searchTerm === '') {\n");
                js.append("        filteredRewards = allRewards.slice(); // 显示所有奖品\n");
                js.append("    } else {\n");
                js.append("        filteredRewards = allRewards.filter(reward => {\n");
                js.append("            return reward.name.toLowerCase().includes(searchTerm) ||\n");
                js.append("                   reward.description.toLowerCase().includes(searchTerm) ||\n");
                js.append("                   reward.id.toLowerCase().includes(searchTerm);\n");
                js.append("        });\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 应用当前排序\n");
                js.append("    sortRewards();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成奖品批量操作相关的JavaScript函数
         */
        private String generateRewardBatchOperationFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 奖品批量操作相关函数\n");
                js.append("function updateSelection() {\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    selectedRewards.clear();\n");
                js.append("    \n");
                js.append("    checkboxes.forEach(checkbox => {\n");
                js.append("        if (checkbox.checked) {\n");
                js.append("            selectedRewards.add(checkbox.value);\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updateSelectionDisplay() {\n");
                js.append("    const selectAllCheckbox = document.getElementById('selectAllCheckbox');\n");
                js.append("    const selectAllBtn = document.getElementById('selectAllRewardsBtn');\n");
                js.append("    const deleteSelectedBtn = document.getElementById('deleteSelectedRewardsBtn');\n");
                js.append("    \n");
                js.append("    // 更新删除选中按钮状态\n");
                js.append("    if (deleteSelectedBtn) {\n");
                js.append("        deleteSelectedBtn.disabled = selectedRewards.size === 0;\n");
                js.append("        deleteSelectedBtn.textContent = '🗑️ 删除选中 (' + selectedRewards.size + ')';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 更新全选按钮文本\n");
                js.append("    if (selectAllBtn) {\n");
                js.append("        const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("        const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;\n");
                js.append("        selectAllBtn.textContent = checkedCount === checkboxes.length && checkboxes.length > 0 ? '取消全选' : '全选';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 更新全选复选框状态\n");
                js.append("    if (selectAllCheckbox) {\n");
                js.append("        const currentPageCheckboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("        const checkedCount = Array.from(currentPageCheckboxes).filter(cb => cb.checked).length;\n");
                js.append("        selectAllCheckbox.checked = currentPageCheckboxes.length > 0 && checkedCount === currentPageCheckboxes.length;\n");
                js.append("        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < currentPageCheckboxes.length;\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function toggleSelectAll() {\n");
                js.append("    const selectAllCheckbox = document.getElementById('selectAllCheckbox');\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    \n");
                js.append("    checkboxes.forEach(checkbox => {\n");
                js.append("        checkbox.checked = selectAllCheckbox.checked;\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    updateSelection();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function clearSelection() {\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    const selectAllCheckbox = document.getElementById('selectAllCheckbox');\n");
                js.append("    \n");
                js.append("    checkboxes.forEach(checkbox => {\n");
                js.append("        checkbox.checked = false;\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    if (selectAllCheckbox) {\n");
                js.append("        selectAllCheckbox.checked = false;\n");
                js.append("        selectAllCheckbox.indeterminate = false;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    selectedRewards.clear();\n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function selectAllRewards() {\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    const selectAllBtn = document.getElementById('selectAllRewardsBtn');\n");
                js.append("    \n");
                js.append("    if (selectAllBtn.textContent === '全选') {\n");
                js.append("        checkboxes.forEach(checkbox => {\n");
                js.append("            checkbox.checked = true;\n");
                js.append("            selectedRewards.add(checkbox.value);\n");
                js.append("        });\n");
                js.append("        selectAllBtn.textContent = '取消全选';\n");
                js.append("    } else {\n");
                js.append("        checkboxes.forEach(checkbox => {\n");
                js.append("            checkbox.checked = false;\n");
                js.append("        });\n");
                js.append("        selectedRewards.clear();\n");
                js.append("        selectAllBtn.textContent = '全选';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function batchDeleteRewards() {\n");
                js.append("    if (selectedRewards.size === 0) {\n");
                js.append("        showResult('请先选择要删除的奖品', 'info');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '🗑️ 批量删除奖品',\n");
                js.append("        `确定要删除选中的 <strong>${selectedRewards.size}</strong> 个奖品吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可撤销！</span>`,\n");
                js.append("        () => {\n");
                js.append("    \n");
                js.append("    const rewardIds = Array.from(selectedRewards);\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'batch_delete_rewards',\n");
                js.append("            api_key: '").append(webServer.getAdminKey()).append("',\n");
                js.append("            reward_ids: rewardIds\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            showResult('✅ ' + data.message + '<br>删除了 ' + data.deleted_count + ' 个奖品', 'success');\n");
                js.append("            selectedRewards.clear();\n");
                js.append("            loadRewards();\n");
                js.append("        } else {\n");
                js.append("            showResult('❌ ' + data.message, 'error');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("    });\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                // 图标选择器函数已在AdminHandler中定义

                return js.toString();
        }
}
