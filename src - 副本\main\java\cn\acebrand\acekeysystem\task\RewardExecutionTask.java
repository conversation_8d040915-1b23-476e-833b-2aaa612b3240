package cn.acebrand.acekeysystem.task;

import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 奖励执行任务
 * 在主线程中执行奖励命令
 */
public final class RewardExecutionTask extends BukkitRunnable {

    private final AceKeySystem plugin;
    private final String command;
    private final String playerName;

    /**
     * 构造函数
     *
     * @param plugin     插件实例
     * @param command    要执行的命令
     * @param playerName 目标玩家名称
     */
    public RewardExecutionTask(AceKeySystem plugin, String command, String playerName) {
        this.plugin = plugin;
        this.command = command;
        this.playerName = playerName;
    }

    @Override
    public void run() {
        try {
            // 在主线程中执行控制台命令
            boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), this.command);

            if (success) {
                this.plugin.getLogger().info("成功为玩家 " + this.playerName + " 执行奖励命令: " + this.command);
            } else {
                this.plugin.getLogger().warning("为玩家 " + this.playerName + " 执行奖励命令失败: " + this.command);
            }
        } catch (Exception e) {
            this.plugin.getLogger().severe("执行奖励命令时发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
