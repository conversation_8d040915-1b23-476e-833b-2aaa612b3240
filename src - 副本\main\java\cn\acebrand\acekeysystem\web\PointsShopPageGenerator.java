package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 积分商店页面生成器
 * 负责生成积分商店管理页面的HTML和JavaScript代码
 */
public class PointsShopPageGenerator {

        private final AceKeySystem plugin;
        private final WebServer webServer;

        public PointsShopPageGenerator(AceKeySystem plugin, WebServer webServer) {
                this.plugin = plugin;
                this.webServer = webServer;
        }

        /**
         * 生成积分商店管理页面HTML
         */
        public String generatePointsShopPage() {
                StringBuilder html = new StringBuilder();

                html.append("            <div class=\"page-header\">\n");
                html.append("                <h1>🛒 积分商店管理</h1>\n");
                html.append("                <p>管理积分商店物品和兑换设置</p>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 操作工具栏 -->\n");
                html.append("            <div class=\"toolbar\">\n");
                html.append("                <div class=\"toolbar-left\">\n");
                html.append("                    <div class=\"form-group inline\">\n");
                html.append("                        <button onclick=\"addNewShopItem()\" class=\"btn btn-primary\">+ 新增物品</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                <div class=\"toolbar-right\">\n");
                html.append("                    <span class=\"key-count\">总计: <strong id=\"totalItemsCount\">0</strong> 个商品</span>\n");
                html.append("                    <div class=\"sort-controls\">\n");
                html.append("                        <label for=\"sortBy\">排序:</label>\n");
                html.append("                        <select id=\"sortBy\" class=\"form-input small\" onchange=\"sortShopItems()\">\n");
                html.append("                            <option value=\"name\">按名称</option>\n");
                html.append("                            <option value=\"cost\">按积分</option>\n");
                html.append("                            <option value=\"stock\">按库存</option>\n");
                html.append("                            <option value=\"enabled\">按状态</option>\n");
                html.append("                            <option value=\"id\">按ID</option>\n");
                html.append("                        </select>\n");
                html.append("                        <select id=\"sortOrder\" class=\"form-input small\" onchange=\"sortShopItems()\">\n");
                html.append("                            <option value=\"asc\">升序</option>\n");
                html.append("                            <option value=\"desc\">降序</option>\n");
                html.append("                        </select>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"sort-controls\">\n");
                html.append("                        <label for=\"pageSize\">每页:</label>\n");
                html.append("                        <select id=\"pageSize\" class=\"form-input small\" onchange=\"updateItemsPerPage()\">\n");
                html.append("                            <option value=\"10\">10条</option>\n");
                html.append("                            <option value=\"20\">20条</option>\n");
                html.append("                            <option value=\"50\">50条</option>\n");
                html.append("                            <option value=\"100\">100条</option>\n");
                html.append("                        </select>\n");
                html.append("                    </div>\n");
                html.append("                    <button onclick=\"selectAllShopItems()\" class=\"btn btn-secondary\" id=\"selectAllBtn\">全选</button>\n");
                html.append("                    <button onclick=\"batchDeleteShopItems()\" class=\"btn btn-danger\" id=\"deleteSelectedBtn\" disabled>🗑️ 删除选中</button>\n");
                html.append("                    <button onclick=\"refreshShopItems()\" class=\"btn btn-info\">🔄 刷新</button>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 商店物品列表 -->\n");
                html.append("            <div class=\"content-card\">\n");
                html.append("                <div class=\"card-header\">\n");
                html.append("                    <h3>商店物品列表</h3>\n");
                html.append("                    <div class=\"search-box\">\n");
                html.append("                        <input type=\"text\" id=\"shopSearchInput\" placeholder=\"搜索物品名称、描述或ID...\" class=\"form-input\" onkeyup=\"filterShopItems()\">\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 全选复选框 -->\n");
                html.append("                <div class=\"select-all-container\" style=\"margin-bottom: 15px;\">\n");
                html.append("                    <label class=\"checkbox-label\">\n");
                html.append("                        <input type=\"checkbox\" id=\"selectAllCheckbox\" onchange=\"toggleSelectAll()\">\n");
                html.append("                        <span>全选当前页</span>\n");
                html.append("                    </label>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <div class=\"shop-cards-container\" id=\"shopItemsContainer\">\n");
                html.append("                    <div class=\"loading-spinner\">正在加载商店物品...</div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 分页控件 -->\n");
                html.append("                <div class=\"pagination-container\" id=\"paginationContainer\" style=\"display: none;\">\n");
                html.append("                    <div class=\"pagination\">\n");
                html.append("                        <button class=\"page-btn\" id=\"firstPageBtn\" onclick=\"goToPage(1)\" title=\"首页\">⏮️</button>\n");
                html.append("                        <button class=\"page-btn\" id=\"prevPageBtn\" onclick=\"goToPrevPage()\" title=\"上一页\">⬅️</button>\n");
                html.append("                        <div class=\"page-numbers\" id=\"pageNumbers\"></div>\n");
                html.append("                        <button class=\"page-btn\" id=\"nextPageBtn\" onclick=\"goToNextPage()\" title=\"下一页\">➡️</button>\n");
                html.append("                        <button class=\"page-btn\" id=\"lastPageBtn\" onclick=\"goToLastPage()\" title=\"末页\">⏭️</button>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"pagination-info\">\n");
                html.append("                        <span id=\"paginationInfo\">第 1 页，共 1 页</span>\n");
                html.append("                        <span class=\"items-info\">共 <span id=\"totalItemsDisplay\">0</span> 个商品</span>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                // 添加编辑弹窗
                html.append(generateEditModal());

                // 添加图标选择器弹窗
                html.append(generateIconSelectorModal());

                return html.toString();
        }

        /**
         * 生成编辑商店物品弹窗HTML
         */
        private String generateEditModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 编辑商店物品弹窗 -->\n");
                html.append("            <div id=\"editShopItemModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3 id=\"editShopItemModalTitle\">编辑商店物品</h3>\n");
                html.append(
                                "                        <span class=\"close\" onclick=\"closeEditShopItemModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"shopItemForm\" onsubmit=\"saveShopItem(event)\">\n");
                html.append("                            <input type=\"hidden\" id=\"shopItemId\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemName\">物品名称</label>\n");
                html.append("                                <div class=\"input-with-icon\">\n");
                html.append(
                                "                                    <input type=\"text\" id=\"shopItemName\" placeholder=\"例如：💎 钻石剑\" required class=\"form-input\">\n");
                html.append(
                                "                                    <button type=\"button\" class=\"icon-btn\" onclick=\"showIconSelector('shopItemName')\" title=\"选择图标插入到名称\">🎨</button>\n");
                html.append("                                </div>\n");
                html.append("                                <small>物品名称可以包含emoji图标</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemDescription\">物品描述</label>\n");
                html.append(
                                "                                <input type=\"text\" id=\"shopItemDescription\" placeholder=\"锋利的钻石剑，战斗必备\" required class=\"form-input\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemCost\">积分消耗</label>\n");
                html.append(
                                "                                <input type=\"number\" id=\"shopItemCost\" placeholder=\"100\" min=\"1\" required class=\"form-input\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemIcon\">物品图标</label>\n");
                html.append("                                <div class=\"input-with-icon\">\n");
                html.append(
                                "                                    <input type=\"text\" id=\"shopItemIcon\" placeholder=\"💎\" maxlength=\"2\" class=\"form-input\">\n");
                html.append(
                                "                                    <button type=\"button\" class=\"icon-btn\" onclick=\"showIconSelector('shopItemIcon')\" title=\"选择图标\">🎨</button>\n");
                html.append("                                </div>\n");
                html.append("                                <small>输入一个emoji图标或点击按钮选择</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemStock\">库存数量</label>\n");
                html.append(
                                "                                <input type=\"number\" id=\"shopItemStock\" placeholder=\"-1\" min=\"-1\" class=\"form-input\">\n");
                html.append("                                <small>-1表示无限库存</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemMaxPurchase\">每人限购</label>\n");
                html.append(
                                "                                <input type=\"number\" id=\"shopItemMaxPurchase\" placeholder=\"-1\" min=\"-1\" class=\"form-input\">\n");
                html.append("                                <small>-1表示无限制</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemResetInterval\">限购重置时间（小时）</label>\n");
                html.append(
                                "                                <input type=\"number\" id=\"shopItemResetInterval\" placeholder=\"-1\" min=\"-1\" class=\"form-input\">\n");
                html.append("                                <small>-1表示永不重置，24表示每24小时重置一次</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"shopItemCommands\">执行指令</label>\n");
                html.append(
                                "                                <textarea id=\"shopItemCommands\" rows=\"6\" placeholder=\"give {player} diamond_sword 1&#10;tellraw {player} {&quot;text&quot;:&quot;恭喜获得钻石剑！&quot;,&quot;color&quot;:&quot;green&quot;}\" required class=\"form-input\"></textarea>\n");
                html.append("                                <small>使用 {player} 来代表玩家名称，每行一个指令</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>\n");
                html.append("                                    <input type=\"checkbox\" id=\"shopItemEnabled\" checked>\n");
                html.append("                                    启用此物品\n");
                html.append("                                </label>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"form-actions\">\n");
                html.append(
                                "                        <button type=\"button\" onclick=\"closeEditShopItemModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append(
                                "                        <button type=\"button\" onclick=\"document.getElementById('shopItemForm').dispatchEvent(new Event('submit', {cancelable: true, bubbles: true}))\" class=\"btn btn-primary\">保存</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                return html.toString();
        }

        /**
         * 生成图标选择器弹窗HTML
         */
        private String generateIconSelectorModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 图标选择弹窗 -->\n");
                html.append("            <div id=\"iconSelectorModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content icon-modal\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>选择图标</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeIconSelector()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <div class=\"icon-categories\">\n");
                html.append(
                                "                            <button class=\"category-btn active\" onclick=\"showIconCategory('all')\">全部</button>\n");
                html.append(
                                "                            <button class=\"category-btn\" onclick=\"showIconCategory('trophy')\">奖杯</button>\n");
                html.append(
                                "                            <button class=\"category-btn\" onclick=\"showIconCategory('gem')\">宝石</button>\n");
                html.append(
                                "                            <button class=\"category-btn\" onclick=\"showIconCategory('tool')\">工具</button>\n");
                html.append(
                                "                            <button class=\"category-btn\" onclick=\"showIconCategory('food')\">食物</button>\n");
                html.append(
                                "                            <button class=\"category-btn\" onclick=\"showIconCategory('other')\">其他</button>\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"icon-grid\" id=\"iconGrid\">\n");
                html.append("                            <!-- 图标将通过JavaScript动态生成 -->\n");
                html.append("                        </div>\n");
                html.append("                        <div class=\"icon-help\">\n");
                html.append("                            <p>💡 点击图标即可选择并插入到输入框</p>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");

                return html.toString();
        }

        /**
         * 生成积分商店管理相关的JavaScript代码
         */
        public String generatePointsShopJavaScript() {
                StringBuilder js = new StringBuilder();

                js.append("// 积分商店管理相关函数\n");
                js.append("let shopItems = [];\n");
                js.append("let filteredItems = [];\n");
                js.append("let currentEditingItem = null;\n");
                js.append("let selectedItems = new Set();\n");
                js.append("\n");
                js.append("// 积分商店分页相关变量\n");
                js.append("let shopCurrentPage = 1;\n");
                js.append("let shopTotalPages = 1;\n");
                js.append("let shopItemsPerPage = 10; // 每页显示10个商品\n");
                js.append("\n");
                js.append("// 页面加载时初始化积分商店\n");
                js.append("document.addEventListener('DOMContentLoaded', function() {\n");
                js.append("    const urlPage = new URLSearchParams(window.location.search).get('page');\n");
                js.append("    if (urlPage === 'points-shop') {\n");
                js.append("        loadShopItems();\n");
                js.append("    }\n");
                js.append("});\n");
                js.append("\n");

                // 添加加载商店物品函数
                js.append(generateLoadShopItemsFunction());

                // 添加显示商店物品函数
                js.append(generateDisplayShopItemsFunction());

                // 添加其他管理函数
                js.append(generateShopManagementFunctions());

                return js.toString();
        }

        /**
         * 生成加载商店物品的JavaScript函数
         */
        private String generateLoadShopItemsFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 加载商店物品\n");
                js.append("function loadShopItems() {\n");
                js.append("    const container = document.getElementById('shopItemsContainer');\n");
                js.append("    if (!container) return;\n");
                js.append("    \n");
                js.append("    container.innerHTML = '<div class=\"loading-spinner\">正在加载商店物品...</div>';\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'get_admin_shop_items',\n");
                js.append("            api_key: '").append(webServer.getAdminKey()).append("'\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            shopItems = data.items || [];\n");
                js.append("            filteredItems = shopItems.slice(); // 复制所有商品\n");
                js.append("            shopCurrentPage = 1; // 重置到第一页\n");
                js.append("            updatePagination();\n");
                js.append("            displayShopItems();\n");
                js.append("            updateShopItemsCount();\n");
                js.append("        } else {\n");
                js.append(
                                "            container.innerHTML = '<div class=\"error-message\">加载失败: ' + data.message + '</div>';\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        container.innerHTML = '<div class=\"error-message\">网络错误: ' + error.message + '</div>';\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成显示商店物品的JavaScript函数
         */
        private String generateDisplayShopItemsFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 显示商店物品\n");
                js.append("function displayShopItems() {\n");
                js.append("    const container = document.getElementById('shopItemsContainer');\n");
                js.append("    if (!container) return;\n");
                js.append("    \n");
                js.append("    if (filteredItems.length === 0) {\n");
                js.append("        container.innerHTML = '<div class=\"no-items\">暂无商店物品，点击上方按钮添加物品</div>';\n");
                js.append("        document.getElementById('paginationContainer').style.display = 'none';\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 计算分页\n");
                js.append("    const startIndex = (shopCurrentPage - 1) * shopItemsPerPage;\n");
                js.append("    const endIndex = startIndex + shopItemsPerPage;\n");
                js.append("    const pageItems = filteredItems.slice(startIndex, endIndex);\n");
                js.append("    \n");
                js.append("    let html = '';\n");
                js.append("    \n");
                js.append("    pageItems.forEach(item => {\n");
                js.append("        const statusClass = item.enabled ? 'enabled' : 'disabled';\n");
                js.append("        const statusText = item.enabled ? '启用' : '禁用';\n");
                js.append("        const stockText = item.stock === -1 ? '无限' : item.stock;\n");
                js.append(
                                "        const maxPurchaseText = item.maxPurchasePerPlayer === -1 ? '无限制' : item.maxPurchasePerPlayer + '次';\n");
                js.append(
                                "        const resetIntervalText = item.resetIntervalHours === -1 ? '永不重置' : item.resetIntervalHours + '小时';\n");
                js.append("        \n");
                js.append("        const itemClasses = ['shop-card'];\n");
                js.append("        if (selectedItems.has(item.id)) itemClasses.push('selected');\n");
                js.append("        if (!item.enabled) itemClasses.push('disabled');\n");
                js.append("        html += '<div class=\"' + itemClasses.join(' ') + '\" data-item-id=\"' + item.id + '\">';\n");
                js.append("        html += '  <div class=\"card-header\">';\n");
                js.append("        html += '    <input type=\"checkbox\" class=\"card-checkbox\" value=\"' + item.id + '\" onchange=\"updateSelection()\" ' + (selectedItems.has(item.id) ? 'checked' : '') + '>';\n");
                js.append("        html += '    <div class=\"card-actions\">';\n");
                js.append(
                                "        html += '      <button class=\"action-btn ' + (item.enabled ? 'disable' : 'enable') + '\" onclick=\"toggleShopItemStatus(\\'' + item.id + '\\')\" title=\"' + (item.enabled ? '禁用' : '启用') + '\">' + (item.enabled ? '🚫' : '✅') + '</button>';\n");
                js.append(
                                "        html += '      <button class=\"action-btn edit\" onclick=\"editShopItem(\\'' + item.id + '\\')\" title=\"编辑\">✏️</button>';\n");
                js.append(
                                "        html += '      <button class=\"action-btn delete\" onclick=\"deleteShopItem(\\'' + item.id + '\\')\" title=\"删除\">🗑️</button>';\n");
                js.append("        html += '    </div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '  <div class=\"card-content\">';\n");
                js.append("        html += '    <div class=\"item-icon\">' + (item.icon || '📦') + '</div>';\n");
                js.append("        html += '    <div class=\"item-info\">';\n");
                js.append("        html += '      <h3 class=\"item-name\">' + item.name + '</h3>';\n");
                js.append("        html += '      <div class=\"item-cost\">' + item.cost + ' 积分</div>';\n");
                js.append("        html += '    </div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '  <div class=\"card-body\">';\n");
                js.append("        html += '    <div class=\"item-description\">' + item.description + '</div>';\n");
                js.append("        html += '    <div class=\"item-details\">';\n");
                js.append("        html += '      <div class=\"detail-row\">';\n");
                js.append("        html += '        <span class=\"detail-label\">库存:</span>';\n");
                js.append("        html += '        <span class=\"detail-value\">' + stockText + '</span>';\n");
                js.append("        html += '      </div>';\n");
                js.append("        html += '      <div class=\"detail-row\">';\n");
                js.append("        html += '        <span class=\"detail-label\">限购:</span>';\n");
                js.append("        html += '        <span class=\"detail-value\">' + maxPurchaseText + '</span>';\n");
                js.append("        html += '      </div>';\n");
                js.append("        html += '    </div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '  <div class=\"card-footer\">';\n");
                js.append("        html += '    <div class=\"status-indicator status-' + statusClass + '\">' + statusText + '</div>';\n");
                js.append("        html += '  </div>';\n");
                js.append("        html += '</div>';\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    container.innerHTML = html;\n");
                js.append("    \n");
                js.append("    // 显示分页控件\n");
                js.append("    if (filteredItems.length > shopItemsPerPage) {\n");
                js.append("        document.getElementById('paginationContainer').style.display = 'block';\n");
                js.append("        updatePaginationInfo();\n");
                js.append("    } else {\n");
                js.append("        document.getElementById('paginationContainer').style.display = 'none';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 更新选择状态\n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成商店管理相关的JavaScript函数
         */
        private String generateShopManagementFunctions() {
                StringBuilder js = new StringBuilder();

                // 更新商店物品数量
                js.append("// 更新商店物品数量\n");
                js.append("function updateShopItemsCount() {\n");
                js.append("    const countElement = document.getElementById('totalItemsCount');\n");
                js.append("    if (countElement) {\n");
                js.append("        countElement.textContent = shopItems.length;\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                // 刷新商店物品
                js.append("// 刷新商店物品\n");
                js.append("function refreshShopItems() {\n");
                js.append("    loadShopItems();\n");
                js.append("}\n");
                js.append("\n");

                js.append("// 更新每页显示数量\n");
                js.append("function updateItemsPerPage() {\n");
                js.append("    const pageSize = document.getElementById('pageSize').value;\n");
                js.append("    shopItemsPerPage = parseInt(pageSize);\n");
                js.append("    shopCurrentPage = 1; // 重置到第一页\n");
                js.append("    updatePagination();\n");
                js.append("    displayShopItems();\n");
                js.append("}\n");
                js.append("\n");

                js.append("// 全选商品\n");
                js.append("function selectAllShopItems() {\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    const selectAllBtn = document.getElementById('selectAllBtn');\n");
                js.append("    const deleteBtn = document.getElementById('deleteSelectedBtn');\n");
                js.append("    \n");
                js.append("    if (selectAllBtn.textContent === '全选') {\n");
                js.append("        checkboxes.forEach(checkbox => {\n");
                js.append("            checkbox.checked = true;\n");
                js.append("            selectedItems.add(checkbox.value);\n");
                js.append("        });\n");
                js.append("        selectAllBtn.textContent = '取消全选';\n");
                js.append("        deleteBtn.disabled = false;\n");
                js.append("    } else {\n");
                js.append("        checkboxes.forEach(checkbox => {\n");
                js.append("            checkbox.checked = false;\n");
                js.append("        });\n");
                js.append("        selectedItems.clear();\n");
                js.append("        selectAllBtn.textContent = '全选';\n");
                js.append("        deleteBtn.disabled = true;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                js.append("// 批量删除商品\n");
                js.append("function batchDeleteShopItems() {\n");
                js.append("    if (selectedItems.size === 0) {\n");
                js.append("        showResult('请先选择要删除的商品', 'info');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '🗑️ 批量删除商品',\n");
                js.append("        `确定要删除选中的 <strong>${selectedItems.size}</strong> 个商品吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可撤销！</span>`,\n");
                js.append("        () => {\n");
                js.append("    \n");
                js.append("    const itemIds = Array.from(selectedItems);\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'batch_delete_shop_items',\n");
                js.append("            api_key: '").append(webServer.getAdminKey()).append("',\n");
                js.append("            item_ids: itemIds\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            showResult('✅ ' + data.message + '<br>删除了 ' + data.deleted_count + ' 个商品', 'success');\n");
                js.append("            selectedItems.clear();\n");
                js.append("            loadShopItems();\n");
                js.append("        } else {\n");
                js.append("            showResult('❌ ' + data.message, 'error');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("    });\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                // 新增商店物品
                js.append("// 新增商店物品\n");
                js.append("function addNewShopItem() {\n");
                js.append("    currentEditingItem = null;\n");
                js.append("    document.getElementById('editShopItemModalTitle').textContent = '新增商店物品';\n");
                js.append("    document.getElementById('shopItemForm').reset();\n");
                js.append("    document.getElementById('shopItemId').value = '';\n");
                js.append("    document.getElementById('shopItemEnabled').checked = true;\n");
                js.append("    document.getElementById('editShopItemModal').style.display = 'block';\n");
                js.append("}\n");
                js.append("\n");

                // 使用JavaScript生成器生成其他函数
                PointsShopJSGenerator jsGenerator = new PointsShopJSGenerator(webServer.getAdminKey());

                // 编辑商店物品
                js.append(jsGenerator.generateEditShopItemFunction());

                // 删除商店物品
                js.append(jsGenerator.generateDeleteShopItemFunction());

                // 切换状态
                js.append(jsGenerator.generateToggleStatusFunction());

                // 保存商店物品
                js.append(jsGenerator.generateSaveShopItemFunction());

                // 其他辅助函数
                js.append(jsGenerator.generateUtilityFunctions());

                // 添加分页、排序、搜索和批量操作函数
                js.append(generatePaginationFunctions());
                js.append(generateSortingFunctions());
                js.append(generateSearchFunctions());
                js.append(generateBatchOperationFunctions());

                return js.toString();
        }

        /**
         * 生成分页相关的JavaScript函数
         */
        private String generatePaginationFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 分页相关函数\n");
                js.append("function updatePagination() {\n");
                js.append("    shopTotalPages = Math.ceil(filteredItems.length / shopItemsPerPage);\n");
                js.append("    \n");
                js.append("    // 确保当前页在有效范围内\n");
                js.append("    if (shopCurrentPage > shopTotalPages) {\n");
                js.append("        shopCurrentPage = Math.max(1, shopTotalPages);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    updatePaginationButtons();\n");
                js.append("    updatePaginationInfo();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updatePaginationButtons() {\n");
                js.append("    const firstBtn = document.getElementById('firstPageBtn');\n");
                js.append("    const prevBtn = document.getElementById('prevPageBtn');\n");
                js.append("    const nextBtn = document.getElementById('nextPageBtn');\n");
                js.append("    const lastBtn = document.getElementById('lastPageBtn');\n");
                js.append("    const pageNumbers = document.getElementById('pageNumbers');\n");
                js.append("    \n");
                js.append("    if (!firstBtn) return;\n");
                js.append("    \n");
                js.append("    // 更新导航按钮状态\n");
                js.append("    firstBtn.disabled = shopCurrentPage === 1;\n");
                js.append("    prevBtn.disabled = shopCurrentPage === 1;\n");
                js.append("    nextBtn.disabled = shopCurrentPage === shopTotalPages;\n");
                js.append("    lastBtn.disabled = shopCurrentPage === shopTotalPages;\n");
                js.append("    \n");
                js.append("    // 生成页码按钮\n");
                js.append("    let pageNumbersHtml = '';\n");
                js.append("    const maxVisiblePages = 5;\n");
                js.append("    let startPage = Math.max(1, shopCurrentPage - Math.floor(maxVisiblePages / 2));\n");
                js.append("    let endPage = Math.min(shopTotalPages, startPage + maxVisiblePages - 1);\n");
                js.append("    \n");
                js.append("    // 调整起始页\n");
                js.append("    if (endPage - startPage + 1 < maxVisiblePages) {\n");
                js.append("        startPage = Math.max(1, endPage - maxVisiblePages + 1);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    for (let i = startPage; i <= endPage; i++) {\n");
                js.append("        const activeClass = i === shopCurrentPage ? ' active' : '';\n");
                js.append("        pageNumbersHtml += `<span class=\"page-number${activeClass}\" onclick=\"goToPage(${i})\">${i}</span>`;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    pageNumbers.innerHTML = pageNumbersHtml;\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updatePaginationInfo() {\n");
                js.append("    const paginationInfo = document.getElementById('paginationInfo');\n");
                js.append("    const totalItemsDisplay = document.getElementById('totalItemsDisplay');\n");
                js.append("    \n");
                js.append("    if (paginationInfo) {\n");
                js.append("        paginationInfo.textContent = `第 ${shopCurrentPage} 页，共 ${shopTotalPages} 页`;\n");
                js.append("    }\n");
                js.append("    if (totalItemsDisplay) {\n");
                js.append("        totalItemsDisplay.textContent = filteredItems.length;\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToPage(page) {\n");
                js.append("    if (page >= 1 && page <= shopTotalPages && page !== shopCurrentPage) {\n");
                js.append("        shopCurrentPage = page;\n");
                js.append("        updatePaginationButtons();\n");
                js.append("        displayShopItems();\n");
                js.append("        \n");
                js.append("        // 滚动到商品区域顶部\n");
                js.append("        const container = document.getElementById('shopItemsContainer');\n");
                js.append("        if (container) {\n");
                js.append("            container.scrollIntoView({ behavior: 'smooth' });\n");
                js.append("        }\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToPrevPage() {\n");
                js.append("    if (shopCurrentPage > 1) {\n");
                js.append("        goToPage(shopCurrentPage - 1);\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToNextPage() {\n");
                js.append("    if (shopCurrentPage < shopTotalPages) {\n");
                js.append("        goToPage(shopCurrentPage + 1);\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function goToLastPage() {\n");
                js.append("    if (shopTotalPages > 0) {\n");
                js.append("        goToPage(shopTotalPages);\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成排序相关的JavaScript函数
         */
        private String generateSortingFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 排序相关函数\n");
                js.append("function sortShopItems() {\n");
                js.append("    const sortBy = document.getElementById('sortBy').value;\n");
                js.append("    const sortOrder = document.getElementById('sortOrder').value;\n");
                js.append("    \n");
                js.append("    filteredItems.sort((a, b) => {\n");
                js.append("        let valueA, valueB;\n");
                js.append("        \n");
                js.append("        switch (sortBy) {\n");
                js.append("            case 'name':\n");
                js.append("                valueA = a.name.toLowerCase();\n");
                js.append("                valueB = b.name.toLowerCase();\n");
                js.append("                break;\n");
                js.append("            case 'cost':\n");
                js.append("                valueA = a.cost;\n");
                js.append("                valueB = b.cost;\n");
                js.append("                break;\n");
                js.append("            case 'stock':\n");
                js.append("                valueA = a.stock === -1 ? 999999 : a.stock;\n");
                js.append("                valueB = b.stock === -1 ? 999999 : b.stock;\n");
                js.append("                break;\n");
                js.append("            case 'enabled':\n");
                js.append("                valueA = a.enabled ? 1 : 0;\n");
                js.append("                valueB = b.enabled ? 1 : 0;\n");
                js.append("                break;\n");
                js.append("            case 'id':\n");
                js.append("            default:\n");
                js.append("                valueA = a.id;\n");
                js.append("                valueB = b.id;\n");
                js.append("                break;\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        if (sortOrder === 'desc') {\n");
                js.append("            return valueA < valueB ? 1 : valueA > valueB ? -1 : 0;\n");
                js.append("        } else {\n");
                js.append("            return valueA > valueB ? 1 : valueA < valueB ? -1 : 0;\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    shopCurrentPage = 1; // 重置到第一页\n");
                js.append("    updatePagination();\n");
                js.append("    displayShopItems();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成搜索相关的JavaScript函数
         */
        private String generateSearchFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 搜索相关函数\n");
                js.append("function filterShopItems() {\n");
                js.append("    const searchTerm = document.getElementById('shopSearchInput').value.toLowerCase();\n");
                js.append("    \n");
                js.append("    if (searchTerm === '') {\n");
                js.append("        filteredItems = shopItems.slice(); // 显示所有商品\n");
                js.append("    } else {\n");
                js.append("        filteredItems = shopItems.filter(item => {\n");
                js.append("            return item.name.toLowerCase().includes(searchTerm) ||\n");
                js.append("                   item.description.toLowerCase().includes(searchTerm) ||\n");
                js.append("                   item.id.toLowerCase().includes(searchTerm);\n");
                js.append("        });\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 应用当前排序\n");
                js.append("    sortShopItems();\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成批量操作相关的JavaScript函数
         */
        private String generateBatchOperationFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 批量操作相关函数\n");
                js.append("function updateSelection() {\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    selectedItems.clear();\n");
                js.append("    \n");
                js.append("    checkboxes.forEach(checkbox => {\n");
                js.append("        if (checkbox.checked) {\n");
                js.append("            selectedItems.add(checkbox.value);\n");
                js.append("        }\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function updateSelectionDisplay() {\n");
                js.append("    const selectAllBtn = document.getElementById('selectAllBtn');\n");
                js.append("    const deleteBtn = document.getElementById('deleteSelectedBtn');\n");
                js.append("    const selectAllCheckbox = document.getElementById('selectAllCheckbox');\n");
                js.append("    \n");
                js.append("    // 更新按钮状态\n");
                js.append("    if (selectedItems.size > 0) {\n");
                js.append("        deleteBtn.disabled = false;\n");
                js.append("        selectAllBtn.textContent = '取消全选';\n");
                js.append("    } else {\n");
                js.append("        deleteBtn.disabled = true;\n");
                js.append("        selectAllBtn.textContent = '全选';\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 更新全选复选框状态\n");
                js.append("    if (selectAllCheckbox) {\n");
                js.append("        const currentPageCheckboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("        const checkedCount = Array.from(currentPageCheckboxes).filter(cb => cb.checked).length;\n");
                js.append("        selectAllCheckbox.checked = currentPageCheckboxes.length > 0 && checkedCount === currentPageCheckboxes.length;\n");
                js.append("        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < currentPageCheckboxes.length;\n");
                js.append("    }\n");
                js.append("}\n");
                js.append("\n");

                js.append("function toggleSelectAll() {\n");
                js.append("    const selectAllCheckbox = document.getElementById('selectAllCheckbox');\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    \n");
                js.append("    checkboxes.forEach(checkbox => {\n");
                js.append("        checkbox.checked = selectAllCheckbox.checked;\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    updateSelection();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function clearSelection() {\n");
                js.append("    const checkboxes = document.querySelectorAll('.card-checkbox');\n");
                js.append("    const selectAllCheckbox = document.getElementById('selectAllCheckbox');\n");
                js.append("    \n");
                js.append("    checkboxes.forEach(checkbox => {\n");
                js.append("        checkbox.checked = false;\n");
                js.append("    });\n");
                js.append("    \n");
                js.append("    if (selectAllCheckbox) {\n");
                js.append("        selectAllCheckbox.checked = false;\n");
                js.append("        selectAllCheckbox.indeterminate = false;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    selectedItems.clear();\n");
                js.append("    updateSelectionDisplay();\n");
                js.append("}\n");
                js.append("\n");

                js.append("function batchEnable() {\n");
                js.append("    if (selectedItems.size === 0) {\n");
                js.append("        showResult('请先选择要启用的商品', 'info');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '✅ 批量启用商品',\n");
                js.append("        `确定要启用选中的 <strong>${selectedItems.size}</strong> 个商品吗？`,\n");
                js.append("        () => {\n");
                js.append("            batchUpdateStatus(true);\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                js.append("function batchDisable() {\n");
                js.append("    if (selectedItems.size === 0) {\n");
                js.append("        showResult('请先选择要禁用的商品', 'info');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '❌ 批量禁用商品',\n");
                js.append("        `确定要禁用选中的 <strong>${selectedItems.size}</strong> 个商品吗？`,\n");
                js.append("        () => {\n");
                js.append("            batchUpdateStatus(false);\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                js.append("function batchDelete() {\n");
                js.append("    if (selectedItems.size === 0) {\n");
                js.append("        showResult('请先选择要删除的商品', 'info');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '🗑️ 批量删除商品',\n");
                js.append("        `确定要删除选中的 <strong>${selectedItems.size}</strong> 个商品吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可撤销！</span>`,\n");
                js.append("        () => {\n");
                js.append("        const itemIds = Array.from(selectedItems);\n");
                js.append("        \n");
                js.append("        fetch('/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'batch_delete_shop_items',\n");
                js.append("                api_key: '").append(webServer.getAdminKey()).append("',\n");
                js.append("                item_ids: itemIds\n");
                js.append("            })\n");
                js.append("        })\n");
                js.append("        .then(response => response.json())\n");
                js.append("        .then(data => {\n");
                js.append("            if (data.success) {\n");
                js.append("                showResult(`✅ 成功删除 ${data.deleted_count} 个商品`, 'success');\n");
                js.append("                clearSelection();\n");
                js.append("                loadShopItems();\n");
                js.append("            } else {\n");
                js.append("                showResult('❌ 批量删除失败: ' + data.message, 'error');\n");
                js.append("            }\n");
                js.append("        })\n");
                js.append("        .catch(error => {\n");
                js.append("            showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("        });\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                js.append("function batchUpdateStatus(enabled) {\n");
                js.append("    const itemIds = Array.from(selectedItems);\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify({\n");
                js.append("            action: 'batch_update_shop_status',\n");
                js.append("            api_key: '").append(webServer.getAdminKey()).append("',\n");
                js.append("            item_ids: itemIds,\n");
                js.append("            enabled: enabled\n");
                js.append("        })\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            const action = enabled ? '启用' : '禁用';\n");
                js.append("            showResult(`✅ 成功${action} ${data.updated_count} 个商品`, 'success');\n");
                js.append("            clearSelection();\n");
                js.append("            loadShopItems();\n");
                js.append("        } else {\n");
                js.append("            showResult('❌ 批量操作失败: ' + data.message, 'error');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }
}
