package cn.acebrand.acekeysystem.web;

/**
 * 统计分析页面搜索功能生成器
 * 负责生成玩家搜索和标签页切换相关的JavaScript代码
 */
public class StatisticsSearchGenerator {

    private final String adminKey;

    public StatisticsSearchGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成玩家搜索相关的JavaScript函数
     */
    public String generateSearchFunctions() {
        StringBuilder js = new StringBuilder();

        js.append("// 玩家搜索功能\n");
        js.append("function searchPlayer() {\n");
        js.append("    const playerName = document.getElementById('playerSearchInput').value.trim();\n");
        js.append("    if (!playerName) {\n");
        js.append("        showResult('请输入玩家名', 'error');\n");
        js.append("        return;\n");
        js.append("    }\n");
        js.append("    searchPlayerByName(playerName);\n");
        js.append("}\n");
        js.append("\n");

        js.append("function searchPlayerByName(playerName) {\n");
        js.append("    // 先切换到玩家搜索标签页\n");
        js.append("    showStatisticsTab('search');\n");
        js.append("    \n");
        js.append("    // 等待标签页切换完成后再执行搜索\n");
        js.append("    setTimeout(() => {\n");
        js.append("        const resultsContainer = document.getElementById('playerResults');\n");
        js.append("        if (!resultsContainer) {\n");
        js.append("            console.error('搜索结果容器未找到');\n");
        js.append("            return;\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        // 填充搜索框\n");
        js.append("        const searchInput = document.getElementById('playerSearchInput');\n");
        js.append("        if (searchInput) {\n");
        js.append("            searchInput.value = playerName;\n");
        js.append("        }\n");
        js.append("        \n");
        js.append("        resultsContainer.style.display = 'block';\n");
        js.append("        resultsContainer.innerHTML = '<div class=\"loading-spinner\">正在搜索玩家数据...</div>';\n");
        js.append("\n");
        js.append("        fetch('/api', {\n");
        js.append("            method: 'POST',\n");
        js.append("            headers: { 'Content-Type': 'application/json' },\n");
        js.append("            body: JSON.stringify({\n");
        js.append("                action: 'search_player',\n");
        js.append("                api_key: '").append(adminKey).append("',\n");
        js.append("                player_name: playerName\n");
        js.append("            })\n");
        js.append("        })\n");
        js.append("        .then(response => response.json())\n");
        js.append("        .then(data => {\n");
        js.append("            if (data.success) {\n");
        js.append("                displayPlayerSearchResults(data.player_info);\n");
        js.append("            } else {\n");
        js.append(
                "                resultsContainer.innerHTML = `<div class=\"error-message\">搜索失败: ${data.message}</div>`;\n");
        js.append("            }\n");
        js.append("        })\n");
        js.append("        .catch(error => {\n");
        js.append("            console.error('Error:', error);\n");
        js.append("            resultsContainer.innerHTML = '<div class=\"error-message\">搜索时出错</div>';\n");
        js.append("        });\n");
        js.append("    }, 200); // 等待200ms确保标签页切换完成\n");
        js.append("}\n");
        js.append("\n");

        return js.toString();
    }

    /**
     * 生成搜索结果显示函数
     */
    public String generateSearchResultsFunctions() {
        StringBuilder js = new StringBuilder();

        js.append("function displayPlayerSearchResults(playerInfo) {\n");
        js.append("    const container = document.getElementById('playerResults');\n");
        js.append("    if (!container) return;\n");
        js.append("\n");
        js.append("    if (playerInfo.total_count === 0) {\n");
        js.append("        container.innerHTML = `\n");
        js.append("            <div class=\"no-results\">\n");
        js.append("                <h4>未找到玩家 \"${playerInfo.player_name}\" 的相关记录</h4>\n");
        js.append("                <p>该玩家可能从未获得过卡密或使用过卡密。</p>\n");
        js.append("            </div>\n");
        js.append("        `;\n");
        js.append("        return;\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 保存玩家信息到全局变量，供标签页切换使用\n");
        js.append("    window.currentPlayerInfo = playerInfo;\n");
        js.append("\n");
        js.append("    let html = `\n");
        js.append("        <div class=\"player-info-header\">\n");
        js.append("            <h4>🔍 玩家 \"${playerInfo.player_name}\" 的详细信息</h4>\n");
        js.append("            <div class=\"player-stats\">\n");
        js.append("                <span class=\"stat-item\">📋 总计: ${playerInfo.total_count}</span>\n");
        js.append("                <span class=\"stat-item\">🎫 已分配: ${playerInfo.assigned_count}</span>\n");
        js.append("                <span class=\"stat-item\">❌ 已使用: ${playerInfo.used_count}</span>\n");
        js.append("                <span class=\"stat-item\">⏰ 已失效: ${playerInfo.expired_count}</span>\n");
        js.append("            </div>\n");
        js.append("        </div>\n");
        js.append("\n");
        js.append("        <!-- 玩家卡密导航标签页 -->\n");
        js.append("        <div class=\"player-keys-nav\">\n");
        js.append("            <div class=\"nav-tabs\">\n");
        js.append(
                "                <button class=\"nav-tab active\" onclick=\"showPlayerKeysTab('assigned')\">🎫 已分配 (${playerInfo.assigned_count})</button>\n");
        js.append(
                "                <button class=\"nav-tab\" onclick=\"showPlayerKeysTab('used')\">❌ 已使用 (${playerInfo.used_count})</button>\n");
        js.append(
                "                <button class=\"nav-tab\" onclick=\"showPlayerKeysTab('expired')\">⏰ 已失效 (${playerInfo.expired_count})</button>\n");
        js.append("            </div>\n");
        js.append("        </div>\n");
        js.append("\n");
        js.append("        <!-- 已分配卡密标签页 -->\n");
        js.append("        <div id=\"player-tab-assigned\" class=\"player-keys-tab-content active\">\n");
        js.append("            <div class=\"key-section\">\n");
        js.append("                <div class=\"key-list\" id=\"assignedKeysList\">\n");
        js.append("                    <!-- 已分配卡密列表将在这里显示 -->\n");
        js.append("                </div>\n");
        js.append("            </div>\n");
        js.append("        </div>\n");
        js.append("\n");
        js.append("        <!-- 已使用卡密标签页 -->\n");
        js.append("        <div id=\"player-tab-used\" class=\"player-keys-tab-content\">\n");
        js.append("            <div class=\"key-section\">\n");
        js.append("                <div class=\"key-list\" id=\"usedKeysList\">\n");
        js.append("                    <!-- 已使用卡密列表将在这里显示 -->\n");
        js.append("                </div>\n");
        js.append("            </div>\n");
        js.append("        </div>\n");
        js.append("\n");
        js.append("        <!-- 已失效卡密标签页 -->\n");
        js.append("        <div id=\"player-tab-expired\" class=\"player-keys-tab-content\">\n");
        js.append("            <div class=\"key-section\">\n");
        js.append("                <div class=\"key-list\" id=\"expiredKeysList\">\n");
        js.append("                    <!-- 已失效卡密列表将在这里显示 -->\n");
        js.append("                </div>\n");
        js.append("            </div>\n");
        js.append("        </div>\n");
        js.append("    `;\n");
        js.append("\n");
        js.append("    container.innerHTML = html;\n");
        js.append("\n");
        js.append("    // 默认显示已分配的卡密\n");
        js.append("    showPlayerKeysTab('assigned');\n");
        js.append("}\n");
        js.append("\n");

        return js.toString();
    }

    /**
     * 生成标签页切换相关的JavaScript函数
     */
    public String generateTabFunctions() {
        StringBuilder js = new StringBuilder();

        js.append("// 统计分析标签页切换功能\n");
        js.append("function showStatisticsTab(tabName) {\n");
        js.append("    // 隐藏所有标签页内容\n");
        js.append("    const allTabs = document.querySelectorAll('.statistics-tab-content');\n");
        js.append("    allTabs.forEach(tab => {\n");
        js.append("        tab.classList.remove('active');\n");
        js.append("    });\n");
        js.append("\n");
        js.append("    // 移除所有标签按钮的激活状态\n");
        js.append("    const allTabButtons = document.querySelectorAll('.statistics-nav .nav-tab');\n");
        js.append("    allTabButtons.forEach(button => {\n");
        js.append("        button.classList.remove('active');\n");
        js.append("    });\n");
        js.append("\n");
        js.append("    // 显示选中的标签页\n");
        js.append("    const selectedTab = document.getElementById('tab-' + tabName);\n");
        js.append("    if (selectedTab) {\n");
        js.append("        selectedTab.classList.add('active');\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 激活对应的标签按钮\n");
        js.append(
                "    const selectedButton = event ? event.target : document.querySelector('.statistics-nav .nav-tab');\n");
        js.append("    if (selectedButton) {\n");
        js.append("        selectedButton.classList.add('active');\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 根据标签页类型执行特定操作\n");
        js.append("    switch(tabName) {\n");
        js.append("        case 'trends':\n");
        js.append("            // 重新绘制图表（如果数据已加载）\n");
        js.append("            if (window.statisticsData && window.statisticsData.daily_usage) {\n");
        js.append(
                "                setTimeout(() => displayDailyUsageChart(window.statisticsData.daily_usage), 100);\n");
        js.append("            }\n");
        js.append("            break;\n");
        js.append("        case 'search':\n");
        js.append("            // 聚焦搜索框\n");
        js.append("            setTimeout(() => {\n");
        js.append("                const searchInput = document.getElementById('playerSearchInput');\n");
        js.append("                if (searchInput) searchInput.focus();\n");
        js.append("            }, 100);\n");
        js.append("            break;\n");
        js.append("    }\n");
        js.append("}\n");
        js.append("\n");

        return js.toString();
    }
}
