package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 管理员处罚页面CSS样式生成器
 * 负责生成管理员处罚页面相关的所有CSS样式
 * 包括基础样式、管理员专用样式、撤销弹窗样式等
 *
 * <AUTHOR>
 * @version 1.0
 */
public class AdminPunishmentCSSGenerator {

    private final AceKeySystem plugin;

    public AdminPunishmentCSSGenerator(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    /**
     * 生成完整的管理员处罚页面CSS样式
     */
    public String generateAdminPunishmentPageCSS() {
        StringBuilder css = new StringBuilder();

        css.append(generateBasicCSS());
        css.append(generateRevokeModalCSS());
        css.append(generateBackgroundCSS());

        return css.toString();
    }

    /**
     * 生成基础CSS样式
     */
    private String generateBasicCSS() {
        StringBuilder css = new StringBuilder();

        css.append(generateCSSVariables());
        css.append(generateBasicElementStyles());
        css.append(generateAdminHeaderStyles());
        css.append(generateStandardHeaderStyles());
        css.append(generatePageHeaderStyles());
        css.append(generateUtilityClasses());
        css.append(generateNavigationStyles());
        css.append(generateStatisticsStyles());
        css.append(generateSearchStyles());
        css.append(generateTableStyles());
        css.append(generateFormStyles());
        css.append(generateModalStyles());
        css.append(generateResponsiveStyles());

        return css.toString();
    }

    /**
     * 生成CSS变量定义
     */
    private String generateCSSVariables() {
        return "/* ==================== CSS 变量定义 ==================== */\n" +
                ":root {\n" +
                "    --background: 0 0% 100%;\n" +
                "    --foreground: 240 10% 3.9%;\n" +
                "    --card: 0 0% 100%;\n" +
                "    --card-foreground: 240 10% 3.9%;\n" +
                "    --muted: 240 4.8% 95.9%;\n" +
                "    --muted-foreground: 240 3.8% 46.1%;\n" +
                "    --border: 240 5.9% 90%;\n" +
                "    --input: 240 5.9% 90%;\n" +
                "    --primary: 240 5.9% 10%;\n" +
                "    --primary-foreground: 0 0% 98%;\n" +
                "    --secondary: 240 4.8% 95.9%;\n" +
                "    --secondary-foreground: 240 5.9% 10%;\n" +
                "    --accent: 240 4.8% 95.9%;\n" +
                "    --accent-foreground: 240 5.9% 10%;\n" +
                "    --destructive: 0 84.2% 60.2%;\n" +
                "    \n" +
                "    /* 管理员专用颜色变量 - 亮色模式 */\n" +
                "    --admin-header-bg: 240 100% 70%;\n" +
                "    --admin-header-shadow: 240 100% 70%;\n" +
                "    --admin-header-text: 0 0% 100%;\n" +
                "    --admin-nav-text: 0 0% 100%;\n" +
                "    --admin-nav-hover: 0 0% 100%;\n" +
                "    --admin-nav-active: 0 0% 100%;\n" +
                "    --admin-nav-shadow: 0 0% 0%;\n" +
                "    --admin-logo-filter: brightness(0) invert(1);\n" +
                "}\n" +
                "\n" +
                ".dark {\n" +
                "    --background: 240 10% 3.9%;\n" +
                "    --foreground: 0 0% 98%;\n" +
                "    --card: 240 10% 3.9%;\n" +
                "    --card-foreground: 0 0% 98%;\n" +
                "    --muted: 240 3.7% 15.9%;\n" +
                "    --muted-foreground: 240 5% 64.9%;\n" +
                "    --border: 240 3.7% 15.9%;\n" +
                "    --input: 240 3.7% 15.9%;\n" +
                "    --primary: 0 0% 98%;\n" +
                "    --primary-foreground: 240 5.9% 10%;\n" +
                "    --secondary: 240 3.7% 15.9%;\n" +
                "    --secondary-foreground: 0 0% 98%;\n" +
                "    --accent: 240 3.7% 15.9%;\n" +
                "    --accent-foreground: 0 0% 98%;\n" +
                "    --destructive: 0 62.8% 30.6%;\n" +
                "    \n" +
                "    /* 管理员专用颜色变量 - 暗色模式 */\n" +
                "    --admin-header-bg: 240 10% 3.9%;\n" +
                "    --admin-header-shadow: 240 10% 3.9%;\n" +
                "    --admin-header-text: 0 0% 98%;\n" +
                "    --admin-nav-text: 0 0% 98%;\n" +
                "    --admin-nav-hover: 0 0% 98%;\n" +
                "    --admin-nav-active: 0 0% 98%;\n" +
                "    --admin-nav-shadow: 0 0% 0%;\n" +
                "    --admin-logo-filter: brightness(0) invert(1);\n" +
                "}\n\n";
    }

    /**
     * 生成基础元素样式
     */
    private String generateBasicElementStyles() {
        return "/* ==================== 基础样式 ==================== */\n" +
                "* {\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                "body {\n" +
                "    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n" +
                "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "    min-height: 100vh;\n" +
                "    color: #333;\n" +
                "    transition: background-color 0.3s ease, color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".container {\n" +
                "    max-width: 1400px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 20px;\n" +
                "}\n\n";
    }

    /**
     * 生成管理员头部样式
     */
    private String generateAdminHeaderStyles() {
        return "/* ==================== 管理员专用样式 ==================== */\n" +
                ".admin-site-header {\n" +
                "    position: sticky;\n" +
                "    top: 0;\n" +
                "    z-index: 50;\n" +
                "    width: 100%;\n" +
                "    border-bottom: 1px solid hsl(var(--border) / 0.4);\n" +
                "    background: hsl(var(--admin-header-bg, 240 100% 70%));\n" +
                "    backdrop-filter: blur(8px);\n" +
                "    -webkit-backdrop-filter: blur(8px);\n" +
                "    box-shadow: 0 4px 20px hsl(var(--admin-header-shadow, 240 100% 70%) / 0.3);\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-header-container {\n" +
                "    display: flex;\n" +
                "    height: 3.5rem;\n" +
                "    max-width: 1400px;\n" +
                "    margin: 0 auto;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    padding: 0 20px;\n" +
                "}\n" +
                "\n" +
                ".admin-header-left {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    margin-right: 1rem;\n" +
                "}\n" +
                "\n" +
                ".admin-logo-link {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "    margin-right: 1.5rem;\n" +
                "    text-decoration: none;\n" +
                "    color: hsl(var(--admin-header-text, 0 0% 100%));\n" +
                "    font-weight: 600;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-logo-image {\n" +
                "    width: 24px;\n" +
                "    height: 24px;\n" +
                "    margin-right: 0.5rem;\n" +
                "    border: none;\n" +
                "    outline: none;\n" +
                "    box-shadow: none;\n" +
                "    background: transparent;\n" +
                "    /* 移除filter，直接显示原始logo */\n" +
                "    /* filter: var(--admin-logo-filter, brightness(0) invert(1)); */\n" +
                "    transition: filter 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-logo-text {\n" +
                "    font-weight: 700;\n" +
                "    font-size: 1.125rem;\n" +
                "    color: hsl(var(--admin-header-text, 0 0% 100%));\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-main-nav {\n" +
                "    display: none;\n" +
                "    align-items: center;\n" +
                "    gap: 1.5rem;\n" +
                "    font-size: 0.875rem;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 768px) {\n" +
                "    .admin-main-nav {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".admin-nav-link {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    color: hsl(var(--admin-nav-text, 0 0% 100%) / 0.8);\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    padding: 8px 12px;\n" +
                "    border-radius: 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-nav-link:hover {\n" +
                "    color: hsl(var(--admin-nav-text, 0 0% 100%));\n" +
                "    background: hsl(var(--admin-nav-hover, 0 0% 100%) / 0.1);\n" +
                "    transform: translateY(-1px);\n" +
                "}\n" +
                "\n" +
                ".admin-nav-link.active {\n" +
                "    color: hsl(var(--admin-nav-text, 0 0% 100%));\n" +
                "    background: hsl(var(--admin-nav-active, 0 0% 100%) / 0.2);\n" +
                "    box-shadow: 0 2px 8px hsl(var(--admin-nav-shadow, 0 0% 0%) / 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-nav-badge {\n" +
                "    display: none;\n" +
                "    padding: 0 0.25rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    border-radius: 0.375rem;\n" +
                "    margin-left: 0.25rem;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .admin-nav-badge {\n" +
                "        display: inline-flex;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".admin-badge-default {\n" +
                "    background: hsl(var(--background));\n" +
                "    color: hsl(var(--primary));\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".admin-badge-secondary {\n" +
                "    background: hsl(var(--admin-nav-hover, 0 0% 100%) / 0.3);\n" +
                "    color: hsl(var(--admin-header-text, 0 0% 100%));\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".admin-header-right {\n" +
                "    display: flex;\n" +
                "    flex: 1;\n" +
                "    justify-content: flex-end;\n" +
                "}\n" +
                "\n" +
                ".admin-header-actions {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "}\n" +
                "\n" +
                ".admin-header-search {\n" +
                "    position: relative;\n" +
                "    height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".admin-search-form-header {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".admin-search-input-wrapper {\n" +
                "    position: relative;\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".admin-header-search-input {\n" +
                "    display: flex;\n" +
                "    height: 2.5rem;\n" +
                "    width: 100%;\n" +
                "    border-radius: 0.375rem;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "    background: hsl(var(--background));\n" +
                "    padding: 0.5rem 0.75rem;\n" +
                "    padding-right: 2.5rem;\n" +
                "    font-size: 0.875rem;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    transition: all 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-header-search-input::placeholder {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".admin-header-search-input:focus {\n" +
                "    outline: none;\n" +
                "    background: hsl(var(--background));\n" +
                "    border-color: hsl(var(--primary));\n" +
                "    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-header-player-avatar {\n" +
                "    position: absolute;\n" +
                "    right: 0.25rem;\n" +
                "    top: 50%;\n" +
                "    transform: translateY(-50%);\n" +
                "    width: 2rem;\n" +
                "    height: 2rem;\n" +
                "    border-radius: 0.25rem;\n" +
                "    z-index: 10;\n" +
                "}\n" +
                "\n" +
                ".admin-theme-toggle-btn {\n" +
                "    padding: 8px;\n" +
                "    background: hsl(var(--admin-nav-hover, 0 0% 100%) / 0.1);\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "    border-radius: 8px;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 40px;\n" +
                "    height: 40px;\n" +
                "    font-size: 1.1em;\n" +
                "    color: hsl(var(--admin-header-text, 0 0% 100%));\n" +
                "}\n" +
                "\n" +
                ".admin-theme-toggle-btn:hover {\n" +
                "    background: hsl(var(--admin-nav-hover, 0 0% 100%) / 0.2);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 4px 12px hsl(var(--foreground) / 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-theme-icon {\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-theme-toggle-btn:hover .admin-theme-icon {\n" +
                "    transform: scale(1.1);\n" +
                "}\n\n";
    }

    /**
     * 生成标准头部样式
     */
    private String generateStandardHeaderStyles() {
        return "/* ==================== 顶部导航栏样式（Next-LiteBans 风格）==================== */\n" +
                ".site-header {\n" +
                "    position: sticky;\n" +
                "    top: 0;\n" +
                "    z-index: 50;\n" +
                "    width: 100%;\n" +
                "    border-bottom: 1px solid hsl(var(--border) / 0.4);\n" +
                "    background: hsl(var(--background) / 0.95);\n" +
                "    backdrop-filter: blur(8px);\n" +
                "    -webkit-backdrop-filter: blur(8px);\n" +
                "}\n" +
                "\n" +
                ".header-container {\n" +
                "    display: flex;\n" +
                "    height: 3.5rem;\n" +
                "    max-width: 1400px;\n" +
                "    margin: 0 auto;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    padding: 0 20px;\n" +
                "}\n" +
                "\n" +
                ".header-left {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    margin-right: 1rem;\n" +
                "}\n" +
                "\n" +
                ".logo-link {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "    margin-right: 1.5rem;\n" +
                "    text-decoration: none;\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".logo-image {\n" +
                "    width: 24px;\n" +
                "    height: 24px;\n" +
                "    margin-right: 0.5rem;\n" +
                "    border: none;\n" +
                "    outline: none;\n" +
                "    box-shadow: none;\n" +
                "    background: transparent;\n" +
                "}\n" +
                "\n" +
                ".logo-text {\n" +
                "    font-weight: 700;\n" +
                "    font-size: 1.125rem;\n" +
                "    display: none;\n" +
                "}\n" +
                "\n" +
                ".main-nav {\n" +
                "    display: none;\n" +
                "    align-items: center;\n" +
                "    gap: 1.5rem;\n" +
                "    font-size: 0.875rem;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 768px) {\n" +
                "    .main-nav {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".nav-link {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    color: hsl(var(--foreground) / 0.6);\n" +
                "    text-decoration: none;\n" +
                "    transition: color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".nav-link:hover {\n" +
                "    color: hsl(var(--foreground) / 0.8);\n" +
                "}\n" +
                "\n" +
                ".nav-link.active {\n" +
                "    color: hsl(var(--foreground) / 0.8);\n" +
                "}\n" +
                "\n" +
                ".nav-badge {\n" +
                "    display: none;\n" +
                "    padding: 0 0.25rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    border-radius: 0.375rem;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .nav-badge {\n" +
                "        display: inline-flex;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".badge-default {\n" +
                "    background: hsl(var(--primary));\n" +
                "    color: hsl(var(--primary-foreground));\n" +
                "}\n" +
                "\n" +
                ".header-right {\n" +
                "    display: flex;\n" +
                "    flex: 1;\n" +
                "    justify-content: flex-end;\n" +
                "}\n" +
                "\n" +
                ".header-actions {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "}\n" +
                "\n" +
                ".header-search {\n" +
                "    position: relative;\n" +
                "    height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".search-form-header {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".search-input-wrapper {\n" +
                "    position: relative;\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".header-search-input {\n" +
                "    display: flex;\n" +
                "    height: 2.5rem;\n" +
                "    width: 100%;\n" +
                "    border-radius: 0.375rem;\n" +
                "    border: 1px solid hsl(var(--input));\n" +
                "    background: hsl(var(--background));\n" +
                "    padding: 0.5rem 0.75rem;\n" +
                "    padding-right: 2.5rem;\n" +
                "    font-size: 0.875rem;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    transition: all 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".header-search-input::placeholder {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".header-search-input:focus {\n" +
                "    outline: none;\n" +
                "    ring: 2px;\n" +
                "    ring-color: hsl(var(--ring));\n" +
                "    ring-offset: 2px;\n" +
                "}\n" +
                "\n" +
                ".header-player-avatar {\n" +
                "    position: absolute;\n" +
                "    right: 0.25rem;\n" +
                "    top: 50%;\n" +
                "    transform: translateY(-50%);\n" +
                "    width: 2rem;\n" +
                "    height: 2rem;\n" +
                "    border-radius: 0.25rem;\n" +
                "    z-index: 10;\n" +
                "}\n\n";
    }

    /**
     * 生成页面头部样式
     */
    private String generatePageHeaderStyles() {
        return "/* ==================== 页面标题 ==================== */\n" +
                ".page-header {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 30px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".page-header h1 {\n" +
                "    font-size: 2.5em;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 10px;\n" +
                "}\n" +
                "\n" +
                ".page-description {\n" +
                "    font-size: 1.1em;\n" +
                "    color: #666;\n" +
                "    margin-bottom: 20px;\n" +
                "}\n" +
                "\n" +
                ".player-view-header {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n" +
                "}\n\n";
    }

    /**
     * 生成工具类样式
     */
    private String generateUtilityClasses() {
        return "/* ==================== Next-LiteBans 样式复制 ==================== */\n" +
                ".flex {\n" +
                "    display: flex;\n" +
                "}\n" +
                "\n" +
                ".h-full {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".flex-col {\n" +
                "    flex-direction: column;\n" +
                "}\n" +
                "\n" +
                ".items-center {\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".gap-4 {\n" +
                "    gap: 1rem;\n" +
                "}\n" +
                "\n" +
                ".py-8 {\n" +
                "    padding-top: 2rem;\n" +
                "    padding-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".space-y-2 > * + * {\n" +
                "    margin-top: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".mx-auto {\n" +
                "    margin-left: auto;\n" +
                "    margin-right: auto;\n" +
                "}\n" +
                "\n" +
                ".space-y-1 > * + * {\n" +
                "    margin-top: 0.25rem;\n" +
                "}\n" +
                "\n" +
                ".text-center {\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".text-4xl {\n" +
                "    font-size: 2.25rem;\n" +
                "    line-height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".font-bold {\n" +
                "    font-weight: 700;\n" +
                "}\n" +
                "\n" +
                ".leading-tight {\n" +
                "    line-height: 1.25;\n" +
                "}\n" +
                "\n" +
                ".tracking-tighter {\n" +
                "    letter-spacing: -0.05em;\n" +
                "}\n" +
                "\n" +
                ".space-x-2 > * + * {\n" +
                "    margin-left: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".whitespace-nowrap {\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".w-full {\n" +
                "    width: 100%;\n" +
                "}\n" +
                "\n" +
                ".badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    border-radius: 0.375rem;\n" +
                "    padding: 0.125rem 0.5rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    font-weight: 600;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    border: 1px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".badge-primary {\n" +
                "    background-color: hsl(210 40% 85%);\n" +
                "    color: hsl(215.4 16.3% 46.9%);\n" +
                "    border: 1px solid hsl(210 40% 70%);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".badge-primary:hover {\n" +
                "    background-color: hsl(210 40% 80%);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".badge-secondary {\n" +
                "    background-color: hsl(var(--secondary));\n" +
                "    color: hsl(var(--secondary-foreground));\n" +
                "    opacity: 0.5;\n" +
                "}\n" +
                "\n" +
                ".badge-secondary:hover {\n" +
                "    opacity: 1;\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".badge-icon {\n" +
                "    width: 1rem;\n" +
                "    height: 1rem;\n" +
                "    margin-right: 0.25rem;\n" +
                "}\n\n";
    }

    /**
     * 生成导航样式
     */
    private String generateNavigationStyles() {
        return "/* ==================== 导航样式 ==================== */\n" +
                ".nav-buttons {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 15px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".nav-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 10px 20px;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".nav-btn:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                "/* ==================== 导航标签页 ==================== */\n" +
                ".nav-tabs {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    display: flex;\n" +
                "    gap: 10px;\n" +
                "    flex-wrap: wrap;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".tab-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 12px 20px;\n" +
                "    background: #f8f9fa;\n" +
                "    color: #666;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 2px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".tab-btn:hover {\n" +
                "    background: #e9ecef;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".tab-btn.active {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n\n";
    }

    /**
     * 生成响应式样式
     */
    private String generateResponsiveStyles() {
        return "/* 响应式设计 */\n" +
                "@media (min-width: 768px) {\n" +
                "    .md\\:py-12 {\n" +
                "        padding-top: 3rem;\n" +
                "        padding-bottom: 3rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:pb-8 {\n" +
                "        padding-bottom: 2rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:flex {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:space-x-4 > * + * {\n" +
                "        margin-left: 1rem;\n" +
                "        margin-top: 0;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:w-\\[350px\\] {\n" +
                "        width: 350px;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:py-4 {\n" +
                "        padding-top: 1rem;\n" +
                "        padding-bottom: 1rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:text-left {\n" +
                "        text-align: left;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .lg\\:py-18 {\n" +
                "        padding-top: 4.5rem;\n" +
                "        padding-bottom: 4.5rem;\n" +
                "    }\n" +
                "    \n" +
                "    .lg\\:leading-\\[1\\.1\\] {\n" +
                "        line-height: 1.1;\n" +
                "    }\n" +
                "    \n" +
                "    .lg\\:w-\\[1024px\\] {\n" +
                "        width: 1024px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 640px) {\n" +
                "    .sm\\:text-5xl {\n" +
                "        font-size: 3rem;\n" +
                "        line-height: 1;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".nav-buttons {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 15px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".nav-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 10px 20px;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".nav-btn:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                "/* ==================== 导航标签页 ==================== */\n" +
                ".nav-tabs {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    display: flex;\n" +
                "    gap: 10px;\n" +
                "    flex-wrap: wrap;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".tab-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 12px 20px;\n" +
                "    background: #f8f9fa;\n" +
                "    color: #666;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 2px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".tab-btn:hover {\n" +
                "    background: #e9ecef;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".tab-btn.active {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n\n";
    }

    /**
     * 生成统计样式
     */
    private String generateStatisticsStyles() {
        return "/* ==================== 统计信息 ==================== */\n" +
                ".statistics-section {\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".stats-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n" +
                "    gap: 20px;\n" +
                "}\n" +
                "\n" +
                ".stat-card-link {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    display: block;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".stat-card-link:hover {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "}\n" +
                "\n" +
                ".stat-card {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 20px;\n" +
                "    border-left: 5px solid #667eea;\n" +
                "    transition: all 0.3s ease;\n" +
                "    cursor: pointer;\n" +
                "}\n" +
                "\n" +
                ".stat-card:hover {\n" +
                "    transform: translateY(-5px);\n" +
                "    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".stat-card-link:hover .stat-card {\n" +
                "    transform: translateY(-5px) scale(1.02);\n" +
                "    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.2);\n" +
                "}\n" +
                "\n" +
                ".stat-card.active {\n" +
                "    border-left-color: #e74c3c;\n" +
                "}\n" +
                "\n" +
                ".stat-card.today {\n" +
                "    border-left-color: #f39c12;\n" +
                "}\n" +
                "\n" +
                ".stat-icon {\n" +
                "    font-size: 2.5em;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                ".stat-info {\n" +
                "    flex: 1;\n" +
                "}\n" +
                "\n" +
                ".stat-value {\n" +
                "    font-size: 2em;\n" +
                "    font-weight: 700;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 5px;\n" +
                "}\n" +
                "\n" +
                ".stat-label {\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "    font-weight: 600;\n" +
                "}\n\n";
    }

    /**
     * 生成搜索样式
     */
    private String generateSearchStyles() {
        return "/* ==================== 搜索区域 ==================== */\n" +
                ".search-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".search-form {\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".search-input-group {\n" +
                "    display: flex;\n" +
                "    gap: 15px;\n" +
                "    align-items: center;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".search-input-container {\n" +
                "    position: relative;\n" +
                "    flex: 1;\n" +
                "    min-width: 300px;\n" +
                "}\n" +
                "\n" +
                ".search-input {\n" +
                "    width: 100%;\n" +
                "    padding: 15px 50px 15px 20px;\n" +
                "    border: 2px solid #e9ecef;\n" +
                "    border-radius: 25px;\n" +
                "    font-size: 1em;\n" +
                "    transition: all 0.3s ease;\n" +
                "    background: white;\n" +
                "}\n" +
                "\n" +
                ".search-input:hover {\n" +
                "    border-color: #d1d5db;\n" +
                "    background-color: #f9fafb;\n" +
                "}\n" +
                "\n" +
                ".player-avatar-preview {\n" +
                "    position: absolute;\n" +
                "    right: 10px;\n" +
                "    top: 50%;\n" +
                "    transform: translateY(-50%);\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    background: #f8f9fa;\n" +
                "    z-index: 10;\n" +
                "}\n" +
                "\n" +
                ".search-input:focus {\n" +
                "    outline: none;\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n" +
                "}\n" +
                "\n" +
                ".search-btn {\n" +
                "    padding: 15px 30px;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".search-btn:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                ".theme-toggle-btn {\n" +
                "    padding: 15px;\n" +
                "    background: rgba(255, 255, 255, 0.9);\n" +
                "    border: 2px solid #e9ecef;\n" +
                "    border-radius: 50%;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 50px;\n" +
                "    height: 50px;\n" +
                "    font-size: 1.2em;\n" +
                "}\n" +
                "\n" +
                ".theme-toggle-btn:hover {\n" +
                "    background: #f8f9fa;\n" +
                "    border-color: #667eea;\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\n" +
                "}\n" +
                "\n" +
                ".theme-icon {\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".theme-toggle-btn:hover .theme-icon {\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".search-result-info {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    padding: 10px 15px;\n" +
                "    background: #f8f9fa;\n" +
                "    border-radius: 10px;\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".clear-search {\n" +
                "    color: #e74c3c;\n" +
                "    text-decoration: none;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".clear-search:hover {\n" +
                "    text-decoration: underline;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 管理员统计信息区域 ==================== */\n" +
                ".admin-statistics-section {\n" +
                "    background: hsl(var(--background));\n" +
                "    border-radius: 20px;\n" +
                "    padding: 30px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 10px 40px hsl(var(--foreground) / 0.1);\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-statistics-title {\n" +
                "    font-size: 1.5em;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    margin-bottom: 20px;\n" +
                "    text-align: center;\n" +
                "    font-weight: 600;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-statistics-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n" +
                "    gap: 20px;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    box-shadow: 0 8px 30px hsl(var(--foreground) / 0.1);\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 20px;\n" +
                "    border-left: 5px solid hsl(var(--primary));\n" +
                "    transition: all 0.3s ease;\n" +
                "    cursor: pointer;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".admin-stat-card:hover {\n" +
                "    transform: translateY(-5px);\n" +
                "    box-shadow: 0 12px 40px hsl(var(--foreground) / 0.15);\n" +
                "    border-color: hsl(var(--primary));\n" +
                "}\n" +
                "\n" +
                ".admin-stat-card-active {\n" +
                "    border-left-color: hsl(var(--destructive));\n" +
                "    background: hsl(var(--primary) / 0.1);\n" +
                "}\n" +
                "\n" +
                ".admin-stat-icon {\n" +
                "    font-size: 2.5em;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-content {\n" +
                "    flex: 1;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-value {\n" +
                "    font-size: 2em;\n" +
                "    font-weight: 700;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    margin-bottom: 5px;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-label {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    font-weight: 600;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-active {\n" +
                "    font-size: 0.8em;\n" +
                "    color: hsl(var(--destructive));\n" +
                "    font-weight: 600;\n" +
                "    margin-top: 5px;\n" +
                "    min-height: 1.2rem; /* 预留固定高度，保持卡片对齐 */\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-card-link {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    display: block;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-card-link:hover {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "}\n" +
                "\n" +
                ".admin-stat-card-link:hover .admin-stat-card {\n" +
                "    transform: translateY(-5px);\n" +
                "    box-shadow: 0 12px 40px hsl(var(--foreground) / 0.15);\n" +
                "    border-color: hsl(var(--primary));\n" +
                "}\n" +
                "\n" +
                "/* ==================== 管理员过滤器区域 ==================== */\n" +
                ".admin-filters-section {\n" +
                "    background: hsl(var(--background));\n" +
                "    border-radius: 15px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 8px 30px hsl(var(--foreground) / 0.1);\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filters-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    gap: 15px;\n" +
                "}\n" +
                "\n" +
                ".admin-filters-title {\n" +
                "    font-size: 1.1em;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    font-weight: 600;\n" +
                "    margin: 0;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-tags {\n" +
                "    display: flex;\n" +
                "    gap: 15px;\n" +
                "    align-items: center;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-tag {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    background: hsl(var(--muted));\n" +
                "    border: 2px solid hsl(var(--border));\n" +
                "    border-radius: 25px;\n" +
                "    padding: 8px 15px;\n" +
                "    gap: 10px;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-tag:hover {\n" +
                "    border-color: hsl(var(--primary));\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 15px hsl(var(--primary) / 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-filter-label {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    font-weight: 600;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-separator {\n" +
                "    width: 1px;\n" +
                "    height: 20px;\n" +
                "    background: hsl(var(--border));\n" +
                "    transition: background 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-badge {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-avatar {\n" +
                "    width: 24px;\n" +
                "    height: 24px;\n" +
                "    border-radius: 4px;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "    transition: border-color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-name {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    font-weight: 600;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-remove {\n" +
                "    background: hsl(var(--destructive));\n" +
                "    border: none;\n" +
                "    border-radius: 50%;\n" +
                "    width: 20px;\n" +
                "    height: 20px;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-filter-remove:hover {\n" +
                "    background: hsl(var(--destructive) / 0.8);\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".admin-remove-icon {\n" +
                "    color: white;\n" +
                "    font-size: 0.8em;\n" +
                "    font-weight: bold;\n" +
                "}\n" +
                "\n" +
                ".admin-clear-filters-btn {\n" +
                "    background: hsl(var(--primary));\n" +
                "    color: hsl(var(--primary-foreground));\n" +
                "    border: none;\n" +
                "    border-radius: 20px;\n" +
                "    padding: 8px 20px;\n" +
                "    font-size: 0.9em;\n" +
                "    font-weight: 600;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".admin-clear-filters-btn:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 15px hsl(var(--primary) / 0.3);\n" +
                "    background: hsl(var(--primary) / 0.9);\n" +
                "}\n\n";
    }

    /**
     * 生成表格样式
     */
    private String generateTableStyles() {
        return "/* ==================== 管理员表格区域 ==================== */\n" +
                ".admin-table-section {\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".admin-table-container {\n" +
                "    position: relative;\n" +
                "    width: 100%;\n" +
                "    overflow: auto;\n" +
                "    border-radius: 15px;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "    box-shadow: 0 8px 30px hsl(var(--foreground) / 0.1);\n" +
                "    background: hsl(var(--background));\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-punishment-table {\n" +
                "    width: 100%;\n" +
                "    caption-side: bottom;\n" +
                "    font-size: 0.875rem;\n" +
                "    border-collapse: collapse;\n" +
                "    margin: 0;\n" +
                "}\n" +
                "\n" +
                ".admin-punishment-table th,\n" +
                ".admin-punishment-table td {\n" +
                "    padding: 16px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    border-bottom: 1px solid hsl(var(--border));\n" +
                "    font-size: 1em;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-punishment-table tbody tr:last-child td {\n" +
                "    border-bottom: none;\n" +
                "}\n" +
                "\n" +
                ".admin-punishment-table th {\n" +
                "    background: hsl(var(--muted));\n" +
                "    font-weight: 600;\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    font-size: 0.85rem;\n" +
                "    text-transform: uppercase;\n" +
                "    letter-spacing: 0.025em;\n" +
                "    height: 50px;\n" +
                "    border-bottom: 2px solid hsl(var(--border));\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-record-row {\n" +
                "    transition: all 0.2s ease;\n" +
                "    background: hsl(var(--background));\n" +
                "}\n" +
                "\n" +
                ".admin-record-row:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.5) !important;\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 4px 15px hsl(var(--foreground) / 0.1);\n" +
                "}\n" +
                "\n" +
                "/* ==================== 表格区域 ==================== */\n" +
                ".table-section {\n" +
                "    margin-bottom: 8px;\n" +
                "}\n" +
                "\n" +
                ".table-container {\n" +
                "    position: relative;\n" +
                "    width: 100%;\n" +
                "    overflow: auto;\n" +
                "    border-radius: 12px;\n" +
                "    border: 1px solid hsl(240 5.9% 90%);\n" +
                "    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n" +
                "    background: white;\n" +
                "}\n" +
                "\n" +
                "@media (max-width: 1023px) {\n" +
                "    .table-container {\n" +
                "        border-radius: 0;\n" +
                "        border-left: none;\n" +
                "        border-right: none;\n" +
                "        border-top: 1px solid hsl(240 5.9% 90%);\n" +
                "        border-bottom: 1px solid hsl(240 5.9% 90%);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".punishment-table {\n" +
                "    width: 100%;\n" +
                "    caption-side: bottom;\n" +
                "    font-size: 0.875rem;\n" +
                "    border-collapse: collapse;\n" +
                "    margin: 0;\n" +
                "}\n" +
                "\n" +
                ".punishment-table th,\n" +
                ".punishment-table td {\n" +
                "    padding: 16px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    border-bottom: 1px solid hsl(240 5.9% 90%);\n" +
                "    font-size: 1.1em;\n" +
                "}\n" +
                "\n" +
                ".punishment-table tbody tr:last-child td {\n" +
                "    border-bottom: none;\n" +
                "}\n" +
                "\n" +
                ".punishment-table th {\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    font-weight: 500;\n" +
                "    color: hsl(240 3.8% 46.1%);\n" +
                "    font-size: 0.75rem;\n" +
                "    text-transform: uppercase;\n" +
                "    letter-spacing: 0.025em;\n" +
                "    height: 48px;\n" +
                "    border-bottom: 1px solid hsl(240 5.9% 90%);\n" +
                "}\n" +
                "\n" +
                ".punishment-row {\n" +
                "    transition: background-color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".punishment-row:hover,\n" +
                ".player-page-container .punishment-row:hover {\n" +
                "    background-color: hsl(240 4.8% 95.9% / 0.3) !important;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 管理员表格单元格样式 ==================== */\n" +
                ".admin-type-cell {\n" +
                "    width: 100px;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".admin-type-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 8px 14px;\n" +
                "    border-radius: 20px;\n" +
                "    color: white;\n" +
                "    font-size: 0.9em;\n" +
                "    font-weight: 600;\n" +
                "    white-space: nowrap;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".admin-type-ban {\n" +
                "    background: linear-gradient(135deg, #e74c3c, #c0392b);\n" +
                "}\n" +
                "\n" +
                ".admin-type-mute {\n" +
                "    background: linear-gradient(135deg, #f39c12, #e67e22);\n" +
                "}\n" +
                "\n" +
                ".admin-type-warn {\n" +
                "    background: linear-gradient(135deg, #f1c40f, #f39c12);\n" +
                "}\n" +
                "\n" +
                ".admin-type-kick {\n" +
                "    background: linear-gradient(135deg, #9b59b6, #8e44ad);\n" +
                "}\n" +
                "\n" +
                ".admin-player-cell {\n" +
                "    width: 140px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-player-info {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-player-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-player-avatar:hover {\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".admin-player-name {\n" +
                "    font-weight: 600;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    font-size: 0.9em;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 120px;\n" +
                "    text-align: center;\n" +
                "    transition: color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                "/* 管理员执行者头像和名字样式 */\n" +
                ".admin-staff-cell {\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-avatar-name-link {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    display: inline-block;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-avatar-name-link:hover {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".admin-avatar-name-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 4px;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".admin-staff-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "    display: block;\n" +
                "    margin: 0 auto;\n" +
                "    object-fit: cover;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-staff-avatar:hover {\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".admin-staff-name {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    font-weight: 600;\n" +
                "    text-align: center;\n" +
                "    margin-top: 4px;\n" +
                "    transition: color 0.3s ease;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 80px;\n" +
                "}\n" +
                "\n" +
                "/* 管理员到期时间动态显示样式 */\n" +
                ".admin-until-container {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    gap: 6px;\n" +
                "}\n" +
                "\n" +
                ".admin-status-dot {\n" +
                "    width: 8px;\n" +
                "    height: 8px;\n" +
                "    border-radius: 50%;\n" +
                "    display: inline-block;\n" +
                "    flex-shrink: 0;\n" +
                "}\n" +
                "\n" +
                ".admin-status-dot.admin-status-active {\n" +
                "    background-color: #10b981;\n" +
                "    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-status-dot.admin-status-expired {\n" +
                "    background-color: #ef4444;\n" +
                "    box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-status-dot.admin-status-permanent {\n" +
                "    background-color: #10b981;\n" +
                "    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-relative-time {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    text-align: center;\n" +
                "    white-space: nowrap;\n" +
                "    cursor: help;\n" +
                "}\n" +
                "\n" +
                ".admin-until-permanent {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    font-weight: 500;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".admin-until-na {\n" +
                "    font-size: 0.9em;\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    font-weight: 500;\n" +
                "    text-align: center;\n" +
                "    font-style: italic;\n" +
                "}\n" +
                "\n" +
                "/* 管理员查看详情按钮样式 */\n" +
                ".admin-info-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    background: hsl(var(--secondary));\n" +
                "    color: hsl(var(--secondary-foreground));\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".admin-info-btn:hover {\n" +
                "    background: hsl(var(--primary));\n" +
                "    color: hsl(var(--primary-foreground));\n" +
                "    border-color: hsl(var(--primary));\n" +
                "    transform: translateY(-1px);\n" +
                "    text-decoration: none;\n" +
                "}\n" +
                "\n" +
                ".admin-info-icon {\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "    stroke-width: 2;\n" +
                "}\n" +
                "\n" +
                "/* 管理员警告通知状态样式 */\n" +
                ".admin-notify-cell {\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-notify-status {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 50%;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-notify-status.admin-notify-sent {\n" +
                "    background: rgba(16, 185, 129, 0.1);\n" +
                "    color: #10b981;\n" +
                "    border: 2px solid rgba(16, 185, 129, 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-notify-status.admin-notify-sent:hover {\n" +
                "    background: rgba(16, 185, 129, 0.2);\n" +
                "    border-color: #10b981;\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".admin-notify-status.admin-notify-failed {\n" +
                "    background: rgba(239, 68, 68, 0.1);\n" +
                "    color: #ef4444;\n" +
                "    border: 2px solid rgba(239, 68, 68, 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-notify-status.admin-notify-failed:hover {\n" +
                "    background: rgba(239, 68, 68, 0.2);\n" +
                "    border-color: #ef4444;\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".admin-notify-icon {\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "    stroke-width: 2.5;\n" +
                "}\n" +
                "\n" +
                "/* 管理员详细页面样式 */\n" +
                ".admin-detail-section {\n" +
                "    margin: 2rem 0;\n" +
                "    background: var(--card-bg);\n" +
                "    border-radius: 12px;\n" +
                "    padding: 2rem;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".admin-detail-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: 1fr 1fr;\n" +
                "    gap: 2rem;\n" +
                "    margin-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                "@media (max-width: 768px) {\n" +
                "    .admin-detail-grid {\n" +
                "        grid-template-columns: 1fr;\n" +
                "        gap: 1rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".admin-detail-card {\n" +
                "    background: var(--card-bg);\n" +
                "    border: 1px solid var(--border-color);\n" +
                "    border-radius: 12px;\n" +
                "    padding: 1.5rem;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-card:hover {\n" +
                "    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".admin-detail-card h3 {\n" +
                "    margin: 0 0 1rem 0;\n" +
                "    color: var(--text-primary);\n" +
                "    font-size: 1.25rem;\n" +
                "    font-weight: 600;\n" +
                "    border-bottom: 2px solid var(--primary-color);\n" +
                "    padding-bottom: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".admin-player-detail {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 1rem;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-avatar {\n" +
                "    width: 64px;\n" +
                "    height: 64px;\n" +
                "    border-radius: 8px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "}\n" +
                "\n" +
                ".admin-player-info h4 {\n" +
                "    margin: 0 0 0.5rem 0;\n" +
                "    color: var(--text-primary);\n" +
                "    font-size: 1.1rem;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".admin-uuid {\n" +
                "    margin: 0;\n" +
                "    color: var(--text-secondary);\n" +
                "    font-size: 0.85rem;\n" +
                "    font-family: 'Courier New', monospace;\n" +
                "    word-break: break-all;\n" +
                "}\n" +
                "\n" +
                ".admin-punishment-detail {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    gap: 1rem;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-item {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    gap: 0.25rem;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-item label {\n" +
                "    color: var(--text-secondary);\n" +
                "    font-size: 0.9rem;\n" +
                "    font-weight: 500;\n" +
                "    margin: 0;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-item span {\n" +
                "    color: var(--text-primary);\n" +
                "    font-size: 1rem;\n" +
                "    word-break: break-word;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-actions {\n" +
                "    position: absolute;\n" +
                "    bottom: 80px;\n" +
                "    left: 50%;\n" +
                "    transform: translateX(-50%);\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 1rem;\n" +
                "    z-index: 10;\n" +
                "}\n" +
                "\n" +
                "/* 处罚信息卡片中的操作按钮区域 */\n" +
                ".punishment-action-area {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    align-items: center;\n" +
                "    padding: 2rem 1rem;\n" +
                "    background: linear-gradient(135deg, rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.02));\n" +
                "    border-radius: 12px;\n" +
                "    margin: 1rem 0;\n" +
                "}\n" +
                "\n" +
                ".punishment-action-btn {\n" +
                "    background: linear-gradient(135deg, #e74c3c, #c0392b);\n" +
                "    color: white;\n" +
                "    border: none;\n" +
                "    padding: 1.2rem 3rem;\n" +
                "    font-size: 1.2rem;\n" +
                "    font-weight: 700;\n" +
                "    border-radius: 12px;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.3);\n" +
                "    text-transform: uppercase;\n" +
                "    letter-spacing: 0.5px;\n" +
                "    position: relative;\n" +
                "    overflow: hidden;\n" +
                "}\n" +
                "\n" +
                ".punishment-action-btn:hover {\n" +
                "    background: linear-gradient(135deg, #c0392b, #a93226);\n" +
                "    transform: translateY(-4px);\n" +
                "    box-shadow: 0 12px 35px rgba(231, 76, 60, 0.6);\n" +
                "}\n" +
                "\n" +
                ".punishment-action-btn:active {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);\n" +
                "}\n" +
                "\n" +
                ".admin-detail-actions .admin-action-btn {\n" +
                "    padding: 1rem 2rem;\n" +
                "    font-size: 1rem;\n" +
                "    font-weight: 600;\n" +
                "    border-radius: 10px;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);\n" +
                "    white-space: nowrap;\n" +
                "    min-width: 100px;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-actions .admin-revoke-btn {\n" +
                "    background: linear-gradient(135deg, #e74c3c, #c0392b);\n" +
                "    color: white;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "}\n" +
                "\n" +
                ".admin-detail-actions .admin-revoke-btn:hover {\n" +
                "    background: linear-gradient(135deg, #c0392b, #a93226);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 - 移动端调整按钮位置 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .admin-detail-actions {\n" +
                "        position: static;\n" +
                "        justify-content: center;\n" +
                "        margin-top: 1.5rem;\n" +
                "        padding-top: 1.5rem;\n" +
                "        border-top: 1px solid var(--border-color);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 769px) and (max-width: 1024px) {\n" +
                "    .admin-detail-actions {\n" +
                "        bottom: 75px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".admin-reason-cell {\n" +
                "    min-width: 200px;\n" +
                "    max-width: 300px;\n" +
                "    text-align: center;\n" +
                "    padding: 16px;\n" +
                "}\n" +
                "\n" +
                ".admin-reason-text {\n" +
                "    display: inline-block;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    color: #4a5568;\n" +
                "    font-size: 0.9em;\n" +
                "    text-align: center;\n" +
                "    max-width: 100%;\n" +
                "}\n" +
                "\n" +
                ".admin-staff-cell {\n" +
                "    width: 120px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-staff-name {\n" +
                "    font-weight: 500;\n" +
                "    color: #666;\n" +
                "    font-size: 0.9em;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 100px;\n" +
                "}\n" +
                "\n" +
                ".admin-time-cell,\n" +
                ".admin-until-cell {\n" +
                "    width: 180px;\n" +
                "    font-size: 0.85em;\n" +
                "    color: #666;\n" +
                "    font-family: 'Consolas', 'Monaco', monospace;\n" +
                "}\n" +
                "\n" +
                ".admin-time-text,\n" +
                ".admin-until-text {\n" +
                "    display: inline-block;\n" +
                "}\n" +
                "\n" +
                ".admin-status-cell {\n" +
                "    width: 100px;\n" +
                "}\n" +
                "\n" +
                ".admin-status-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 6px 12px;\n" +
                "    border-radius: 15px;\n" +
                "    font-size: 0.85em;\n" +
                "    font-weight: 600;\n" +
                "    white-space: nowrap;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".admin-status-active {\n" +
                "    background: linear-gradient(135deg, #27ae60, #2ecc71);\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".admin-status-inactive {\n" +
                "    background: linear-gradient(135deg, #95a5a6, #7f8c8d);\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".admin-status-completed {\n" +
                "    background: linear-gradient(135deg, #3498db, #2980b9);\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".admin-actions-cell {\n" +
                "    width: 120px;\n" +
                "    text-align: center;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".admin-action-buttons {\n" +
                "    display: flex;\n" +
                "    gap: 8px;\n" +
                "    justify-content: center;\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".admin-action-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 8px;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.2s ease;\n" +
                "    font-size: 1.1em;\n" +
                "}\n" +
                "\n" +
                ".admin-view-btn {\n" +
                "    background: linear-gradient(135deg, #3498db, #2980b9);\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".admin-view-btn:hover {\n" +
                "    transform: scale(1.1);\n" +
                "    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);\n" +
                "}\n" +
                "\n" +
                ".admin-revoke-btn {\n" +
                "    background: linear-gradient(135deg, #e74c3c, #c0392b);\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".admin-revoke-btn:hover {\n" +
                "    transform: scale(1.1);\n" +
                "    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);\n" +
                "}\n" +
                "\n" +
                ".admin-no-records {\n" +
                "    text-align: center;\n" +
                "    padding: 60px 20px;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".admin-empty-state {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 15px;\n" +
                "}\n" +
                "\n" +
                ".admin-empty-icon {\n" +
                "    font-size: 4em;\n" +
                "    opacity: 0.5;\n" +
                "}\n" +
                "\n" +
                ".admin-empty-text {\n" +
                "    font-size: 1.2em;\n" +
                "    color: #2d3748;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".type-cell {\n" +
                "    width: 80px;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".type-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 8px 14px;\n" +
                "    border-radius: 20px;\n" +
                "    color: white;\n" +
                "    font-size: 0.95em;\n" +
                "    font-weight: 600;\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".player-cell {\n" +
                "    width: 120px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".player-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "    display: block;\n" +
                "    margin: 0 auto 6px auto;\n" +
                "}\n" +
                "\n" +
                ".player-info {\n" +
                "    flex: 1;\n" +
                "    min-width: 0;\n" +
                "}\n" +
                "\n" +
                "/* 表格中的玩家名字样式 - 使用更具体的选择器避免冲突 */\n" +
                ".punishment-table .player-name {\n" +
                "    font-weight: 600;\n" +
                "    color: #2d3748;\n" +
                "    font-size: 0.9em;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 100px;\n" +
                "    margin: 0 auto;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".player-uuid {\n" +
                "    font-size: 0.8em;\n" +
                "    color: #666;\n" +
                "    font-family: monospace;\n" +
                "}\n" +
                "\n" +
                ".reason-cell {\n" +
                "    min-width: 200px;\n" +
                "    max-width: 300px;\n" +
                "    text-align: center;\n" +
                "    padding: 16px;\n" +
                "}\n" +
                "\n" +
                ".reason-text {\n" +
                "    display: inline-block;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    color: #4a5568;\n" +
                "    font-size: 0.9em;\n" +
                "    text-align: center;\n" +
                "    max-width: 100%;\n" +
                "}\n" +
                "\n" +
                ".staff-cell {\n" +
                "    width: 120px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".staff-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "    display: block;\n" +
                "    margin: 0 auto 6px auto;\n" +
                "}\n" +
                "\n" +
                ".console-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "    display: block;\n" +
                "    margin: 0 auto 6px auto;\n" +
                "    object-fit: cover;\n" +
                "}\n" +
                "\n" +
                ".console-bust-large {\n" +
                "    border-radius: 8px;\n" +
                "    border: none;\n" +
                "    background: transparent;\n" +
                "    object-fit: cover;\n" +
                "}\n" +
                "\n" +
                "/* 表格中的执行者名字样式 - 使用更具体的选择器避免冲突 */\n" +
                ".punishment-table .staff-name {\n" +
                "    font-weight: 500;\n" +
                "    color: #666;\n" +
                "    font-size: 0.9em;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 100px;\n" +
                "    margin: 0 auto;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                "/* 头像和名字容器（按照 next-litebans 布局）*/\n" +
                ".avatar-name-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                "/* 头像名字链接样式（按照 next-litebans AvatarName 组件）*/\n" +
                ".avatar-name-link {\n" +
                "    display: block;\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    transition: all 0.2s ease;\n" +
                "    border-radius: 8px;\n" +
                "    padding: 4px;\n" +
                "}\n" +
                "\n" +
                ".avatar-name-link:hover {\n" +
                "    background-color: hsl(240 4.8% 95.9% / 0.4);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".avatar-name-link:hover .player-avatar,\n" +
                ".avatar-name-link:hover .staff-avatar {\n" +
                "    transform: scale(1.05);\n" +
                "}\n" +
                "\n" +
                ".avatar-name-link:hover .punishment-table .player-name,\n" +
                ".avatar-name-link:hover .punishment-table .staff-name {\n" +
                "    color: #2d3748;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".time-cell,\n" +
                ".until-cell {\n" +
                "    width: 180px;\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "    font-family: monospace;\n" +
                "}\n" +
                "\n" +
                ".status-cell {\n" +
                "    width: 100px;\n" +
                "}\n" +
                "\n" +
                ".status-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 6px 12px;\n" +
                "    border-radius: 15px;\n" +
                "    font-size: 0.95em;\n" +
                "    font-weight: 600;\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-active {\n" +
                "    background: hsl(142.1 76.2% 36.3% / 0.1);\n" +
                "    color: hsl(142.1 70.6% 45.3%);\n" +
                "    border: 1px solid hsl(142.1 76.2% 36.3% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-expired {\n" +
                "    background: hsl(47.9 95.8% 53.1% / 0.1);\n" +
                "    color: hsl(32.6 95.4% 44%);\n" +
                "    border: 1px solid hsl(47.9 95.8% 53.1% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-removed {\n" +
                "    background: hsl(0 84.2% 60.2% / 0.1);\n" +
                "    color: hsl(0 72.2% 50.6%);\n" +
                "    border: 1px solid hsl(0 84.2% 60.2% / 0.2);\n" +
                "}\n" +
                "\n" +
                "/* 详情按钮样式（按照 next-litebans PunishmentInfoButton）*/\n" +
                ".info-cell {\n" +
                "    text-align: center;\n" +
                "    padding: 12px 8px;\n" +
                "    width: 60px;\n" +
                "}\n" +
                "\n" +
                ".info-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    background: hsl(var(--secondary));\n" +
                "    color: hsl(var(--secondary-foreground));\n" +
                "    border-radius: 6px;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease-in-out;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".info-btn:hover {\n" +
                "    background: hsl(var(--secondary) / 0.8);\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".info-icon {\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "}\n" +
                "\n" +
                "/* 无记录状态 */\n" +
                ".no-records {\n" +
                "    text-align: center;\n" +
                "    padding: 60px 20px;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".no-records-icon {\n" +
                "    font-size: 4em;\n" +
                "    margin-bottom: 20px;\n" +
                "    opacity: 0.5;\n" +
                "}\n" +
                "\n" +
                ".no-records h3 {\n" +
                "    font-size: 1.5em;\n" +
                "    margin-bottom: 10px;\n" +
                "    color: #2d3748;\n" +
                "}\n" +
                "\n" +
                ".no-records p {\n" +
                "    font-size: 1em;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".db-status {\n" +
                "    color: #e74c3c !important;\n" +
                "    font-weight: 600;\n" +
                "    margin-top: 10px;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 管理员分页导航 ==================== */\n" +
                ".admin-pagination-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    text-align: center;\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".admin-pagination {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    align-items: center;\n" +
                "    gap: 8px;\n" +
                "    margin-bottom: 15px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".admin-pagination-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    padding: 10px 15px;\n" +
                "    background: #f8f9fa;\n" +
                "    color: #495057;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 8px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 2px solid transparent;\n" +
                "    min-width: 44px;\n" +
                "    height: 44px;\n" +
                "}\n\n";
    }

    /**
     * 生成表单样式
     */
    private String generateFormStyles() {
        return "/* ==================== 表单样式 ==================== */\n" +
                ".admin-pagination-btn:hover {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".admin-pagination-current {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2) !important;\n" +
                "    color: white !important;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n" +
                "    transform: translateY(-1px);\n" +
                "}\n" +
                "\n" +
                ".admin-pagination-btn.disabled {\n" +
                "    background: #e9ecef;\n" +
                "    color: #adb5bd;\n" +
                "    cursor: not-allowed;\n" +
                "    opacity: 0.6;\n" +
                "}\n" +
                "\n" +
                ".admin-pagination-btn.disabled:hover {\n" +
                "    background: #e9ecef;\n" +
                "    color: #adb5bd;\n" +
                "    transform: none;\n" +
                "    box-shadow: none;\n" +
                "}\n" +
                "\n" +
                ".admin-pagination-ellipsis {\n" +
                "    padding: 10px 5px;\n" +
                "    color: #6c757d;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".admin-pagination-info {\n" +
                "    color: #6c757d;\n" +
                "    font-size: 0.9em;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 分页导航 ==================== */\n" +
                ".pagination-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".pagination {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    align-items: center;\n" +
                "    gap: 8px;\n" +
                "    margin-bottom: 15px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".page-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    min-width: 40px;\n" +
                "    height: 40px;\n" +
                "    padding: 0 12px;\n" +
                "    background: #f8f9fa;\n" +
                "    color: #666;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 8px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".page-btn:hover {\n" +
                "    background: #e9ecef;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".page-btn.current {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".page-btn.prev-btn,\n" +
                ".page-btn.next-btn {\n" +
                "    padding: 0 16px;\n" +
                "}\n" +
                "\n" +
                ".page-dots {\n" +
                "    color: #666;\n" +
                "    font-weight: 600;\n" +
                "    padding: 0 8px;\n" +
                "}\n" +
                "\n" +
                ".page-info {\n" +
                "    color: #666;\n" +
                "    font-size: 0.9em;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 响应式设计 ==================== */\n" +
                "@media (max-width: 768px) {\n" +
                "    .container {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .page-header {\n" +
                "        padding: 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .page-header h1 {\n" +
                "        font-size: 2em;\n" +
                "    }\n" +
                "    \n" +
                "    .player-header {\n" +
                "        padding: 20px;\n" +
                "        flex-direction: column;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-info-container {\n" +
                "        flex-direction: column;\n" +
                "        gap: 20px;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-bust-image {\n" +
                "        width: 128px;\n" +
                "        height: 128px;\n" +
                "    }\n" +
                "    \n" +
                "    .player-name-title {\n" +
                "        font-size: 2rem;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-details-section {\n" +
                "        max-width: 100%;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-badges {\n" +
                "        justify-content: center;\n" +
                "    }\n" +
                "    \n" +
                "    .nav-tabs {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .tab-btn {\n" +
                "        padding: 10px 15px;\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "    \n" +
                "    .stats-grid {\n" +
                "        grid-template-columns: 1fr;\n" +
                "    }\n" +
                "    \n" +
                "    .search-input {\n" +
                "        min-width: 250px;\n" +
                "    }\n" +
                "    \n" +
                "    .search-input-group {\n" +
                "        flex-direction: column;\n" +
                "        align-items: stretch;\n" +
                "    }\n" +
                "    \n" +
                "    .punishment-table {\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "    \n" +
                "    .punishment-table th,\n" +
                "    .punishment-table td {\n" +
                "        padding: 10px 8px;\n" +
                "    }\n" +
                "    \n" +
                "    .player-cell {\n" +
                "        width: 150px;\n" +
                "    }\n" +
                "    \n" +
                "    .reason-cell {\n" +
                "        max-width: 200px;\n" +
                "    }\n" +
                "    \n" +
                "    .time-cell,\n" +
                "    .until-cell {\n" +
                "        width: 120px;\n" +
                "        font-size: 0.8em;\n" +
                "    }\n" +
                "    \n" +
                "    .pagination {\n" +
                "        gap: 5px;\n" +
                "    }\n" +
                "    \n" +
                "    .page-btn {\n" +
                "        min-width: 35px;\n" +
                "        height: 35px;\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "}\n\n";
    }

    /**
     * 生成模态框样式
     */
    private String generateModalStyles() {
        return "/* ==================== 夜间模式样式 ==================== */\n" +
                ".dark body {\n" +
                "    background: hsl(var(--background));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .page-header {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .page-header h1 {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .page-description {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .player-view-header {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .player-header {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .player-name-title {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-badge {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .nav-tabs {\n" +
                "    background: hsl(var(--card));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .tab-btn {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .tab-btn:hover {\n" +
                "    background: hsl(var(--accent));\n" +
                "}\n" +
                "\n" +
                ".dark .stat-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .stat-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .stat-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .search-section {\n" +
                "    background: hsl(var(--card));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .search-input {\n" +
                "    background: hsl(var(--input));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .search-input:hover {\n" +
                "    border-color: hsl(var(--border));\n" +
                "    background-color: hsl(var(--muted) / 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .search-input:focus {\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);\n" +
                "}\n" +
                "\n" +
                ".dark .theme-toggle-btn {\n" +
                "    background: hsl(var(--muted));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .theme-toggle-btn:hover {\n" +
                "    background: hsl(var(--accent));\n" +
                "    border-color: #667eea;\n" +
                "}\n" +
                "\n" +
                ".dark .search-result-info {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .table-container {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table th {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table td {\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-row:hover,\n" +
                ".dark .player-page-container .punishment-row:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.2) !important;\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table .player-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table .staff-name {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .reason-text {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .time-cell,\n" +
                ".dark .until-cell {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .player-avatar,\n" +
                ".dark .staff-avatar {\n" +
                "    background: transparent;\n" +
                "}\n" +
                "\n" +
                ".dark .console-avatar {\n" +
                "    background: transparent;\n" +
                "}\n" +
                "\n" +
                ".dark .avatar-name-link:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .avatar-name-link:hover .punishment-table .player-name,\n" +
                ".dark .avatar-name-link:hover .punishment-table .staff-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 过滤器标签样式（按照 next-litebans Filters 组件）*/\n" +
                ".filters-section {\n" +
                "    margin-bottom: 1rem;\n" +
                "}\n" +
                "\n" +
                ".filters-container {\n" +
                "    display: flex;\n" +
                "    flex-wrap: wrap;\n" +
                "    gap: 0.5rem;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .filters-container {\n" +
                "        justify-content: flex-start;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".filter-tag {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    height: 2rem;\n" +
                "    border: 1px dashed hsl(240 5.9% 90%);\n" +
                "    border-radius: 0.375rem;\n" +
                "    background: hsl(0 0% 100%);\n" +
                "    font-size: 0.875rem;\n" +
                "    margin: 0 auto;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .filter-tag {\n" +
                "        margin: 0;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".filter-label {\n" +
                "    padding: 0 0.5rem;\n" +
                "    font-weight: 500;\n" +
                "    color: hsl(240 5.9% 10%);\n" +
                "}\n" +
                "\n" +
                ".filter-separator {\n" +
                "    width: 1px;\n" +
                "    height: 1rem;\n" +
                "    background: hsl(240 5.9% 90%);\n" +
                "    margin: 0 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".filter-badge {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    padding: 0.125rem 0.25rem;\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    border-radius: 0.125rem;\n" +
                "    font-weight: normal;\n" +
                "}\n" +
                "\n" +
                ".filter-avatar {\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "    border-radius: 2px;\n" +
                "}\n" +
                "\n" +
                ".filter-name {\n" +
                "    color: hsl(240 5.9% 10%);\n" +
                "    font-size: 0.875rem;\n" +
                "}\n" +
                "\n" +
                ".filter-remove {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 1rem;\n" +
                "    height: 1rem;\n" +
                "    margin-left: 0.5rem;\n" +
                "    margin-right: 0.25rem;\n" +
                "    background: none;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "    border-radius: 50%;\n" +
                "    transition: background-color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".filter-remove:hover {\n" +
                "    background: hsl(0 84.2% 60.2% / 0.1);\n" +
                "}\n" +
                "\n" +
                ".remove-icon {\n" +
                "    font-size: 0.75rem;\n" +
                "    color: hsl(0 84.2% 60.2%);\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的过滤器样式 */\n" +
                ".dark .filter-tag {\n" +
                "    border-color: hsl(var(--border));\n" +
                "    background: hsl(var(--background));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-label {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-separator {\n" +
                "    background: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-badge {\n" +
                "    background: hsl(var(--muted));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .no-records {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .no-records h3 {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .pagination-section {\n" +
                "    background: hsl(var(--card));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn {\n" +
                "    background: hsl(240 3.7% 15.9%);\n" +
                "    color: hsl(240 5% 64.9%);\n" +
                "    border-color: hsl(240 3.7% 15.9%);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn:hover {\n" +
                "    background: hsl(240 3.7% 20%);\n" +
                "    color: hsl(0 0% 98%);\n" +
                "    border-color: hsl(240 3.7% 25%);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn.current {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn.current:hover {\n" +
                "    background: linear-gradient(135deg, #5a6fd8, #6a4190);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);\n" +
                "}\n" +
                "\n" +
                ".dark .page-dots {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .page-info {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的管理员分页样式 */\n" +
                ".dark .admin-pagination-section {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-btn {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-btn:hover {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border-color: #667eea;\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-current {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2) !important;\n" +
                "    color: white !important;\n" +
                "    border-color: #667eea !important;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-btn.disabled {\n" +
                "    background: hsl(var(--muted) / 0.5);\n" +
                "    color: hsl(var(--muted-foreground) / 0.5);\n" +
                "    border-color: hsl(var(--border));\n" +
                "    cursor: not-allowed;\n" +
                "    opacity: 0.6;\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-btn.disabled:hover {\n" +
                "    background: hsl(var(--muted) / 0.5);\n" +
                "    color: hsl(var(--muted-foreground) / 0.5);\n" +
                "    transform: none;\n" +
                "    box-shadow: none;\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-ellipsis {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .admin-pagination-info {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                "/* ==================== 详细信息页面样式 ==================== */\n" +
                ".detail-page-container {\n" +
                "    position: relative;\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "    padding: 2rem 1rem;\n" +
                "    min-height: calc(100vh - 200px);\n" +
                "}\n" +
                "\n" +
                ".detail-header {\n" +
                "    text-align: center;\n" +
                "    margin-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".detail-title {\n" +
                "    font-size: 3rem;\n" +
                "    font-weight: bold;\n" +
                "    line-height: 1.1;\n" +
                "    margin-bottom: 1rem;\n" +
                "    color: #2d3748;\n" +
                "    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".detail-badges {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 0.5rem;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-ipban {\n" +
                "    background: hsl(271.5 81.3% 55.9% / 0.1);\n" +
                "    color: hsl(271.5 81.3% 55.9%);\n" +
                "    border: 1px solid hsl(271.5 81.3% 55.9% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".punishment-info-section {\n" +
                "    width: 100%;\n" +
                "    max-width: 1200px;\n" +
                "}\n" +
                "\n" +
                ".punishment-info-card {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: 1fr 2fr 1fr;\n" +
                "    gap: 3rem;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 3rem;\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".player-info-area,\n" +
                ".staff-info-area {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "}\n" +
                "\n" +
                ".detail-info-area {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    gap: 1.5rem;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".info-area-title {\n" +
                "    font-size: 1.5rem;\n" +
                "    font-weight: bold;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 1rem;\n" +
                "}\n" +
                "\n" +
                ".player-body-image,\n" +
                ".staff-body-image {\n" +
                "    width: 200px;\n" +
                "    height: 400px;\n" +
                "    object-fit: contain;\n" +
                "    border-radius: 10px;\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".player-body-image:hover,\n" +
                ".staff-body-image:hover {\n" +
                "    transform: scale(1.05);\n" +
                "}\n" +
                "\n" +
                ".staff-body-image {\n" +
                "    transform: scaleX(-1);\n" +
                "}\n" +
                "\n" +
                ".staff-body-image:hover {\n" +
                "    transform: scaleX(-1.05) scaleY(1.05);\n" +
                "}\n" +
                "\n" +
                ".player-name-area,\n" +
                ".staff-name-area {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.75rem;\n" +
                "}\n" +
                "\n" +
                ".player-avatar-large,\n" +
                ".staff-avatar-large {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 4px;\n" +
                "    border: none;\n" +
                "}\n" +
                "\n" +
                "/* 玩家和执行者名字样式 - 使用更具体的选择器 */\n" +
                ".player-name-area .player-name-large,\n" +
                ".staff-name-area .staff-name-large {\n" +
                "    font-size: 1.25rem !important;\n" +
                "    font-weight: 600 !important;\n" +
                "    color: #2d3748 !important;\n" +
                "    margin: 0 !important;\n" +
                "    line-height: 1.2 !important;\n" +
                "    text-align: center !important;\n" +
                "}\n" +
                "\n" +
                "/* 确保p标签和h3标签都使用相同样式 */\n" +
                ".player-name-area p.player-name-large,\n" +
                ".staff-name-area p.staff-name-large,\n" +
                ".player-name-area h3.player-name-large,\n" +
                ".staff-name-area h3.staff-name-large {\n" +
                "    font-size: 1.25rem !important;\n" +
                "    font-weight: 600 !important;\n" +
                "    color: #2d3748 !important;\n" +
                "    margin: 0 !important;\n" +
                "    line-height: 1.2 !important;\n" +
                "    text-align: center !important;\n" +
                "}\n" +
                "\n" +
                ".detail-item {\n" +
                "    padding: 1rem 0;\n" +
                "    border-bottom: 1px solid #e2e8f0;\n" +
                "}\n" +
                "\n" +
                ".detail-item:last-child {\n" +
                "    border-bottom: none;\n" +
                "}\n" +
                "\n" +
                ".detail-label {\n" +
                "    font-size: 1.125rem;\n" +
                "    font-weight: 600;\n" +
                "    color: #4a5568;\n" +
                "    margin-bottom: 0.5rem;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".detail-value {\n" +
                "    font-size: 1rem;\n" +
                "    color: #2d3748;\n" +
                "    line-height: 1.5;\n" +
                "    word-break: break-word;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                "/* Flex 布局工具类 */\n" +
                ".flex {\n" +
                "    display: flex;\n" +
                "}\n" +
                "\n" +
                ".items-center {\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".justify-center {\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                "/* 状态点样式（按照 next-litebans PunishmentStatusDot）*/\n" +
                ".status-dot {\n" +
                "    width: 8px;\n" +
                "    height: 8px;\n" +
                "    border-radius: 50%;\n" +
                "    flex-shrink: 0;\n" +
                "    margin-right: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".status-dot.status-active {\n" +
                "    background-color: #10b981; /* green-500 */\n" +
                "}\n" +
                "\n" +
                ".status-dot.status-expired {\n" +
                "    background-color: #ef4444; /* red-500 */\n" +
                "}\n" +
                "\n" +
                ".status-dot.status-temporal {\n" +
                "    background-color: #f59e0b; /* orange-500 */\n" +
                "}\n" +
                "\n" +
                "/* 移动端详细信息样式（按照 next-litebans 响应式设计）*/\n" +
                ".mobile-details {\n" +
                "    display: none;\n" +
                "    width: 100%;\n" +
                "    max-width: 350px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 1.5rem;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-item {\n" +
                "    margin-bottom: 1.5rem;\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-item:last-child {\n" +
                "    margin-bottom: 0;\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-label {\n" +
                "    font-size: 1.125rem;\n" +
                "    font-weight: 600;\n" +
                "    color: #4a5568;\n" +
                "    margin-bottom: 0.5rem;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-value {\n" +
                "    font-size: 1rem;\n" +
                "    color: #2d3748;\n" +
                "    line-height: 1.5;\n" +
                "    word-break: break-word;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的移动端详细信息样式 */\n" +
                ".dark .mobile-details {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .mobile-detail-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .mobile-detail-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的详细信息页面样式 */\n" +
                ".dark .detail-title {\n" +
                "    color: hsl(var(--foreground));\n" +
                "    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-info-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .info-area-title {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的玩家和执行者名字样式 */\n" +
                ".dark .player-name-area .player-name-large,\n" +
                ".dark .staff-name-area .staff-name-large,\n" +
                ".dark .player-name-area p.player-name-large,\n" +
                ".dark .staff-name-area p.staff-name-large,\n" +
                ".dark .player-name-area h3.player-name-large,\n" +
                ".dark .staff-name-area h3.staff-name-large {\n" +
                "    color: hsl(var(--foreground)) !important;\n" +
                "    font-size: 1.25rem !important;\n" +
                "    font-weight: 600 !important;\n" +
                "}\n" +
                "\n" +
                ".dark .detail-item {\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .detail-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .detail-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .player-avatar-large,\n" +
                ".dark .staff-avatar-large {\n" +
                "    background: transparent;\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 - 详细信息页面 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .detail-title {\n" +
                "        font-size: 2rem;\n" +
                "    }\n" +
                "    \n" +
                "    .punishment-info-card {\n" +
                "        display: none; /* 在移动端隐藏桌面版详细信息卡片 */\n" +
                "    }\n" +
                "    \n" +
                "    .mobile-details {\n" +
                "        display: block; /* 在移动端显示移动版详细信息 */\n" +
                "    }\n" +
                "    \n" +
                "    .player-body-image,\n" +
                "    .staff-body-image {\n" +
                "        width: 150px;\n" +
                "        height: 300px;\n" +
                "    }\n" +
                "    \n" +
                "    .info-area-title {\n" +
                "        font-size: 1.25rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 769px) {\n" +
                "    .mobile-details {\n" +
                "        display: none; /* 在桌面端隐藏移动版详细信息 */\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* ==================== 玩家页面样式 ==================== */\n" +
                ".player-page-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "    padding: 2rem 1rem;\n" +
                "    min-height: calc(100vh - 200px);\n" +
                "}\n" +
                "\n" +
                "/* Tailwind-like utility classes for player header */\n" +
                ".flex {\n" +
                "    display: flex;\n" +
                "}\n" +
                "\n" +
                ".h-full {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".flex-col {\n" +
                "    flex-direction: column;\n" +
                "}\n" +
                "\n" +
                ".items-center {\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".gap-4 {\n" +
                "    gap: 1rem;\n" +
                "}\n" +
                "\n" +
                ".py-8 {\n" +
                "    padding-top: 2rem;\n" +
                "    padding-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".space-y-2 > * + * {\n" +
                "    margin-top: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".space-x-2 > * + * {\n" +
                "    margin-left: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".space-x-4 > * + * {\n" +
                "    margin-left: 1rem;\n" +
                "}\n" +
                "\n" +
                ".mx-auto {\n" +
                "    margin-left: auto;\n" +
                "    margin-right: auto;\n" +
                "}\n" +
                "\n" +
                ".text-center {\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".text-4xl {\n" +
                "    font-size: 2.25rem;\n" +
                "    line-height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".font-bold {\n" +
                "    font-weight: 700;\n" +
                "}\n" +
                "\n" +
                ".leading-tight {\n" +
                "    line-height: 1.25;\n" +
                "}\n" +
                "\n" +
                ".tracking-tighter {\n" +
                "    letter-spacing: -0.05em;\n" +
                "}\n" +
                "\n" +
                ".whitespace-nowrap {\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 768px) {\n" +
                "    .md\\:flex {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:space-x-4 > * + * {\n" +
                "        margin-left: 1rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:w-\\[350px\\] {\n" +
                "        width: 350px;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:py-4 {\n" +
                "        padding-top: 1rem;\n" +
                "        padding-bottom: 1rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:text-left {\n" +
                "        text-align: left;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:py-12 {\n" +
                "        padding-top: 3rem;\n" +
                "        padding-bottom: 3rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:pb-8 {\n" +
                "        padding-bottom: 2rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 640px) {\n" +
                "    .sm\\:text-5xl {\n" +
                "        font-size: 3rem;\n" +
                "        line-height: 1;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .lg\\:leading-\\[1\\.1\\] {\n" +
                "        line-height: 1.1;\n" +
                "    }\n" +
                "    \n" +
                "    .lg\\:py-18 {\n" +
                "        padding-top: 4.5rem;\n" +
                "        padding-bottom: 4.5rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".player-header {\n" +
                "    width: 100%;\n" +
                "    max-width: 1200px;\n" +
                "    margin-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".player-info-card {\n" +
                "    display: flex;\n" +
                "    flex-direction: row;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 2rem;\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".player-skin-container {\n" +
                "    flex-shrink: 0;\n" +
                "}\n" +
                "\n" +
                ".player-bust-image {\n" +
                "    width: 128px;\n" +
                "    height: 128px;\n" +
                "    border-radius: 10px;\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".player-bust-image:hover {\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".player-details {\n" +
                "    flex: 1;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".player-name {\n" +
                "    font-size: 2.5rem;\n" +
                "    font-weight: bold;\n" +
                "    margin-bottom: 1rem;\n" +
                "    text-align: center;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    -webkit-background-clip: text;\n" +
                "    -webkit-text-fill-color: transparent;\n" +
                "    background-clip: text;\n" +
                "}\n" +
                "\n" +
                ".player-badges {\n" +
                "    display: flex;\n" +
                "    gap: 0.75rem;\n" +
                "    flex-wrap: wrap;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".player-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "    padding: 0.5rem 1rem;\n" +
                "    border-radius: 12px;\n" +
                "    font-size: 0.875rem;\n" +
                "    font-weight: 600;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    border: 1px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".player-badge:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".player-badge.active {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n" +
                "    border: 2px solid currentColor;\n" +
                "}\n" +
                "\n" +
                "/* Badge styles (next-litebans compatible) */\n" +
                ".badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    border-radius: 9999px;\n" +
                "    padding: 0.25rem 0.75rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    font-weight: 600;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".badge-secondary {\n" +
                "    background-color: hsl(210 40% 95%);\n" +
                "    color: hsl(215.4 16.3% 46.9%);\n" +
                "    border: 1px solid hsl(214.3 31.8% 91.4%);\n" +
                "}\n" +
                "\n" +
                ".badge-secondary:hover {\n" +
                "    background-color: hsl(210 40% 90%);\n" +
                "    transform: translateY(-1px);\n" +
                "}\n" +
                "\n" +
                ".badge-secondary.active {\n" +
                "    background-color: hsl(210 40% 85%);\n" +
                "    border-color: hsl(210 40% 70%);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".badge-icon {\n" +
                "    margin-right: 0.25rem;\n" +
                "}\n" +
                "\n" +
                "/* Dark mode badge styles */\n" +
                ".dark .badge-secondary {\n" +
                "    background-color: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .badge-secondary:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.8);\n" +
                "}\n" +
                "\n" +
                ".dark .badge-secondary.active {\n" +
                "    background-color: hsl(var(--muted) / 0.6);\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .badge-primary {\n" +
                "    background-color: hsl(220 13% 18%);\n" +
                "    color: hsl(210 40% 85%);\n" +
                "    border: 1px solid hsl(220 13% 25%);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);\n" +
                "}\n" +
                "\n" +
                ".dark .badge-primary:hover {\n" +
                "    background-color: hsl(220 13% 22%);\n" +
                "    border-color: hsl(220 13% 30%);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.15);\n" +
                "}\n" +
                "\n" +
                ".ban-badge {\n" +
                "    background: hsl(0 84.2% 60.2% / 0.1);\n" +
                "    color: hsl(0 72.2% 50.6%);\n" +
                "    border-color: hsl(0 84.2% 60.2% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".mute-badge {\n" +
                "    background: hsl(25 95% 53% / 0.1);\n" +
                "    color: hsl(25 95% 53%);\n" +
                "    border-color: hsl(25 95% 53% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".warn-badge {\n" +
                "    background: hsl(48 96% 53% / 0.1);\n" +
                "    color: hsl(48 96% 53%);\n" +
                "    border-color: hsl(48 96% 53% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".kick-badge {\n" +
                "    background: hsl(271.5 81.3% 55.9% / 0.1);\n" +
                "    color: hsl(271.5 81.3% 55.9%);\n" +
                "    border-color: hsl(271.5 81.3% 55.9% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".player-punishments-section {\n" +
                "    width: 100%;\n" +
                "    max-width: 1200px;\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的玩家页面样式 */\n" +
                ".dark .player-info-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .player-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 - 玩家页面 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .player-info-card {\n" +
                "        flex-direction: column;\n" +
                "        text-align: center;\n" +
                "        gap: 1.5rem;\n" +
                "        padding: 1.5rem;\n" +
                "    }\n" +
                "    \n" +
                "    .player-name {\n" +
                "        font-size: 2rem;\n" +
                "    }\n" +
                "    \n" +
                "    .player-badges {\n" +
                "        justify-content: center;\n" +
                "    }\n" +
                "}\n";
    }

    /**
     * 生成撤销弹窗CSS样式
     */
    private String generateRevokeModalCSS() {
        return "/* ==================== 撤销确认弹窗样式 ==================== */\n" +
                ".revoke-modal-overlay {\n" +
                "    position: fixed;\n" +
                "    top: 0;\n" +
                "    left: 0;\n" +
                "    right: 0;\n" +
                "    bottom: 0;\n" +
                "    background: rgba(0, 0, 0, 0.6);\n" +
                "    backdrop-filter: blur(4px);\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    z-index: 10000;\n" +
                "    opacity: 0;\n" +
                "    visibility: hidden;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-overlay.show {\n" +
                "    opacity: 1;\n" +
                "    visibility: visible;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal {\n" +
                "    background: white;\n" +
                "    border-radius: 16px;\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                "    width: 90%;\n" +
                "    max-width: 480px;\n" +
                "    max-height: 90vh;\n" +
                "    overflow: hidden;\n" +
                "    transform: scale(0.9) translateY(20px);\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-overlay.show .revoke-modal {\n" +
                "    transform: scale(1) translateY(0);\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-header {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    padding: 24px 24px 16px 24px;\n" +
                "    border-bottom: 1px solid #f1f3f4;\n" +
                "    background: linear-gradient(135deg, #ff6b6b, #ee5a52);\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-icon {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    margin-right: 12px;\n" +
                "    color: white;\n" +
                "    opacity: 0.9;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-icon svg {\n" +
                "    width: 100%;\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-title {\n" +
                "    flex: 1;\n" +
                "    font-size: 1.25rem;\n" +
                "    font-weight: 600;\n" +
                "    margin: 0;\n" +
                "    color: white;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-close {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border: none;\n" +
                "    background: rgba(255, 255, 255, 0.2);\n" +
                "    color: white;\n" +
                "    border-radius: 8px;\n" +
                "    cursor: pointer;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    transition: all 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-close:hover {\n" +
                "    background: rgba(255, 255, 255, 0.3);\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-close svg {\n" +
                "    width: 18px;\n" +
                "    height: 18px;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-body {\n" +
                "    padding: 24px;\n" +
                "    background: #fafbfc;\n" +
                "}\n" +
                "\n" +
                ".revoke-player-section {\n" +
                "    background: white;\n" +
                "    border-radius: 12px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 20px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n" +
                "}\n" +
                "\n" +
                ".revoke-player-info {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 16px;\n" +
                "}\n" +
                "\n" +
                ".revoke-player-avatar {\n" +
                "    width: 48px;\n" +
                "    height: 48px;\n" +
                "    border-radius: 8px;\n" +
                "    border: 2px solid #e9ecef;\n" +
                "    background: #f8f9fa;\n" +
                "}\n" +
                "\n" +
                ".revoke-player-details {\n" +
                "    flex: 1;\n" +
                "}\n" +
                "\n" +
                ".revoke-player-name {\n" +
                "    margin: 0 0 4px 0;\n" +
                "    font-size: 1.1rem;\n" +
                "    font-weight: 600;\n" +
                "    color: #2d3748;\n" +
                "}\n" +
                "\n" +
                ".revoke-player-label {\n" +
                "    font-size: 0.85rem;\n" +
                "    color: #6c757d;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                ".revoke-info-card {\n" +
                "    background: white;\n" +
                "    border-radius: 12px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 20px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n" +
                "}\n" +
                "\n" +
                ".revoke-info-item {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    padding: 12px 0;\n" +
                "    border-bottom: 1px solid #f1f3f4;\n" +
                "}\n" +
                "\n" +
                ".revoke-info-item:last-child {\n" +
                "    border-bottom: none;\n" +
                "}\n" +
                "\n" +
                ".revoke-info-label {\n" +
                "    font-weight: 600;\n" +
                "    color: #495057;\n" +
                "    font-size: 0.95rem;\n" +
                "}\n" +
                "\n" +
                ".revoke-info-value {\n" +
                "    font-weight: 500;\n" +
                "    color: #2d3748;\n" +
                "    font-size: 0.95rem;\n" +
                "}\n" +
                "\n" +
                ".revoke-type-ban {\n" +
                "    color: #e74c3c;\n" +
                "}\n" +
                "\n" +
                ".revoke-type-mute {\n" +
                "    color: #f39c12;\n" +
                "}\n" +
                "\n" +
                ".revoke-type-kick {\n" +
                "    color: #e67e22;\n" +
                "}\n" +
                "\n" +
                ".revoke-type-warn {\n" +
                "    color: #f1c40f;\n" +
                "}\n" +
                "\n" +
                ".revoke-reason-section {\n" +
                "    background: white;\n" +
                "    border-radius: 12px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 20px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n" +
                "}\n" +
                "\n" +
                ".revoke-reason-label {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    font-weight: 600;\n" +
                "    color: #495057;\n" +
                "    margin-bottom: 12px;\n" +
                "    font-size: 0.95rem;\n" +
                "}\n" +
                "\n" +
                ".revoke-reason-label svg {\n" +
                "    width: 18px;\n" +
                "    height: 18px;\n" +
                "    margin-right: 8px;\n" +
                "    color: #6c757d;\n" +
                "}\n" +
                "\n" +
                ".revoke-reason-input {\n" +
                "    width: 100%;\n" +
                "    padding: 12px 16px;\n" +
                "    border: 2px solid #e9ecef;\n" +
                "    border-radius: 8px;\n" +
                "    font-size: 0.95rem;\n" +
                "    font-family: inherit;\n" +
                "    resize: vertical;\n" +
                "    min-height: 80px;\n" +
                "    transition: all 0.2s ease;\n" +
                "    background: #fafbfc;\n" +
                "}\n" +
                "\n" +
                ".revoke-reason-input:focus {\n" +
                "    outline: none;\n" +
                "    border-color: #667eea;\n" +
                "    background: white;\n" +
                "    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n" +
                "}\n" +
                "\n" +
                ".revoke-warning {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    padding: 16px;\n" +
                "    background: linear-gradient(135deg, #fff3cd, #ffeaa7);\n" +
                "    border: 1px solid #ffeaa7;\n" +
                "    border-radius: 8px;\n" +
                "    color: #856404;\n" +
                "    font-size: 0.9rem;\n" +
                "    font-weight: 500;\n" +
                "}\n" +
                "\n" +
                ".revoke-warning svg {\n" +
                "    width: 20px;\n" +
                "    height: 20px;\n" +
                "    margin-right: 10px;\n" +
                "    color: #f39c12;\n" +
                "    flex-shrink: 0;\n" +
                "}\n" +
                "\n" +
                ".revoke-modal-footer {\n" +
                "    display: flex;\n" +
                "    gap: 12px;\n" +
                "    padding: 20px 24px 24px 24px;\n" +
                "    background: white;\n" +
                "    border-top: 1px solid #f1f3f4;\n" +
                "}\n" +
                "\n" +
                ".revoke-btn-cancel,\n" +
                ".revoke-btn-confirm {\n" +
                "    flex: 1;\n" +
                "    padding: 12px 24px;\n" +
                "    border: none;\n" +
                "    border-radius: 8px;\n" +
                "    font-size: 0.95rem;\n" +
                "    font-weight: 600;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.2s ease;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    min-height: 44px;\n" +
                "}\n" +
                "\n" +
                ".revoke-btn-cancel {\n" +
                "    background: #f8f9fa;\n" +
                "    color: #495057;\n" +
                "    border: 2px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".revoke-btn-cancel:hover {\n" +
                "    background: #e9ecef;\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".revoke-btn-confirm {\n" +
                "    background: linear-gradient(135deg, #e74c3c, #c0392b);\n" +
                "    color: white;\n" +
                "    border: 2px solid #e74c3c;\n" +
                "}\n" +
                "\n" +
                ".revoke-btn-confirm:hover {\n" +
                "    background: linear-gradient(135deg, #c0392b, #a93226);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);\n" +
                "}\n" +
                "\n" +
                "/* 撤销弹窗响应式 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .revoke-modal {\n" +
                "        width: 95%;\n" +
                "        max-width: 400px;\n" +
                "        margin: 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .revoke-modal-header {\n" +
                "        padding: 20px 20px 12px 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .revoke-modal-body {\n" +
                "        padding: 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .revoke-modal-footer {\n" +
                "        padding: 16px 20px 20px 20px;\n" +
                "        flex-direction: column;\n" +
                "    }\n" +
                "    \n" +
                "    .revoke-btn-cancel,\n" +
                "    .revoke-btn-confirm {\n" +
                "        width: 100%;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的撤销弹窗 */\n" +
                ".dark .revoke-modal {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-modal-body {\n" +
                "    background: hsl(var(--background));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-player-section,\n" +
                ".dark .revoke-info-card,\n" +
                ".dark .revoke-reason-section {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-player-avatar {\n" +
                "    border-color: hsl(var(--border));\n" +
                "    background: hsl(var(--muted));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-player-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-player-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-info-item {\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-info-label,\n" +
                ".dark .revoke-reason-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-info-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-reason-input {\n" +
                "    background: hsl(var(--input));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-reason-input:focus {\n" +
                "    background: hsl(var(--background));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-modal-footer {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-top-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-btn-cancel {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .revoke-btn-cancel:hover {\n" +
                "    background: hsl(var(--accent));\n" +
                "}\n";
    }

    /**
     * 生成背景CSS样式
     */
    private String generateBackgroundCSS() {
        // 获取配置中的背景设置
        String backgroundType = plugin.getConfig().getString("web-server.background.type", "gradient");
        String customImage = plugin.getConfig().getString("web-server.background.custom-image", "");

        switch (backgroundType.toLowerCase()) {
            case "image":
                if (!customImage.isEmpty()) {
                    return "    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)), url('" + customImage + "') center/cover;\n";
                }
                // 如果没有自定义图片，回退到渐变
                return "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n";
            case "solid":
                String solidColor = plugin.getConfig().getString("web-server.background.solid-color", "#f8f9fa");
                return "    background: " + solidColor + ";\n";
            case "gradient":
            default:
                return "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n";
        }
    }
}