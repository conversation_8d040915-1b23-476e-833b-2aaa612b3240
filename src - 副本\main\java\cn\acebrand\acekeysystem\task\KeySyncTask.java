package cn.acebrand.acekeysystem.task;

import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 卡密同步任务
 * 用于将卡密数据同步到网站
 */
public final class KeySyncTask extends BukkitRunnable {

    private final AceKeySystem plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public KeySyncTask(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void run() {
        // 执行卡密同步到网站
        this.plugin.syncKeysToWebsite();
    }
}
