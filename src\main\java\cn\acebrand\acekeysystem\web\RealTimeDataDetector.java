package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 实时数据检测器
 * 负责生成前端实时数据检测和自动更新功能的JavaScript代码
 * 类似积分商店的自动更新机制
 *
 * <AUTHOR>
 * @version 1.0
 */
public class RealTimeDataDetector {

    private final AceKeySystem plugin;

    public RealTimeDataDetector(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    /**
     * 生成实时数据检测功能（类似积分商店的自动更新机制）
     */
    public String generateAutoUpdateFunctions() {
        return "\n" +
                "// ==================== 实时数据检测功能 ====================\n" +
                "// 自动检测变量\n" +
                "let lastDataHash = null;\n" +
                "let autoUpdateInterval = null;\n" +
                "let isAutoUpdateEnabled = true;\n" +
                "let currentPageUrl = window.location.href;\n" +
                "\n" +
                "// 页面加载完成后启动自动检测\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    // 快速启动，实现实时更新\n" +
                "    setTimeout(() => {\n" +
                "        startAutoUpdate();\n" +
                "    }, 500);\n" +
                "});\n" +
                "\n" +
                "// 启动自动检测更新\n" +
                "function startAutoUpdate() {\n" +
                "    if (autoUpdateInterval) {\n" +
                "        clearInterval(autoUpdateInterval);\n" +
                "    }\n" +
                "    \n" +
                "    // 每1秒检测一次数据变化（实时更新）\n" +
                "    autoUpdateInterval = setInterval(() => {\n" +
                "        if (isAutoUpdateEnabled) {\n" +
                "            checkForDataUpdates();\n" +
                "        }\n" +
                "    }, 1000);\n" +
                "}\n" +
                "\n" +
                "// 停止自动检测更新\n" +
                "function stopAutoUpdate() {\n" +
                "    if (autoUpdateInterval) {\n" +
                "        clearInterval(autoUpdateInterval);\n" +
                "        autoUpdateInterval = null;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 检测数据更新\n" +
                "function checkForDataUpdates() {\n" +
                "    // 获取当前页面的URL参数\n" +
                "    const urlParams = new URLSearchParams(window.location.search);\n" +
                "    const currentPage = urlParams.get('page') || '1';\n" +
                "    const playerFilter = urlParams.get('player') || '';\n" +
                "    const staffFilter = urlParams.get('staff') || '';\n" +
                "    const typeFilter = getCurrentTypeFromPath();\n"
                +
                "    \n" +
                "    // 构建检测请求\n" +
                "    const requestData = {\n" +
                "        action: 'checkDataUpdate',\n" +
                "        page: currentPage,\n" +
                "        player: playerFilter,\n" +
                "        staff: staffFilter,\n" +
                "        type: typeFilter\n" +
                "    };\n" +
                "    \n" +
                "    fetch('/admin-punishments', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json',\n" +
                "        },\n" +
                "        body: JSON.stringify(requestData)\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "\n" +
                "        if (data.success && data.dataHash) {\n" +
                "            const currentHash = data.dataHash;\n" +
                "\n" +
                "            if (lastDataHash && lastDataHash !== currentHash) {\n" +
                "\n" +
                "                // 数据发生变化，更新页面内容\n" +
                "                updatePageContent(data);\n" +
                "                showUpdateNotification('🔄 数据已更新');\n" +
                "            } else if (!lastDataHash) {\n" +
                "\n" +
                "            } else {\n" +
                "\n" +
                "            }\n" +
                "            lastDataHash = currentHash;\n" +
                "        } else {\n" +
                "\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "\n" +
                "        // 静默失败，不影响用户体验\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新页面内容\n" +
                "function updatePageContent(data) {\n" +
                "\n" +
                "    \n" +
                "    // 检查是否是详细页面\n" +
                "    const isDetailPage = window.location.pathname.includes('/admin-punishments/') && \n" +
                "                         window.location.pathname.split('/').length >= 4;\n" +
                "    \n" +
                "    if (isDetailPage) {\n" +
                "        // 详细页面更新\n" +
                "        updateDetailPage(data);\n" +
                "    } else {\n" +
                "        // 列表页面更新\n" +
                "        updateListPage(data);\n" +
                "    }\n" +
                "    \n" +
                "    // 更新统计信息\n" +
                "    if (data.statistics) {\n" +
                "        updateStatistics(data.statistics);\n" +
                "    }\n" +
                "    \n" +
                "    // 重新初始化动态时间显示\n" +
                "    if (typeof initRelativeTime === 'function') {\n" +
                "        initRelativeTime();\n" +
                "    }\n" +
                "    \n" +
                "    // 重新初始化通知功能\n" +
                "    if (typeof initNotifications === 'function') {\n" +
                "        initNotifications();\n" +
                "    }\n" +
                "    \n" +
                "\n" +
                "}\n" +
                "\n" +
                "// 更新列表页面\n" +
                "function updateListPage(data) {\n" +
                "    // 尝试多种表格选择器\n" +
                "    let tableBody = document.querySelector('.punishment-table tbody');\n" +
                "    if (!tableBody) {\n" +
                "        tableBody = document.querySelector('.admin-punishment-table tbody');\n" +
                "    }\n" +
                "    if (!tableBody) {\n" +
                "        tableBody = document.querySelector('table tbody');\n" +
                "    }\n" +
                "    \n" +
                "\n" +
                "\n" +
                "    \n" +
                "    if (tableBody && data.tableRows) {\n" +
                "        // 保存当前滚动位置\n" +
                "        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n" +
                "        \n" +
                "        // 更新表格内容\n" +
                "        tableBody.innerHTML = data.tableRows;\n" +
                "\n" +
                "        \n" +
                "        // 恢复滚动位置\n" +
                "        window.scrollTo(0, scrollTop);\n" +
                "    } else {\n" +
                "\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新详细页面\n" +
                "function updateDetailPage(data) {\n" +
                "\n" +
                "    \n" +
                "    // 如果有记录数据，更新详细页面内容\n" +
                "    if (data.recordData) {\n" +
                "        updateDetailPageContent(data.recordData);\n" +
                "    } else {\n" +
                "        // 没有具体记录数据，重新获取当前记录的最新状态\n" +
                "        const pathParts = window.location.pathname.split('/');\n" +
                "        if (pathParts.length >= 4) {\n" +
                "            const recordId = pathParts[pathParts.length - 1];\n" +
                "            const recordType = pathParts[pathParts.length - 2];\n" +
                "            fetchDetailPageUpdate(recordType, recordId);\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 获取详细页面更新\n" +
                "function fetchDetailPageUpdate(type, id) {\n" +
                "    fetch(`/admin-punishments/${type}/${id}`, {\n" +
                "        method: 'GET',\n" +
                "        headers: {\n" +
                "            'X-Requested-With': 'XMLHttpRequest'\n" +
                "        }\n" +
                "    })\n" +
                "    .then(response => response.text())\n" +
                "    .then(html => {\n" +
                "        // 解析返回的HTML，提取关键信息\n" +
                "        const parser = new DOMParser();\n" +
                "        const doc = parser.parseFromString(html, 'text/html');\n" +
                "        \n" +
                "        // 更新状态徽章\n" +
                "        const statusBadges = doc.querySelector('.detail-badges');\n" +
                "        if (statusBadges) {\n" +
                "            const currentBadges = document.querySelector('.detail-badges');\n" +
                "            if (currentBadges) {\n" +
                "                currentBadges.innerHTML = statusBadges.innerHTML;\n" +
                "                console.log('状态徽章已更新');\n" +
                "            }\n" +
                "        }\n" +
                "        \n" +
                "        // 更新操作按钮\n" +
                "        const actionButtons = doc.querySelector('.admin-detail-actions');\n" +
                "        const currentActions = document.querySelector('.admin-detail-actions');\n" +
                "        \n" +
                "        if (actionButtons && currentActions) {\n" +
                "            // 有新的操作按钮，更新内容\n" +
                "            currentActions.innerHTML = actionButtons.innerHTML;\n" +
                "            currentActions.style.display = '';\n" +
                "            console.log('操作按钮已更新');\n" +
                "        } else if (!actionButtons && currentActions) {\n" +
                "            // 没有操作按钮了（处罚已被撤销），隐藏按钮区域\n" +
                "            currentActions.style.display = 'none';\n" +
                "            console.log('操作按钮已隐藏（处罚已撤销）');\n" +
                "        }\n" +
                "        \n" +
                "        // 更新动态时间\n" +
                "        const relativeTimeElements = doc.querySelectorAll('.relative-time');\n" +
                "        relativeTimeElements.forEach((newElement, index) => {\n" +
                "            const currentElement = document.querySelectorAll('.relative-time')[index];\n" +
                "            if (currentElement && newElement) {\n" +
                "                currentElement.setAttribute('data-timestamp', newElement.getAttribute('data-timestamp'));\n"
                +
                "                currentElement.textContent = newElement.textContent;\n" +
                "            }\n" +
                "        });\n" +
                "        \n" +
                "        console.log('详细页面内容已更新');\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.warn('获取详细页面更新失败:', error);\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新统计信息\n" +
                "function updateStatistics(statistics) {\n" +
                "    // 参数检查\n" +
                "    if (!statistics) {\n" +
                "        console.error('updateStatistics: statistics 参数未定义');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    console.log('updateStatistics 调用，参数:', statistics);\n" +
                "    \n" +
                "    // 检查是否是玩家页面，如果是则只更新玩家徽章\n" +
                "    const isPlayerPage = window.location.pathname.includes('/admin-player/') || window.location.pathname.includes('/player/');\n"
                +
                "    \n" +
                "    if (isPlayerPage) {\n" +
                "        // 玩家页面：获取新的DOM来更新徽章（使用与普通玩家页面相同的方式）\n" +
                "        const playerName = window.location.pathname.split('/').pop();\n" +
                "        const currentUrl = window.location.href;\n" +
                "        \n" +
                "        // 获取新的页面内容来提取徽章数据\n" +
                "        fetch(currentUrl, {\n" +
                "            method: 'GET',\n" +
                "            headers: {\n" +
                "                'X-Requested-With': 'XMLHttpRequest'\n" +
                "            }\n" +
                "        })\n" +
                "        .then(response => response.text())\n" +
                "        .then(html => {\n" +
                "            const parser = new DOMParser();\n" +
                "            const doc = parser.parseFromString(html, 'text/html');\n" +
                "            \n" +
                "            // 使用DOM提取方式更新所有记录徽章\n" +
                "            updateAdminNavigationBadges(doc);\n" +
                "        })\n" +
                "        .catch(error => {\n" +
                "            console.warn('获取玩家页面更新失败，使用统计数据方式:', error);\n" +
                "            // 降级到统计数据方式\n" +
                "            Object.keys(statistics).forEach(key => {\n" +
                "                if (key === 'total_ban' || key === 'totalBans') {\n" +
                "                    updatePlayerBadge('ban', statistics[key]);\n" +
                "                } else if (key === 'total_mute' || key === 'totalMutes') {\n" +
                "                    updatePlayerBadge('mute', statistics[key]);\n" +
                "                } else if (key === 'total_warn' || key === 'totalWarns') {\n" +
                "                    updatePlayerBadge('warn', statistics[key]);\n" +
                "                } else if (key === 'total_kick' || key === 'totalKicks') {\n" +
                "                    updatePlayerBadge('kick', statistics[key]);\n" +
                "                }\n" +
                "            });\n" +
                "        });\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    // 非玩家页面：更新导航栏中的徽章数量\n" +
                "    Object.keys(statistics).forEach(key => {\n" +
                "        // 尝试多种选择器来找到统计元素\n" +
                "        let elements = [\n" +
                "            document.querySelector(`[data-stat='${key}']`),\n" +
                "            document.querySelector(`.nav-badge[data-stat='${key}']`),\n" +
                "            document.querySelector(`.admin-nav-badge[data-stat='${key}']`),\n" +
                "            document.querySelector(`#stat-${key}`),\n" +
                "            document.querySelector(`.stat-${key}`)\n" +
                "        ].filter(el => el !== null);\n" +
                "        \n" +
                "        elements.forEach(element => {\n" +
                "            element.textContent = statistics[key];\n" +
                "\n"
                +
                "        });\n" +
                "        \n" +
                "        // 调试：如果没有找到元素，记录日志\n" +
                "        if (elements.length === 0) {\n" +
                "\n" +
                "        }\n" +
                "        \n" +
                "        // 特殊处理：更新处罚统计卡片（支持PunishmentManager的键名格式）\n" +
                "        if (key === 'total_ban' || key === 'totalBans') {\n" +
                "            updateStatCard('ban', statistics[key]);\n" +
                "            updateNavigationBadge('ban', statistics[key]);\n" +
                "            updatePlayerBadge('ban', statistics[key]);\n" +
                "        } else if (key === 'total_mute' || key === 'totalMutes') {\n" +
                "            updateStatCard('mute', statistics[key]);\n" +
                "            updateNavigationBadge('mute', statistics[key]);\n" +
                "            updatePlayerBadge('mute', statistics[key]);\n" +
                "        } else if (key === 'total_warn' || key === 'totalWarns') {\n" +
                "            updateStatCard('warn', statistics[key]);\n" +
                "            updateNavigationBadge('warn', statistics[key]);\n" +
                "            updatePlayerBadge('warn', statistics[key]);\n" +
                "        } else if (key === 'total_kick' || key === 'totalKicks') {\n" +
                "            updateStatCard('kick', statistics[key]);\n" +
                "            updateNavigationBadge('kick', statistics[key]);\n" +
                "            updatePlayerBadge('kick', statistics[key]);\n" +
                "        }\n" +
                "    });\n" +
                "    \n" +
                "    // 计算总数并更新所有记录徽章\n" +
                "    const totalAll = (parseInt(statistics['total_ban'] || 0) + \n" +
                "                     parseInt(statistics['total_mute'] || 0) + \n" +
                "                     parseInt(statistics['total_warn'] || 0) + \n" +
                "                     parseInt(statistics['total_kick'] || 0));\n" +
                "    \n" +
                "    console.log('计算所有记录总数:', {\n" +
                "        ban: statistics['total_ban'] || 0,\n" +
                "        mute: statistics['total_mute'] || 0,\n" +
                "        warn: statistics['total_warn'] || 0,\n" +
                "        kick: statistics['total_kick'] || 0,\n" +
                "        total: totalAll\n" +
                "    });\n" +
                "    \n" +
                "    const playerName = window.location.pathname.split('/').pop();\n" +
                "    const allBadgeSelectors = [\n" +
                "        'a.badge[data-type=\"all\"][data-stat=\"total_all\"]',\n" +
                "        'a.badge[data-type=\"all\"]',\n" +
                "        '[data-type=\"all\"][data-stat=\"total_all\"]',\n" +
                "        '[data-type=\"all\"]',\n" +
                "        '[data-stat=\"total_all\"]',\n" +
                "        `a.badge[href='/admin-player/${playerName}']:not([href*=\"type=\"]):not([href*=\"?\"])`,\n" +
                "        `a.badge[href='/player/${playerName}']:not([href*=\"type\"]):not([href*=\"?\"])`,\n" +
                "        'a.badge.badge-primary:not([href*=\"type\"]):not([href*=\"?\"])'\n" +
                "    ];\n" +
                "    \n" +
                "    allBadgeSelectors.forEach(selector => {\n" +
                "        const allBadges = document.querySelectorAll(selector);\n" +
                "        console.log(`查找 all 徽章，选择器: ${selector}，找到: ${allBadges.length} 个`);\n" +
                "        allBadges.forEach(badge => {\n" +
                "            if (badge.classList.contains('badge')) {\n" +
                "                const countElement = badge.querySelector('.badge-count');\n" +
                "                if (countElement) {\n" +
                "                    console.log(`更新 all 徽章数量: ${countElement.textContent} -> ${totalAll}`);\n" +
                "                    countElement.textContent = totalAll;\n" +
                "                }\n" +
                "                \n" +
                "                // 如果数量为0，隐藏徽章；如果大于0，显示徽章\n" +
                "                if (totalAll > 0) {\n" +
                "                    badge.style.display = '';\n" +
                "                } else {\n" +
                "                    badge.style.display = 'none';\n" +
                "                }\n" +
                "            }\n" +
                "        });\n" +
                "    });\n" +
                "    \n" +
                "    // 同时调用updatePlayerBadge来更新'all'类型徽章\n" +
                "    updatePlayerBadge('all', totalAll);\n" +
                "    \n" +
                "    // 重新开始forEach循环处理其他统计\n" +
                "    Object.keys(statistics).forEach(key => {\n" +
                "        \n" +
                "        // 更新活跃数量\n" +
                "        if (key === 'active_ban') {\n" +
                "            updateActiveCount('ban', statistics[key]);\n" +
                "        } else if (key === 'active_mute') {\n" +
                "            updateActiveCount('mute', statistics[key]);\n" +
                "        } else if (key === 'active_warn') {\n" +
                "            updateActiveCount('warn', statistics[key]);\n" +
                "        } else if (key === 'active_kick') {\n" +
                "            updateActiveCount('kick', statistics[key]);\n" +
                "        }\n" +
                "        \n" +
                "        // 更新今日数量\n" +
                "        if (key === 'today_ban') {\n" +
                "            updateTodayCount('ban', statistics[key]);\n" +
                "        } else if (key === 'today_mute') {\n" +
                "            updateTodayCount('mute', statistics[key]);\n" +
                "        } else if (key === 'today_warn') {\n" +
                "            updateTodayCount('warn', statistics[key]);\n" +
                "        } else if (key === 'today_kick') {\n" +
                "            updateTodayCount('kick', statistics[key]);\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 专门更新管理员页面的所有记录徽章（复制自普通玩家页面的逻辑）\n" +
                "function updateAdminAllRecordsBadge(statistics) {\n" +
                "    // 计算总数\n" +
                "    const totalAll = (parseInt(statistics['total_ban'] || 0) + \n" +
                "                     parseInt(statistics['total_mute'] || 0) + \n" +
                "                     parseInt(statistics['total_warn'] || 0) + \n" +
                "                     parseInt(statistics['total_kick'] || 0));\n" +
                "    \n" +
                "    console.log('管理员页面 - 计算所有记录总数:', {\n" +
                "        ban: statistics['total_ban'] || 0,\n" +
                "        mute: statistics['total_mute'] || 0,\n" +
                "        warn: statistics['total_warn'] || 0,\n" +
                "        kick: statistics['total_kick'] || 0,\n" +
                "        total: totalAll\n" +
                "    });\n" +
                "    \n" +
                "    const playerName = window.location.pathname.split('/').pop();\n" +
                "    const allBadgeSelectors = [\n" +
                "        '[data-type=\"all\"]',\n" +
                "        '[data-stat=\"total_all\"]',\n" +
                "        `a.badge[href='/admin-player/${playerName}']`,\n" +
                "        `a.badge[href='/player/${playerName}']`,\n" +
                "        'a.badge.badge-primary:not([href*=\"type\"])'\n" +
                "    ];\n" +
                "    \n" +
                "    allBadgeSelectors.forEach(selector => {\n" +
                "        const allBadges = document.querySelectorAll(selector);\n" +
                "        if (allBadges.length > 0) {\n" +
                "            console.log(`管理员页面 - 找到 ${allBadges.length} 个匹配的 all 徽章，选择器: ${selector}`);\n" +
                "        }\n" +
                "        allBadges.forEach(badge => {\n" +
                "            if (badge.classList.contains('badge')) {\n" +
                "                const countElement = badge.querySelector('.badge-count');\n" +
                "                if (countElement) {\n" +
                "                    console.log(`管理员页面 - 更新 all 徽章数量: ${countElement.textContent} -> ${totalAll}`);\n"
                +
                "                    countElement.textContent = totalAll;\n" +
                "                }\n" +
                "                \n" +
                "                // 如果数量为0，隐藏徽章；如果大于0，显示徽章\n" +
                "                if (totalAll > 0) {\n" +
                "                    badge.style.display = '';\n" +
                "                } else {\n" +
                "                    badge.style.display = 'none';\n" +
                "                }\n" +
                "            }\n" +
                "        });\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新统计卡片\n" +
                "function updateStatCard(type, count) {\n" +
                "    const selectors = [\n" +
                "        `.admin-stat-card[data-type='${type}'] .admin-stat-number`,\n" +
                "        `.stat-card[data-type='${type}'] .stat-number`,\n" +
                "        `.punishment-stat[data-type='${type}'] .stat-count`,\n" +
                "        `[data-punishment-type='${type}'] .count`,\n" +
                "        `#${type}-count`,\n" +
                "        `.${type}-total`,\n" +
                "        `[data-stat='total_${type}']`,\n" +
                "        `[data-stat='${type}_total']`\n" +
                "    ];\n" +
                "    \n" +
                "    selectors.forEach(selector => {\n" +
                "        const element = document.querySelector(selector);\n" +
                "        if (element) {\n" +
                "            element.textContent = count;\n" +
                "\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新导航徽章\n" +
                "function updateNavigationBadge(type, count) {\n" +
                "    const selectors = [\n" +
                "        `.admin-nav-badge[data-type='${type}']`,\n" +
                "        `.nav-badge[data-type='${type}']`,\n" +
                "        `.tab-btn[href*='${type}'] .badge`,\n" +
                "        `[data-nav-type='${type}'] .badge`,\n" +
                "        `#nav-${type}-count`\n" +
                "    ];\n" +
                "    \n" +
                "    selectors.forEach(selector => {\n" +
                "        const element = document.querySelector(selector);\n" +
                "        if (element) {\n" +
                "            element.textContent = count;\n" +
                "\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新活跃数量\n" +
                "function updateActiveCount(type, count) {\n" +
                "    const countInt = parseInt(count) || 0;\n" +
                "    \n" +
                "    // 查找活跃状态容器\n" +
                "    const activeContainer = document.querySelector(`[data-stat='active_${type}']`);\n" +
                "    if (activeContainer) {\n" +
                "        if (type === 'kick' || type === 'warn') {\n" +
                "            // 踢出和警告：使用总数，只有大于0时才显示\n" +
                "            const totalElement = document.querySelector(`[data-stat='total_${type}']`);\n" +
                "            const totalCount = totalElement ? parseInt(totalElement.textContent) || 0 : 0;\n" +
                "            \n" +
                "            if (totalCount > 0) {\n" +
                "                const statusLabel = type === 'kick' ? '总踢出' : '总警告';\n" +
                "                activeContainer.innerHTML = `${statusLabel}: <span class=\"active-count\">${totalCount}</span>`;\n"
                +
                "            } else {\n" +
                "                activeContainer.innerHTML = '&nbsp;';\n" +
                "            }\n" +
                "        } else {\n" +
                "            // 封禁和禁言：使用活跃数，只有大于0时才显示\n" +
                "            if (countInt > 0) {\n" +
                "                activeContainer.innerHTML = `生效中: <span class=\"active-count\">${countInt}</span>`;\n"
                +
                "            } else {\n" +
                "                activeContainer.innerHTML = '&nbsp;';\n" +
                "            }\n" +
                "        }\n" +
                "\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新玩家页面徽章\n" +
                "function updatePlayerBadge(type, count) {\n" +
                "    const countInt = parseInt(count) || 0;\n" +
                "    \n" +
                "    // 查找玩家页面的徽章\n" +
                "    // 查找玩家页面的徽章 - 使用多种选择器\n" +
                "    const selectors = [\n" +
                "        `[data-type='${type}'][data-stat='total_${type}']`,\n" +
                "        `[data-type='${type}'].badge`,\n" +
                "        `.badge[data-type='${type}']`,\n" +
                "        `a.badge[href*='${type}']`,\n" +
                "        `a.badge[href*='player'][href*='${type}']`\n" +
                "    ];\n" +
                "    \n" +
                "    // 特殊处理：对于'all'类型，使用更通用的选择器\n" +
                "    if (type === 'all') {\n" +
                "        const playerName = window.location.pathname.split('/').pop();\n" +
                "        selectors.length = 0; // 清空默认选择器，使用专门的选择器\n" +
                "        selectors.push(\n" +
                "            `a.badge[data-type='all'][data-stat='total_all']`,\n" +
                "            `a.badge[data-type='all']`,\n" +
                "            `[data-stat='total_all']`,\n" +
                "            `a.badge[href='/admin-player/${playerName}']:not([href*='type='])`,\n" +
                "            `a.badge[href='/player/${playerName}']:not([href*='type='])`,\n" +
                "            `a.badge.badge-primary:not([href*='type='])`\n" +
                "        );\n" +
                "    }\n" +
                "    \n" +
                "    let updated = false;\n" +
                "    \n" +
                "    selectors.forEach(selector => {\n" +
                "        const badges = document.querySelectorAll(selector);\n" +
                "        if (badges.length > 0) {\n" +
                "            console.log(`找到 ${badges.length} 个匹配的 ${type} 徽章，选择器: ${selector}`);\n" +
                "        }\n" +
                "        badges.forEach(badge => {\n" +
                "            if (badge.classList.contains('badge')) {\n" +
                "                const countElement = badge.querySelector('.badge-count');\n" +
                "                if (countElement) {\n" +
                "                    console.log(`更新 ${type} 徽章数量: ${countElement.textContent} -> ${countInt}`);\n" +
                "                    countElement.textContent = countInt;\n" +
                "                    updated = true;\n" +
                "                }\n" +
                "                \n" +
                "                // 如果数量为0，隐藏徽章；如果大于0，显示徽章\n" +
                "                if (countInt > 0) {\n" +
                "                    badge.style.display = '';\n" +
                "                } else {\n" +
                "                    badge.style.display = 'none';\n" +
                "                }\n" +
                "            }\n" +
                "        });\n" +
                "    });\n" +
                "    \n" +
                "    if (!updated && type === 'all') {\n" +
                "        console.warn('未能更新 all 类型徽章，尝试查找所有可能的徽章元素');\n" +
                "        const allBadges = document.querySelectorAll('a.badge');\n" +
                "        console.log('页面上所有徽章:', allBadges);\n" +
                "        allBadges.forEach((badge, index) => {\n" +
                "            console.log(`徽章 ${index}:`, {\n" +
                "                href: badge.href,\n" +
                "                dataType: badge.getAttribute('data-type'),\n" +
                "                dataStat: badge.getAttribute('data-stat'),\n" +
                "                classes: badge.className,\n" +
                "                text: badge.textContent.trim()\n" +
                "            });\n" +
                "        });\n" +
                "    }\n" +
                "    \n" +
                "    // 注意：不自动创建新徽章，因为徽章应该在服务器端生成\n" +
                "}\n" +
                "\n" +
                "// 创建新的玩家徽章\n" +
                "function createPlayerBadge(type, count) {\n" +
                "    const badgeContainer = document.querySelector('.flex.space-x-2.whitespace-nowrap');\n" +
                "    if (!badgeContainer) return;\n" +
                "    \n" +
                "    // 检查是否已存在该类型的徽章\n" +
                "    const existingBadge = badgeContainer.querySelector(`[data-type='${type}']`);\n" +
                "    if (existingBadge) return;\n" +
                "    \n" +
                "    // 创建新徽章\n" +
                "    const badge = document.createElement('a');\n" +
                "    badge.className = 'badge badge-secondary';\n" +
                "    badge.setAttribute('data-type', type);\n" +
                "    badge.setAttribute('data-stat', `total_${type}`);\n" +
                "    \n" +
                "    // 设置链接\n" +
                "    const playerName = window.location.pathname.split('/').pop();\n" +
                "    if (window.location.pathname.includes('/admin-player/')) {\n" +
                "        badge.href = `/admin-punishments/${type}?player=${encodeURIComponent(playerName)}`;\n" +
                "    } else {\n" +
                "        badge.href = `/punishments/${type}?search=${encodeURIComponent(playerName)}`;\n" +
                "    }\n" +
                "    \n" +
                "    // 设置图标和文本\n" +
                "    const icons = { ban: '🚫', mute: '🔇', warn: '⚠️', kick: '👢' };\n" +
                "    const names = { ban: '封禁', mute: '禁言', warn: '警告', kick: '踢出' };\n" +
                "    \n" +
                "    badge.innerHTML = `<span class=\"badge-icon\">${icons[type] || '📋'}</span> <span class=\"badge-count\">${count}</span> ${names[type] || type}`;\n"
                +
                "    \n" +
                "    // 添加到容器\n" +
                "    badgeContainer.appendChild(badge);\n" +
                "}\n" +
                "\n" +
                "// 更新今日数量\n" +
                "function updateTodayCount(type, count) {\n" +
                "    const selectors = [\n" +
                "        `.${type}-today`,\n" +
                "        `[data-stat='today_${type}']`,\n" +
                "        `[data-today-type='${type}']`,\n" +
                "        `#${type}-today-count`,\n" +
                "        `.admin-stat-card[data-type='${type}'] .today-count`\n" +
                "    ];\n" +
                "    \n" +
                "    selectors.forEach(selector => {\n" +
                "        const element = document.querySelector(selector);\n" +
                "        if (element) {\n" +
                "            element.textContent = count;\n" +
                "\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 获取当前页面的处罚类型\n" +
                "function getCurrentTypeFromPath() {\n" +
                "    const path = window.location.pathname;\n" +
                "    if (path.includes('/ban')) return 'ban';\n" +
                "    if (path.includes('/mute')) return 'mute';\n" +
                "    if (path.includes('/warn')) return 'warn';\n" +
                "    if (path.includes('/kick')) return 'kick';\n" +
                "    return '';\n" +
                "}\n" +
                "\n" +
                "// 显示更新通知\n" +
                "function showUpdateNotification(message) {\n" +
                "    const notification = document.createElement('div');\n" +
                "    notification.className = 'update-notification';\n" +
                "    notification.textContent = message;\n" +
                "    notification.style.cssText = `\n" +
                "        position: fixed;\n" +
                "        top: 20px;\n" +
                "        left: 50%;\n" +
                "        transform: translateX(-50%);\n" +
                "        padding: 12px 24px;\n" +
                "        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "        color: white;\n" +
                "        border-radius: 25px;\n" +
                "        font-weight: 600;\n" +
                "        font-size: 0.9rem;\n" +
                "        z-index: 10000;\n" +
                "        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\n" +
                "        animation: slideInFromTop 0.5s ease;\n" +
                "    `;\n" +
                "    \n" +
                "    document.body.appendChild(notification);\n" +
                "    \n" +
                "    setTimeout(() => {\n" +
                "        notification.style.animation = 'slideOutToTop 0.5s ease';\n" +
                "        setTimeout(() => {\n" +
                "            if (notification.parentNode) {\n" +
                "                document.body.removeChild(notification);\n" +
                "            }\n" +
                "        }, 500);\n" +
                "    }, 3000);\n" +
                "}\n" +
                "\n" +
                "// 添加更新通知动画样式\n" +
                "const updateStyle = document.createElement('style');\n" +
                "updateStyle.textContent = `\n" +
                "    @keyframes slideInFromTop {\n" +
                "        from { transform: translate(-50%, -100%); opacity: 0; }\n" +
                "        to { transform: translate(-50%, 0); opacity: 1; }\n" +
                "    }\n" +
                "    @keyframes slideOutToTop {\n" +
                "        from { transform: translate(-50%, 0); opacity: 1; }\n" +
                "        to { transform: translate(-50%, -100%); opacity: 0; }\n" +
                "    }\n" +
                "`;\n" +
                "document.head.appendChild(updateStyle);\n" +
                "\n" +
                "// 页面可见性变化时的处理\n" +
                "document.addEventListener('visibilitychange', function() {\n" +
                "    if (document.hidden) {\n" +
                "        // 页面隐藏时停止检测\n" +
                "        isAutoUpdateEnabled = false;\n" +
                "    } else {\n" +
                "        // 页面显示时恢复检测\n" +
                "        isAutoUpdateEnabled = true;\n" +
                "        // 立即检测一次\n" +
                "        setTimeout(checkForDataUpdates, 100);\n" +
                "    }\n" +
                "});\n" +
                "\n" +
                "// 页面卸载时清理\n" +
                "window.addEventListener('beforeunload', function() {\n" +
                "    stopAutoUpdate();\n" +
                "});\n" +
                "\n" +
                "// 更新管理员导航徽章（从DOM中提取）\n" +
                "function updateAdminNavigationBadges(doc) {\n" +
                "    if (!doc) return;\n" +
                "    \n" +
                "    console.log('开始更新管理员导航徽章');\n" +
                "    \n" +
                "    // 查找新页面中的所有徽章\n" +
                "    const newBadges = doc.querySelectorAll('.badge');\n" +
                "    console.log(`在新页面中找到 ${newBadges.length} 个徽章`);\n" +
                "    \n" +
                "    newBadges.forEach((newBadge, index) => {\n" +
                "        const dataType = newBadge.getAttribute('data-type');\n" +
                "        const dataStat = newBadge.getAttribute('data-stat');\n" +
                "        const href = newBadge.getAttribute('href');\n" +
                "        \n" +
                "        console.log(`处理徽章 ${index}:`, { dataType, dataStat, href });\n" +
                "        \n" +
                "        // 找到对应的当前徽章\n" +
                "        let currentBadge = null;\n" +
                "        if (dataType && dataStat) {\n" +
                "            currentBadge = document.querySelector(`[data-type='${dataType}'][data-stat='${dataStat}']`);\n"
                +
                "        } else if (href) {\n" +
                "            currentBadge = document.querySelector(`[href='${href}']`);\n" +
                "        }\n" +
                "        \n" +
                "        if (currentBadge) {\n" +
                "            // 更新徽章内容\n" +
                "            const newCount = newBadge.querySelector('.badge-count');\n" +
                "            const currentCount = currentBadge.querySelector('.badge-count');\n" +
                "            \n" +
                "            if (newCount && currentCount) {\n" +
                "                const newValue = parseInt(newCount.textContent) || 0;\n" +
                "                const oldValue = parseInt(currentCount.textContent) || 0;\n" +
                "                \n" +
                "                if (newValue !== oldValue) {\n" +
                "                    console.log(`更新徽章 ${dataType || href}: ${oldValue} -> ${newValue}`);\n" +
                "                    currentCount.textContent = newValue;\n" +
                "                    \n" +
                "                    // 根据数量显示或隐藏徽章\n" +
                "                    if (newValue > 0) {\n" +
                "                        currentBadge.style.display = '';\n" +
                "                    } else {\n" +
                "                        currentBadge.style.display = 'none';\n" +
                "                    }\n" +
                "                }\n" +
                "            }\n" +
                "        } else {\n" +
                "            console.log(`未找到对应的当前徽章: ${dataType || href}`);\n" +
                "        }\n" +
                "    });\n" +
                "    \n" +
                "    console.log('管理员导航徽章更新完成');\n" +
                "}\n";
    }
}