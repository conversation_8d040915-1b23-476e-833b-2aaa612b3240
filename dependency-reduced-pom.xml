<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>cn.acebrand</groupId>
  <artifactId>acekeysystem</artifactId>
  <name>Minecraft卡密生成和管理插件</name>
  <version>1.0-SNAPSHOT</version>
  <description>AceKeySystem - Minecraft卡密生成和管理插件</description>
  <build>
    <defaultGoal>clean package</defaultGoal>
    <resources>
      <resource>
        <filtering>true</filtering>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>web/**</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <includes>
          <include>web/**</include>
        </includes>
      </resource>
    </resources>
    <finalName>${project.artifactId}-${project.version}</finalName>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.4</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <relocations>
                <relocation>
                  <pattern>org.json.simple</pattern>
                  <shadedPattern>cn.acebrand.acekeysystem.libs.json</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.mysql</pattern>
                  <shadedPattern>cn.acebrand.acekeysystem.libs.mysql</shadedPattern>
                </relocation>
                <relocation>
                  <pattern>com.zaxxer.hikari</pattern>
                  <shadedPattern>cn.acebrand.acekeysystem.libs.hikari</shadedPattern>
                </relocation>
              </relocations>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <repositories>
    <repository>
      <id>spigot-repo</id>
      <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
    </repository>
    <repository>
      <id>sonatype</id>
      <url>https://oss.sonatype.org/content/groups/public/</url>
    </repository>
    <repository>
      <id>nms-repo</id>
      <url>https://repo.codemc.org/repository/nms/</url>
    </repository>
  </repositories>
  <dependencies>
    <dependency>
      <groupId>org.spigotmc</groupId>
      <artifactId>spigot-api</artifactId>
      <version>1.20.1-R0.1-SNAPSHOT</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
      <version>8.0.33</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>me.clip</groupId>
      <artifactId>placeholderapi</artifactId>
      <version>2.11.6</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/libs/PlaceholderAPI-2.11.6.jar</systemPath>
    </dependency>
    <dependency>
      <groupId>net.milkbowl.vault</groupId>
      <artifactId>VaultAPI</artifactId>
      <version>1.7</version>
      <scope>system</scope>
      <systemPath>${project.basedir}/libs/Vault.jar</systemPath>
    </dependency>
  </dependencies>
  <properties>
    <maven.compiler.target>8</maven.compiler.target>
    <maven.compiler.source>8</maven.compiler.source>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>
</project>
