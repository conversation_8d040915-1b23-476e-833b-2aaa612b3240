package cn.acebrand.acekeysystem.task;

import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 奖励检查任务
 * 用于检查和执行网站上的奖励
 */
public final class RewardCheckTask extends BukkitRunnable {

    private final AceKeySystem plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public RewardCheckTask(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void run() {
        // 检查并执行网站奖励
        this.plugin.checkAndExecuteRewards();
    }
}
