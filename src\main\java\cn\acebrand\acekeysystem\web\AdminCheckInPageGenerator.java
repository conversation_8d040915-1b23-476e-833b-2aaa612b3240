package cn.acebrand.acekeysystem.web;

/**
 * 管理员签到管理页面生成器
 * 负责生成管理员签到管理页面的HTML内容
 */
public class AdminCheckInPageGenerator {
    private final String adminKey;

    public AdminCheckInPageGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成签到管理页面
     */
    public String generateCheckInManagePage() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"page-header\">\n");
        html.append("                <h1>📅 签到系统管理</h1>\n");
        html.append("                <p>管理签到系统的各项功能和配置</p>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 导航标签页
        html.append(generateNavigationTabs());

        // 标签页内容
        html.append(generateTabContents());

        return html.toString();
    }

    /**
     * 生成导航标签页
     */
    private String generateNavigationTabs() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"checkin-nav-tabs\">\n");
        html.append("                <button class=\"nav-tab active\" onclick=\"switchCheckinTab('rewards')\">\n");
        html.append("                    <i class=\"fas fa-gift\"></i> 奖励配置\n");
        html.append("                </button>\n");
        html.append("                <button class=\"nav-tab\" onclick=\"switchCheckinTab('statistics')\">\n");
        html.append("                    <i class=\"fas fa-chart-bar\"></i> 签到统计\n");
        html.append("                </button>\n");
        html.append("                <button class=\"nav-tab\" onclick=\"switchCheckinTab('leaderboard')\">\n");
        html.append("                    <i class=\"fas fa-trophy\"></i> 签到排行榜\n");
        html.append("                </button>\n");
        html.append("            </div>\n");
        html.append("            \n");

        return html.toString();
    }

    /**
     * 生成标签页内容
     */
    private String generateTabContents() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"checkin-tab-contents\">\n");

        // 奖励配置页面
        html.append("                <div id=\"rewards-tab\" class=\"checkin-tab-content active\">\n");
        html.append(generateRewardsPage());
        html.append("                </div>\n");
        html.append("                \n");

        // 签到统计页面
        html.append("                <div id=\"statistics-tab\" class=\"checkin-tab-content\">\n");
        html.append(generateStatisticsPage());
        html.append("                </div>\n");
        html.append("                \n");

        // 签到排行榜页面
        html.append("                <div id=\"leaderboard-tab\" class=\"checkin-tab-content\">\n");
        html.append(generateLeaderboardPage());
        html.append("                </div>\n");

        html.append("            </div>\n");

        return html.toString();
    }

    /**
     * 生成奖励配置页面
     */
    private String generateRewardsPage() {
        StringBuilder html = new StringBuilder();

        // 操作工具栏
        html.append(generateToolbar());

        // 每日奖励配置卡片
        html.append(generateDailyRewardCards());

        return html.toString();
    }

    /**
     * 生成签到统计页面
     */
    private String generateStatisticsPage() {
        StringBuilder html = new StringBuilder();

        html.append("                    <div class=\"statistics-page\">\n");
        html.append("                        <div class=\"page-section-header\">\n");
        html.append("                            <h2><i class=\"fas fa-chart-bar\"></i> 签到统计数据</h2>\n");
        html.append("                            <p>查看服务器签到活跃度和统计信息</p>\n");
        html.append("                        </div>\n");
        html.append("                        \n");
        html.append("                        <div class=\"stats-grid\">\n");
        html.append("                            <div class=\"stat-card\">\n");
        html.append("                                <div class=\"stat-icon\">\n");
        html.append("                                    <i class=\"fas fa-users\"></i>\n");
        html.append("                                </div>\n");
        html.append("                                <div class=\"stat-info\">\n");
        html.append("                                    <h3>今日签到</h3>\n");
        html.append("                                    <p id=\"todayCheckIns\">-</p>\n");
        html.append("                                    <small>人次</small>\n");
        html.append("                                </div>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"stat-card\">\n");
        html.append("                                <div class=\"stat-icon\">\n");
        html.append("                                    <i class=\"fas fa-calendar-week\"></i>\n");
        html.append("                                </div>\n");
        html.append("                                <div class=\"stat-info\">\n");
        html.append("                                    <h3>本周签到</h3>\n");
        html.append("                                    <p id=\"weekCheckIns\">-</p>\n");
        html.append("                                    <small>人次</small>\n");
        html.append("                                </div>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"stat-card\">\n");
        html.append("                                <div class=\"stat-icon\">\n");
        html.append("                                    <i class=\"fas fa-calendar-alt\"></i>\n");
        html.append("                                </div>\n");
        html.append("                                <div class=\"stat-info\">\n");
        html.append("                                    <h3>本月签到</h3>\n");
        html.append("                                    <p id=\"monthCheckIns\">-</p>\n");
        html.append("                                    <small>人次</small>\n");
        html.append("                                </div>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"stat-card\">\n");
        html.append("                                <div class=\"stat-icon\">\n");
        html.append("                                    <i class=\"fas fa-fire\"></i>\n");
        html.append("                                </div>\n");
        html.append("                                <div class=\"stat-info\">\n");
        html.append("                                    <h3>最高连续</h3>\n");
        html.append("                                    <p id=\"maxConsecutive\">-</p>\n");
        html.append("                                    <small>天</small>\n");
        html.append("                                </div>\n");
        html.append("                            </div>\n");
        html.append("                        </div>\n");
        html.append("                        \n");
        html.append("                        <div class=\"refresh-section\">\n");
        html.append("                            <button class=\"btn btn-primary\" onclick=\"loadCheckInStats()\">\n");
        html.append("                                <i class=\"fas fa-sync-alt\"></i> 刷新统计数据\n");
        html.append("                            </button>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");

        return html.toString();
    }

    /**
     * 生成签到排行榜页面
     */
    private String generateLeaderboardPage() {
        StringBuilder html = new StringBuilder();

        html.append("                    <div class=\"leaderboard-page\">\n");
        html.append("                        <div class=\"page-section-header\">\n");
        html.append("                            <h2><i class=\"fas fa-trophy\"></i> 签到排行榜</h2>\n");
        html.append("                            <p>查看玩家签到排名和记录</p>\n");
        html.append("                        </div>\n");
        html.append("                        \n");
        html.append("                        <div class=\"leaderboard-controls\">\n");
        html.append("                            <div class=\"leaderboard-tabs\">\n");
        html.append(
                "                                <button class=\"tab-btn active\" onclick=\"switchLeaderboard('consecutive')\">\n");
        html.append("                                    <i class=\"fas fa-fire\"></i> 连续签到排行\n");
        html.append("                                </button>\n");
        html.append(
                "                                <button class=\"tab-btn\" onclick=\"switchLeaderboard('total')\">\n");
        html.append("                                    <i class=\"fas fa-calendar-check\"></i> 总签到排行\n");
        html.append("                                </button>\n");
        html.append("                            </div>\n");
        html.append(
                "                            <button class=\"btn btn-primary\" onclick=\"loadLeaderboard('consecutive')\">\n");
        html.append("                                <i class=\"fas fa-sync-alt\"></i> 刷新排行榜\n");
        html.append("                            </button>\n");
        html.append("                        </div>\n");
        html.append("                        \n");
        html.append("                        <div class=\"leaderboard-container\">\n");
        html.append("                            <div id=\"leaderboardList\" class=\"leaderboard-list\">\n");
        html.append("                                <div class=\"loading-placeholder\">\n");
        html.append("                                    <i class=\"fas fa-spinner fa-spin\"></i> 加载中...\n");
        html.append("                                </div>\n");
        html.append("                            </div>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");

        return html.toString();
    }

    /**
     * 生成操作工具栏
     */
    private String generateToolbar() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"toolbar\">\n");
        html.append("                <div class=\"toolbar-left\">\n");
        html.append("                    <button class=\"btn btn-primary\" onclick=\"addDailyReward()\">\n");
        html.append("                        <i class=\"fas fa-plus\"></i> 添加每日奖励\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"btn btn-success\" onclick=\"addConsecutiveReward()\">\n");
        html.append("                        <i class=\"fas fa-fire\"></i> 添加连续奖励\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"btn btn-info\" onclick=\"refreshData()\">\n");
        html.append("                        <i class=\"fas fa-sync-alt\"></i> 刷新数据\n");
        html.append("                    </button>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"toolbar-right\">\n");
        html.append("                    <button class=\"btn btn-warning\" onclick=\"exportConfig()\">\n");
        html.append("                        <i class=\"fas fa-download\"></i> 导出配置\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"btn btn-secondary\" onclick=\"importConfig()\">\n");
        html.append("                        <i class=\"fas fa-upload\"></i> 导入配置\n");
        html.append("                    </button>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        return html.toString();
    }

    /**
     * 生成每日奖励配置卡片
     */
    private String generateDailyRewardCards() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"daily-rewards-section\">\n");
        html.append("                <div class=\"section-header\">\n");
        html.append("                    <h2>📅 每日奖励配置</h2>\n");
        html.append("                    <p>点击日期卡片编辑对应日期的签到奖励</p>\n");
        html.append("                </div>\n");
        html.append("                \n");
        html.append("                <div class=\"daily-cards-grid\" id=\"dailyCardsGrid\">\n");

        // 生成1-31号的卡片
        for (int day = 1; day <= 31; day++) {
            html.append("                    <div class=\"daily-reward-card\" data-day=\"").append(day).append("\">\n");
            html.append("                        <div class=\"card-header\">\n");
            html.append("                            <div class=\"day-number\">").append(day).append("</div>\n");
            html.append("                            <div class=\"day-suffix\">号</div>\n");
            html.append("                        </div>\n");
            html.append("                        <div class=\"card-body\">\n");
            html.append("                            <div class=\"reward-info\" id=\"reward-").append(day)
                    .append("\">\n");
            html.append("                                <div class=\"points-reward\">\n");
            html.append("                                    <i class=\"fas fa-coins\"></i>\n");
            html.append("                                    <span class=\"points-value\">10</span> 积分\n");
            html.append("                                </div>\n");
            html.append("                                <div class=\"items-reward\">\n");
            html.append("                                    <i class=\"fas fa-gift\"></i>\n");
            html.append("                                    <span class=\"items-text\">无额外奖励</span>\n");
            html.append("                                </div>\n");
            html.append("                            </div>\n");
            html.append("                        </div>\n");
            html.append("                        <div class=\"card-actions\">\n");
            html.append("                            <button class=\"btn btn-edit\" onclick=\"editDayReward(")
                    .append(day).append(")\">\n");
            html.append("                                <i class=\"fas fa-edit\"></i> 编辑\n");
            html.append("                            </button>\n");
            html.append("                        </div>\n");
            html.append("                    </div>\n");
        }

        html.append("                </div>\n");
        html.append("            </div>\n");

        return html.toString();
    }

    /**
     * 生成奖励配置区域
     */
    private String generateRewardConfig() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"config-section\">\n");
        html.append("                <div class=\"section-tabs\">\n");
        html.append("                    <button class=\"tab-btn active\" onclick=\"switchTab('daily')\">\n");
        html.append("                        <i class=\"fas fa-calendar-day\"></i> 每日奖励\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"tab-btn\" onclick=\"switchTab('consecutive')\">\n");
        html.append("                        <i class=\"fas fa-fire\"></i> 连续奖励\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"tab-btn\" onclick=\"switchTab('monthly')\">\n");
        html.append("                        <i class=\"fas fa-calendar-alt\"></i> 月度奖励\n");
        html.append("                    </button>\n");
        html.append("                </div>\n");
        html.append("                \n");

        // 每日奖励配置
        html.append("                <div id=\"daily-tab\" class=\"tab-content active\">\n");
        html.append("                    <div class=\"tab-header\">\n");
        html.append("                        <h3>每日签到奖励配置</h3>\n");
        html.append("                        <p>配置玩家每天签到获得的基础奖励</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div id=\"dailyRewardsList\" class=\"rewards-list\">\n");
        html.append("                        <!-- 每日奖励列表将通过JavaScript加载 -->\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                \n");

        // 连续奖励配置
        html.append("                <div id=\"consecutive-tab\" class=\"tab-content\">\n");
        html.append("                    <div class=\"tab-header\">\n");
        html.append("                        <h3>连续签到奖励配置</h3>\n");
        html.append("                        <p>配置玩家连续签到达到指定天数时的额外奖励</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div id=\"consecutiveRewardsList\" class=\"rewards-list\">\n");
        html.append("                        <!-- 连续奖励列表将通过JavaScript加载 -->\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                \n");

        // 月度奖励配置
        html.append("                <div id=\"monthly-tab\" class=\"tab-content\">\n");
        html.append("                    <div class=\"tab-header\">\n");
        html.append("                        <h3>月度签到奖励配置</h3>\n");
        html.append("                        <p>配置每个月不同日期的签到奖励</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"month-selector\">\n");
        html.append("                        <label for=\"monthSelect\">选择月份：</label>\n");
        html.append("                        <select id=\"monthSelect\" onchange=\"loadMonthlyRewards()\">\n");
        html.append("                            <!-- 月份选项将通过JavaScript生成 -->\n");
        html.append("                        </select>\n");
        html.append("                    </div>\n");
        html.append("                    <div id=\"monthlyRewardsList\" class=\"rewards-list\">\n");
        html.append("                        <!-- 月度奖励列表将通过JavaScript加载 -->\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        return html.toString();
    }

    /**
     * 生成统计信息区域
     */
    private String generateStatistics() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"statistics-section\">\n");
        html.append("                <h2><i class=\"fas fa-chart-bar\"></i> 签到统计</h2>\n");
        html.append("                <div class=\"stats-grid\">\n");
        html.append("                    <div class=\"stat-card\">\n");
        html.append("                        <div class=\"stat-icon\">\n");
        html.append("                            <i class=\"fas fa-users\"></i>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"stat-info\">\n");
        html.append("                            <h3>今日签到</h3>\n");
        html.append("                            <p id=\"todayCheckIns\">-</p>\n");
        html.append("                            <small>人次</small>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-card\">\n");
        html.append("                        <div class=\"stat-icon\">\n");
        html.append("                            <i class=\"fas fa-calendar-week\"></i>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"stat-info\">\n");
        html.append("                            <h3>本周签到</h3>\n");
        html.append("                            <p id=\"weekCheckIns\">-</p>\n");
        html.append("                            <small>人次</small>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-card\">\n");
        html.append("                        <div class=\"stat-icon\">\n");
        html.append("                            <i class=\"fas fa-calendar-alt\"></i>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"stat-info\">\n");
        html.append("                            <h3>本月签到</h3>\n");
        html.append("                            <p id=\"monthCheckIns\">-</p>\n");
        html.append("                            <small>人次</small>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-card\">\n");
        html.append("                        <div class=\"stat-icon\">\n");
        html.append("                            <i class=\"fas fa-fire\"></i>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"stat-info\">\n");
        html.append("                            <h3>最高连续</h3>\n");
        html.append("                            <p id=\"maxConsecutive\">-</p>\n");
        html.append("                            <small>天</small>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                \n");

        // 签到排行榜
        html.append("                <div class=\"leaderboard-section\">\n");
        html.append("                    <h3><i class=\"fas fa-trophy\"></i> 签到排行榜</h3>\n");
        html.append("                    <div class=\"leaderboard-tabs\">\n");
        html.append(
                "                        <button class=\"tab-btn active\" onclick=\"switchLeaderboard('consecutive')\">\n");
        html.append("                            连续签到\n");
        html.append("                        </button>\n");
        html.append("                        <button class=\"tab-btn\" onclick=\"switchLeaderboard('total')\">\n");
        html.append("                            总签到天数\n");
        html.append("                        </button>\n");
        html.append("                    </div>\n");
        html.append("                    <div id=\"leaderboardList\" class=\"leaderboard-list\">\n");
        html.append("                        <!-- 排行榜将通过JavaScript加载 -->\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");

        return html.toString();
    }

    /**
     * 生成奖励编辑模态框
     */
    public String generateRewardEditModal() {
        StringBuilder html = new StringBuilder();

        html.append("<!-- 奖励编辑模态框 -->\n");
        html.append("<div id=\"rewardEditModal\" class=\"modal\">\n");
        html.append("    <div class=\"modal-content\">\n");
        html.append("        <div class=\"modal-header\">\n");
        html.append("            <h3 id=\"modalTitle\">编辑奖励</h3>\n");
        html.append("            <button class=\"modal-close\" onclick=\"closeRewardModal()\">\n");
        html.append("                <i class=\"fas fa-times\"></i>\n");
        html.append("            </button>\n");
        html.append("        </div>\n");
        html.append("        <div class=\"modal-body\">\n");
        html.append("            <form id=\"rewardForm\">\n");
        html.append("                <div class=\"form-group\">\n");
        html.append("                    <label for=\"rewardDay\">天数/日期：</label>\n");
        html.append("                    <input type=\"number\" id=\"rewardDay\" min=\"1\" required>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"form-group\">\n");
        html.append("                    <label for=\"rewardPoints\">积分奖励：</label>\n");
        html.append("                    <input type=\"number\" id=\"rewardPoints\" min=\"0\" required>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"form-group\">\n");
        html.append("                    <label for=\"rewardCommands\">奖励命令：</label>\n");
        html.append("                    <textarea id=\"rewardCommands\" rows=\"4\" \n");
        html.append("                        placeholder=\"每行一个命令，使用 {player} 作为玩家名占位符\"></textarea>\n");
        html.append("                </div>\n");
        html.append("            </form>\n");
        html.append("        </div>\n");
        html.append("        <div class=\"modal-footer\">\n");
        html.append("            <button class=\"btn btn-secondary\" onclick=\"closeRewardModal()\">取消</button>\n");
        html.append("            <button class=\"btn btn-primary\" onclick=\"saveReward()\">保存</button>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("</div>\n");

        return html.toString();
    }
}
