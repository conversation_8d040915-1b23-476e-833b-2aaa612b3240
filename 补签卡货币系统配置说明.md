# 补签卡货币系统配置说明

## 概述
补签卡商店支持多种货币系统，包括金币、点券、积分和自定义货币。每种货币都需要正确配置余额检测和扣除方式。

## 配置文件位置
配置文件：`plugins/AceKeySystem/config.yml`
配置节点：`makeup_cards`

## 支持的货币系统

### 1. 金币系统（Vault经济）

#### 配置示例：
```yaml
makeup_cards:
  gold:
    enabled: true
    price: 100
    currency_name: "金币"
    description: "使用游戏内金币购买"
    balance_variable: "%vault_eco_balance%"
    deduct_command: "eco take {player} {amount}"
```

#### 要求：
- **Vault插件**：必须安装Vault插件
- **经济插件**：需要安装支持Vault的经济插件（如EssentialsX、CMI等）
- **PlaceholderAPI**：推荐安装PlaceholderAPI以获取准确余额

#### 余额检测方式：
1. **优先**：通过PlaceholderAPI的 `%vault_eco_balance%` 变量
2. **备用**：通过内置Vault集成直接获取

### 2. 点券系统（PlayerPoints等）

#### 配置示例：
```yaml
makeup_cards:
  points:
    enabled: true
    price: 50
    currency_name: "点券"
    description: "使用点券购买"
    balance_variable: "%playerpoints_points%"
    deduct_command: "pp take {player} {amount}"
```

#### 要求：
- **PlayerPoints插件**：或其他点券插件
- **PlaceholderAPI**：必须安装PlaceholderAPI
- **PlaceholderAPI扩展**：需要安装对应的PlaceholderAPI扩展

#### 余额检测方式：
- **唯一方式**：通过PlaceholderAPI变量获取余额
- **常用变量**：
  - PlayerPoints: `%playerpoints_points%`
  - TokenManager: `%tokenmanager_tokens%`
  - 其他插件请查看对应文档

### 3. 积分系统（内置）

#### 配置示例：
```yaml
makeup_cards:
  score:
    enabled: true
    price: 200
    currency_name: "积分"
    description: "使用积分商店积分购买"
    balance_variable: "internal"
    deduct_command: "acepoints remove {player} {amount}"
```

#### 要求：
- **无额外要求**：使用插件内置积分系统

#### 余额检测方式：
- **直接获取**：从插件内置积分系统获取

### 4. 自定义货币系统

#### 配置示例：
```yaml
makeup_cards:
  custom:
    enabled: true
    price: 150
    currency_name: "钻石"
    description: "使用钻石购买"
    balance_variable: "%custom_diamonds%"
    deduct_command: "diamonds take {player} {amount}"
```

#### 要求：
- **自定义插件**：提供货币功能的插件
- **PlaceholderAPI**：必须安装PlaceholderAPI
- **PlaceholderAPI扩展**：对应插件的PlaceholderAPI扩展

#### 余额检测方式：
- **通过PlaceholderAPI**：使用自定义变量获取余额

## 配置参数说明

### 必需参数：
- `enabled`: 是否启用该支付方式
- `price`: 单张补签卡价格
- `currency_name`: 货币显示名称
- `description`: 货币描述
- `balance_variable`: 余额变量（PlaceholderAPI变量或"internal"）
- `deduct_command`: 扣除指令模板

### 变量占位符：
- `{player}`: 玩家名称
- `{amount}`: 扣除数量

## 余额检测流程

### 1. PlaceholderAPI方式（推荐）
```
获取玩家对象 → 调用PlaceholderAPI → 解析余额字符串 → 返回数值
```

### 2. 内置系统方式（备用）
```
检查货币类型 → 调用对应内置系统 → 返回余额
```

## 常见问题解决

### Q: 金币余额显示为0？
**A**: 检查以下项目：
1. Vault插件是否正确安装
2. 经济插件是否支持Vault
3. PlaceholderAPI是否安装
4. `%vault_eco_balance%` 变量是否正常工作

### Q: 点券购买失败？
**A**: 检查以下项目：
1. PlayerPoints插件是否安装
2. PlaceholderAPI是否安装
3. PlayerPoints的PlaceholderAPI扩展是否安装
4. `%playerpoints_points%` 变量是否返回正确值

### Q: 自定义货币不工作？
**A**: 检查以下项目：
1. 自定义插件是否提供PlaceholderAPI支持
2. PlaceholderAPI变量是否正确
3. 扣除指令格式是否正确
4. 插件是否支持离线玩家操作

## 测试方法

### 1. 测试余额获取：
```
/papi parse <玩家名> %变量名%
```

### 2. 测试扣除指令：
```
/eco take <玩家名> <数量>
/pp take <玩家名> <数量>
```

### 3. 查看日志：
检查服务器日志中的余额检测和扣除记录

## 推荐配置

### 标准服务器配置：
```yaml
makeup_cards:
  gold:
    enabled: true
    price: 1000
    currency_name: "金币"
    description: "使用游戏内金币购买"
    balance_variable: "%vault_eco_balance%"
    deduct_command: "eco take {player} {amount}"
  
  points:
    enabled: true
    price: 50
    currency_name: "点券"
    description: "使用点券购买"
    balance_variable: "%playerpoints_points%"
    deduct_command: "pp take {player} {amount}"
  
  score:
    enabled: true
    price: 200
    currency_name: "积分"
    description: "使用积分商店积分购买"
    balance_variable: "internal"
    deduct_command: "acepoints remove {player} {amount}"
```

## 注意事项

1. **PlaceholderAPI变量**：确保变量名称正确，包括百分号
2. **指令权限**：确保控制台有执行扣除指令的权限
3. **离线玩家**：某些插件可能不支持离线玩家操作
4. **数值精度**：余额会转换为整数进行比较
5. **错误处理**：系统会记录详细的错误日志

## 支持的插件列表

### 经济插件（金币）：
- EssentialsX
- CMI
- iConomy
- BOSEconomy
- 其他支持Vault的经济插件

### 点券插件：
- PlayerPoints
- TokenManager
- CustomCurrency
- 其他提供PlaceholderAPI支持的点券插件

### 自定义货币插件：
- 任何提供PlaceholderAPI变量的插件
- 支持控制台指令扣除的插件
