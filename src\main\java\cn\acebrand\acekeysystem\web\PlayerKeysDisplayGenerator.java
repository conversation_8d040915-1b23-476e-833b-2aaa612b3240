package cn.acebrand.acekeysystem.web;

/**
 * 玩家卡密显示生成器
 * 负责生成玩家卡密详细信息显示相关的JavaScript代码
 */
public class PlayerKeysDisplayGenerator {
    
    /**
     * 生成玩家卡密标签页切换函数
     */
    public String generatePlayerKeysTabFunctions() {
        StringBuilder js = new StringBuilder();
        
        js.append("// 玩家卡密标签页切换功能\n");
        js.append("function showPlayerKeysTab(tabName) {\n");
        js.append("    if (!window.currentPlayerInfo) return;\n");
        js.append("\n");
        js.append("    // 隐藏所有标签页内容\n");
        js.append("    const allTabs = document.querySelectorAll('.player-keys-tab-content');\n");
        js.append("    allTabs.forEach(tab => {\n");
        js.append("        tab.classList.remove('active');\n");
        js.append("    });\n");
        js.append("\n");
        js.append("    // 移除所有标签按钮的激活状态\n");
        js.append("    const allTabButtons = document.querySelectorAll('.player-keys-nav .nav-tab');\n");
        js.append("    allTabButtons.forEach(button => {\n");
        js.append("        button.classList.remove('active');\n");
        js.append("    });\n");
        js.append("\n");
        js.append("    // 显示选中的标签页\n");
        js.append("    const selectedTab = document.getElementById('player-tab-' + tabName);\n");
        js.append("    if (selectedTab) {\n");
        js.append("        selectedTab.classList.add('active');\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 激活对应的标签按钮\n");
        js.append("    if (event && event.target) {\n");
        js.append("        event.target.classList.add('active');\n");
        js.append("    } else {\n");
        js.append("        // 如果没有event，找到对应的按钮并激活\n");
        js.append("        const buttons = document.querySelectorAll('.player-keys-nav .nav-tab');\n");
        js.append("        buttons.forEach((button, index) => {\n");
        js.append("            if ((tabName === 'assigned' && index === 0) ||\n");
        js.append("                (tabName === 'used' && index === 1) ||\n");
        js.append("                (tabName === 'expired' && index === 2)) {\n");
        js.append("                button.classList.add('active');\n");
        js.append("            }\n");
        js.append("        });\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 根据标签页类型显示对应的卡密列表\n");
        js.append("    const playerInfo = window.currentPlayerInfo;\n");
        js.append("    switch(tabName) {\n");
        js.append("        case 'assigned':\n");
        js.append("            displayPlayerKeys(playerInfo.assigned_keys, 'assignedKeysList', 'assigned');\n");
        js.append("            break;\n");
        js.append("        case 'used':\n");
        js.append("            displayPlayerKeys(playerInfo.used_keys, 'usedKeysList', 'used');\n");
        js.append("            break;\n");
        js.append("        case 'expired':\n");
        js.append("            displayPlayerKeys(playerInfo.expired_keys, 'expiredKeysList', 'expired');\n");
        js.append("            break;\n");
        js.append("    }\n");
        js.append("}\n");
        js.append("\n");
        
        return js.toString();
    }
    
    /**
     * 生成玩家卡密列表显示函数
     */
    public String generatePlayerKeysDisplayFunctions() {
        StringBuilder js = new StringBuilder();
        
        js.append("// 显示玩家卡密列表\n");
        js.append("function displayPlayerKeys(keys, containerId, keyType) {\n");
        js.append("    const container = document.getElementById(containerId);\n");
        js.append("    if (!container) return;\n");
        js.append("\n");
        js.append("    if (keys.length === 0) {\n");
        js.append("        const typeText = keyType === 'assigned' ? '已分配' : keyType === 'used' ? '已使用' : '已失效';\n");
        js.append("        container.innerHTML = `<div class=\"no-keys\">暂无${typeText}的卡密</div>`;\n");
        js.append("        return;\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    let html = '';\n");
        js.append("    keys.forEach(key => {\n");
        js.append("        const createdDate = key.created_date || '未知';\n");
        js.append("        const neverExpire = key.never_expire ? ' <span class=\"never-expire\">♾️ 永不失效</span>' : '';\n");
        js.append("        \n");
        js.append("        // 获取状态样式类\n");
        js.append("        let statusClass = '';\n");
        js.append("        let statusIcon = '';\n");
        js.append("        let statusText = '';\n");
        js.append("        \n");
        js.append("        switch(keyType) {\n");
        js.append("            case 'assigned':\n");
        js.append("                statusClass = 'status-warning';\n");
        js.append("                statusIcon = '🎫';\n");
        js.append("                statusText = '已分配';\n");
        js.append("                break;\n");
        js.append("            case 'used':\n");
        js.append("                statusClass = 'status-danger';\n");
        js.append("                statusIcon = '✅';\n");
        js.append("                statusText = '已使用';\n");
        js.append("                break;\n");
        js.append("            case 'expired':\n");
        js.append("                statusClass = 'status-secondary';\n");
        js.append("                statusIcon = '⏰';\n");
        js.append("                statusText = '已失效';\n");
        js.append("                break;\n");
        js.append("        }\n");
        js.append("\n");
        js.append("        html += `\n");
        js.append("            <div class=\"shop-card key-card ${keyType}\">\n");
        js.append("                <div class=\"card-header\">\n");
        js.append("                    <div class=\"card-title\">\n");
        js.append("                        <div class=\"card-icon\">${statusIcon}</div>\n");
        js.append("                        <div class=\"card-name\">${key.key}</div>\n");
        js.append("                    </div>\n");
        js.append("                    <div class=\"card-status ${statusClass}\">${statusText}${neverExpire}</div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\"card-content\">\n");
        js.append("                    <div class=\"card-info\">\n");
        js.append("                        <div class=\"info-row\">\n");
        js.append("                            <span class=\"info-label\">💬 消息:</span>\n");
        js.append("                            <span class=\"info-value\">${key.message || '无'}</span>\n");
        js.append("                        </div>\n");
        js.append("                        <div class=\"info-row\">\n");
        js.append("                            <span class=\"info-label\">📅 创建时间:</span>\n");
        js.append("                            <span class=\"info-value\">${createdDate}</span>\n");
        js.append("                        </div>\n");
        js.append("        `;\n");
        js.append("\n");
        js.append("        // 根据卡密类型显示不同的时间信息\n");
        js.append("        if (keyType === 'assigned' && key.assigned_time) {\n");
        js.append("            const assignedDate = new Date(key.assigned_time).toLocaleString();\n");
        js.append("            html += `\n");
        js.append("                        <div class=\\\"info-row\\\">\n");
        js.append("                            <span class=\\\"info-label\\\">🎫 分配时间:</span>\n");
        js.append("                            <span class=\\\"info-value\\\">${assignedDate}</span>\n");
        js.append("                        </div>`;\n");
        js.append("        } else if (keyType === 'used') {\n");
        js.append("            if (key.assigned_time) {\n");
        js.append("                const assignedDate = new Date(key.assigned_time).toLocaleString();\n");
        js.append("                html += `\n");
        js.append("                        <div class=\\\"info-row\\\">\n");
        js.append("                            <span class=\\\"info-label\\\">🎫 分配时间:</span>\n");
        js.append("                            <span class=\\\"info-value\\\">${assignedDate}</span>\n");
        js.append("                        </div>`;\n");
        js.append("            }\n");
        js.append("            if (key.used_time) {\n");
        js.append("                const usedDate = new Date(key.used_time).toLocaleString();\n");
        js.append("                html += `\n");
        js.append("                        <div class=\\\"info-row\\\">\n");
        js.append("                            <span class=\\\"info-label\\\">✅ 使用时间:</span>\n");
        js.append("                            <span class=\\\"info-value\\\">${usedDate}</span>\n");
        js.append("                        </div>`;\n");
        js.append("            }\n");
        js.append("        } else if (keyType === 'expired') {\n");
        js.append("            if (key.assigned_time) {\n");
        js.append("                const assignedDate = new Date(key.assigned_time).toLocaleString();\n");
        js.append("                html += `\n");
        js.append("                        <div class=\\\"info-row\\\">\n");
        js.append("                            <span class=\\\"info-label\\\">🎫 分配时间:</span>\n");
        js.append("                            <span class=\\\"info-value\\\">${assignedDate}</span>\n");
        js.append("                        </div>`;\n");
        js.append("            }\n");
        js.append("            if (key.expired_time) {\n");
        js.append("                const expiredDate = new Date(key.expired_time).toLocaleString();\n");
        js.append("                html += `\n");
        js.append("                        <div class=\\\"info-row\\\">\n");
        js.append("                            <span class=\\\"info-label\\\">⏰ 失效时间:</span>\n");
        js.append("                            <span class=\\\"info-value\\\">${expiredDate}</span>\n");
        js.append("                        </div>`;\n");
        js.append("            }\n");
        js.append("        }\n");
        js.append("\n");
        js.append("        html += `\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\\\"card-footer\\\">\n");
        js.append("                    <div class=\\\"card-actions\\\">\n");
        js.append("                        <button class=\\\"action-btn copy\\\" onclick=\\\"copyKey('${key.key}')\\\" title=\\\"复制卡密\\\">📋 复制</button>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("            </div>\n");
        js.append("        `;\n");
        js.append("    });\n");
        js.append("\n");
        js.append("    container.innerHTML = html;\n");
        js.append("}\n");
        js.append("\n");
        
        js.append("// 获取卡密状态文本\n");
        js.append("function getKeyStatusText(keyType) {\n");
        js.append("    switch(keyType) {\n");
        js.append("        case 'assigned': return '🎫 已分配';\n");
        js.append("        case 'used': return '❌ 已使用';\n");
        js.append("        case 'expired': return '⏰ 已失效';\n");
        js.append("        default: return '❓ 未知';\n");
        js.append("    }\n");
        js.append("}\n");
        js.append("\n");

        js.append("// 复制卡密功能\n");
        js.append("function copyKey(keyCode) {\n");
        js.append("    navigator.clipboard.writeText(keyCode).then(function() {\n");
        js.append("        showResult('✅ 卡密已复制到剪贴板', 'success');\n");
        js.append("    }).catch(function(err) {\n");
        js.append("        console.error('复制失败:', err);\n");
        js.append("        showResult('❌ 复制失败', 'error');\n");
        js.append("    });\n");
        js.append("}\n");
        js.append("\n");

        return js.toString();
    }
}
