package cn.acebrand.acekeysystem.integration;

import cn.acebrand.acekeysystem.AceKeySystem;
import net.milkbowl.vault.economy.Economy;
import net.milkbowl.vault.economy.EconomyResponse;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.plugin.RegisteredServiceProvider;

import java.util.List;
import java.util.UUID;

/**
 * Vault 经济系统集成
 * 提供与其他经济插件的集成功能
 */
public class VaultEconomyIntegration {

    private final AceKeySystem plugin;
    private Economy economy;
    private boolean enabled;

    // 配置选项
    private boolean enablePointsToMoney;
    private boolean enableMoneyToPoints;
    private double pointsToMoneyRate;
    private double moneyToPointsRate;
    private int minExchangePoints;
    private int maxExchangePoints;
    private double minExchangeMoney;
    private double maxExchangeMoney;

    public VaultEconomyIntegration(AceKeySystem plugin) {
        this.plugin = plugin;
        this.enabled = false;
        loadConfiguration();
        setupEconomy();
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        enablePointsToMoney = plugin.getConfig().getBoolean("vault.exchange.points-to-money.enabled", true);
        enableMoneyToPoints = plugin.getConfig().getBoolean("vault.exchange.money-to-points.enabled", true);
        pointsToMoneyRate = plugin.getConfig().getDouble("vault.exchange.points-to-money.rate", 0.1);
        moneyToPointsRate = plugin.getConfig().getDouble("vault.exchange.money-to-points.rate", 10.0);
        minExchangePoints = plugin.getConfig().getInt("vault.exchange.points-to-money.min-points", 100);
        maxExchangePoints = plugin.getConfig().getInt("vault.exchange.points-to-money.max-points", 10000);
        minExchangeMoney = plugin.getConfig().getDouble("vault.exchange.money-to-points.min-money", 10.0);
        maxExchangeMoney = plugin.getConfig().getDouble("vault.exchange.money-to-points.max-money", 1000.0);
    }

    /**
     * 设置经济系统
     */
    private boolean setupEconomy() {
        if (plugin.getServer().getPluginManager().getPlugin("Vault") == null) {
            plugin.getLogger().info("Vault 插件未找到，跳过经济系统集成");
            return false;
        }

        RegisteredServiceProvider<Economy> rsp = plugin.getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp == null) {
            plugin.getLogger().warning("未找到经济系统提供者，Vault 集成失败");
            return false;
        }

        economy = rsp.getProvider();
        enabled = economy != null;

        if (enabled) {
            plugin.getLogger().info("Vault 经济系统集成成功: " + economy.getName());
        } else {
            plugin.getLogger().warning("Vault 经济系统集成失败");
        }

        return enabled;
    }

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return enabled && economy != null;
    }

    /**
     * 获取经济系统名称
     */
    public String getEconomyName() {
        return enabled ? economy.getName() : "未知";
    }

    /**
     * 获取玩家余额
     */
    public double getBalance(OfflinePlayer player) {
        if (!enabled) return 0.0;
        return economy.getBalance(player);
    }

    /**
     * 获取玩家余额（通过玩家名）
     */
    public double getBalance(String playerName) {
        if (!enabled) return 0.0;
        @SuppressWarnings("deprecation")
        OfflinePlayer player = Bukkit.getOfflinePlayer(playerName);
        return economy.getBalance(player);
    }

    /**
     * 给玩家添加金钱
     */
    public boolean depositPlayer(OfflinePlayer player, double amount) {
        if (!enabled) return false;
        EconomyResponse response = economy.depositPlayer(player, amount);
        return response.transactionSuccess();
    }

    /**
     * 从玩家扣除金钱
     */
    public boolean withdrawPlayer(OfflinePlayer player, double amount) {
        if (!enabled) return false;
        EconomyResponse response = economy.withdrawPlayer(player, amount);
        return response.transactionSuccess();
    }

    /**
     * 积分兑换金钱
     */
    public ExchangeResult exchangePointsToMoney(String playerName, int points) {
        if (!enabled) {
            return new ExchangeResult(false, "经济系统未启用");
        }

        if (!enablePointsToMoney) {
            return new ExchangeResult(false, "积分兑换金钱功能已禁用");
        }

        if (points < minExchangePoints) {
            return new ExchangeResult(false, "兑换积分不能少于 " + minExchangePoints);
        }

        if (points > maxExchangePoints) {
            return new ExchangeResult(false, "兑换积分不能超过 " + maxExchangePoints);
        }

        // 检查玩家积分
        int playerPoints = plugin.getPointsManager().getPlayerPoints(playerName);
        if (playerPoints < points) {
            return new ExchangeResult(false, "积分不足，当前积分: " + playerPoints);
        }

        // 计算兑换金额
        double money = points * pointsToMoneyRate;

        // 扣除积分
        if (!plugin.getPointsManager().deductPlayerPoints(playerName, points)) {
            return new ExchangeResult(false, "扣除积分失败");
        }

        // 添加金钱
        @SuppressWarnings("deprecation")
        OfflinePlayer player = Bukkit.getOfflinePlayer(playerName);
        if (!depositPlayer(player, money)) {
            // 如果添加金钱失败，返还积分
            plugin.getPointsManager().addPlayerPoints(playerName, points);
            return new ExchangeResult(false, "添加金钱失败");
        }

        plugin.getLogger().info("玩家 " + playerName + " 兑换了 " + points + " 积分为 " + money + " 金钱");
        return new ExchangeResult(true, "成功兑换 " + points + " 积分为 " + String.format("%.2f", money) + " 金钱");
    }

    /**
     * 金钱兑换积分
     */
    public ExchangeResult exchangeMoneyToPoints(String playerName, double money) {
        if (!enabled) {
            return new ExchangeResult(false, "经济系统未启用");
        }

        if (!enableMoneyToPoints) {
            return new ExchangeResult(false, "金钱兑换积分功能已禁用");
        }

        if (money < minExchangeMoney) {
            return new ExchangeResult(false, "兑换金钱不能少于 " + minExchangeMoney);
        }

        if (money > maxExchangeMoney) {
            return new ExchangeResult(false, "兑换金钱不能超过 " + maxExchangeMoney);
        }

        // 检查玩家余额
        @SuppressWarnings("deprecation")
        OfflinePlayer player = Bukkit.getOfflinePlayer(playerName);
        double playerBalance = getBalance(player);
        if (playerBalance < money) {
            return new ExchangeResult(false, "余额不足，当前余额: " + String.format("%.2f", playerBalance));
        }

        // 计算兑换积分
        int points = (int) (money * moneyToPointsRate);

        // 扣除金钱
        if (!withdrawPlayer(player, money)) {
            return new ExchangeResult(false, "扣除金钱失败");
        }

        // 添加积分
        plugin.getPointsManager().addPlayerPoints(playerName, points);

        plugin.getLogger().info("玩家 " + playerName + " 兑换了 " + money + " 金钱为 " + points + " 积分");
        return new ExchangeResult(true, "成功兑换 " + String.format("%.2f", money) + " 金钱为 " + points + " 积分");
    }

    /**
     * 获取兑换汇率信息
     */
    public String getExchangeRateInfo() {
        StringBuilder info = new StringBuilder();
        info.append("§6=== 兑换汇率信息 ===\n");
        
        if (enablePointsToMoney) {
            info.append("§a积分 → 金钱: §f").append(pointsToMoneyRate).append(" 金钱/积分\n");
            info.append("§7最小兑换: §f").append(minExchangePoints).append(" 积分\n");
            info.append("§7最大兑换: §f").append(maxExchangePoints).append(" 积分\n");
        } else {
            info.append("§c积分兑换金钱: 已禁用\n");
        }

        if (enableMoneyToPoints) {
            info.append("§a金钱 → 积分: §f").append(moneyToPointsRate).append(" 积分/金钱\n");
            info.append("§7最小兑换: §f").append(minExchangeMoney).append(" 金钱\n");
            info.append("§7最大兑换: §f").append(maxExchangeMoney).append(" 金钱\n");
        } else {
            info.append("§c金钱兑换积分: 已禁用\n");
        }

        return info.toString();
    }

    /**
     * 重新加载配置
     */
    public void reload() {
        loadConfiguration();
        setupEconomy();
    }

    /**
     * 兑换结果类
     */
    public static class ExchangeResult {
        private final boolean success;
        private final String message;

        public ExchangeResult(boolean success, String message) {
            this.success = success;
            this.message = message;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }
    }

    // Getter 方法
    public boolean isPointsToMoneyEnabled() { return enablePointsToMoney; }
    public boolean isMoneyToPointsEnabled() { return enableMoneyToPoints; }
    public double getPointsToMoneyRate() { return pointsToMoneyRate; }
    public double getMoneyToPointsRate() { return moneyToPointsRate; }
    public int getMinExchangePoints() { return minExchangePoints; }
    public int getMaxExchangePoints() { return maxExchangePoints; }
    public double getMinExchangeMoney() { return minExchangeMoney; }
    public double getMaxExchangeMoney() { return maxExchangeMoney; }
}
