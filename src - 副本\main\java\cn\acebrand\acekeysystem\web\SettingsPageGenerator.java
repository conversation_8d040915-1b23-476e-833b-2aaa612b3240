package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 系统设置页面生成器
 * 负责生成系统设置页面的HTML内容
 */
public class SettingsPageGenerator {
    private final AceKeySystem plugin;
    private final WebServer webServer;

    public SettingsPageGenerator(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    /**
     * 生成系统设置页面
     */
    public String generateSettingsPage() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"page-header\">\n");
        html.append("                <h1>⚙️ 系统设置</h1>\n");
        html.append("                <p>配置系统参数和维护操作</p>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        html.append("            <div class=\"content-grid\">\n");
        
        // 系统维护
        html.append(generateSystemMaintenance());
        
        // 日志设置
        html.append(generateLogSettings());
        
        // 系统信息
        html.append(generateSystemInfo());
        
        html.append("            </div>\n");
        html.append("            \n");
        
        // 日志查看区域
        html.append(generateLogViewer());

        return html.toString();
    }

    /**
     * 生成系统维护区域
     */
    private String generateSystemMaintenance() {
        StringBuilder html = new StringBuilder();
        
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <h3>系统维护</h3>\n");
        html.append("                    <div class=\"button-group vertical\">\n");
        html.append("                        <button onclick=\"reloadConfig()\" class=\"btn btn-primary\">重载配置</button>\n");
        html.append("                        <button onclick=\"refreshStats()\" class=\"btn btn-secondary\">刷新统计</button>\n");
        html.append("                        <button onclick=\"toggleLogs()\" class=\"btn btn-info\">📋 查看日志</button>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        return html.toString();
    }

    /**
     * 生成日志设置区域
     */
    private String generateLogSettings() {
        StringBuilder html = new StringBuilder();
        
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <h3>日志设置</h3>\n");
        html.append("                    <div class=\"form-group\">\n");
        html.append("                        <label>\n");
        html.append("                            <input type=\"checkbox\" id=\"webLogEnabled\" onchange=\"toggleWebLogging()\" ");
        html.append(plugin.getConfig().getBoolean("website.web-logging", true) ? "checked" : "");
        html.append("> 启用网站日志显示\n");
        html.append("                        </label>\n");
        html.append("                        <small>关闭后，控制台将显示日志；开启后，日志将在网站中显示</small>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"form-group\">\n");
        html.append("                        <label>\n");
        html.append("                            <input type=\"checkbox\" id=\"consoleLogEnabled\" onchange=\"toggleConsoleLogging()\" ");
        html.append(plugin.getConfig().getBoolean("website.console-logging", false) ? "checked" : "");
        html.append("> 同时在控制台显示日志\n");
        html.append("                        </label>\n");
        html.append("                        <small>开启后，日志将同时在控制台和网站中显示</small>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        return html.toString();
    }

    /**
     * 生成系统信息区域
     */
    private String generateSystemInfo() {
        StringBuilder html = new StringBuilder();
        
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <h3>系统信息</h3>\n");
        html.append("                    <div class=\"info-list\">\n");
        html.append("                        <div class=\"info-item\">\n");
        html.append("                            <span class=\"info-label\">插件版本:</span>\n");
        html.append("                            <span class=\"info-value\">");
        html.append(plugin.getDescription().getVersion()).append("</span>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"info-item\">\n");
        html.append("                            <span class=\"info-label\">Web服务器:</span>\n");
        html.append("                            <span class=\"info-value\">");
        html.append(webServer.isRunning() ? "运行中" : "已停止").append("</span>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"info-item\">\n");
        html.append("                            <span class=\"info-label\">服务器端口:</span>\n");
        html.append("                            <span class=\"info-value\">").append(webServer.getPort());
        html.append("</span>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        
        return html.toString();
    }

    /**
     * 生成日志查看区域
     */
    private String generateLogViewer() {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 日志查看区域 -->\n");
        html.append("            <div class=\"content-card\" id=\"logsSection\" style=\"display: none; margin-top: 20px;\">\n");
        html.append("                <div class=\"card-header\">\n");
        html.append("                    <h3>📋 系统日志</h3>\n");
        html.append("                    <div class=\"log-controls\">\n");
        html.append("                        <div class=\"log-controls-row\">\n");
        html.append("                            <div class=\"control-group\">\n");
        html.append("                                <label for=\"logLevel\">级别:</label>\n");
        html.append("                                <select id=\"logLevel\" class=\"form-input small\" onchange=\"loadLogs()\">\n");
        html.append("                                    <option value=\"all\">所有级别</option>\n");
        html.append("                                    <option value=\"INFO\">信息</option>\n");
        html.append("                                    <option value=\"WARNING\">警告</option>\n");
        html.append("                                    <option value=\"SEVERE\">错误</option>\n");
        html.append("                                </select>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"control-group\">\n");
        html.append("                                <label for=\"logLines\">显示:</label>\n");
        html.append("                                <select id=\"logLines\" class=\"form-input small\" onchange=\"loadLogs()\">\n");
        html.append("                                    <option value=\"50\">最近50行</option>\n");
        html.append("                                    <option value=\"100\" selected>最近100行</option>\n");
        html.append("                                    <option value=\"200\">最近200行</option>\n");
        html.append("                                    <option value=\"500\">最近500行</option>\n");
        html.append("                                    <option value=\"1000\">最近1000行</option>\n");
        html.append("                                </select>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"control-group\">\n");
        html.append("                                <input type=\"number\" id=\"customLogLines\" class=\"form-input small\" placeholder=\"自定义行数\" min=\"1\" max=\"5000\" style=\"width: 140px;\">\n");
        html.append("                                <button onclick=\"loadLogsWithCustomLines()\" class=\"btn btn-secondary small\">应用</button>\n");
        html.append("                            </div>\n");
        html.append("                        </div>\n");
        html.append("                        <div class=\"log-controls-row\">\n");
        html.append("                            <div class=\"control-group search-group\">\n");
        html.append("                                <label for=\"logSearch\">搜索:</label>\n");
        html.append("                                <input type=\"text\" id=\"logSearch\" class=\"form-input\" placeholder=\"搜索日志内容...\" onkeyup=\"filterLogs()\" style=\"width: 300px;\">\n");
        html.append("                                <button onclick=\"clearLogSearch()\" class=\"btn btn-secondary small\">清空</button>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"control-group\">\n");
        html.append("                                <button onclick=\"loadLogs()\" class=\"btn btn-secondary small\">🔄 刷新</button>\n");
        html.append("                                <button onclick=\"clearLogs()\" class=\"btn btn-danger small\">🧹 清空日志</button>\n");
        html.append("                                <button onclick=\"exportLogs()\" class=\"btn btn-info small\">📥 导出日志</button>\n");
        html.append("                                <button onclick=\"saveLogsToServer()\" class=\"btn btn-primary small\">💾 保存到插件文件夹</button>\n");
        html.append("                            </div>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"log-container\" id=\"logContainer\">\n");
        html.append("                    <div class=\"loading-spinner\">正在加载日志...</div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        
        return html.toString();
    }
}
