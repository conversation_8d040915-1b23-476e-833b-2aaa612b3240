package cn.acebrand.acekeysystem.web;

/**
 * 通用JavaScript生成器
 * 负责生成通用的JavaScript工具函数
 */
public class CommonJSGenerator {

    /**
     * 生成通用JavaScript工具函数
     */
    public String generateCommonJS() {
        return "// ==================== 通用工具函数 ====================\n" +
                "// 装饰性确认对话框函数\n" +
                "function showConfirmModal(title, message, onConfirm, onCancel) {\n" +
                "    // 创建模态对话框\n" +
                "    const modal = document.createElement('div');\n" +
                "    modal.className = 'confirm-modal';\n" +
                "    modal.innerHTML = `\n" +
                "        <div class=\"confirm-modal-content\">\n" +
                "            <div class=\"confirm-modal-header\">\n" +
                "                <h3 class=\"confirm-modal-title\">${title}</h3>\n" +
                "            </div>\n" +
                "            <div class=\"confirm-modal-body\">\n" +
                "                ${message}\n" +
                "            </div>\n" +
                "            <div class=\"confirm-modal-footer\">\n" +
                "                <button class=\"confirm-btn secondary\" onclick=\"closeConfirmModal()\">取消</button>\n" +
                "                <button class=\"confirm-btn primary\" onclick=\"confirmAction()\">确定</button>\n" +
                "            </div>\n" +
                "        </div>\n" +
                "    `;\n" +
                "    \n" +
                "    // 添加到页面\n" +
                "    document.body.appendChild(modal);\n" +
                "    \n" +
                "    // 显示动画\n" +
                "    setTimeout(() => modal.classList.add('show'), 10);\n" +
                "    \n" +
                "    // 绑定事件\n" +
                "    window.closeConfirmModal = () => {\n" +
                "        modal.classList.remove('show');\n" +
                "        setTimeout(() => {\n" +
                "            if (document.body.contains(modal)) {\n" +
                "                document.body.removeChild(modal);\n" +
                "            }\n" +
                "        }, 300);\n" +
                "        if (onCancel) onCancel();\n" +
                "    };\n" +
                "    \n" +
                "    window.confirmAction = () => {\n" +
                "        modal.classList.remove('show');\n" +
                "        setTimeout(() => {\n" +
                "            if (document.body.contains(modal)) {\n" +
                "                document.body.removeChild(modal);\n" +
                "            }\n" +
                "        }, 300);\n" +
                "        if (onConfirm) onConfirm();\n" +
                "    };\n" +
                "    \n" +
                "    // 点击背景关闭\n" +
                "    modal.addEventListener('click', (e) => {\n" +
                "        if (e.target === modal) {\n" +
                "            closeConfirmModal();\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 显示消息函数\n" +
                "function showResult(message, type = 'info') {\n" +
                "    const resultDiv = document.getElementById('result');\n" +
                "    if (resultDiv) {\n" +
                "        resultDiv.innerHTML = '<div class=\"' + type + '\">' + message + '</div>';\n" +
                "        resultDiv.style.display = 'block';\n" +
                "        setTimeout(() => { resultDiv.style.display = 'none'; }, 8000);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 显示消息函数（别名）\n" +
                "function showMessage(message, type = 'info') {\n" +
                "    showResult(message, type);\n" +
                "}\n" +
                "\n" +
                "// 计算数据哈希值用于检测变化\n" +
                "function calculateDataHash(data) {\n" +
                "    const str = JSON.stringify(data);\n" +
                "    let hash = 0;\n" +
                "    for (let i = 0; i < str.length; i++) {\n" +
                "        const char = str.charCodeAt(i);\n" +
                "        hash = ((hash << 5) - hash) + char;\n" +
                "        hash = hash & hash; // 转换为32位整数\n" +
                "    }\n" +
                "    return hash;\n" +
                "}\n" +
                "\n" +
                "// 复制文本到剪贴板\n" +
                "function copyToClipboard(text) {\n" +
                "    if (navigator.clipboard && window.isSecureContext) {\n" +
                "        // 使用现代API\n" +
                "        navigator.clipboard.writeText(text).then(() => {\n" +
                "            showMessage('✅ 已复制到剪贴板', 'success');\n" +
                "        }).catch(err => {\n" +
                "            console.error('复制失败:', err);\n" +
                "            fallbackCopyTextToClipboard(text);\n" +
                "        });\n" +
                "    } else {\n" +
                "        // 降级方案\n" +
                "        fallbackCopyTextToClipboard(text);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 降级复制方案\n" +
                "function fallbackCopyTextToClipboard(text) {\n" +
                "    const textArea = document.createElement('textarea');\n" +
                "    textArea.value = text;\n" +
                "    textArea.style.position = 'fixed';\n" +
                "    textArea.style.left = '-999999px';\n" +
                "    textArea.style.top = '-999999px';\n" +
                "    document.body.appendChild(textArea);\n" +
                "    textArea.focus();\n" +
                "    textArea.select();\n" +
                "    \n" +
                "    try {\n" +
                "        const successful = document.execCommand('copy');\n" +
                "        if (successful) {\n" +
                "            showMessage('✅ 已复制到剪贴板', 'success');\n" +
                "        } else {\n" +
                "            showMessage('❌ 复制失败，请手动复制', 'error');\n" +
                "        }\n" +
                "    } catch (err) {\n" +
                "        console.error('复制失败:', err);\n" +
                "        showMessage('❌ 复制失败，请手动复制', 'error');\n" +
                "    }\n" +
                "    \n" +
                "    document.body.removeChild(textArea);\n" +
                "}\n" +
                "\n" +
                "// 格式化日期时间\n" +
                "function formatDateTime(timestamp) {\n" +
                "    if (!timestamp) return '未知';\n" +
                "    const date = new Date(timestamp);\n" +
                "    return date.toLocaleString('zh-CN', {\n" +
                "        year: 'numeric',\n" +
                "        month: '2-digit',\n" +
                "        day: '2-digit',\n" +
                "        hour: '2-digit',\n" +
                "        minute: '2-digit',\n" +
                "        second: '2-digit'\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 格式化日期\n" +
                "function formatDate(timestamp) {\n" +
                "    if (!timestamp) return '未知';\n" +
                "    const date = new Date(timestamp);\n" +
                "    return date.toLocaleDateString('zh-CN');\n" +
                "}\n" +
                "\n" +
                "// 防抖函数\n" +
                "function debounce(func, wait) {\n" +
                "    let timeout;\n" +
                "    return function executedFunction(...args) {\n" +
                "        const later = () => {\n" +
                "            clearTimeout(timeout);\n" +
                "            func(...args);\n" +
                "        };\n" +
                "        clearTimeout(timeout);\n" +
                "        timeout = setTimeout(later, wait);\n" +
                "    };\n" +
                "}\n" +
                "\n" +
                "// 节流函数\n" +
                "function throttle(func, limit) {\n" +
                "    let inThrottle;\n" +
                "    return function() {\n" +
                "        const args = arguments;\n" +
                "        const context = this;\n" +
                "        if (!inThrottle) {\n" +
                "            func.apply(context, args);\n" +
                "            inThrottle = true;\n" +
                "            setTimeout(() => inThrottle = false, limit);\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 主题切换功能\n" +
                "function toggleTheme() {\n" +
                "    const body = document.body;\n" +
                "    const currentTheme = body.classList.contains('theme-dark') ? 'dark' : 'light';\n" +
                "    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';\n" +
                "    \n" +
                "    body.classList.remove('theme-' + currentTheme);\n" +
                "    body.classList.add('theme-' + newTheme);\n" +
                "    \n" +
                "    // 保存主题设置到本地存储\n" +
                "    localStorage.setItem('admin-theme', newTheme);\n" +
                "    \n" +
                "    showMessage('🎨 已切换到' + (newTheme === 'dark' ? '深色' : '浅色') + '主题', 'success');\n" +
                "}\n" +
                "\n" +
                "// 初始化主题\n" +
                "function initTheme() {\n" +
                "    const savedTheme = localStorage.getItem('admin-theme') || 'light';\n" +
                "    document.body.classList.add('theme-' + savedTheme);\n" +
                "}\n" +
                "\n" +
                "// 页面加载时初始化\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    initTheme();\n" +
                "});\n";
    }

    /**
     * 生成确认对话框CSS样式
     */
    public String generateConfirmModalCSS() {
        return "\n/* ==================== 确认对话框样式 ====================*/\n" +
                ".confirm-modal {\n" +
                "    position: fixed;\n" +
                "    top: 0;\n" +
                "    left: 0;\n" +
                "    width: 100%;\n" +
                "    height: 100%;\n" +
                "    background: rgba(0, 0, 0, 0.7);\n" +
                "    backdrop-filter: blur(8px);\n" +
                "    z-index: 10000;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    opacity: 0;\n" +
                "    visibility: hidden;\n" +
                "    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n" +
                "}\n" +
                "\n" +
                ".confirm-modal.show {\n" +
                "    opacity: 1;\n" +
                "    visibility: visible;\n" +
                "}\n" +
                "\n" +
                ".confirm-modal-content {\n" +
                "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 0;\n" +
                "    max-width: 450px;\n" +
                "    width: 90%;\n" +
                "    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);\n" +
                "    transform: scale(0.7) translateY(50px);\n" +
                "    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n" +
                "    overflow: hidden;\n" +
                "}\n" +
                "\n" +
                ".confirm-modal.show .confirm-modal-content {\n" +
                "    transform: scale(1) translateY(0);\n" +
                "}\n" +
                "\n" +
                ".confirm-modal-header {\n" +
                "    padding: 25px 30px 20px;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".confirm-modal-title {\n" +
                "    margin: 0;\n" +
                "    font-size: 22px;\n" +
                "    font-weight: 700;\n" +
                "    color: white;\n" +
                "    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".confirm-modal-body {\n" +
                "    padding: 0 30px 25px;\n" +
                "    text-align: center;\n" +
                "    color: rgba(255, 255, 255, 0.95);\n" +
                "    font-size: 16px;\n" +
                "    line-height: 1.6;\n" +
                "}\n" +
                "\n" +
                ".confirm-modal-footer {\n" +
                "    padding: 20px 30px 30px;\n" +
                "    display: flex;\n" +
                "    gap: 15px;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".confirm-btn {\n" +
                "    padding: 12px 30px;\n" +
                "    border: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-size: 16px;\n" +
                "    font-weight: 600;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    min-width: 100px;\n" +
                "    position: relative;\n" +
                "    overflow: hidden;\n" +
                "}\n" +
                "\n" +
                ".confirm-btn::before {\n" +
                "    content: '';\n" +
                "    position: absolute;\n" +
                "    top: 50%;\n" +
                "    left: 50%;\n" +
                "    width: 0;\n" +
                "    height: 0;\n" +
                "    background: rgba(255, 255, 255, 0.3);\n" +
                "    border-radius: 50%;\n" +
                "    transform: translate(-50%, -50%);\n" +
                "    transition: width 0.6s, height 0.6s;\n" +
                "}\n" +
                "\n" +
                ".confirm-btn:hover::before {\n" +
                "    width: 300px;\n" +
                "    height: 300px;\n" +
                "}\n" +
                "\n" +
                ".confirm-btn.primary {\n" +
                "    background: linear-gradient(45deg, #ff6b6b, #ee5a24);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 6px 20px rgba(238, 90, 36, 0.4);\n" +
                "}\n" +
                "\n" +
                ".confirm-btn.primary:hover {\n" +
                "    box-shadow: 0 12px 35px rgba(238, 90, 36, 0.6);\n" +
                "}\n" +
                "\n" +
                ".confirm-btn.secondary {\n" +
                "    background: rgba(255, 255, 255, 0.15);\n" +
                "    color: white;\n" +
                "    border: 2px solid rgba(255, 255, 255, 0.3);\n" +
                "    backdrop-filter: blur(10px);\n" +
                "}\n" +
                "\n" +
                ".confirm-btn.secondary:hover {\n" +
                "    background: rgba(255, 255, 255, 0.25);\n" +
                "    border-color: rgba(255, 255, 255, 0.5);\n" +
                "}\n" +
                "\n" +
                ".confirm-btn:active {\n" +
                "    transform: translateY(-1px);\n" +
                "}\n";
    }
}
