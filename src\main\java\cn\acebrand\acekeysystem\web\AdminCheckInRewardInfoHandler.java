package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.*;
import java.nio.charset.StandardCharsets;

/**
 * 签到奖励说明配置处理器
 * 处理签到页面显示的奖励说明信息的配置
 */
public class AdminCheckInRewardInfoHandler implements HttpHandler {
    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final File configFile;
    private FileConfiguration config;

    public AdminCheckInRewardInfoHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
        this.configFile = new File(plugin.getDataFolder(), "checkin-reward-info.yml");
        loadConfig();
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        // 检查管理员权限
        if (!isAdminAuthenticated(exchange)) {
            sendResponse(exchange, 401, "{\"success\": false, \"message\": \"未授权访问\"}");
            return;
        }

        try {
            if ("GET".equals(method)) {
                handleGetRewardInfo(exchange);
            } else if ("POST".equals(method)) {
                handleSaveRewardInfo(exchange);
            } else {
                sendResponse(exchange, 405, "{\"success\": false, \"message\": \"不支持的请求方法\"}");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理奖励说明配置请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "{\"success\": false, \"message\": \"服务器内部错误\"}");
        }
    }

    /**
     * 处理获取奖励说明配置请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetRewardInfo(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        JSONObject rewardInfo = new JSONObject();

        // 每日奖励配置
        JSONObject daily = new JSONObject();
        daily.put("title", config.getString("daily.title", "每日奖励"));
        daily.put("description", config.getString("daily.description", "每天签到获得 <strong>10积分</strong>"));
        rewardInfo.put("daily", daily);

        // 连续奖励配置
        JSONObject consecutive = new JSONObject();
        
        JSONObject day7 = new JSONObject();
        day7.put("title", config.getString("consecutive.day7.title", "连续7天"));
        day7.put("description", config.getString("consecutive.day7.description", "额外获得 <strong>50积分 + 钻石奖励</strong>"));
        consecutive.put("day7", day7);

        JSONObject day15 = new JSONObject();
        day15.put("title", config.getString("consecutive.day15.title", "连续15天"));
        day15.put("description", config.getString("consecutive.day15.description", "额外获得 <strong>100积分 + 豪华奖励</strong>"));
        consecutive.put("day15", day15);

        JSONObject day30 = new JSONObject();
        day30.put("title", config.getString("consecutive.day30.title", "连续30天"));
        day30.put("description", config.getString("consecutive.day30.description", "额外获得 <strong>200积分 + 超级奖励</strong>"));
        consecutive.put("day30", day30);

        rewardInfo.put("consecutive", consecutive);

        response.put("success", true);
        response.put("rewardInfo", rewardInfo);

        sendResponse(exchange, 200, response.toJSONString());
    }

    /**
     * 处理保存奖励说明配置请求
     */
    @SuppressWarnings("unchecked")
    private void handleSaveRewardInfo(HttpExchange exchange) throws IOException {
        try {
            // 读取请求体
            String requestBody = readRequestBody(exchange);
            JSONParser parser = new JSONParser();
            JSONObject requestData = (JSONObject) parser.parse(requestBody);

            // 保存每日奖励配置
            if (requestData.containsKey("daily")) {
                JSONObject daily = (JSONObject) requestData.get("daily");
                if (daily.containsKey("title")) {
                    config.set("daily.title", daily.get("title"));
                }
                if (daily.containsKey("description")) {
                    config.set("daily.description", daily.get("description"));
                }
            }

            // 保存连续奖励配置
            if (requestData.containsKey("consecutive")) {
                JSONObject consecutive = (JSONObject) requestData.get("consecutive");
                
                if (consecutive.containsKey("day7")) {
                    JSONObject day7 = (JSONObject) consecutive.get("day7");
                    if (day7.containsKey("title")) {
                        config.set("consecutive.day7.title", day7.get("title"));
                    }
                    if (day7.containsKey("description")) {
                        config.set("consecutive.day7.description", day7.get("description"));
                    }
                }

                if (consecutive.containsKey("day15")) {
                    JSONObject day15 = (JSONObject) consecutive.get("day15");
                    if (day15.containsKey("title")) {
                        config.set("consecutive.day15.title", day15.get("title"));
                    }
                    if (day15.containsKey("description")) {
                        config.set("consecutive.day15.description", day15.get("description"));
                    }
                }

                if (consecutive.containsKey("day30")) {
                    JSONObject day30 = (JSONObject) consecutive.get("day30");
                    if (day30.containsKey("title")) {
                        config.set("consecutive.day30.title", day30.get("title"));
                    }
                    if (day30.containsKey("description")) {
                        config.set("consecutive.day30.description", day30.get("description"));
                    }
                }
            }

            // 保存配置文件
            saveConfig();

            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "奖励说明配置保存成功");

            sendResponse(exchange, 200, response.toJSONString());

        } catch (Exception e) {
            plugin.getLogger().severe("保存奖励说明配置时出错: " + e.getMessage());
            e.printStackTrace();
            
            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "保存失败: " + e.getMessage());
            
            sendResponse(exchange, 500, response.toJSONString());
        }
    }

    /**
     * 验证管理员权限
     */
    private boolean isAdminAuthenticated(HttpExchange exchange) {
        // 检查会话
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
            return true;
        }

        // 检查API密钥
        String query = exchange.getRequestURI().getQuery();
        if (query != null && query.contains("key=" + webServer.getAdminKey())) {
            return true;
        }

        return false;
    }

    /**
     * 从Cookie中获取会话ID
     */
    private String getSessionFromCookie(HttpExchange exchange) {
        String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
        if (cookieHeader != null) {
            String[] cookies = cookieHeader.split(";");
            for (String cookie : cookies) {
                String[] parts = cookie.trim().split("=", 2);
                if (parts.length == 2 && "admin_session".equals(parts[0])) {
                    return parts[1];
                }
            }
        }
        return null;
    }

    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        }
        return body.toString();
    }

    /**
     * 加载配置文件
     */
    private void loadConfig() {
        if (!configFile.exists()) {
            createDefaultConfig();
        }
        config = YamlConfiguration.loadConfiguration(configFile);
    }

    /**
     * 创建默认配置
     */
    private void createDefaultConfig() {
        try {
            configFile.getParentFile().mkdirs();
            configFile.createNewFile();
            
            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);
            
            // 默认每日奖励配置
            defaultConfig.set("daily.title", "每日奖励");
            defaultConfig.set("daily.description", "每天签到获得 <strong>10积分</strong>");
            
            // 默认连续奖励配置
            defaultConfig.set("consecutive.day7.title", "连续7天");
            defaultConfig.set("consecutive.day7.description", "额外获得 <strong>50积分 + 钻石奖励</strong>");
            
            defaultConfig.set("consecutive.day15.title", "连续15天");
            defaultConfig.set("consecutive.day15.description", "额外获得 <strong>100积分 + 豪华奖励</strong>");
            
            defaultConfig.set("consecutive.day30.title", "连续30天");
            defaultConfig.set("consecutive.day30.description", "额外获得 <strong>200积分 + 超级奖励</strong>");
            
            defaultConfig.save(configFile);
            
        } catch (IOException e) {
            plugin.getLogger().severe("创建默认奖励说明配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 保存配置文件
     */
    private void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存奖励说明配置文件失败: " + e.getMessage());
        }
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String response) throws IOException {
        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        OutputStream os = exchange.getResponseBody();
        os.write(responseBytes);
        os.close();
    }

    /**
     * 获取奖励说明配置（供签到页面使用）
     */
    public JSONObject getRewardInfoForCheckInPage() {
        JSONObject rewardInfo = new JSONObject();
        
        // 每日奖励
        JSONObject daily = new JSONObject();
        daily.put("title", config.getString("daily.title", "每日奖励"));
        daily.put("description", config.getString("daily.description", "每天签到获得 <strong>10积分</strong>"));
        rewardInfo.put("daily", daily);
        
        // 连续奖励
        JSONObject consecutive = new JSONObject();
        
        JSONObject day7 = new JSONObject();
        day7.put("title", config.getString("consecutive.day7.title", "连续7天"));
        day7.put("description", config.getString("consecutive.day7.description", "额外获得 <strong>50积分 + 钻石奖励</strong>"));
        consecutive.put("day7", day7);
        
        JSONObject day15 = new JSONObject();
        day15.put("title", config.getString("consecutive.day15.title", "连续15天"));
        day15.put("description", config.getString("consecutive.day15.description", "额外获得 <strong>100积分 + 豪华奖励</strong>"));
        consecutive.put("day15", day15);
        
        JSONObject day30 = new JSONObject();
        day30.put("title", config.getString("consecutive.day30.title", "连续30天"));
        day30.put("description", config.getString("consecutive.day30.description", "额外获得 <strong>200积分 + 超级奖励</strong>"));
        consecutive.put("day30", day30);
        
        rewardInfo.put("consecutive", consecutive);
        
        return rewardInfo;
    }
}
