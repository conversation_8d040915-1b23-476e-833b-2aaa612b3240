package cn.acebrand.acekeysystem.task;

import org.bukkit.scheduler.BukkitRunnable;
import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 手动同步任务
 * 用于响应手动同步命令
 */
public final class ManualSyncTask extends BukkitRunnable {

    private final AceKeySystem plugin;

    /**
     * 构造函数
     *
     * @param plugin 插件实例
     */
    public ManualSyncTask(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    @Override
    public void run() {
        // 执行手动卡密同步
        this.plugin.syncKeysToWebsite();
    }
}
