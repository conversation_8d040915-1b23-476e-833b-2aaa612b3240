package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.*;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.*;

/**
 * 账号管理处理器
 * 负责管理员账号的增删改查功能
 */
public class AccountManagementHandler implements HttpHandler {
    private final AceKeySystem plugin;
    private final WebServer webServer;
    private File accountsFile;
    private YamlConfiguration accountsConfig;
    private File rolesFile;
    private YamlConfiguration rolesConfig;

    public AccountManagementHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
        initializeAccountsFile();
        initializeRolesFile();
    }

    /**
     * 初始化账号文件
     */
    private void initializeAccountsFile() {
        // 创建data文件夹
        File dataFolder = new File(plugin.getDataFolder(), "data");
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
            plugin.getLogger().info("创建data文件夹: " + dataFolder.getPath());
        }

        accountsFile = new File(dataFolder, "admin_accounts.yml");

        if (!accountsFile.exists()) {
            try {
                accountsFile.createNewFile();
                plugin.getLogger().info("创建管理员账号文件: data/admin_accounts.yml");
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建管理员账号文件: " + e.getMessage());
            }
        }

        accountsConfig = YamlConfiguration.loadConfiguration(accountsFile);

        // 如果文件为空，创建默认配置
        if (!accountsConfig.contains("accounts")) {
            createDefaultAccountsConfig();
        }
    }

    /**
     * 初始化角色文件
     */
    private void initializeRolesFile() {
        // 创建data文件夹
        File dataFolder = new File(plugin.getDataFolder(), "data");
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        rolesFile = new File(dataFolder, "admin_roles.yml");

        if (!rolesFile.exists()) {
            try {
                rolesFile.createNewFile();
                plugin.getLogger().info("创建管理员角色文件: data/admin_roles.yml");
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建管理员角色文件: " + e.getMessage());
            }
        }

        rolesConfig = YamlConfiguration.loadConfiguration(rolesFile);

        // 如果文件为空，创建默认角色配置
        if (!rolesConfig.contains("roles")) {
            createDefaultRolesConfig();
        }
    }

    /**
     * 创建默认角色配置
     */
    private void createDefaultRolesConfig() {
        // 创建默认角色
        rolesConfig.set("roles.viewer.name", "查看者");
        rolesConfig.set("roles.viewer.description", "只能查看数据，无法进行任何操作");
        rolesConfig.set("roles.viewer.permissions.punishment", false);
        rolesConfig.set("roles.viewer.permissions.accountManagement", false);
        rolesConfig.set("roles.viewer.permissions.systemSettings", false);
        rolesConfig.set("roles.viewer.permissions.dataView", true);
        rolesConfig.set("roles.viewer.permissions.keyManagement", false);
        rolesConfig.set("roles.viewer.permissions.rewardManagement", false);
        rolesConfig.set("roles.viewer.permissions.pointsShop", false);
        rolesConfig.set("roles.viewer.permissions.winnersView", false);
        rolesConfig.set("roles.viewer.permissions.statisticsView", false);

        rolesConfig.set("roles.moderator.name", "版主");
        rolesConfig.set("roles.moderator.description", "可以进行封禁操作和查看数据");
        rolesConfig.set("roles.moderator.permissions.punishment", true);
        rolesConfig.set("roles.moderator.permissions.accountManagement", false);
        rolesConfig.set("roles.moderator.permissions.systemSettings", false);
        rolesConfig.set("roles.moderator.permissions.dataView", true);
        rolesConfig.set("roles.moderator.permissions.keyManagement", false);
        rolesConfig.set("roles.moderator.permissions.rewardManagement", false);
        rolesConfig.set("roles.moderator.permissions.pointsShop", false);
        rolesConfig.set("roles.moderator.permissions.winnersView", true);
        rolesConfig.set("roles.moderator.permissions.statisticsView", true);

        rolesConfig.set("roles.manager.name", "管理员");
        rolesConfig.set("roles.manager.description", "拥有大部分管理权限");
        rolesConfig.set("roles.manager.permissions.punishment", true);
        rolesConfig.set("roles.manager.permissions.accountManagement", false);
        rolesConfig.set("roles.manager.permissions.systemSettings", true);
        rolesConfig.set("roles.manager.permissions.dataView", true);
        rolesConfig.set("roles.manager.permissions.keyManagement", true);
        rolesConfig.set("roles.manager.permissions.rewardManagement", true);
        rolesConfig.set("roles.manager.permissions.pointsShop", true);
        rolesConfig.set("roles.manager.permissions.winnersView", true);
        rolesConfig.set("roles.manager.permissions.statisticsView", true);

        saveRolesConfig();
    }

    /**
     * 保存角色配置
     */
    private void saveRolesConfig() {
        try {
            rolesConfig.save(rolesFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存管理员角色配置: " + e.getMessage());
        }
    }

    /**
     * 创建默认账号配置
     */
    private void createDefaultAccountsConfig() {
        // 从主配置文件读取默认管理员账号
        String defaultUsername = plugin.getConfig().getString("admin.username", "admin");
        String defaultPassword = plugin.getConfig().getString("admin.password", "admin123");

        // 生成盐值和哈希密码
        String salt = generateSalt();
        String hashedPassword = hashPassword(defaultPassword, salt);

        accountsConfig.set("accounts." + defaultUsername + ".password_hash", hashedPassword);
        accountsConfig.set("accounts." + defaultUsername + ".salt", salt);
        accountsConfig.set("accounts." + defaultUsername + ".role", "super_admin");
        accountsConfig.set("accounts." + defaultUsername + ".created_time", System.currentTimeMillis());
        accountsConfig.set("accounts." + defaultUsername + ".last_login", 0);
        accountsConfig.set("accounts." + defaultUsername + ".enabled", true);

        saveAccountsConfig();
    }

    /**
     * 保存账号配置
     */
    private void saveAccountsConfig() {
        try {
            accountsConfig.save(accountsFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存管理员账号配置: " + e.getMessage());
        }
    }

    /**
     * 生成随机盐值
     */
    private String generateSalt() {
        SecureRandom random = new SecureRandom();
        byte[] salt = new byte[16];
        random.nextBytes(salt);
        return Base64.getEncoder().encodeToString(salt);
    }

    /**
     * 哈希密码
     */
    private String hashPassword(String password, String salt) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            md.update(Base64.getDecoder().decode(salt));
            byte[] hashedPassword = md.digest(password.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashedPassword);
        } catch (NoSuchAlgorithmException e) {
            plugin.getLogger().severe("无法创建密码哈希: " + e.getMessage());
            return password; // 降级到明文存储（不推荐）
        }
    }

    /**
     * 验证密码
     */
    public boolean validatePassword(String username, String password) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        String storedHash = accountsConfig.getString("accounts." + username + ".password_hash");
        String salt = accountsConfig.getString("accounts." + username + ".salt");
        boolean enabled = accountsConfig.getBoolean("accounts." + username + ".enabled", true);

        if (!enabled) {
            return false;
        }

        // 检查封禁状态
        if (isAccountBanned(username)) {
            return false;
        }

        String inputHash = hashPassword(password, salt);
        boolean isValid = storedHash.equals(inputHash);

        // 更新最后登录时间
        if (isValid) {
            accountsConfig.set("accounts." + username + ".last_login", System.currentTimeMillis());
            saveAccountsConfig();
        }

        return isValid;
    }

    /**
     * 验证密码并返回详细状态信息
     */
    public LoginResult validatePasswordWithDetails(String username, String password) {
        LoginResult result = new LoginResult();

        if (!accountsConfig.contains("accounts." + username)) {
            result.success = false;
            result.message = "账号不存在";
            result.errorType = "ACCOUNT_NOT_FOUND";
            return result;
        }

        String storedHash = accountsConfig.getString("accounts." + username + ".password_hash");
        String salt = accountsConfig.getString("accounts." + username + ".salt");
        boolean enabled = accountsConfig.getBoolean("accounts." + username + ".enabled", true);

        // 先验证密码
        String inputHash = hashPassword(password, salt);
        boolean passwordCorrect = storedHash.equals(inputHash);

        if (!passwordCorrect) {
            result.success = false;
            result.message = "用户名或密码错误";
            result.errorType = "INVALID_CREDENTIALS";
            return result;
        }

        // 优先检查封禁状态（封禁的账号可能enabled=false，但应该显示封禁信息而不是禁用信息）
        boolean isBanned = isAccountBanned(username);
        if (isBanned) {
            Map<String, Object> banInfo = getAccountBanInfo(username);
            result.success = false;
            result.errorType = "ACCOUNT_BANNED";
            result.banInfo = banInfo;

            String reason = (String) banInfo.get("reason");
            long banEndTime = (Long) banInfo.get("ban_end_time");

            if (banEndTime == -1) {
                result.message = "账号已被永久封禁。封禁原因：" + reason;
            } else {
                long remainingTime = banEndTime - System.currentTimeMillis();
                if (remainingTime > 0) {
                    String timeText = formatRemainingTime(remainingTime);
                    result.message = "账号已被封禁，剩余时间：" + timeText + "。封禁原因：" + reason;
                } else {
                    // 封禁已过期，但由于某种原因没有自动解封，手动解封
                    accountsConfig.set("accounts." + username + ".ban_info.banned", false);
                    accountsConfig.set("accounts." + username + ".enabled", true);
                    saveAccountsConfig();
                    result.success = true;
                    result.message = "登录成功";
                }
            }

            if (!result.success) {
                return result;
            }
        }

        // 检查账号是否被禁用（只有在没有封禁的情况下才检查）
        if (!enabled) {
            result.success = false;
            result.message = "账号已被禁用，请联系管理员";
            result.errorType = "ACCOUNT_DISABLED";
            return result;
        }

        // 登录成功
        result.success = true;
        result.message = "登录成功";

        // 更新最后登录时间
        accountsConfig.set("accounts." + username + ".last_login", System.currentTimeMillis());
        saveAccountsConfig();

        return result;
    }

    /**
     * 格式化剩余时间
     */
    private String formatRemainingTime(long remainingMillis) {
        long seconds = remainingMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;

        if (days > 0) {
            return days + "天" + (hours % 24) + "小时";
        } else if (hours > 0) {
            return hours + "小时" + (minutes % 60) + "分钟";
        } else if (minutes > 0) {
            return minutes + "分钟";
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 登录结果类
     */
    public static class LoginResult {
        public boolean success;
        public String message;
        public String errorType;
        public Map<String, Object> banInfo;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        String path = exchange.getRequestURI().getPath();

        try {
            // 验证管理员权限
            if (!isAuthorized(exchange)) {
                handleUnauthorized(exchange);
                return;
            }

            if ("GET".equals(method)) {
                handleGetRequest(exchange);
            } else if ("POST".equals(method)) {
                handlePostRequest(exchange);
            } else {
                sendResponse(exchange, 405, "text/plain", "方法不允许");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理账号管理请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "application/json",
                    "{\"success\":false,\"message\":\"服务器内部错误\"}");
        }
    }

    /**
     * 验证管理员权限 - 只有超级管理员才能访问账号管理
     */
    private boolean isAuthorized(HttpExchange exchange) {
        // 检查会话
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
            // 获取当前登录的用户名并检查是否为超级管理员
            String username = webServer.getAdminLoginHandler().getUsernameFromSession(sessionId);
            if (username != null) {
                return isSuperAdmin(username);
            }
            return false;
        }

        // 检查API密钥（API密钥登录默认拥有超级管理员权限）
        String query = exchange.getRequestURI().getQuery();
        Map<String, String> params = parseQuery(query);
        String providedKey = params.get("key");
        return providedKey != null && providedKey.equals(webServer.getAdminKey());
    }

    /**
     * 从Cookie获取会话ID
     */
    private String getSessionFromCookie(HttpExchange exchange) {
        String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
        if (cookieHeader != null) {
            String[] cookies = cookieHeader.split(";");
            for (String cookie : cookies) {
                String[] parts = cookie.trim().split("=", 2);
                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                    return parts[1];
                }
            }
        }
        return null;
    }

    /**
     * 解析查询参数
     */
    private Map<String, String> parseQuery(String query) {
        Map<String, String> params = new HashMap<>();
        if (query != null && !query.isEmpty()) {
            String[] pairs = query.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2) {
                    params.put(keyValue[0], keyValue[1]);
                }
            }
        }
        return params;
    }

    /**
     * 处理GET请求
     */
    private void handleGetRequest(HttpExchange exchange) throws IOException {
        URI uri = exchange.getRequestURI();
        String query = uri.getQuery();
        Map<String, String> params = parseQuery(query);
        String action = params.getOrDefault("action", "list");

        switch (action) {
            case "list":
                handleListAccounts(exchange);
                break;
            case "get":
                handleGetAccount(exchange, params.get("username"));
                break;
            case "roles":
                handleListRoles(exchange);
                break;
            case "get_role":
                handleGetRole(exchange, params.get("roleId"));
                break;
            case "get_ip_info":
                handleGetIPInfo(exchange, params.get("username"));
                break;
            default:
                sendResponse(exchange, 400, "application/json",
                        "{\"success\":false,\"message\":\"未知操作\"}");
        }
    }

    /**
     * 处理POST请求
     */
    private void handlePostRequest(HttpExchange exchange) throws IOException {
        String requestBody = readRequestBody(exchange);

        try {
            JSONParser parser = new JSONParser();
            JSONObject data = (JSONObject) parser.parse(requestBody);
            String action = (String) data.get("action");

            switch (action) {
                case "create":
                    handleCreateAccount(exchange, data);
                    break;
                case "update":
                    handleUpdateAccount(exchange, data);
                    break;
                case "delete":
                    handleDeleteAccount(exchange, data);
                    break;
                case "toggle":
                    handleToggleAccount(exchange, data);
                    break;
                case "change_password":
                    handleChangePassword(exchange, data);
                    break;
                case "create_role":
                    handleCreateRole(exchange, data);
                    break;
                case "update_role":
                    handleUpdateRole(exchange, data);
                    break;
                case "delete_role":
                    handleDeleteRole(exchange, data);
                    break;
                case "assign_role":
                    handleAssignRole(exchange, data);
                    break;
                case "kick_ip":
                    handleKickIP(exchange, data);
                    break;
                case "ban_ip":
                    handleBanIP(exchange, data);
                    break;
                case "ban_account":
                    handleBanAccount(exchange, data);
                    break;
                case "unban_account":
                    handleUnbanAccount(exchange, data);
                    break;
                default:
                    sendResponse(exchange, 400, "application/json",
                            "{\"success\":false,\"message\":\"未知操作\"}");
            }
        } catch (Exception e) {
            sendResponse(exchange, 400, "application/json",
                    "{\"success\":false,\"message\":\"请求数据格式错误\"}");
        }
    }

    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            StringBuilder body = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
            return body.toString();
        }
    }

    /**
     * 处理账号列表请求
     */
    @SuppressWarnings("unchecked")
    private void handleListAccounts(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        JSONArray accounts = new JSONArray();

        if (accountsConfig.contains("accounts")) {
            Set<String> usernames = accountsConfig.getConfigurationSection("accounts").getKeys(false);

            for (String username : usernames) {
                JSONObject account = new JSONObject();
                account.put("username", username);
                account.put("role", accountsConfig.getString("accounts." + username + ".role", "admin"));
                account.put("enabled", accountsConfig.getBoolean("accounts." + username + ".enabled", true));
                account.put("created_time", accountsConfig.getLong("accounts." + username + ".created_time", 0));
                account.put("last_login", accountsConfig.getLong("accounts." + username + ".last_login", 0));

                // 添加分配的角色信息
                String assignedRole = accountsConfig.getString("accounts." + username + ".assignedRole", null);
                if (assignedRole != null && rolesConfig.contains("roles." + assignedRole)) {
                    account.put("assignedRole", assignedRole);
                    account.put("assignedRoleName",
                            rolesConfig.getString("roles." + assignedRole + ".name", assignedRole));
                } else {
                    account.put("assignedRole", null);
                    account.put("assignedRoleName", null);
                }

                // 添加权限信息
                JSONObject permissions = new JSONObject();
                permissions.put("punishment",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.punishment", false));
                permissions.put("accountManagement",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.accountManagement", false));
                permissions.put("systemSettings",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.systemSettings", false));
                permissions.put("dataView",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.dataView", true));
                permissions.put("keyManagement",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.keyManagement", false));
                permissions.put("rewardManagement",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.rewardManagement", false));
                permissions.put("pointsShop",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.pointsShop", false));
                permissions.put("winnersView",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.winnersView", false));
                permissions.put("statisticsView",
                        accountsConfig.getBoolean("accounts." + username + ".permissions.statisticsView", false));
                account.put("permissions", permissions);

                // 添加IP信息
                Map<String, Object> ipInfo = getAccountIPInfo(username);
                account.put("ipInfo", ipInfo);

                // 添加封禁信息
                Map<String, Object> banInfo = getAccountBanInfo(username);
                account.put("banInfo", banInfo);

                accounts.add(account);
            }
        }

        response.put("success", true);
        response.put("accounts", accounts);

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理获取单个账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetAccount(HttpExchange exchange, String username) throws IOException {
        JSONObject response = new JSONObject();

        if (username == null || username.isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else {
            JSONObject account = new JSONObject();
            account.put("username", username);
            account.put("role", accountsConfig.getString("accounts." + username + ".role", "admin"));
            account.put("enabled", accountsConfig.getBoolean("accounts." + username + ".enabled", true));
            account.put("created_time", accountsConfig.getLong("accounts." + username + ".created_time", 0));
            account.put("last_login", accountsConfig.getLong("accounts." + username + ".last_login", 0));

            // 添加分配的角色信息
            String assignedRole = accountsConfig.getString("accounts." + username + ".assignedRole", null);
            if (assignedRole != null && rolesConfig.contains("roles." + assignedRole)) {
                account.put("assignedRole", assignedRole);
                account.put("assignedRoleName", rolesConfig.getString("roles." + assignedRole + ".name", assignedRole));
            } else {
                account.put("assignedRole", null);
                account.put("assignedRoleName", null);
            }

            // 添加权限信息
            JSONObject permissions = new JSONObject();
            permissions.put("punishment",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.punishment", false));
            permissions.put("accountManagement",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.accountManagement", false));
            permissions.put("systemSettings",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.systemSettings", false));
            permissions.put("dataView",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.dataView", true));
            permissions.put("keyManagement",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.keyManagement", false));
            permissions.put("rewardManagement",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.rewardManagement", false));
            permissions.put("pointsShop",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.pointsShop", false));
            permissions.put("winnersView",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.winnersView", false));
            permissions.put("statisticsView",
                    accountsConfig.getBoolean("accounts." + username + ".permissions.statisticsView", false));
            account.put("permissions", permissions);

            response.put("success", true);
            response.put("account", account);
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理角色列表请求
     */
    @SuppressWarnings("unchecked")
    private void handleListRoles(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        JSONArray roles = new JSONArray();

        if (rolesConfig.contains("roles")) {
            Set<String> roleIds = rolesConfig.getConfigurationSection("roles").getKeys(false);

            for (String roleId : roleIds) {
                JSONObject role = new JSONObject();
                role.put("id", roleId);
                role.put("name", rolesConfig.getString("roles." + roleId + ".name", roleId));
                role.put("description", rolesConfig.getString("roles." + roleId + ".description", ""));

                // 添加权限信息
                JSONObject permissions = new JSONObject();
                permissions.put("punishment",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.punishment", false));
                permissions.put("accountManagement",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.accountManagement", false));
                permissions.put("systemSettings",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.systemSettings", false));
                permissions.put("interfaceSettings",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.interfaceSettings", false));
                permissions.put("dataView", rolesConfig.getBoolean("roles." + roleId + ".permissions.dataView", true));
                permissions.put("keyManagement",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.keyManagement", false));
                permissions.put("rewardManagement",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.rewardManagement", false));
                permissions.put("pointsShop",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.pointsShop", false));
                permissions.put("winnersView",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.winnersView", false));
                permissions.put("statisticsView",
                        rolesConfig.getBoolean("roles." + roleId + ".permissions.statisticsView", false));
                role.put("permissions", permissions);

                roles.add(role);
            }
        }

        response.put("success", true);
        response.put("roles", roles);

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理获取单个角色请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetRole(HttpExchange exchange, String roleId) throws IOException {
        JSONObject response = new JSONObject();

        if (roleId == null || roleId.isEmpty()) {
            response.put("success", false);
            response.put("message", "角色ID不能为空");
        } else if (!rolesConfig.contains("roles." + roleId)) {
            response.put("success", false);
            response.put("message", "角色不存在");
        } else {
            JSONObject role = new JSONObject();
            role.put("id", roleId);
            role.put("name", rolesConfig.getString("roles." + roleId + ".name", roleId));
            role.put("description", rolesConfig.getString("roles." + roleId + ".description", ""));

            // 添加权限信息
            JSONObject permissions = new JSONObject();
            permissions.put("punishment", rolesConfig.getBoolean("roles." + roleId + ".permissions.punishment", false));
            permissions.put("accountManagement",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.accountManagement", false));
            permissions.put("systemSettings",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.systemSettings", false));
            permissions.put("interfaceSettings",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.interfaceSettings", false));
            permissions.put("dataView", rolesConfig.getBoolean("roles." + roleId + ".permissions.dataView", true));
            permissions.put("keyManagement",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.keyManagement", false));
            permissions.put("rewardManagement",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.rewardManagement", false));
            permissions.put("pointsShop", rolesConfig.getBoolean("roles." + roleId + ".permissions.pointsShop", false));
            permissions.put("winnersView",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.winnersView", false));
            permissions.put("statisticsView",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.statisticsView", false));
            role.put("permissions", permissions);

            response.put("success", true);
            response.put("role", role);
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理创建账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleCreateAccount(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        String password = (String) data.get("password");
        String role = (String) data.get("role");
        JSONObject permissions = (JSONObject) data.get("permissions");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (password == null || password.length() < 6) {
            response.put("success", false);
            response.put("message", "密码长度不能少于6位");
        } else if (accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "用户名已存在");
        } else {
            // 创建新账号
            String salt = generateSalt();
            String hashedPassword = hashPassword(password, salt);

            accountsConfig.set("accounts." + username + ".password_hash", hashedPassword);
            accountsConfig.set("accounts." + username + ".salt", salt);
            accountsConfig.set("accounts." + username + ".role", role != null ? role : "admin");
            accountsConfig.set("accounts." + username + ".created_time", System.currentTimeMillis());
            accountsConfig.set("accounts." + username + ".last_login", 0);
            accountsConfig.set("accounts." + username + ".enabled", true);

            // 保存权限信息
            if (permissions != null) {
                Boolean punishmentPermission = (Boolean) permissions.get("punishment");
                Boolean accountManagementPermission = (Boolean) permissions.get("accountManagement");
                Boolean systemSettingsPermission = (Boolean) permissions.get("systemSettings");
                Boolean interfaceSettingsPermission = (Boolean) permissions.get("interfaceSettings");
                Boolean dataViewPermission = (Boolean) permissions.get("dataView");
                Boolean keyManagementPermission = (Boolean) permissions.get("keyManagement");
                Boolean rewardManagementPermission = (Boolean) permissions.get("rewardManagement");
                Boolean pointsShopPermission = (Boolean) permissions.get("pointsShop");
                Boolean winnersViewPermission = (Boolean) permissions.get("winnersView");
                Boolean statisticsViewPermission = (Boolean) permissions.get("statisticsView");

                accountsConfig.set("accounts." + username + ".permissions.punishment",
                        punishmentPermission != null ? punishmentPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.accountManagement",
                        accountManagementPermission != null ? accountManagementPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.systemSettings",
                        systemSettingsPermission != null ? systemSettingsPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.interfaceSettings",
                        interfaceSettingsPermission != null ? interfaceSettingsPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.dataView",
                        dataViewPermission != null ? dataViewPermission : true);
                accountsConfig.set("accounts." + username + ".permissions.keyManagement",
                        keyManagementPermission != null ? keyManagementPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.rewardManagement",
                        rewardManagementPermission != null ? rewardManagementPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.pointsShop",
                        pointsShopPermission != null ? pointsShopPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.winnersView",
                        winnersViewPermission != null ? winnersViewPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.statisticsView",
                        statisticsViewPermission != null ? statisticsViewPermission : false);
            } else {
                // 设置默认权限
                accountsConfig.set("accounts." + username + ".permissions.punishment", false);
                accountsConfig.set("accounts." + username + ".permissions.accountManagement", false);
                accountsConfig.set("accounts." + username + ".permissions.systemSettings", false);
                accountsConfig.set("accounts." + username + ".permissions.interfaceSettings", false);
                accountsConfig.set("accounts." + username + ".permissions.dataView", true);
                accountsConfig.set("accounts." + username + ".permissions.keyManagement", false);
                accountsConfig.set("accounts." + username + ".permissions.rewardManagement", false);
                accountsConfig.set("accounts." + username + ".permissions.pointsShop", false);
                accountsConfig.set("accounts." + username + ".permissions.winnersView", false);
                accountsConfig.set("accounts." + username + ".permissions.statisticsView", false);
            }

            saveAccountsConfig();

            response.put("success", true);
            response.put("message", "账号创建成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理更新账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleUpdateAccount(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        String role = (String) data.get("role");
        JSONObject permissions = (JSONObject) data.get("permissions");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else {
            // 更新账号信息
            if (role != null && !role.trim().isEmpty()) {
                accountsConfig.set("accounts." + username + ".role", role);
            }

            // 更新权限信息
            if (permissions != null) {
                Boolean punishmentPermission = (Boolean) permissions.get("punishment");
                Boolean accountManagementPermission = (Boolean) permissions.get("accountManagement");
                Boolean systemSettingsPermission = (Boolean) permissions.get("systemSettings");
                Boolean interfaceSettingsPermission = (Boolean) permissions.get("interfaceSettings");
                Boolean dataViewPermission = (Boolean) permissions.get("dataView");
                Boolean keyManagementPermission = (Boolean) permissions.get("keyManagement");
                Boolean rewardManagementPermission = (Boolean) permissions.get("rewardManagement");
                Boolean pointsShopPermission = (Boolean) permissions.get("pointsShop");
                Boolean winnersViewPermission = (Boolean) permissions.get("winnersView");
                Boolean statisticsViewPermission = (Boolean) permissions.get("statisticsView");

                accountsConfig.set("accounts." + username + ".permissions.punishment",
                        punishmentPermission != null ? punishmentPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.accountManagement",
                        accountManagementPermission != null ? accountManagementPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.systemSettings",
                        systemSettingsPermission != null ? systemSettingsPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.interfaceSettings",
                        interfaceSettingsPermission != null ? interfaceSettingsPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.dataView",
                        dataViewPermission != null ? dataViewPermission : true);
                accountsConfig.set("accounts." + username + ".permissions.keyManagement",
                        keyManagementPermission != null ? keyManagementPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.rewardManagement",
                        rewardManagementPermission != null ? rewardManagementPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.pointsShop",
                        pointsShopPermission != null ? pointsShopPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.winnersView",
                        winnersViewPermission != null ? winnersViewPermission : false);
                accountsConfig.set("accounts." + username + ".permissions.statisticsView",
                        statisticsViewPermission != null ? statisticsViewPermission : false);
            }

            saveAccountsConfig();

            response.put("success", true);
            response.put("message", "账号更新成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理删除账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleDeleteAccount(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else {
            // 检查是否是最后一个超级管理员
            String role = accountsConfig.getString("accounts." + username + ".role", "admin");
            if ("super_admin".equals(role)) {
                long superAdminCount = accountsConfig.getConfigurationSection("accounts").getKeys(false)
                        .stream()
                        .filter(user -> "super_admin".equals(accountsConfig.getString("accounts." + user + ".role")))
                        .count();

                if (superAdminCount <= 1) {
                    response.put("success", false);
                    response.put("message", "不能删除最后一个超级管理员账号");
                    sendResponse(exchange, 200, "application/json", response.toString());
                    return;
                }
            }

            // 删除账号
            accountsConfig.set("accounts." + username, null);
            saveAccountsConfig();

            response.put("success", true);
            response.put("message", "账号删除成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理启用/禁用账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleToggleAccount(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        Boolean enabled = (Boolean) data.get("enabled");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else if (enabled == null) {
            response.put("success", false);
            response.put("message", "状态参数无效");
        } else {
            // 检查是否是最后一个启用的超级管理员
            if (!enabled) {
                String role = accountsConfig.getString("accounts." + username + ".role", "admin");
                if ("super_admin".equals(role)) {
                    long enabledSuperAdminCount = accountsConfig.getConfigurationSection("accounts").getKeys(false)
                            .stream()
                            .filter(user -> "super_admin".equals(accountsConfig.getString("accounts." + user + ".role"))
                                    && accountsConfig.getBoolean("accounts." + user + ".enabled", true))
                            .count();

                    if (enabledSuperAdminCount <= 1) {
                        response.put("success", false);
                        response.put("message", "不能禁用最后一个启用的超级管理员账号");
                        sendResponse(exchange, 200, "application/json", response.toString());
                        return;
                    }
                }
            }

            // 更新账号状态
            accountsConfig.set("accounts." + username + ".enabled", enabled);
            saveAccountsConfig();

            response.put("success", true);
            response.put("message", enabled ? "账号已启用" : "账号已禁用");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理修改密码请求
     */
    @SuppressWarnings("unchecked")
    private void handleChangePassword(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        String newPassword = (String) data.get("new_password");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (newPassword == null || newPassword.length() < 6) {
            response.put("success", false);
            response.put("message", "新密码长度不能少于6位");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else {
            // 生成新的盐值和哈希密码
            String salt = generateSalt();
            String hashedPassword = hashPassword(newPassword, salt);

            accountsConfig.set("accounts." + username + ".password_hash", hashedPassword);
            accountsConfig.set("accounts." + username + ".salt", salt);

            saveAccountsConfig();

            response.put("success", true);
            response.put("message", "密码修改成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 发送响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String response)
            throws IOException {
        exchange.getResponseHeaders().set("Content-Type", contentType + "; charset=utf-8");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");

        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);

        try (OutputStream os = exchange.getResponseBody()) {
            os.write(responseBytes);
        }
    }

    /**
     * 处理未授权访问
     */
    private void handleUnauthorized(HttpExchange exchange) throws IOException {
        // 检查是否已登录但权限不足
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
            // 已登录但权限不足
            String method = exchange.getRequestMethod();
            if ("POST".equals(method)) {
                // API请求，返回JSON错误
                sendResponse(exchange, 403, "application/json",
                        "{\"success\":false,\"message\":\"权限不足，只有超级管理员才能管理账号\"}");
            } else {
                // 页面请求，显示权限不足页面
                String html = generatePermissionDeniedPage();
                sendResponse(exchange, 403, "text/html; charset=utf-8", html);
            }
        } else {
            // 未登录，重定向到登录页面
            exchange.getResponseHeaders().set("Location", "/admin/login");
            sendResponse(exchange, 302, "text/plain", "Redirecting to login...");
        }
    }

    /**
     * 生成权限不足页面
     */
    private String generatePermissionDeniedPage() {
        return "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>权限不足 - AceKey系统</title>\n" +
                "    <style>\n" +
                "        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); margin: 0; padding: 0; min-height: 100vh; display: flex; align-items: center; justify-content: center; }\n"
                +
                "        .container { background: white; border-radius: 20px; padding: 40px; text-align: center; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 500px; }\n"
                +
                "        .icon { font-size: 80px; color: #ff6b6b; margin-bottom: 20px; }\n" +
                "        h1 { color: #333; margin-bottom: 20px; font-size: 28px; }\n" +
                "        p { color: #666; margin-bottom: 30px; font-size: 16px; line-height: 1.6; }\n" +
                "        .btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 30px; border: none; border-radius: 25px; text-decoration: none; display: inline-block; font-size: 16px; transition: transform 0.3s ease; }\n"
                +
                "        .btn:hover { transform: translateY(-2px); }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <div class=\"icon\">🚫</div>\n" +
                "        <h1>权限不足</h1>\n" +
                "        <p>抱歉，您没有访问账号管理页面的权限。<br>只有超级管理员才能管理账号。</p>\n" +
                "        <a href=\"/admin\" class=\"btn\">返回管理控制台</a>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
    }

    /**
     * 获取用户角色
     */
    public String getUserRole(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return null;
        }
        return accountsConfig.getString("accounts." + username + ".role", "admin");
    }

    /**
     * 检查用户是否有封禁权限
     */
    public boolean hasPunishmentPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.punishment", false);
    }

    /**
     * 检查用户是否有账号管理权限
     */
    public boolean hasAccountManagementPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.accountManagement", false);
    }

    /**
     * 检查用户是否有系统设置权限
     */
    public boolean hasSystemSettingsPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.systemSettings", false);
    }

    /**
     * 检查用户是否有界面设置权限
     */
    public boolean hasInterfaceSettingsPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.interfaceSettings", false);
    }

    /**
     * 检查用户是否有数据查看权限
     */
    public boolean hasDataViewPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.dataView", true);
    }

    /**
     * 检查用户是否有卡密管理权限
     */
    public boolean hasKeyManagementPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.keyManagement", false);
    }

    /**
     * 检查用户是否有奖品管理权限
     */
    public boolean hasRewardManagementPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.rewardManagement", false);
    }

    /**
     * 检查用户是否有积分商店权限
     */
    public boolean hasPointsShopPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.pointsShop", false);
    }

    /**
     * 检查用户是否有中奖记录查看权限
     */
    public boolean hasWinnersViewPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.winnersView", false);
    }

    /**
     * 检查用户是否有统计分析权限
     */
    public boolean hasStatisticsViewPermission(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            return false;
        }

        // 超级管理员默认拥有所有权限
        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        if ("super_admin".equals(role)) {
            return true;
        }

        // 检查具体权限设置
        return accountsConfig.getBoolean("accounts." + username + ".permissions.statisticsView", false);
    }

    /**
     * 检查用户是否为超级管理员
     */
    private boolean isSuperAdmin(String username) {
        if (!accountsConfig.contains("accounts." + username)) {
            // 如果账号不存在，检查是否为配置文件中的默认管理员
            String configUsername = plugin.getConfig().getString("admin.username", "admin");
            return configUsername.equals(username);
        }

        String role = accountsConfig.getString("accounts." + username + ".role", "admin");
        return "super_admin".equals(role);
    }

    /**
     * 处理创建角色请求
     */
    @SuppressWarnings("unchecked")
    private void handleCreateRole(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String roleId = (String) data.get("roleId");
        String name = (String) data.get("name");
        String description = (String) data.get("description");
        JSONObject permissions = (JSONObject) data.get("permissions");

        if (roleId == null || roleId.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "角色ID不能为空");
        } else if (name == null || name.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "角色名称不能为空");
        } else if (rolesConfig.contains("roles." + roleId)) {
            response.put("success", false);
            response.put("message", "角色ID已存在");
        } else {
            // 创建新角色
            rolesConfig.set("roles." + roleId + ".name", name);
            rolesConfig.set("roles." + roleId + ".description", description != null ? description : "");

            // 保存权限信息
            if (permissions != null) {
                Boolean punishmentPermission = (Boolean) permissions.get("punishment");
                Boolean accountManagementPermission = (Boolean) permissions.get("accountManagement");
                Boolean systemSettingsPermission = (Boolean) permissions.get("systemSettings");
                Boolean interfaceSettingsPermission = (Boolean) permissions.get("interfaceSettings");
                Boolean dataViewPermission = (Boolean) permissions.get("dataView");
                Boolean keyManagementPermission = (Boolean) permissions.get("keyManagement");
                Boolean rewardManagementPermission = (Boolean) permissions.get("rewardManagement");
                Boolean pointsShopPermission = (Boolean) permissions.get("pointsShop");
                Boolean winnersViewPermission = (Boolean) permissions.get("winnersView");
                Boolean statisticsViewPermission = (Boolean) permissions.get("statisticsView");

                rolesConfig.set("roles." + roleId + ".permissions.punishment",
                        punishmentPermission != null ? punishmentPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.accountManagement",
                        accountManagementPermission != null ? accountManagementPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.systemSettings",
                        systemSettingsPermission != null ? systemSettingsPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.interfaceSettings",
                        interfaceSettingsPermission != null ? interfaceSettingsPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.dataView",
                        dataViewPermission != null ? dataViewPermission : true);
                rolesConfig.set("roles." + roleId + ".permissions.keyManagement",
                        keyManagementPermission != null ? keyManagementPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.rewardManagement",
                        rewardManagementPermission != null ? rewardManagementPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.pointsShop",
                        pointsShopPermission != null ? pointsShopPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.winnersView",
                        winnersViewPermission != null ? winnersViewPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.statisticsView",
                        statisticsViewPermission != null ? statisticsViewPermission : false);
            } else {
                // 设置默认权限
                rolesConfig.set("roles." + roleId + ".permissions.punishment", false);
                rolesConfig.set("roles." + roleId + ".permissions.accountManagement", false);
                rolesConfig.set("roles." + roleId + ".permissions.systemSettings", false);
                rolesConfig.set("roles." + roleId + ".permissions.interfaceSettings", false);
                rolesConfig.set("roles." + roleId + ".permissions.dataView", true);
                rolesConfig.set("roles." + roleId + ".permissions.keyManagement", false);
                rolesConfig.set("roles." + roleId + ".permissions.rewardManagement", false);
                rolesConfig.set("roles." + roleId + ".permissions.pointsShop", false);
                rolesConfig.set("roles." + roleId + ".permissions.winnersView", false);
                rolesConfig.set("roles." + roleId + ".permissions.statisticsView", false);
            }

            saveRolesConfig();

            response.put("success", true);
            response.put("message", "角色创建成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理更新角色请求
     */
    @SuppressWarnings("unchecked")
    private void handleUpdateRole(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String roleId = (String) data.get("roleId");
        String name = (String) data.get("name");
        String description = (String) data.get("description");
        JSONObject permissions = (JSONObject) data.get("permissions");

        if (roleId == null || roleId.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "角色ID不能为空");
        } else if (!rolesConfig.contains("roles." + roleId)) {
            response.put("success", false);
            response.put("message", "角色不存在");
        } else {
            // 更新角色信息
            if (name != null && !name.trim().isEmpty()) {
                rolesConfig.set("roles." + roleId + ".name", name);
            }
            if (description != null) {
                rolesConfig.set("roles." + roleId + ".description", description);
            }

            // 更新权限信息
            if (permissions != null) {
                Boolean punishmentPermission = (Boolean) permissions.get("punishment");
                Boolean accountManagementPermission = (Boolean) permissions.get("accountManagement");
                Boolean systemSettingsPermission = (Boolean) permissions.get("systemSettings");
                Boolean interfaceSettingsPermission = (Boolean) permissions.get("interfaceSettings");
                Boolean dataViewPermission = (Boolean) permissions.get("dataView");
                Boolean keyManagementPermission = (Boolean) permissions.get("keyManagement");
                Boolean rewardManagementPermission = (Boolean) permissions.get("rewardManagement");
                Boolean pointsShopPermission = (Boolean) permissions.get("pointsShop");
                Boolean winnersViewPermission = (Boolean) permissions.get("winnersView");
                Boolean statisticsViewPermission = (Boolean) permissions.get("statisticsView");

                rolesConfig.set("roles." + roleId + ".permissions.punishment",
                        punishmentPermission != null ? punishmentPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.accountManagement",
                        accountManagementPermission != null ? accountManagementPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.systemSettings",
                        systemSettingsPermission != null ? systemSettingsPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.interfaceSettings",
                        interfaceSettingsPermission != null ? interfaceSettingsPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.dataView",
                        dataViewPermission != null ? dataViewPermission : true);
                rolesConfig.set("roles." + roleId + ".permissions.keyManagement",
                        keyManagementPermission != null ? keyManagementPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.rewardManagement",
                        rewardManagementPermission != null ? rewardManagementPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.pointsShop",
                        pointsShopPermission != null ? pointsShopPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.winnersView",
                        winnersViewPermission != null ? winnersViewPermission : false);
                rolesConfig.set("roles." + roleId + ".permissions.statisticsView",
                        statisticsViewPermission != null ? statisticsViewPermission : false);
            }

            saveRolesConfig();

            response.put("success", true);
            response.put("message", "角色更新成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理删除角色请求
     */
    @SuppressWarnings("unchecked")
    private void handleDeleteRole(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String roleId = (String) data.get("roleId");

        if (roleId == null || roleId.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "角色ID不能为空");
        } else if (!rolesConfig.contains("roles." + roleId)) {
            response.put("success", false);
            response.put("message", "角色不存在");
        } else {
            // 删除角色
            rolesConfig.set("roles." + roleId, null);
            saveRolesConfig();

            response.put("success", true);
            response.put("message", "角色删除成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理分配角色请求
     */
    @SuppressWarnings("unchecked")
    private void handleAssignRole(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        String roleId = (String) data.get("roleId");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (roleId == null || roleId.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "角色ID不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else if (!rolesConfig.contains("roles." + roleId)) {
            response.put("success", false);
            response.put("message", "角色不存在");
        } else {
            // 获取角色权限并应用到账号
            JSONObject permissions = new JSONObject();
            permissions.put("punishment", rolesConfig.getBoolean("roles." + roleId + ".permissions.punishment", false));
            permissions.put("accountManagement",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.accountManagement", false));
            permissions.put("systemSettings",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.systemSettings", false));
            permissions.put("dataView", rolesConfig.getBoolean("roles." + roleId + ".permissions.dataView", true));
            permissions.put("keyManagement",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.keyManagement", false));
            permissions.put("rewardManagement",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.rewardManagement", false));
            permissions.put("pointsShop", rolesConfig.getBoolean("roles." + roleId + ".permissions.pointsShop", false));
            permissions.put("winnersView",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.winnersView", false));
            permissions.put("statisticsView",
                    rolesConfig.getBoolean("roles." + roleId + ".permissions.statisticsView", false));

            // 应用权限到账号
            Boolean punishmentPermission = (Boolean) permissions.get("punishment");
            Boolean accountManagementPermission = (Boolean) permissions.get("accountManagement");
            Boolean systemSettingsPermission = (Boolean) permissions.get("systemSettings");
            Boolean dataViewPermission = (Boolean) permissions.get("dataView");
            Boolean keyManagementPermission = (Boolean) permissions.get("keyManagement");
            Boolean rewardManagementPermission = (Boolean) permissions.get("rewardManagement");
            Boolean pointsShopPermission = (Boolean) permissions.get("pointsShop");
            Boolean winnersViewPermission = (Boolean) permissions.get("winnersView");
            Boolean statisticsViewPermission = (Boolean) permissions.get("statisticsView");

            accountsConfig.set("accounts." + username + ".permissions.punishment", punishmentPermission);
            accountsConfig.set("accounts." + username + ".permissions.accountManagement", accountManagementPermission);
            accountsConfig.set("accounts." + username + ".permissions.systemSettings", systemSettingsPermission);
            accountsConfig.set("accounts." + username + ".permissions.dataView", dataViewPermission);
            accountsConfig.set("accounts." + username + ".permissions.keyManagement", keyManagementPermission);
            accountsConfig.set("accounts." + username + ".permissions.rewardManagement", rewardManagementPermission);
            accountsConfig.set("accounts." + username + ".permissions.pointsShop", pointsShopPermission);
            accountsConfig.set("accounts." + username + ".permissions.winnersView", winnersViewPermission);
            accountsConfig.set("accounts." + username + ".permissions.statisticsView", statisticsViewPermission);

            // 记录分配的角色
            accountsConfig.set("accounts." + username + ".assignedRole", roleId);

            saveAccountsConfig();

            response.put("success", true);
            response.put("message", "角色分配成功");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 更新账号最后登录时间和IP地址
     */
    public void updateLastLogin(String username, String clientIP) {
        if (!accountsConfig.contains("accounts." + username)) {
            return;
        }

        long currentTime = System.currentTimeMillis();

        // 更新最后登录时间
        accountsConfig.set("accounts." + username + ".last_login", currentTime);

        // 记录登录IP历史
        String ipHistoryKey = "accounts." + username + ".login_ips";
        List<String> ipHistory = accountsConfig.getStringList(ipHistoryKey);
        if (ipHistory == null) {
            ipHistory = new ArrayList<>();
        }

        // 添加新的IP记录（格式：IP:时间戳）
        String ipRecord = clientIP + ":" + currentTime;
        ipHistory.add(ipRecord);

        // 只保留最近50次登录记录
        if (ipHistory.size() > 50) {
            ipHistory = ipHistory.subList(ipHistory.size() - 50, ipHistory.size());
        }

        accountsConfig.set(ipHistoryKey, ipHistory);

        // 更新当前登录IP
        accountsConfig.set("accounts." + username + ".current_ip", clientIP);

        saveAccountsConfig();
    }

    /**
     * 获取账号的IP登录历史
     */
    public Map<String, Object> getAccountIPInfo(String username) {
        Map<String, Object> ipInfo = new HashMap<>();

        if (!accountsConfig.contains("accounts." + username)) {
            return ipInfo;
        }

        // 获取IP历史记录
        List<String> ipHistory = accountsConfig.getStringList("accounts." + username + ".login_ips");
        if (ipHistory == null) {
            ipHistory = new ArrayList<>();
        }

        // 统计IP使用情况
        Map<String, Integer> ipCounts = new HashMap<>();
        Map<String, Long> ipLastSeen = new HashMap<>();

        for (String record : ipHistory) {
            String[] parts = record.split(":", 2);
            if (parts.length == 2) {
                String ip = parts[0];
                long timestamp = Long.parseLong(parts[1]);

                ipCounts.put(ip, ipCounts.getOrDefault(ip, 0) + 1);
                ipLastSeen.put(ip, Math.max(ipLastSeen.getOrDefault(ip, 0L), timestamp));
            }
        }

        // 获取当前活跃的IP（从会话中）
        Map<String, Object> sessionInfo = AdminLoginHandler.getUserSessionInfo(username);

        ipInfo.put("ipCounts", ipCounts);
        ipInfo.put("ipLastSeen", ipLastSeen);
        ipInfo.put("uniqueIPs", ipCounts.size());
        ipInfo.put("currentIP", accountsConfig.getString("accounts." + username + ".current_ip", "未知"));
        ipInfo.put("activeSessions", sessionInfo);

        return ipInfo;
    }

    /**
     * 处理获取IP信息请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetIPInfo(HttpExchange exchange, String username) throws IOException {
        JSONObject response = new JSONObject();

        if (username == null || username.isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else {
            Map<String, Object> ipInfo = getAccountIPInfo(username);
            response.put("success", true);
            response.put("ipInfo", ipInfo);
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理踢出IP请求
     */
    @SuppressWarnings("unchecked")
    private void handleKickIP(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        String ip = (String) data.get("ip");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (ip == null || ip.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "IP地址不能为空");
        } else {
            // 踢出指定IP的会话
            int kickedSessions = AdminLoginHandler.kickUserSessions(username, ip);

            response.put("success", true);
            response.put("message", "已踢出 " + kickedSessions + " 个会话");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理封禁IP请求
     */
    @SuppressWarnings("unchecked")
    private void handleBanIP(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String ip = (String) data.get("ip");

        if (ip == null || ip.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "IP地址不能为空");
        } else {
            // 添加IP到封禁列表
            addBannedIP(ip);

            // 踢出该IP的所有会话
            int kickedSessions = AdminLoginHandler.kickIPSessions(ip);

            response.put("success", true);
            response.put("message", "IP已封禁，踢出了 " + kickedSessions + " 个会话");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 添加IP到封禁列表
     */
    private void addBannedIP(String ip) {
        List<String> bannedIPs = accountsConfig.getStringList("banned_ips");
        if (bannedIPs == null) {
            bannedIPs = new ArrayList<>();
        }

        if (!bannedIPs.contains(ip)) {
            bannedIPs.add(ip);
            accountsConfig.set("banned_ips", bannedIPs);
            saveAccountsConfig();
        }
    }

    /**
     * 检查IP是否被封禁
     */
    public boolean isIPBanned(String ip) {
        List<String> bannedIPs = accountsConfig.getStringList("banned_ips");
        return bannedIPs != null && bannedIPs.contains(ip);
    }

    /**
     * 处理封禁账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleBanAccount(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");
        String reason = (String) data.get("reason");
        String customReason = (String) data.get("customReason");
        Long duration = (Long) data.get("duration");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (reason == null || reason.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "封禁原因不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else {
            // 检查是否是最后一个超级管理员
            String role = accountsConfig.getString("accounts." + username + ".role", "admin");
            if ("super_admin".equals(role)) {
                long superAdminCount = accountsConfig.getConfigurationSection("accounts").getKeys(false)
                        .stream()
                        .filter(user -> "super_admin".equals(accountsConfig.getString("accounts." + user + ".role"))
                                && accountsConfig.getBoolean("accounts." + user + ".enabled", true)
                                && !accountsConfig.contains("accounts." + user + ".ban_info"))
                        .count();

                if (superAdminCount <= 1) {
                    response.put("success", false);
                    response.put("message", "不能封禁最后一个启用的超级管理员账号");
                    sendResponse(exchange, 200, "application/json", response.toString());
                    return;
                }
            }

            // 计算封禁结束时间
            long banEndTime = -1; // -1表示永久封禁
            if (duration != null && duration > 0) {
                banEndTime = System.currentTimeMillis() + (duration * 60 * 60 * 1000); // 转换为毫秒
            }

            // 设置封禁信息
            accountsConfig.set("accounts." + username + ".ban_info.banned", true);
            accountsConfig.set("accounts." + username + ".ban_info.reason", reason);
            if (customReason != null && !customReason.trim().isEmpty()) {
                accountsConfig.set("accounts." + username + ".ban_info.custom_reason", customReason);
            }
            accountsConfig.set("accounts." + username + ".ban_info.ban_time", System.currentTimeMillis());
            accountsConfig.set("accounts." + username + ".ban_info.ban_end_time", banEndTime);
            accountsConfig.set("accounts." + username + ".ban_info.banned_by", getCurrentAdminUsername(exchange));

            // 禁用账号
            accountsConfig.set("accounts." + username + ".enabled", false);

            saveAccountsConfig();

            // 踢出该用户的所有会话
            int kickedSessions = AdminLoginHandler.kickUserSessions(username, null);

            String durationText = duration == null || duration == -1 ? "永久" : duration + "小时";
            response.put("success", true);
            response.put("message", "账号已封禁（" + durationText + "），踢出了 " + kickedSessions + " 个会话");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 处理解封账号请求
     */
    @SuppressWarnings("unchecked")
    private void handleUnbanAccount(HttpExchange exchange, JSONObject data) throws IOException {
        JSONObject response = new JSONObject();

        String username = (String) data.get("username");

        if (username == null || username.trim().isEmpty()) {
            response.put("success", false);
            response.put("message", "用户名不能为空");
        } else if (!accountsConfig.contains("accounts." + username)) {
            response.put("success", false);
            response.put("message", "账号不存在");
        } else if (!isAccountBanned(username)) {
            response.put("success", false);
            response.put("message", "该账号未被封禁");
        } else {
            // 清除封禁信息
            accountsConfig.set("accounts." + username + ".ban_info.banned", false);
            accountsConfig.set("accounts." + username + ".ban_info.unban_time", System.currentTimeMillis());
            accountsConfig.set("accounts." + username + ".ban_info.unbanned_by", getCurrentAdminUsername(exchange));

            // 启用账号
            accountsConfig.set("accounts." + username + ".enabled", true);

            saveAccountsConfig();

            response.put("success", true);
            response.put("message", "账号已解封");
        }

        sendResponse(exchange, 200, "application/json", response.toString());
    }

    /**
     * 获取当前管理员用户名
     */
    private String getCurrentAdminUsername(HttpExchange exchange) {
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null) {
            AdminLoginHandler loginHandler = new AdminLoginHandler(plugin, webServer);
            return loginHandler.getUsernameFromSession(sessionId);
        }
        return "unknown";
    }

    /**
     * 检查账号是否被封禁
     */
    public boolean isAccountBanned(String username) {
        if (!accountsConfig.contains("accounts." + username + ".ban_info.banned")) {
            return false;
        }

        boolean banned = accountsConfig.getBoolean("accounts." + username + ".ban_info.banned", false);
        if (!banned) {
            return false;
        }

        // 检查封禁是否已过期
        long banEndTime = accountsConfig.getLong("accounts." + username + ".ban_info.ban_end_time", -1);
        if (banEndTime > 0 && System.currentTimeMillis() > banEndTime) {
            // 封禁已过期，自动解封
            accountsConfig.set("accounts." + username + ".ban_info.banned", false);
            accountsConfig.set("accounts." + username + ".enabled", true);
            saveAccountsConfig();
            return false;
        }

        return true;
    }

    /**
     * 获取账号封禁信息
     */
    public Map<String, Object> getAccountBanInfo(String username) {
        Map<String, Object> banInfo = new HashMap<>();

        if (isAccountBanned(username)) {
            banInfo.put("banned", true);
            banInfo.put("reason", accountsConfig.getString("accounts." + username + ".ban_info.reason", "未知原因"));
            banInfo.put("custom_reason",
                    accountsConfig.getString("accounts." + username + ".ban_info.custom_reason", ""));
            banInfo.put("ban_time", accountsConfig.getLong("accounts." + username + ".ban_info.ban_time", 0));
            banInfo.put("ban_end_time", accountsConfig.getLong("accounts." + username + ".ban_info.ban_end_time", -1));
            banInfo.put("banned_by",
                    accountsConfig.getString("accounts." + username + ".ban_info.banned_by", "unknown"));
        } else {
            banInfo.put("banned", false);
        }

        return banInfo;
    }
}
