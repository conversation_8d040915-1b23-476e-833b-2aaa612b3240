<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>玩家搜索功能测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2d3748;
            margin-bottom: 30px;
        }
        
        .search-section {
            margin-bottom: 30px;
        }
        
        .search-input-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .search-input {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e9ecef;
            border-radius: 25px;
            font-size: 1em;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .player-avatar-preview {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            background: #f8f9fa;
            z-index: 10;
        }
        
        .test-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
        }
        
        .test-btn {
            padding: 10px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }
        
        .test-btn:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .info {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        
        .demo-links {
            margin-top: 30px;
            text-align: center;
        }
        
        .demo-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #764ba2;
            color: white;
            text-decoration: none;
            border-radius: 15px;
            transition: all 0.3s ease;
        }
        
        .demo-links a:hover {
            background: #6a4190;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 玩家搜索功能测试</h1>
        
        <div class="info">
            <strong>🎮 Next-LiteBans 风格功能：</strong>
            <ul>
                <li>✨ 在搜索框中输入玩家名，会实时显示玩家皮肤</li>
                <li>🚀 按回车键在当前页面添加 <code>?player=PlayerName</code> 参数</li>
                <li>🖼️ 显示半身皮肤和玩家信息（完全复制 next-litebans 布局）</li>
                <li>📊 显示可点击的处罚统计徽章</li>
                <li>🎯 点击徽章跳转到对应类型页面（如 <code>/punishments/ban?player=PlayerName</code>）</li>
                <li>📋 保留顶部导航和页面结构，只更新表格内容</li>
                <li>📱 完全响应式设计，支持移动端</li>
                <li>🌙 支持深色模式切换</li>
            </ul>
        </div>

        <div class="info" style="background: #fff3cd; border-left-color: #ffc107;">
            <strong>🔥 皮肤 API 说明：</strong>
            <ul>
                <li><strong>半身皮肤：</strong> https://visage.surgeplay.com/bust/512/{playerName}</li>
                <li><strong>备用皮肤：</strong> https://mc-heads.net/body/{playerName}/192</li>
                <li><strong>头像预览：</strong> https://mc-heads.net/avatar/{playerName}/32</li>
            </ul>
        </div>
        
        <div class="search-section">
            <h3>搜索测试</h3>
            <div class="search-input-container">
                <input type="text" id="playerSearchInput" placeholder="输入玩家名称（如：Moonlads）..." 
                       class="search-input" oninput="updatePlayerAvatar(this.value)" onkeydown="handleSearchEnter(event)">
                <img id="playerAvatarPreview" src="" alt="玩家头像" class="player-avatar-preview" style="display: none;">
            </div>
            
            <div class="test-buttons">
                <button class="test-btn" onclick="testPlayer('Moonlads')">测试 Moonlads</button>
                <button class="test-btn" onclick="testPlayer('Steve')">测试 Steve</button>
                <button class="test-btn" onclick="testPlayer('Alex')">测试 Alex</button>
                <button class="test-btn" onclick="testPlayer('Notch')">测试 Notch</button>
            </div>
        </div>
        
        <div class="demo-links">
            <h3>🔗 快速测试链接（Next-LiteBans 风格）</h3>
            <a href="http://localhost:8080/punishments?player=Moonlads" target="_blank">🎮 Moonlads 玩家记录</a>
            <a href="http://localhost:8080/punishments?player=Steve" target="_blank">🎮 Steve 玩家记录</a>
            <a href="http://localhost:8080/punishments" target="_blank">📋 所有处罚记录</a>
            <a href="http://localhost:8080/punishments/ban?player=Moonlads" target="_blank">🚫 Moonlads 封禁记录</a>
            <a href="http://localhost:8080/punishments/mute?player=Moonlads" target="_blank">� Moonlads 禁言记录</a>
            <a href="http://localhost:8080/punishments/warn?player=Moonlads" target="_blank">⚠️ Moonlads 警告记录</a>
        </div>

        <div class="info" style="background: #d1ecf1; border-left-color: #17a2b8;">
            <h4>🎯 实现的 Next-LiteBans 功能</h4>
            <ul>
                <li>✅ 完全复制的布局结构：<code>flex h-full flex-col items-center gap-4</code></li>
                <li>✅ 半身皮肤显示：<code>https://visage.surgeplay.com/bust/512/{playerName}</code></li>
                <li>✅ 响应式设计：<code>space-y-2 md:flex md:space-x-4</code></li>
                <li>✅ 玩家标题样式：<code>text-4xl font-bold leading-tight tracking-tighter</code></li>
                <li>✅ 徽章组件：<code>Badge variant="secondary" className="px-1"</code></li>
                <li>✅ 表格容器：<code>w-full lg:w-[1024px]</code></li>
            </ul>
        </div>

        <div class="demo-section" style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 15px;">
            <h3>🖼️ 皮肤预览示例</h3>
            <div style="display: flex; gap: 20px; justify-content: center; flex-wrap: wrap; margin-top: 15px;">
                <div style="text-align: center;">
                    <img src="https://visage.surgeplay.com/bust/256/Moonlads" alt="Moonlads" style="width: 128px; height: 128px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <p style="margin-top: 8px; font-weight: 600;">Moonlads</p>
                </div>
                <div style="text-align: center;">
                    <img src="https://visage.surgeplay.com/bust/256/Steve" alt="Steve" style="width: 128px; height: 128px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <p style="margin-top: 8px; font-weight: 600;">Steve</p>
                </div>
                <div style="text-align: center;">
                    <img src="https://visage.surgeplay.com/bust/256/Alex" alt="Alex" style="width: 128px; height: 128px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
                    <p style="margin-top: 8px; font-weight: 600;">Alex</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 更新玩家头像预览
        function updatePlayerAvatar(playerName) {
            const avatarPreview = document.getElementById('playerAvatarPreview');
            
            if (!playerName || playerName.trim().length < 3) {
                avatarPreview.style.display = 'none';
                return;
            }
            
            // 清理玩家名（移除特殊字符，只保留字母数字和下划线）
            const cleanName = playerName.trim().replace(/[^a-zA-Z0-9_]/g, '');
            
            if (cleanName.length >= 3 && cleanName.length <= 16) {
                avatarPreview.src = `https://mc-heads.net/avatar/${cleanName}/32`;
                avatarPreview.style.display = 'block';
                
                // 处理头像加载失败
                avatarPreview.onerror = function() {
                    this.style.display = 'none';
                };
            } else {
                avatarPreview.style.display = 'none';
            }
        }

        // 处理搜索回车事件
        function handleSearchEnter(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                const playerName = event.target.value.trim();
                
                if (playerName) {
                    // 检查是否是有效的玩家名
                    const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');
                    
                    if (cleanName.length >= 3 && cleanName.length <= 16) {
                        // 跳转到玩家记录页面（Next-LiteBans 风格）
                        window.location.href = `http://localhost:8080/punishments?player=${cleanName}`;
                    } else {
                        alert('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）');
                    }
                }
            }
        }

        // 测试特定玩家
        function testPlayer(playerName) {
            const input = document.getElementById('playerSearchInput');
            input.value = playerName;
            updatePlayerAvatar(playerName);
        }
    </script>
</body>
</html>
