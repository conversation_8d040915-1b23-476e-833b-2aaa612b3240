# 卡密格式设置
key-format:
  # 字母数量（大写英文字母）
  letters: 4
  # 数字数量
  numbers: 6

# 卡密文件保存文件夹名称
keys-folder: "keys"

# 默认提示信息
default-message: "请复制这个卡密并在网站上使用！"

# 卡密物品设置
key-item:
  # 物品类型 (可选: PAPER, BOOK, MAP, NAME_TAG 等)
  material: "PAPER"
  # 物品名称
  name: "§6§l卡密兑换券"
  # 物品说明
  lore:
    - "§7这是一张神奇的兑换券"
    - "§7右键点击可以获得卡密"
    - "§c注意：仅可使用一次！"
  # 是否发光
  glow: true

# 兑换券失效设置
voucher-expiry:
  # 兑换券失效时间（秒），-1表示永不失效
  expire-seconds: 86400
  # 检查失效兑换券的间隔（秒）
  check-interval-seconds: 3600

# 内置Web服务器设置
web-server:
  # 是否启用内置Web服务器
  enabled: true
  # 服务器端口
  port: 8080
  # 服务器绑定地址（0.0.0.0表示所有网卡）
  host: "0.0.0.0"
  # 管理员密钥（用于访问管理员界面）
  admin-key: "MCTV_ADMIN_2024_SECURE"
  # 网站标题
  title: "MCTV卡密系统"
  # 是否启用调试模式
  debug: false
  # 登录页面背景图片URL（留空使用默认渐变背景）
  login-background-image: ""
  # 登录页面背景透明度（0-80，数值越小背景图越明显）
  login-background-opacity: 30
  # 管理员控制台主题设置
  admin-theme:
    # 默认主题模式 (light/dark/auto)
    default-mode: "light"
    # 是否启用夜间模式
    dark-mode-enabled: true
    # 是否允许用户切换主题
    allow-theme-switch: true

# 管理员账号设置
admin:
  # 管理员用户名
  username: "admin"
  # 管理员密码
  password: "admin123"

# 网站同步设置（保持兼容性）
website:
  # 是否启用网站同步（现在指向内置服务器）
  enabled: true
  # API地址 - 现在指向内置服务器
  url: "http://localhost:8080/api"
  # API密钥（确保与网站配置相同）
  api-key: "MCTV_KEY_2024_SECURE"
  # 自动同步间隔（分钟）
  sync-interval: 5
  # 是否在生成卡密时立即同步
  sync-on-generate: true
  # 奖励检查间隔（秒）
  rewards-check-interval: 30
  # 抽奖页面背景图片URL（留空使用默认渐变背景）
  background-image: ""
  # 排行榜页面背景图片URL（留空使用默认渐变背景）
  leaderboard-background-image: ""
  # 积分商店页面背景图片URL（留空使用默认渐变背景）
  shop-background-image: ""
  # 积分商店页面背景透明度（0-80，数值越小背景图越明显）
  shop-background-opacity: 30
  # 抽奖页面背景透明度（0-80，数值越小背景图越明显）
  background-opacity: 30
  # 排行榜页面背景透明度（0-80，数值越小背景图越明显）
  leaderboard-background-opacity: 30
  # 处罚记录页面背景图片URL（留空使用默认渐变背景）
  punishment-background-image: ""
  # 处罚记录页面背景透明度（0-80，数值越小背景图越明显）
  punishment-background-opacity: 30
  # 首页设置
  homepage:
    # 首页主标题
    title: "🎮 游戏娱乐中心"
    # 首页副标题
    subtitle: "欢迎来到游戏娱乐中心"
    # 首页描述
    description: "🎯 参与精彩抽奖活动，赢取丰厚奖品\n🏆 查看排行榜，与其他玩家一较高下\n🛒 使用积分购买心仪的游戏道具"
    # 首页背景图片URL（留空使用默认渐变背景）
    background-image: ""
    # 首页背景透明度（0-80，数值越小背景图越明显）
    background-opacity: 30
    # 是否显示抽奖系统卡片
    show-lottery-card: true
    # 是否显示排行榜卡片
    show-leaderboard-card: true
    # 是否显示积分商店卡片
    show-shop-card: true
    # 是否显示系统状态区域
    show-system-status: true
  # 开场动画设置
  opening-animation:
    # 是否启用开场动画
    enabled: true
    # 动画持续时间（毫秒）
    duration: 3000
    # 动画样式（classic/modern/custom）
    style: "classic"
    # 自定义动画文件URL（当style为custom时使用）
    custom-url: ""
  # 音效设置
  sound-effects:
    # 是否启用音效
    enabled: true
    # 开场音效URL
    opening-sound: ""
    # 中奖音效URL
    winning-sound: ""
    # 音量（0-100）
    volume: 50

# 抽奖奖励配置文件路径
# 奖励配置已移动到单独的 rewards.yml 文件中，方便管理
lottery-rewards-file: "rewards.yml"

# 待处理奖励配置
pending-rewards:
  # 待处理奖励过期天数，超过这个天数将被视为放弃
  expire-days: 15
  # 检查过期奖励的间隔（秒）
  check-interval: 300

# 待处理购买配置
pending-purchases:
  # 待处理购买过期天数，超过这个天数将被清除
  expire-days: 15
  # 检查过期购买的间隔（秒）
  check-interval: 300

# 绑定系统设置
binding:
  # 绑定码有效期（分钟）
  code-expiry: 5
  # 绑定码长度
  code-length: 6
  # 解绑时间限制
  unbind-cooldown:
    # 时间数值
    value: 24
    # 时间单位：hours（小时）或 seconds（秒）
    unit: "hours"

# LiteBans封禁系统集成设置
litebans:
  # 是否启用封禁记录功能
  enabled: true
  # LiteBans数据库连接设置
  database:
    # 数据库主机地址
    host: "localhost"
    # 数据库端口
    port: 3306
    # 数据库名称
    database: "litebans"
    # 数据库用户名
    username: "root"
    # 数据库密码
    password: "password"
    # 连接池设置
    pool:
      # 最大连接数
      maximum-pool-size: 10
      # 最小空闲连接数
      minimum-idle: 2
      # 连接超时时间（毫秒）
      connection-timeout: 30000
      # 空闲超时时间（毫秒）
      idle-timeout: 600000
      # 最大生命周期（毫秒）
      max-lifetime: 1800000
  # 处罚记录显示设置
  display:
    # 每页显示的记录数
    records-per-page: 10
    # 是否显示处罚记录卡片
    show-punishment-card: true
    # 处罚记录卡片标题
    punishment-card-title: "📋 处罚记录"
    # 处罚记录卡片描述
    punishment-card-description: "查看服务器所有处罚历史记录"
    # 是否显示玩家头像
    show-player-avatars: true
    # 头像服务提供商 (minotar/crafatar/mc-heads)
    avatar-provider: "minotar"

# Vault 经济系统集成配置
vault:
  exchange:
    # 积分兑换金钱
    points-to-money:
      enabled: true
      rate: 0.1  # 1积分 = 0.1金钱
      min-points: 100
      max-points: 10000
    # 金钱兑换积分
    money-to-points:
      enabled: true
      rate: 10.0  # 1金钱 = 10积分
      min-money: 10.0
      max-money: 1000.0

# 补签卡价格配置
makeup_cards:
  gold:
    enabled: true
    price: 100
    currency_name: "金币"
    description: "使用游戏内金币购买"
    balance_variable: "%vault_eco_balance%"  # PlaceholderAPI变量
    deduct_command: "eco take {player} {amount}"  # 扣除指令
  points:
    enabled: true
    price: 50
    currency_name: "点券"
    description: "使用点券购买"
    balance_variable: "%playerpoints_points%"  # PlaceholderAPI变量
    deduct_command: "pp take {player} {amount}"  # 扣除指令
  score:
    enabled: true
    price: 200
    currency_name: "积分"
    description: "使用积分商店积分购买"
    balance_variable: "internal"  # 使用积分商店的积分系统
    deduct_command: "acepoints remove {player} {amount}"  # 使用积分系统的扣除指令
  custom:
    enabled: false
    price: 150
    currency_name: "自定义货币"
    description: "使用自定义货币购买"
    balance_variable: "%custom_balance%"  # 自定义PlaceholderAPI变量
    deduct_command: "customcurrency take {player} {amount}"  # 自定义扣除指令