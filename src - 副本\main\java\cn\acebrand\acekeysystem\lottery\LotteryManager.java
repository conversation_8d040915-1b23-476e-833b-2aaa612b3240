package cn.acebrand.acekeysystem.lottery;

import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.File;
import java.io.IOException;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 抽奖管理器
 */
public class LotteryManager {

    private final AceKeySystem plugin;
    private final Random random;
    private List<LotteryReward> rewards;
    private int totalWeight;
    private boolean placeholderAPIEnabled;
    private FileConfiguration rewardsConfig;
    private File rewardsFile;

    public LotteryManager(AceKeySystem plugin) {
        this.plugin = plugin;
        this.random = new Random();
        this.placeholderAPIEnabled = Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null;
        if (placeholderAPIEnabled) {
            plugin.getLogger().info("检测到PlaceholderAPI，已启用变量支持");
        }
        initializeRewardsFile();
        loadRewards();
    }

    /**
     * 初始化奖励配置文件
     */
    private void initializeRewardsFile() {
        String fileName = plugin.getConfig().getString("lottery-rewards-file", "rewards.yml");
        rewardsFile = new File(plugin.getDataFolder(), fileName);

        // 如果文件不存在，从资源中复制
        if (!rewardsFile.exists()) {
            plugin.saveResource(fileName, false);
            plugin.getLogger().info("已创建奖励配置文件: " + fileName);
        }

        rewardsConfig = YamlConfiguration.loadConfiguration(rewardsFile);
    }

    /**
     * 从配置文件加载奖励
     */
    public void loadRewards() {
        rewards = new ArrayList<>();
        totalWeight = 0;

        ConfigurationSection rewardsSection = rewardsConfig.getConfigurationSection("rewards");
        if (rewardsSection == null) {
            plugin.getLogger().warning("未找到抽奖奖励配置，使用默认奖励");
            loadDefaultRewards();
            return;
        }

        for (String rewardId : rewardsSection.getKeys(false)) {
            ConfigurationSection rewardSection = rewardsSection.getConfigurationSection(rewardId);
            if (rewardSection == null)
                continue;

            String name = rewardSection.getString("name", rewardId);
            String description = rewardSection.getString("description", "");
            int weight = rewardSection.getInt("weight", 10);
            double probability = rewardSection.getDouble("probability", 10.0);
            List<String> commands = rewardSection.getStringList("commands");

            LotteryReward reward = new LotteryReward(rewardId, name, description, weight, probability, commands);
            rewards.add(reward);
            totalWeight += weight;

            plugin.getLogger().info("加载抽奖奖励: " + name + " (权重: " + weight + ", 概率: " + probability + "%)");
        }

        plugin.getLogger().info("共加载 " + rewards.size() + " 个抽奖奖励，总权重: " + totalWeight);
    }

    /**
     * 加载默认奖励
     */
    private void loadDefaultRewards() {
        List<String> diamondCommands = new ArrayList<>();
        diamondCommands.add("give {player} diamond 10");
        rewards.add(new LotteryReward("diamond", "钻石礼包", "获得10个珍贵的钻石", 20, 20.0, diamondCommands));

        List<String> goldCommands = new ArrayList<>();
        goldCommands.add("give {player} gold_ingot 20");
        rewards.add(new LotteryReward("gold", "金锭大礼包", "获得20个金锭，财富满满", 25, 25.0, goldCommands));

        List<String> expCommands = new ArrayList<>();
        expCommands.add("give {player} experience_bottle 5");
        rewards.add(new LotteryReward("exp", "经验宝瓶", "获得5瓶经验，快速升级", 30, 30.0, expCommands));

        List<String> emeraldCommands = new ArrayList<>();
        emeraldCommands.add("give {player} emerald 5");
        rewards.add(new LotteryReward("emerald", "绿宝石珍藏", "获得5个稀有绿宝石", 15, 15.0, emeraldCommands));

        // 积分奖励
        List<String> points10Commands = new ArrayList<>();
        points10Commands.add("acepoints add {player} 10");
        rewards.add(new LotteryReward("points_10", "🪙 积分奖励", "获得10积分", 20, 20.0, points10Commands));

        List<String> points50Commands = new ArrayList<>();
        points50Commands.add("acepoints add {player} 50");
        rewards.add(new LotteryReward("points_50", "💰 积分大奖", "获得50积分", 10, 10.0, points50Commands));

        List<String> points100Commands = new ArrayList<>();
        points100Commands.add("acepoints add {player} 100");
        rewards.add(new LotteryReward("points_100", "💎 积分巨奖", "获得100积分", 5, 5.0, points100Commands));

        List<String> nothingCommands = new ArrayList<>();
        rewards.add(new LotteryReward("nothing", "谢谢参与", "很遗憾，这次没有获得奖励", 5, 5.0, nothingCommands));

        totalWeight = 135;
    }

    /**
     * 进行抽奖
     */
    public LotteryReward drawReward() {
        if (rewards.isEmpty()) {
            plugin.getLogger().warning("没有可用的抽奖奖励！");
            return null;
        }

        int randomValue = random.nextInt(totalWeight);
        int currentWeight = 0;

        for (LotteryReward reward : rewards) {
            currentWeight += reward.getWeight();
            if (randomValue < currentWeight) {
                return reward;
            }
        }

        // 如果没有找到，返回最后一个奖励
        return rewards.get(rewards.size() - 1);
    }

    /**
     * 给玩家发放奖励
     */
    public boolean giveRewardToPlayer(String playerName, LotteryReward reward) {
        if (reward == null || !reward.hasCommands()) {
            return true; // 没有命令的奖励（如"谢谢参与"）也算成功
        }

        Player player = Bukkit.getPlayer(playerName);
        if (player == null) {
            // 玩家不在线，将奖励添加到待处理奖励中
            plugin.getLogger().info("玩家 " + playerName + " 不在线，将奖励添加到待处理奖励: " + reward.getName());
            plugin.addPendingReward(playerName, reward.getName(), reward.getCommands());
            return true; // 返回true表示奖励已经被处理（保存到待处理奖励）
        }

        try {
            for (String command : reward.getCommands()) {
                String processedCommand = processPlaceholders(command, player);

                // 在主线程执行命令
                Bukkit.getScheduler().runTask(plugin, () -> {
                    boolean success = Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
                    if (!success) {
                        plugin.getLogger().warning("执行奖励命令失败: " + processedCommand);
                    }
                });
            }

            plugin.getLogger().info("成功给玩家 " + playerName + " 发放奖励: " + reward.getName());
            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("给玩家 " + playerName + " 发放奖励时出错: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取所有奖励名称（用于显示）
     */
    public List<String> getRewardNames() {
        List<String> names = new ArrayList<>();
        for (LotteryReward reward : rewards) {
            names.add(reward.getName());
        }
        return names;
    }

    /**
     * 处理变量替换
     */
    private String processPlaceholders(String text, Player player) {
        if (text == null)
            return "";

        // 基本变量替换
        String processed = text.replace("{player}", player.getName())
                .replace("{player_name}", player.getName())
                .replace("{player_uuid}", player.getUniqueId().toString())
                .replace("{player_displayname}", player.getDisplayName())
                .replace("{world}", player.getWorld().getName())
                .replace("{x}", String.valueOf(player.getLocation().getBlockX()))
                .replace("{y}", String.valueOf(player.getLocation().getBlockY()))
                .replace("{z}", String.valueOf(player.getLocation().getBlockZ()));

        // 如果启用了PlaceholderAPI，使用反射调用
        if (placeholderAPIEnabled) {
            try {
                Class<?> placeholderAPIClass = Class.forName("me.clip.placeholderapi.PlaceholderAPI");
                java.lang.reflect.Method setPlaceholdersMethod = placeholderAPIClass.getMethod("setPlaceholders",
                        Player.class, String.class);
                processed = (String) setPlaceholdersMethod.invoke(null, player, processed);
            } catch (Exception e) {
                plugin.getLogger().warning("处理PlaceholderAPI变量时出错: " + e.getMessage());
            }
        }

        return processed;
    }

    /**
     * 保存奖励配置
     */
    public void saveRewardsConfig() {
        try {
            rewardsConfig.save(rewardsFile);
            plugin.getLogger().info("奖励配置已保存");
        } catch (IOException e) {
            plugin.getLogger().severe("保存奖励配置失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 重新加载奖励配置
     */
    public void reload() {
        rewardsConfig = YamlConfiguration.loadConfiguration(rewardsFile);
        loadRewards();
        plugin.getLogger().info("奖励配置已重新加载");
    }

    /**
     * 获取奖励配置
     */
    public FileConfiguration getRewardsConfig() {
        return rewardsConfig;
    }

    /**
     * 获取所有奖励对象
     */
    public List<LotteryReward> getAllRewards() {
        return new ArrayList<>(rewards);
    }
}
