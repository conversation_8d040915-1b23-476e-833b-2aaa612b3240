package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.lottery.LotteryReward;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.UUID;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * Web API处理器
 * 处理所有API请求，包括插件同步和Web界面调用
 */
public class WebAPIHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;
    private final Random random = new Random();

    public WebAPIHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        if (webServer.isDebug()) {
            plugin.getLogger().info("API请求: " + method + " " + exchange.getRequestURI().getPath());
        }

        try {
            if ("POST".equals(method)) {
                handlePostRequest(exchange);
            } else if ("GET".equals(method)) {
                handleGetRequest(exchange);
            } else {
                sendErrorResponse(exchange, 405, "方法不允许");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理API请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendErrorResponse(exchange, 500, "服务器内部错误: " + e.getMessage());
        }
    }

    /**
     * 处理POST请求
     */
    private void handlePostRequest(HttpExchange exchange) throws Exception {
        // 检查是否为文件上传请求
        String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
        if (contentType != null && contentType.startsWith("multipart/form-data")) {
            handleFileUploadRequest(exchange);
            return;
        }

        // 读取请求体
        String requestBody = readRequestBody(exchange);

        if (requestBody.isEmpty()) {
            sendErrorResponse(exchange, 400, "请求体为空");
            return;
        }

        // 解析JSON - 每次创建新的解析器实例以避免线程安全问题
        JSONObject request;
        try {
            JSONParser parser = new JSONParser();
            Object parsed = parser.parse(requestBody);
            if (!(parsed instanceof JSONObject)) {
                sendErrorResponse(exchange, 400, "请求体必须是JSON对象");
                return;
            }
            request = (JSONObject) parsed;
        } catch (Exception e) {
            plugin.getLogger().warning("JSON解析失败: " + e.getMessage() + ", 请求体: " + requestBody);
            sendErrorResponse(exchange, 400, "JSON格式错误: " + e.getMessage());
            return;
        }

        String action = (String) request.get("action");
        String apiKey = (String) request.get("api_key");

        if (action == null || action.isEmpty()) {
            sendErrorResponse(exchange, 400, "action参数不能为空");
            return;
        }

        // 验证API密钥（公开接口除外）
        if (!isPublicAction(action) && !isValidApiKey(apiKey)) {
            sendErrorResponse(exchange, 401, "API密钥无效");
            return;
        }

        // 根据action处理请求
        JSONObject response = new JSONObject();

        switch (action) {
            case "sync_keys":
                handleSyncKeys(request, response);
                break;
            case "get_rewards":
                handleGetRewards(request, response);
                break;
            case "mark_reward_completed":
                handleMarkRewardCompleted(request, response);
                break;
            case "use_key":
                handleUseKey(request, response);
                break;
            case "get_lottery_info":
                handleGetLotteryInfo(request, response);
                break;
            case "participate_lottery":
                handleParticipateLottery(request, response);
                break;
            case "claim_reward":
                handleClaimReward(request, response);
                break;
            case "generate_keys":
                handleGenerateKeys(request, response);
                break;
            case "get_all_keys":
                handleGetAllKeys(request, response);
                break;
            case "clear_keys":
                handleClearKeys(request, response);
                break;
            case "reload_config":
                handleReloadConfig(request, response);
                break;
            case "get_lottery_rewards":
                handleGetLotteryRewards(request, response);
                break;
            case "save_lottery_reward":
                handleSaveLotteryReward(request, response);
                break;
            case "create_reward":
                handleCreateReward(request, response);
                break;
            case "update_reward":
                handleUpdateReward(request, response);
                break;
            case "delete_reward":
                handleDeleteReward(request, response);
                break;
            case "delete_lottery_reward":
                handleDeleteReward(request, response);
                break;
            case "batch_delete_rewards":
                handleBatchDeleteRewards(request, response);
                break;
            case "save_reward":
                handleSaveReward(request, response);
                break;
            case "delete_keys":
                handleDeleteKeys(request, response);
                break;
            case "get_winners":
                handleGetWinners(request, response);
                break;
            case "clear_winners":
                handleClearWinners(request, response);
                break;
            case "get_logs":
                handleGetLogs(request, response);
                break;
            case "clear_logs":
                handleClearLogs(request, response);
                break;
            case "toggle_web_logging":
                handleToggleWebLogging(request, response);
                break;
            case "toggle_console_logging":
                handleToggleConsoleLogging(request, response);
                break;
            case "save_logs_to_server":
                handleSaveLogsToServer(request, response);
                break;
            case "get_statistics":
                handleGetStatistics(request, response);
                break;
            case "search_player":
                handleSearchPlayer(request, response);
                break;
            case "get_leaderboard":
                handleGetLeaderboard(request, response);
                break;
            case "get_public_leaderboard":
                handleGetPublicLeaderboard(request, response);
                break;
            case "save_homepage_settings":
                handleSaveHomepageSettings(request, response);
                break;
            case "save_background_settings":
                handleSaveBackgroundSettings(request, response);
                break;
            case "save_interface_settings":
                handleSaveInterfaceSettings(request, response);
                break;
            case "save_advanced_settings":
                handleSaveAdvancedSettings(request, response);
                break;
            case "save_shop_settings":
                handleSaveShopSettings(request, response);
                break;
            case "save_admin_theme_settings":
                handleSaveAdminThemeSettings(request, response);
                break;
            case "save_punishment_settings":
                handleSavePunishmentSettings(request, response);
                break;
            case "upload_background_image":
                handleUploadBackgroundImage(exchange, response);
                break;
            case "upload_login_logo":
                handleUploadLoginLogo(exchange, response);
                break;
            case "get_user_points":
                handleGetUserPoints(request, response);
                break;
            case "get_shop_items":
                handleGetShopItems(request, response);
                break;
            case "purchase_item":
                handlePurchaseItem(request, response);
                break;
            case "get_admin_shop_items":
                handleGetAdminShopItems(request, response);
                break;
            case "save_shop_item":
                handleSaveShopItem(request, response);
                break;
            case "delete_shop_item":
                handleDeleteShopItem(request, response);
                break;
            case "toggle_shop_item_status":
                handleToggleShopItemStatus(request, response);
                break;
            case "batch_delete_shop_items":
                handleBatchDeleteShopItems(request, response);
                break;
            case "batch_update_shop_status":
                handleBatchUpdateShopStatus(request, response);
                break;
            case "get_player_purchase_info":
                handleGetPlayerPurchaseInfo(request, response);
                break;
            case "generate_bind_code":
                handleGenerateBindCode(request, response);
                break;
            case "verify_bind_code":
                handleVerifyBindCode(request, response);
                break;
            case "get_bind_status":
                handleGetBindStatus(request, response);
                break;
            case "unbind_account":
                handleUnbindAccount(request, response);
                break;
            case "verify_binding":
                handleVerifyBinding(request, response);
                break;
            case "get_dashboard_stats":
                handleGetDashboardStats(request, response);
                break;
            case "get_ban_records":
                handleGetBanRecords(request, response);
                break;
            case "get_ban_statistics":
                handleGetBanStatistics(request, response);
                break;
            case "search_ban_records":
                handleSearchBanRecords(request, response);
                break;
            default:
                sendErrorResponse(exchange, 400, "未知的action: " + action);
                return;
        }

        sendJsonResponse(exchange, 200, response);
    }

    /**
     * 处理文件上传请求
     */
    private void handleFileUploadRequest(HttpExchange exchange) throws Exception {
        String query = exchange.getRequestURI().getQuery();
        String action = null;

        if (query != null) {
            String[] params = query.split("&");
            for (String param : params) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 2 && "action".equals(keyValue[0])) {
                    action = keyValue[1];
                    break;
                }
            }
        }

        JSONObject response = new JSONObject();

        if ("upload_background_image".equals(action)) {
            handleUploadBackgroundImage(exchange, response);
        } else if ("upload_login_logo".equals(action)) {
            handleUploadLoginLogo(exchange, response);
        } else if ("upload_sound_file".equals(action)) {
            handleUploadSoundFile(exchange, response);
        } else if ("upload_animation_file".equals(action)) {
            handleUploadAnimationFile(exchange, response);
        } else if ("upload_logo".equals(action)) {
            handleUploadLogo(exchange, response);
        } else {
            response.put("success", false);
            response.put("message", "未知的文件上传操作: " + action);
        }

        sendJsonResponse(exchange, 200, response);
    }

    /**
     * 处理GET请求
     */
    private void handleGetRequest(HttpExchange exchange) throws Exception {
        String path = exchange.getRequestURI().getPath();
        String query = exchange.getRequestURI().getQuery();

        JSONObject response = new JSONObject();

        if (path.endsWith("/status")) {
            handleGetStatus(response);
        } else if (path.endsWith("/keys")) {
            handleGetKeys(response);
        } else {
            sendErrorResponse(exchange, 404, "API端点未找到");
            return;
        }

        sendJsonResponse(exchange, 200, response);
    }

    /**
     * 处理卡密同步
     */
    private void handleSyncKeys(JSONObject request, JSONObject response) {
        try {
            JSONArray keys = (JSONArray) request.get("keys");

            if (keys != null) {
                // 这里可以存储卡密到数据库或文件
                // 目前只是记录日志
                plugin.getLogger().info("收到卡密同步请求，卡密数量: " + keys.size());
            }

            response.put("success", true);
            response.put("message", "卡密同步成功");
            response.put("synced_count", keys != null ? keys.size() : 0);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "同步失败: " + e.getMessage());
        }
    }

    /**
     * 处理获取奖励
     */
    private void handleGetRewards(JSONObject request, JSONObject response) {
        try {
            JSONArray rewards = new JSONArray();

            // 从LotteryManager获取所有奖励
            List<LotteryReward> allRewards = plugin.getLotteryManager().getAllRewards();

            for (LotteryReward reward : allRewards) {
                JSONObject rewardObj = new JSONObject();
                rewardObj.put("id", reward.getId());
                rewardObj.put("name", reward.getName());
                rewardObj.put("description", reward.getDescription());
                rewardObj.put("probability", reward.getProbability());
                rewardObj.put("weight", reward.getWeight());

                JSONArray commandsArray = new JSONArray();
                if (reward.getCommands() != null) {
                    for (String command : reward.getCommands()) {
                        commandsArray.add(command);
                    }
                }
                rewardObj.put("commands", commandsArray);

                rewards.add(rewardObj);
            }

            response.put("success", true);
            response.put("rewards", rewards);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取奖励失败: " + e.getMessage());
        }
    }

    /**
     * 处理标记奖励完成
     */
    private void handleMarkRewardCompleted(JSONObject request, JSONObject response) {
        try {
            String rewardId = (String) request.get("reward_id");

            if (rewardId == null || rewardId.isEmpty()) {
                response.put("success", false);
                response.put("message", "奖励ID不能为空");
                return;
            }

            // 这里应该标记奖励为已完成
            plugin.getLogger().info("标记奖励完成: " + rewardId);

            response.put("success", true);
            response.put("message", "奖励已标记为完成");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "标记奖励完成失败: " + e.getMessage());
        }
    }

    /**
     * 处理使用卡密
     */
    private void handleUseKey(JSONObject request, JSONObject response) {
        try {
            String key = (String) request.get("key");
            String username = (String) request.get("username");

            if (key == null || key.isEmpty()) {
                response.put("success", false);
                response.put("message", "卡密不能为空");
                return;
            }

            // 检查卡密是否存在
            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            if (keysSection == null || !keysSection.contains(key)) {
                response.put("success", false);
                response.put("message", "卡密不存在或已被使用");
                return;
            }

            // 移除卡密
            keysSection.set(key, null);
            plugin.saveKeysConfig();

            // 这里可以添加奖励逻辑
            response.put("success", true);
            response.put("message", "卡密使用成功！");
            response.put("reward", "感谢使用卡密系统！");

            plugin.getLogger().info("用户 " + username + " 使用了卡密: " + key);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "使用卡密失败: " + e.getMessage());
        }
    }

    /**
     * 处理获取抽奖信息
     */
    private void handleGetLotteryInfo(JSONObject request, JSONObject response) {
        try {
            JSONObject lotteryInfo = new JSONObject();
            lotteryInfo.put("title", "每日抽奖");
            lotteryInfo.put("description", "每天可以免费抽奖一次！");
            lotteryInfo.put("available", true);

            JSONArray prizes = new JSONArray();
            prizes.add("钻石 x10");
            prizes.add("金锭 x20");
            prizes.add("经验瓶 x5");
            prizes.add("谢谢参与");

            lotteryInfo.put("prizes", prizes);

            response.put("success", true);
            response.put("data", lotteryInfo);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取抽奖信息失败: " + e.getMessage());
        }
    }

    /**
     * 处理参与抽奖
     */
    private void handleParticipateLottery(JSONObject request, JSONObject response) {
        try {
            String username = (String) request.get("username");
            String keyData = (String) request.get("key");

            if (username == null || username.isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            if (keyData == null || keyData.isEmpty()) {
                response.put("success", false);
                response.put("message", "卡密不能为空");
                return;
            }

            // 验证卡密是否有效
            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            if (keysSection == null || !keysSection.contains(keyData)) {
                response.put("success", false);
                response.put("message", "卡密不存在");
                return;
            }

            // 检查卡密状态
            boolean isUsed = keysSection.getBoolean(keyData + ".used", false);
            boolean isExpired = keysSection.getBoolean(keyData + ".expired", false);
            boolean isAssigned = keysSection.getBoolean(keyData + ".assigned", false);

            if (isUsed) {
                response.put("success", false);
                response.put("message", "此卡密已被使用");
                return;
            }

            if (isExpired) {
                response.put("success", false);
                response.put("message", "此卡密已失效");
                return;
            }

            if (!isAssigned) {
                response.put("success", false);
                response.put("message", "此卡密尚未分配");
                return;
            }

            // 标记卡密为已使用
            keysSection.set(keyData + ".used", true);
            keysSection.set(keyData + ".used_time", System.currentTimeMillis());
            keysSection.set(keyData + ".used_by", username);

            // 保留分配和失效记录，不清除历史信息
            // 这样可以在管理界面查看完整的使用历史

            // 保存配置
            plugin.saveKeysConfig();

            // 使用抽奖管理器进行抽奖
            LotteryReward reward = plugin.getLotteryManager().drawReward();
            if (reward == null) {
                response.put("success", false);
                response.put("message", "抽奖系统暂时不可用");
                return;
            }

            // 记录中奖信息
            recordWinnerInfo(username, reward.getName());

            // 不立即发放奖励，只返回抽奖结果
            // 奖励将在前端弹窗显示后通过 claim_reward API 发放
            response.put("success", true);
            response.put("message", "抽奖成功！卡密已使用");
            response.put("prize", reward.getName());
            response.put("reward_id", reward.getId());
            response.put("username", username);
            response.put("key_used", keyData);

            plugin.getLogger().info("用户 " + username + " 使用卡密 " + keyData + " 参与抽奖，获得: " + reward.getName());

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "参与抽奖失败: " + e.getMessage());
            plugin.getLogger().severe("处理抽奖请求时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理领取奖励
     */
    private void handleClaimReward(JSONObject request, JSONObject response) {
        try {
            String username = (String) request.get("username");
            String rewardId = (String) request.get("reward_id");

            if (username == null || username.isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            if (rewardId == null || rewardId.isEmpty()) {
                response.put("success", false);
                response.put("message", "奖励ID不能为空");
                return;
            }

            // 根据奖励ID获取奖励信息
            LotteryReward reward = null;
            List<LotteryReward> allRewards = plugin.getLotteryManager().getAllRewards();
            for (LotteryReward r : allRewards) {
                if (r.getId().equals(rewardId)) {
                    reward = r;
                    break;
                }
            }

            if (reward == null) {
                response.put("success", false);
                response.put("message", "奖励不存在");
                return;
            }

            // 发放奖励
            boolean success = plugin.getLotteryManager().giveRewardToPlayer(username, reward);

            if (success) {
                response.put("success", true);
                response.put("message", "奖励发放成功！");
                plugin.getLogger().info("用户 " + username + " 成功领取奖励: " + reward.getName());
            } else {
                response.put("success", false);
                response.put("message", "奖励发放失败");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "领取奖励失败: " + e.getMessage());
            plugin.getLogger().severe("处理领取奖励请求时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取状态
     */
    private void handleGetStatus(JSONObject response) {
        try {
            response.put("success", true);
            response.put("server_time", System.currentTimeMillis());
            response.put("plugin_version", plugin.getDescription().getVersion());
            response.put("web_server_running", webServer.isRunning());

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取状态失败: " + e.getMessage());
        }
    }

    /**
     * 处理获取卡密列表
     */
    private void handleGetKeys(JSONObject response) {
        try {
            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            int keyCount = keysSection != null ? keysSection.getKeys(false).size() : 0;

            response.put("success", true);
            response.put("total_keys", keyCount);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取卡密信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证API密钥
     */
    private boolean isValidApiKey(String apiKey) {
        if (apiKey == null)
            return false;

        String configApiKey = plugin.getConfig().getString("website.api-key", "");
        String adminKey = webServer.getAdminKey();

        if (webServer.isDebug()) {
            plugin.getLogger().info("API密钥验证 - 收到: " + apiKey);
            plugin.getLogger().info("配置中的用户密钥: " + configApiKey);
            plugin.getLogger().info("管理员密钥: " + adminKey);
        }

        boolean isValid = apiKey.equals(configApiKey) || apiKey.equals(adminKey);

        if (!isValid && webServer.isDebug()) {
            plugin.getLogger().warning("API密钥验证失败！");
        }

        return isValid;
    }

    /**
     * 判断是否为公开接口（不需要API密钥验证）
     */
    private boolean isPublicAction(String action) {
        switch (action) {
            case "get_public_leaderboard":
            case "participate_lottery":
            case "claim_reward":
            case "get_lottery_info":
                return true;
            default:
                return false;
        }
    }

    /**
     * 读取请求体
     */
    private String readRequestBody(HttpExchange exchange) throws IOException {
        StringBuilder body = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                body.append(line);
            }
        }
        return body.toString();
    }

    /**
     * 发送JSON响应
     */
    private void sendJsonResponse(HttpExchange exchange, int statusCode, JSONObject json) throws IOException {
        String response = json.toJSONString();
        byte[] bytes = response.getBytes(StandardCharsets.UTF_8);

        exchange.getResponseHeaders().set("Content-Type", "application/json; charset=utf-8");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");

        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }

    /**
     * 处理生成卡密
     */
    private void handleGenerateKeys(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            Object countObj = request.get("count");
            int count = 10;
            if (countObj instanceof Number) {
                count = ((Number) countObj).intValue();
            }

            if (count <= 0 || count > 1000) {
                response.put("success", false);
                response.put("message", "生成数量必须在1-1000之间");
                return;
            }

            // 调用插件的生成卡密方法
            List<String> generatedKeys = new ArrayList<>();
            long currentTime = System.currentTimeMillis();

            for (int i = 0; i < count; i++) {
                String key = plugin.generateKey();
                generatedKeys.add(key);

                // 保存到配置文件，包含创建时间和消息
                plugin.getKeysConfig().set("keys." + key + ".message",
                        plugin.getConfig().getString("default-message", "请复制这个卡密并在网站上使用！"));
                plugin.getKeysConfig().set("keys." + key + ".created_time", currentTime);
                plugin.getKeysConfig().set("keys." + key + ".created_date",
                        new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(currentTime)));
            }

            // 保存配置
            plugin.saveKeysConfig();

            response.put("success", true);
            response.put("message", "成功生成 " + count + " 个卡密");
            response.put("generated_count", count);
            response.put("keys", generatedKeys);

            plugin.getLogger().info("管理员生成了 " + count + " 个卡密");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "生成卡密失败: " + e.getMessage());
            plugin.getLogger().severe("生成卡密时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取所有卡密（支持分页和排序）
     */
    private void handleGetAllKeys(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取分页参数
            int page = 1;
            int pageSize = 20;
            String sortOrderParam = "desc"; // desc=最新在前, asc=最旧在前

            if (request.get("page") instanceof Number) {
                page = ((Number) request.get("page")).intValue();
            }
            if (request.get("page_size") instanceof Number) {
                pageSize = ((Number) request.get("page_size")).intValue();
            }
            if (request.get("sort_order") instanceof String) {
                sortOrderParam = (String) request.get("sort_order");
            }

            final String sortOrder = sortOrderParam;

            // 限制分页参数
            page = Math.max(1, page);
            pageSize = Math.max(1, Math.min(100, pageSize));

            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            List<JSONObject> allKeysList = new ArrayList<>();

            // 统计各状态的卡密数量
            int availableCount = 0;
            int assignedCount = 0;
            int usedCount = 0;
            int expiredCount = 0;

            if (keysSection != null) {
                for (String key : keysSection.getKeys(false)) {
                    JSONObject keyInfo = new JSONObject();
                    keyInfo.put("key", key);

                    // 检查是否是新格式（包含子配置）还是旧格式（直接字符串）
                    if (keysSection.isConfigurationSection(key)) {
                        ConfigurationSection keySection = keysSection.getConfigurationSection(key);
                        keyInfo.put("message", keySection.getString("message", ""));
                        keyInfo.put("created_time", keySection.getLong("created_time", 0));
                        keyInfo.put("created_date", keySection.getString("created_date", "未知"));

                        // 添加状态信息
                        boolean isUsed = keySection.getBoolean("used", false);
                        boolean isAssigned = keySection.getBoolean("assigned", false);
                        boolean isExpired = keySection.getBoolean("expired", false);

                        keyInfo.put("used", isUsed);
                        keyInfo.put("assigned", isAssigned);
                        keyInfo.put("expired", isExpired);

                        // 添加分配信息
                        if (isAssigned) {
                            keyInfo.put("assigned_to", keySection.getString("assigned_to", ""));
                            keyInfo.put("assigned_time", keySection.getLong("assigned_time", 0));
                        }

                        // 添加使用信息
                        if (isUsed) {
                            keyInfo.put("used_by", keySection.getString("used_by", ""));
                            keyInfo.put("used_time", keySection.getLong("used_time", 0));
                        }

                        // 添加失效信息
                        if (isExpired) {
                            keyInfo.put("expired_time", keySection.getLong("expired_time", 0));
                        }

                        // 统计状态
                        if (isUsed) {
                            usedCount++;
                        } else if (isExpired) {
                            expiredCount++;
                        } else if (isAssigned) {
                            assignedCount++;
                        } else {
                            availableCount++;
                        }

                    } else {
                        // 旧格式兼容
                        keyInfo.put("message", keysSection.getString(key, ""));
                        keyInfo.put("created_time", 0L);
                        keyInfo.put("created_date", "未知");
                        keyInfo.put("used", false);
                        keyInfo.put("assigned", false);
                        keyInfo.put("expired", false);
                        availableCount++;
                    }

                    allKeysList.add(keyInfo);
                }
            }

            // 按创建时间排序
            allKeysList.sort((a, b) -> {
                long timeA = (Long) a.get("created_time");
                long timeB = (Long) b.get("created_time");

                if ("asc".equals(sortOrder)) {
                    return Long.compare(timeA, timeB);
                } else {
                    return Long.compare(timeB, timeA);
                }
            });

            // 计算分页
            int totalKeys = allKeysList.size();
            int totalPages = (int) Math.ceil((double) totalKeys / pageSize);
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, totalKeys);

            JSONArray keysList = new JSONArray();
            for (int i = startIndex; i < endIndex; i++) {
                keysList.add(allKeysList.get(i));
            }

            // 添加状态统计信息
            JSONObject stats = new JSONObject();
            stats.put("all", totalKeys);
            stats.put("available", availableCount);
            stats.put("assigned", assignedCount);
            stats.put("used", usedCount);
            stats.put("expired", expiredCount);

            response.put("success", true);
            response.put("total_keys", totalKeys);
            response.put("total_pages", totalPages);
            response.put("current_page", page);
            response.put("page_size", pageSize);
            response.put("sort_order", sortOrder);
            response.put("keys", keysList);
            response.put("stats", stats);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取卡密列表失败: " + e.getMessage());
            plugin.getLogger().severe("获取卡密列表时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取抽奖奖励列表
     */
    private void handleGetLotteryRewards(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            JSONArray rewardsList = new JSONArray();
            List<LotteryReward> rewards = plugin.getLotteryManager().getAllRewards();

            for (LotteryReward reward : rewards) {
                JSONObject rewardInfo = new JSONObject();
                rewardInfo.put("id", reward.getId());
                rewardInfo.put("name", reward.getName());
                rewardInfo.put("description", reward.getDescription());
                rewardInfo.put("weight", reward.getWeight());
                rewardInfo.put("probability", reward.getProbability());

                JSONArray commandsArray = new JSONArray();
                for (String command : reward.getCommands()) {
                    commandsArray.add(command);
                }
                rewardInfo.put("commands", commandsArray);

                rewardsList.add(rewardInfo);
            }

            response.put("success", true);
            response.put("rewards", rewardsList);
            response.put("total_count", rewards.size());

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取奖励列表失败: " + e.getMessage());
            plugin.getLogger().severe("获取奖励列表时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存抽奖奖励
     */
    private void handleSaveLotteryReward(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String rewardId = (String) request.get("id");
            String name = (String) request.get("name");
            String description = (String) request.get("description");
            Object weightObj = request.get("weight");
            Object probabilityObj = request.get("probability");
            JSONArray commandsArray = (JSONArray) request.get("commands");

            // 验证必填字段 - 如果ID为空，自动生成一个
            if (rewardId == null || rewardId.trim().isEmpty()) {
                // 自动生成ID：使用时间戳 + 随机数
                rewardId = "reward_" + System.currentTimeMillis() + "_" + (int) (Math.random() * 1000);
            }

            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖励名称不能为空");
                return;
            }

            // 安全地处理权重值
            int weight = 10;
            if (weightObj instanceof Number) {
                weight = ((Number) weightObj).intValue();
            }

            // 安全地处理概率值
            double prob = 10.0;
            if (probabilityObj instanceof Number) {
                prob = ((Number) probabilityObj).doubleValue();
            }

            // 保存到配置文件
            String configPath = "rewards." + rewardId;
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".name", name);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".description",
                    description != null ? description : "");
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".weight", weight);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".probability", prob);

            List<String> commands = new ArrayList<>();
            if (commandsArray != null) {
                for (Object cmd : commandsArray) {
                    if (cmd instanceof String) {
                        commands.add((String) cmd);
                    }
                }
            }
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".commands", commands);

            // 保存配置文件
            plugin.getLotteryManager().saveRewardsConfig();

            // 重新加载奖励
            plugin.getLotteryManager().reload();

            response.put("success", true);
            response.put("message", "奖励保存成功");

            plugin.getLogger().info("管理员保存了奖励: " + name + " (ID: " + rewardId + ")");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存奖励失败: " + e.getMessage());
            plugin.getLogger().severe("保存奖励时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理清空卡密
     */
    private void handleClearKeys(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取当前卡密数量
            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            int oldCount = keysSection != null ? keysSection.getKeys(false).size() : 0;

            // 清空卡密
            plugin.getKeysConfig().set("keys", null);
            plugin.saveKeysConfig();

            response.put("success", true);
            response.put("message", "成功清空所有卡密");
            response.put("cleared_count", oldCount);

            plugin.getLogger().info("管理员清空了所有卡密，共 " + oldCount + " 个");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "清空卡密失败: " + e.getMessage());
        }
    }

    /**
     * 处理重载配置
     */
    private void handleReloadConfig(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 重载插件配置
            plugin.reloadConfig();
            webServer.reloadConfiguration();

            response.put("success", true);
            response.put("message", "配置重载成功");

            plugin.getLogger().info("管理员重载了插件配置");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "重载配置失败: " + e.getMessage());
        }
    }

    /**
     * 处理创建奖品
     */
    private void handleCreateReward(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String name = (String) request.get("name");
            String description = (String) request.get("description");
            Object probabilityObj = request.get("probability");
            Object commandsObj = request.get("commands");

            // 验证必填字段
            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖品名称不能为空");
                return;
            }

            double probability = 10.0;
            if (probabilityObj instanceof Number) {
                probability = ((Number) probabilityObj).doubleValue();
            }

            // 生成唯一ID
            String rewardId = "reward_" + System.currentTimeMillis();

            // 处理命令列表
            List<String> commands = new ArrayList<>();
            if (commandsObj instanceof JSONArray) {
                JSONArray commandsArray = (JSONArray) commandsObj;
                for (Object cmd : commandsArray) {
                    if (cmd instanceof String && !((String) cmd).trim().isEmpty()) {
                        commands.add((String) cmd);
                    }
                }
            }

            // 计算权重（基于概率）
            int weight = (int) Math.round(probability);

            // 保存到配置文件
            String configPath = "rewards." + rewardId;
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".name", name);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".description",
                    description != null ? description : "");
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".weight", weight);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".probability", probability);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".commands", commands);

            // 保存配置文件
            plugin.getLotteryManager().saveRewardsConfig();

            // 重新加载奖励
            plugin.getLotteryManager().reload();

            response.put("success", true);
            response.put("message", "奖品创建成功");
            response.put("reward_id", rewardId);

            plugin.getLogger().info("管理员创建了新奖品: " + name + " (ID: " + rewardId + ")");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "创建奖品失败: " + e.getMessage());
            plugin.getLogger().severe("创建奖品时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理更新奖品
     */
    private void handleUpdateReward(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String rewardId = (String) request.get("reward_id");
            String name = (String) request.get("name");
            String description = (String) request.get("description");
            Object probabilityObj = request.get("probability");
            Object commandsObj = request.get("commands");

            // 验证必填字段
            if (rewardId == null || rewardId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖品ID不能为空");
                return;
            }

            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖品名称不能为空");
                return;
            }

            // 检查奖品是否存在
            String configPath = "rewards." + rewardId;
            if (!plugin.getLotteryManager().getRewardsConfig().contains(configPath)) {
                response.put("success", false);
                response.put("message", "奖品不存在");
                return;
            }

            double probability = 10.0;
            if (probabilityObj instanceof Number) {
                probability = ((Number) probabilityObj).doubleValue();
            }

            // 处理命令列表
            List<String> commands = new ArrayList<>();
            if (commandsObj instanceof JSONArray) {
                JSONArray commandsArray = (JSONArray) commandsObj;
                for (Object cmd : commandsArray) {
                    if (cmd instanceof String && !((String) cmd).trim().isEmpty()) {
                        commands.add((String) cmd);
                    }
                }
            }

            // 计算权重（基于概率）
            int weight = (int) Math.round(probability);

            // 更新配置文件
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".name", name);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".description",
                    description != null ? description : "");
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".weight", weight);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".probability", probability);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".commands", commands);

            // 保存配置文件
            plugin.getLotteryManager().saveRewardsConfig();

            // 重新加载奖励
            plugin.getLotteryManager().reload();

            response.put("success", true);
            response.put("message", "奖品更新成功");

            plugin.getLogger().info("管理员更新了奖品: " + name + " (ID: " + rewardId + ")");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "更新奖品失败: " + e.getMessage());
            plugin.getLogger().severe("更新奖品时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理删除奖品
     */
    private void handleDeleteReward(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String rewardId = (String) request.get("reward_id");

            // 验证必填字段
            if (rewardId == null || rewardId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖品ID不能为空");
                return;
            }

            // 检查奖品是否存在
            String configPath = "rewards." + rewardId;
            if (!plugin.getLotteryManager().getRewardsConfig().contains(configPath)) {
                response.put("success", false);
                response.put("message", "奖品不存在");
                return;
            }

            // 获取奖品名称用于日志
            String rewardName = plugin.getLotteryManager().getRewardsConfig().getString(configPath + ".name", rewardId);

            // 删除奖品配置
            plugin.getLotteryManager().getRewardsConfig().set(configPath, null);

            // 保存配置文件
            plugin.getLotteryManager().saveRewardsConfig();

            // 重新加载奖励
            plugin.getLotteryManager().reload();

            response.put("success", true);
            response.put("message", "奖品删除成功");

            plugin.getLogger().info("管理员删除了奖品: " + rewardName + " (ID: " + rewardId + ")");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除奖品失败: " + e.getMessage());
            plugin.getLogger().severe("删除奖品时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理批量删除奖品
     */
    private void handleBatchDeleteRewards(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            JSONArray rewardIds = (JSONArray) request.get("reward_ids");
            if (rewardIds == null || rewardIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择要删除的奖品");
                return;
            }

            int deletedCount = 0;
            for (Object rewardIdObj : rewardIds) {
                String rewardId = (String) rewardIdObj;
                if (rewardId != null && !rewardId.trim().isEmpty()) {
                    String configPath = "rewards." + rewardId;
                    if (plugin.getLotteryManager().getRewardsConfig().contains(configPath)) {
                        plugin.getLotteryManager().getRewardsConfig().set(configPath, null);
                        deletedCount++;
                    }
                }
            }

            // 保存配置文件
            plugin.getLotteryManager().saveRewardsConfig();

            // 重新加载奖励
            plugin.getLotteryManager().reload();

            response.put("success", true);
            response.put("message", "批量删除完成");
            response.put("deleted_count", deletedCount);

            plugin.getLogger().info("管理员批量删除了 " + deletedCount + " 个奖品");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量删除失败: " + e.getMessage());
            plugin.getLogger().severe("批量删除奖品时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存奖品（统一处理新建和编辑）
     */
    private void handleSaveReward(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String rewardId = (String) request.get("reward_id");
            String name = (String) request.get("name");
            String description = (String) request.get("description");
            Object probabilityObj = request.get("probability");
            Object commandsObj = request.get("commands");

            // 验证必填字段
            if (rewardId == null || rewardId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖品ID不能为空");
                return;
            }

            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "奖品名称不能为空");
                return;
            }

            double probability = 10.0;
            if (probabilityObj instanceof Number) {
                probability = ((Number) probabilityObj).doubleValue();
            }

            // 处理命令列表
            List<String> commands = new ArrayList<>();
            if (commandsObj instanceof JSONArray) {
                JSONArray commandsArray = (JSONArray) commandsObj;
                for (Object cmd : commandsArray) {
                    if (cmd instanceof String && !((String) cmd).trim().isEmpty()) {
                        commands.add((String) cmd);
                    }
                }
            }

            if (commands.isEmpty()) {
                response.put("success", false);
                response.put("message", "至少需要一个执行指令");
                return;
            }

            // 计算权重（基于概率）
            int weight = (int) Math.round(probability);

            // 检查是否为新建还是编辑
            String configPath = "rewards." + rewardId;
            boolean isNew = !plugin.getLotteryManager().getRewardsConfig().contains(configPath);

            // 保存到配置文件
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".name", name);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".description",
                    description != null ? description : "");
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".weight", weight);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".probability", probability);
            plugin.getLotteryManager().getRewardsConfig().set(configPath + ".commands", commands);

            // 保存配置文件
            plugin.getLotteryManager().saveRewardsConfig();

            // 重新加载奖励
            plugin.getLotteryManager().reload();

            response.put("success", true);
            response.put("message", isNew ? "奖品创建成功" : "奖品更新成功");

            plugin.getLogger().info("管理员" + (isNew ? "创建" : "更新") + "了奖品: " + name + " (ID: " + rewardId + ")");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存奖品失败: " + e.getMessage());
            plugin.getLogger().severe("保存奖品时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理批量删除卡密
     */
    private void handleDeleteKeys(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            Object keysObj = request.get("keys");
            if (!(keysObj instanceof JSONArray)) {
                response.put("success", false);
                response.put("message", "卡密列表格式错误");
                return;
            }

            JSONArray keysArray = (JSONArray) keysObj;
            if (keysArray.isEmpty()) {
                response.put("success", false);
                response.put("message", "卡密列表不能为空");
                return;
            }

            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            if (keysSection == null) {
                response.put("success", false);
                response.put("message", "卡密配置不存在");
                return;
            }

            int deletedCount = 0;
            for (Object keyObj : keysArray) {
                if (keyObj instanceof String) {
                    String key = (String) keyObj;
                    if (keysSection.contains(key)) {
                        keysSection.set(key, null);
                        deletedCount++;
                    }
                }
            }

            // 保存配置
            plugin.saveKeysConfig();

            response.put("success", true);
            response.put("message", "成功删除 " + deletedCount + " 个卡密");
            response.put("deleted_count", deletedCount);

            plugin.getLogger().info("管理员批量删除了 " + deletedCount + " 个卡密");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除卡密失败: " + e.getMessage());
            plugin.getLogger().severe("删除卡密时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 记录中奖信息
     */
    private void recordWinnerInfo(String username, String prize) {
        try {
            ConfigurationSection winnersSection = plugin.getKeysConfig().getConfigurationSection("winners");
            if (winnersSection == null) {
                winnersSection = plugin.getKeysConfig().createSection("winners");
            }

            long currentTime = System.currentTimeMillis();
            String recordId = "winner_" + currentTime + "_" + username.hashCode();

            winnersSection.set(recordId + ".username", username);
            winnersSection.set(recordId + ".prize", prize);
            winnersSection.set(recordId + ".timestamp", currentTime);
            winnersSection.set(recordId + ".date",
                    new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date(currentTime)));

            plugin.saveKeysConfig();
            plugin.getLogger().info("记录中奖信息: " + username + " 获得 " + prize);

        } catch (Exception e) {
            plugin.getLogger().severe("记录中奖信息失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取中奖记录
     */
    private void handleGetWinners(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取分页参数
            int page = 1;
            int pageSize = 20;
            String sortOrder = "desc"; // desc=最新在前, asc=最旧在前

            if (request.get("page") instanceof Number) {
                page = ((Number) request.get("page")).intValue();
            }
            if (request.get("page_size") instanceof Number) {
                pageSize = ((Number) request.get("page_size")).intValue();
            }
            if (request.get("sort_order") instanceof String) {
                sortOrder = (String) request.get("sort_order");
            }

            // 限制分页参数
            page = Math.max(1, page);
            pageSize = Math.max(1, Math.min(100, pageSize));

            ConfigurationSection winnersSection = plugin.getKeysConfig().getConfigurationSection("winners");
            List<JSONObject> allWinnersList = new ArrayList<>();

            if (winnersSection != null) {
                for (String recordId : winnersSection.getKeys(false)) {
                    ConfigurationSection recordSection = winnersSection.getConfigurationSection(recordId);
                    if (recordSection != null) {
                        JSONObject winnerInfo = new JSONObject();
                        winnerInfo.put("id", recordId);
                        winnerInfo.put("username", recordSection.getString("username", "未知"));
                        winnerInfo.put("prize", recordSection.getString("prize", "未知"));
                        winnerInfo.put("timestamp", recordSection.getLong("timestamp", 0));
                        winnerInfo.put("date", recordSection.getString("date", "未知"));

                        allWinnersList.add(winnerInfo);
                    }
                }
            }

            // 按时间排序
            final String finalSortOrder = sortOrder;
            allWinnersList.sort((a, b) -> {
                long timeA = (Long) a.get("timestamp");
                long timeB = (Long) b.get("timestamp");

                if ("asc".equals(finalSortOrder)) {
                    return Long.compare(timeA, timeB);
                } else {
                    return Long.compare(timeB, timeA);
                }
            });

            // 计算分页
            int totalWinners = allWinnersList.size();
            int totalPages = (int) Math.ceil((double) totalWinners / pageSize);
            int startIndex = (page - 1) * pageSize;
            int endIndex = Math.min(startIndex + pageSize, totalWinners);

            JSONArray winnersList = new JSONArray();
            for (int i = startIndex; i < endIndex; i++) {
                winnersList.add(allWinnersList.get(i));
            }

            response.put("success", true);
            response.put("total_winners", totalWinners);
            response.put("total_pages", totalPages);
            response.put("current_page", page);
            response.put("page_size", pageSize);
            response.put("sort_order", sortOrder);
            response.put("winners", winnersList);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取中奖记录失败: " + e.getMessage());
            plugin.getLogger().severe("获取中奖记录时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理清空中奖记录
     */
    private void handleClearWinners(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            ConfigurationSection winnersSection = plugin.getKeysConfig().getConfigurationSection("winners");
            int clearedCount = 0;

            if (winnersSection != null) {
                clearedCount = winnersSection.getKeys(false).size();
                plugin.getKeysConfig().set("winners", null);
                plugin.saveKeysConfig();
            }

            response.put("success", true);
            response.put("message", "中奖记录清空成功");
            response.put("cleared_count", clearedCount);

            plugin.getLogger().info("管理员清空了中奖记录，共清空 " + clearedCount + " 条记录");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "清空中奖记录失败: " + e.getMessage());
            plugin.getLogger().severe("清空中奖记录时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取日志
     */
    private void handleGetLogs(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取参数
            int lines = 100;
            String level = "all";

            if (request.get("lines") instanceof Number) {
                lines = ((Number) request.get("lines")).intValue();
            }
            if (request.get("level") instanceof String) {
                level = (String) request.get("level");
            }

            // 限制行数
            lines = Math.max(10, Math.min(1000, lines));

            // 读取日志文件
            List<String> logLines = readLogFile(lines, level);

            response.put("success", true);
            response.put("logs", logLines);
            response.put("total_lines", logLines.size());
            response.put("level_filter", level);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取日志失败: " + e.getMessage());
            plugin.getLogger().severe("获取日志时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理清空日志
     */
    private void handleClearLogs(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 清空日志文件
            boolean success = clearLogFile();

            if (success) {
                response.put("success", true);
                response.put("message", "日志清空成功");
                plugin.getLogger().info("管理员清空了系统日志");
            } else {
                response.put("success", false);
                response.put("message", "日志清空失败");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "清空日志失败: " + e.getMessage());
            plugin.getLogger().severe("清空日志时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 读取日志文件
     */
    private List<String> readLogFile(int maxLines, String levelFilter) {
        List<String> logLines = new ArrayList<>();

        try {
            // 检查是否启用了网站日志显示
            boolean webLogging = plugin.getConfig().getBoolean("website.web-logging", true);

            if (!webLogging) {
                logLines.add("网站日志显示已禁用，请在系统设置中启用");
                return logLines;
            }

            // 使用WebLogHandler获取日志
            if (plugin.getWebLogHandler() != null) {
                logLines = plugin.getWebLogHandler().getRecentLogs(maxLines, levelFilter);
            } else {
                logLines.add("日志处理器未初始化");
            }

            // 如果没有找到相关日志，添加提示信息
            if (logLines.isEmpty()) {
                logLines.add("暂无AceKeySystem相关日志记录");
            }

        } catch (Exception e) {
            logLines.add("读取日志时出错: " + e.getMessage());
            plugin.getLogger().severe("读取日志时出错: " + e.getMessage());
        }

        return logLines;
    }

    /**
     * 清空日志文件
     */
    private boolean clearLogFile() {
        try {
            // 使用WebLogHandler清空日志
            if (plugin.getWebLogHandler() != null) {
                plugin.getWebLogHandler().clearLogs();
                return true;
            } else {
                plugin.getLogger().severe("日志处理器未初始化");
                return false;
            }
        } catch (Exception e) {
            plugin.getLogger().severe("清空日志时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 处理切换网站日志显示
     */
    private void handleToggleWebLogging(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            boolean enabled = Boolean.parseBoolean(String.valueOf(request.get("enabled")));

            plugin.getConfig().set("website.web-logging", enabled);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("网站日志设置保存成功: " + enabled);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存网站日志配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            // 动态添加或移除日志处理器
            if (plugin.getWebLogHandler() != null) {
                if (enabled) {
                    // 确保日志处理器已添加
                    if (!plugin.getLogger().getHandlers().toString().contains("WebLogHandler")) {
                        plugin.getLogger().addHandler(plugin.getWebLogHandler());
                    }
                    // 如果启用网站日志但控制台日志被禁用，则禁用控制台输出
                    boolean consoleLogging = plugin.getConfig().getBoolean("website.console-logging", false);
                    plugin.getLogger().setUseParentHandlers(consoleLogging);
                } else {
                    // 移除日志处理器，恢复控制台输出
                    plugin.getLogger().removeHandler(plugin.getWebLogHandler());
                    plugin.getLogger().setUseParentHandlers(true);
                }
            }

            response.put("success", true);
            response.put("message", enabled ? "已启用网站日志显示" : "已禁用网站日志显示");
            response.put("web_logging", enabled);

            plugin.getLogger().info("管理员" + (enabled ? "启用" : "禁用") + "了网站日志显示");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "切换网站日志显示失败: " + e.getMessage());
            plugin.getLogger().severe("切换网站日志显示时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理切换控制台日志显示
     */
    private void handleToggleConsoleLogging(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            boolean enabled = Boolean.parseBoolean(String.valueOf(request.get("enabled")));

            plugin.getConfig().set("website.console-logging", enabled);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("控制台日志设置保存成功: " + enabled);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存控制台日志配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            // 动态切换控制台输出
            boolean webLogging = plugin.getConfig().getBoolean("website.web-logging", true);
            if (webLogging) {
                // 如果网站日志启用，根据控制台日志设置决定是否使用父处理器
                plugin.getLogger().setUseParentHandlers(enabled);
            }

            response.put("success", true);
            response.put("message", enabled ? "已启用控制台日志显示" : "已禁用控制台日志显示");
            response.put("console_logging", enabled);

            plugin.getLogger().info("管理员" + (enabled ? "启用" : "禁用") + "了控制台日志显示");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "切换控制台日志显示失败: " + e.getMessage());
            plugin.getLogger().severe("切换控制台日志显示时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存日志到服务器文件夹
     */
    private void handleSaveLogsToServer(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 手动保存日志到插件logs文件夹
            if (plugin.getWebLogHandler() != null) {
                plugin.getWebLogHandler().manualSaveToServerFolder();
                response.put("success", true);
                response.put("message", "日志已成功保存到插件logs文件夹");
                plugin.getLogger().info("管理员手动保存了网站日志到插件logs文件夹");
            } else {
                response.put("success", false);
                response.put("message", "日志处理器未初始化");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存日志失败: " + e.getMessage());
            plugin.getLogger().severe("保存日志到服务器文件夹时出错: " + e.getMessage());
        }
    }

    /**
     * 处理获取统计信息
     */
    private void handleGetStatistics(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            if (keysSection == null) {
                response.put("success", false);
                response.put("message", "无法获取卡密数据");
                return;
            }

            // 统计各种状态的卡密数量
            int totalKeys = 0;
            int unusedKeys = 0;
            int assignedKeys = 0;
            int usedKeys = 0;
            int expiredKeys = 0;
            int neverExpireKeys = 0;

            // 统计每日使用情况（最近7天）
            JSONArray dailyUsage = new JSONArray();
            long currentTime = System.currentTimeMillis();
            long oneDayMs = 24 * 60 * 60 * 1000L;

            for (int i = 6; i >= 0; i--) {
                long dayStart = currentTime - (i + 1) * oneDayMs;
                long dayEnd = currentTime - i * oneDayMs;
                int dayUsage = 0;

                for (String key : keysSection.getKeys(false)) {
                    long usedTime = keysSection.getLong(key + ".used_time", 0);
                    if (usedTime >= dayStart && usedTime < dayEnd) {
                        dayUsage++;
                    }
                }

                JSONObject dayData = new JSONObject();
                dayData.put("date", new java.text.SimpleDateFormat("MM-dd").format(new java.util.Date(dayStart)));
                dayData.put("count", dayUsage);
                dailyUsage.add(dayData);
            }

            // 统计玩家使用情况
            JSONObject playerStats = new JSONObject();
            JSONArray topPlayers = new JSONArray();

            for (String key : keysSection.getKeys(false)) {
                totalKeys++;

                boolean isUsed = keysSection.getBoolean(key + ".used", false);
                boolean isExpired = keysSection.getBoolean(key + ".expired", false);
                boolean isAssigned = keysSection.getBoolean(key + ".assigned", false);
                boolean neverExpire = keysSection.getBoolean(key + ".never_expire", false);

                if (neverExpire) {
                    neverExpireKeys++;
                }

                if (isUsed) {
                    usedKeys++;
                    String usedBy = keysSection.getString(key + ".used_by", "未知");
                    if (!usedBy.equals("未知")) {
                        int currentCount = playerStats.containsKey(usedBy) ? (Integer) playerStats.get(usedBy) : 0;
                        playerStats.put(usedBy, currentCount + 1);
                    }
                } else if (isExpired) {
                    expiredKeys++;
                } else if (isAssigned) {
                    assignedKeys++;
                } else {
                    unusedKeys++;
                }
            }

            // 转换玩家统计为排序数组
            for (Object playerObj : playerStats.keySet()) {
                String player = (String) playerObj;
                int count = (Integer) playerStats.get(player);
                JSONObject playerData = new JSONObject();
                playerData.put("player", player);
                playerData.put("count", count);
                topPlayers.add(playerData);
            }

            // 构建响应
            JSONObject statistics = new JSONObject();

            // 基本统计
            JSONObject basicStats = new JSONObject();
            basicStats.put("total", totalKeys);
            basicStats.put("unused", unusedKeys);
            basicStats.put("assigned", assignedKeys);
            basicStats.put("used", usedKeys);
            basicStats.put("expired", expiredKeys);
            basicStats.put("never_expire", neverExpireKeys);
            statistics.put("basic", basicStats);

            // 每日使用统计
            statistics.put("daily_usage", dailyUsage);

            // 玩家统计
            statistics.put("top_players", topPlayers);

            // 系统信息
            JSONObject systemInfo = new JSONObject();
            systemInfo.put("server_time", currentTime);
            systemInfo.put("plugin_version", plugin.getDescription().getVersion());
            systemInfo.put("expire_seconds", plugin.getConfig().getInt("voucher-expiry.expire-seconds", 86400));
            systemInfo.put("check_interval", plugin.getConfig().getInt("voucher-expiry.check-interval-seconds", 3600));
            statistics.put("system", systemInfo);

            response.put("success", true);
            response.put("statistics", statistics);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            plugin.getLogger().severe("获取统计信息时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理搜索玩家
     */
    private void handleSearchPlayer(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String playerName = (String) request.get("player_name");
            if (playerName == null || playerName.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "玩家名不能为空");
                return;
            }

            playerName = playerName.trim();

            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            if (keysSection == null) {
                response.put("success", false);
                response.put("message", "无法获取卡密数据");
                return;
            }

            // 搜索玩家相关的卡密
            JSONArray assignedKeys = new JSONArray();
            JSONArray usedKeys = new JSONArray();
            JSONArray expiredKeys = new JSONArray();

            for (String key : keysSection.getKeys(false)) {
                String assignedTo = keysSection.getString(key + ".assigned_to", "");
                String usedBy = keysSection.getString(key + ".used_by", "");

                // 检查是否与搜索的玩家相关
                if (assignedTo.equalsIgnoreCase(playerName) || usedBy.equalsIgnoreCase(playerName)) {
                    JSONObject keyInfo = new JSONObject();
                    keyInfo.put("key", key);
                    keyInfo.put("message", keysSection.getString(key + ".message", ""));
                    keyInfo.put("created_time", keysSection.getLong(key + ".created_time", 0));
                    keyInfo.put("created_date", keysSection.getString(key + ".created_date", ""));

                    boolean isUsed = keysSection.getBoolean(key + ".used", false);
                    boolean isExpired = keysSection.getBoolean(key + ".expired", false);
                    boolean isAssigned = keysSection.getBoolean(key + ".assigned", false);
                    boolean neverExpire = keysSection.getBoolean(key + ".never_expire", false);

                    keyInfo.put("used", isUsed);
                    keyInfo.put("expired", isExpired);
                    keyInfo.put("assigned", isAssigned);
                    keyInfo.put("never_expire", neverExpire);

                    if (isAssigned) {
                        keyInfo.put("assigned_to", assignedTo);
                        keyInfo.put("assigned_time", keysSection.getLong(key + ".assigned_time", 0));
                    }

                    if (isUsed) {
                        keyInfo.put("used_by", usedBy);
                        keyInfo.put("used_time", keysSection.getLong(key + ".used_time", 0));
                        usedKeys.add(keyInfo);
                    } else if (isExpired) {
                        keyInfo.put("expired_time", keysSection.getLong(key + ".expired_time", 0));
                        expiredKeys.add(keyInfo);
                    } else if (assignedTo.equalsIgnoreCase(playerName)) {
                        assignedKeys.add(keyInfo);
                    }
                }
            }

            // 构建玩家信息
            JSONObject playerInfo = new JSONObject();
            playerInfo.put("player_name", playerName);
            playerInfo.put("assigned_count", assignedKeys.size());
            playerInfo.put("used_count", usedKeys.size());
            playerInfo.put("expired_count", expiredKeys.size());
            playerInfo.put("total_count", assignedKeys.size() + usedKeys.size() + expiredKeys.size());

            playerInfo.put("assigned_keys", assignedKeys);
            playerInfo.put("used_keys", usedKeys);
            playerInfo.put("expired_keys", expiredKeys);

            response.put("success", true);
            response.put("player_info", playerInfo);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "搜索玩家失败: " + e.getMessage());
            plugin.getLogger().severe("搜索玩家时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取排行榜
     */
    private void handleGetLeaderboard(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            ConfigurationSection winnersSection = plugin.getKeysConfig().getConfigurationSection("winners");

            if (keysSection == null) {
                response.put("success", false);
                response.put("message", "无法获取卡密数据");
                return;
            }

            // 统计玩家数据
            Map<String, PlayerStats> playerStatsMap = new HashMap<>();

            // 统计卡密使用次数（抽奖次数）
            for (String key : keysSection.getKeys(false)) {
                String usedBy = keysSection.getString(key + ".used_by", "");
                if (!usedBy.isEmpty()) {
                    PlayerStats stats = playerStatsMap.getOrDefault(usedBy, new PlayerStats(usedBy));
                    stats.draws++;
                    playerStatsMap.put(usedBy, stats);
                }
            }

            // 统计获奖次数
            if (winnersSection != null) {
                for (String winnerId : winnersSection.getKeys(false)) {
                    String username = winnersSection.getString(winnerId + ".username", "");
                    if (!username.isEmpty()) {
                        PlayerStats stats = playerStatsMap.getOrDefault(username, new PlayerStats(username));
                        stats.wins++;
                        playerStatsMap.put(username, stats);
                    }
                }
            }

            // 转换为列表并排序
            List<PlayerStats> playerStatsList = new ArrayList<>(playerStatsMap.values());

            // 获奖次数排行
            List<PlayerStats> winsList = new ArrayList<>(playerStatsList);
            winsList.sort((a, b) -> Integer.compare(b.wins, a.wins));

            // 抽奖次数排行
            List<PlayerStats> drawsList = new ArrayList<>(playerStatsList);
            drawsList.sort((a, b) -> Integer.compare(b.draws, a.draws));

            // 构建响应数据
            JSONObject leaderboard = new JSONObject();

            // 获奖次数排行榜
            JSONArray winsArray = new JSONArray();
            for (int i = 0; i < Math.min(10, winsList.size()); i++) {
                PlayerStats stats = winsList.get(i);
                if (stats.wins > 0) { // 只显示有获奖记录的玩家
                    JSONObject playerObj = new JSONObject();
                    playerObj.put("player", stats.player);
                    playerObj.put("wins", stats.wins);
                    playerObj.put("total_draws", stats.draws);
                    winsArray.add(playerObj);
                }
            }
            leaderboard.put("wins", winsArray);

            // 抽奖次数排行榜
            JSONArray drawsArray = new JSONArray();
            for (int i = 0; i < Math.min(10, drawsList.size()); i++) {
                PlayerStats stats = drawsList.get(i);
                if (stats.draws > 0) { // 只显示有抽奖记录的玩家
                    JSONObject playerObj = new JSONObject();
                    playerObj.put("player", stats.player);
                    playerObj.put("draws", stats.draws);
                    playerObj.put("wins", stats.wins);
                    drawsArray.add(playerObj);
                }
            }
            leaderboard.put("draws", drawsArray);

            response.put("success", true);
            response.put("leaderboard", leaderboard);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取排行榜失败: " + e.getMessage());
            plugin.getLogger().severe("获取排行榜时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取公开排行榜（不需要管理员权限）
     */
    private void handleGetPublicLeaderboard(JSONObject request, JSONObject response) {
        try {
            // 公开接口，不需要验证API密钥
            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            ConfigurationSection winnersSection = plugin.getKeysConfig().getConfigurationSection("winners");

            if (keysSection == null) {
                response.put("success", false);
                response.put("message", "无法获取卡密数据");
                return;
            }

            // 统计玩家数据
            Map<String, PlayerStats> playerStatsMap = new HashMap<>();

            // 统计卡密使用次数（抽奖次数）
            for (String key : keysSection.getKeys(false)) {
                String usedBy = keysSection.getString(key + ".used_by", "");
                if (!usedBy.isEmpty()) {
                    PlayerStats stats = playerStatsMap.getOrDefault(usedBy, new PlayerStats(usedBy));
                    stats.draws++;
                    playerStatsMap.put(usedBy, stats);
                }
            }

            // 统计获奖次数
            if (winnersSection != null) {
                for (String winnerId : winnersSection.getKeys(false)) {
                    String username = winnersSection.getString(winnerId + ".username", "");
                    if (!username.isEmpty()) {
                        PlayerStats stats = playerStatsMap.getOrDefault(username, new PlayerStats(username));
                        stats.wins++;
                        playerStatsMap.put(username, stats);
                    }
                }
            }

            // 转换为列表并排序
            List<PlayerStats> playerStatsList = new ArrayList<>(playerStatsMap.values());

            // 获奖次数排行
            List<PlayerStats> winsList = new ArrayList<>(playerStatsList);
            winsList.sort((a, b) -> Integer.compare(b.wins, a.wins));

            // 抽奖次数排行
            List<PlayerStats> drawsList = new ArrayList<>(playerStatsList);
            drawsList.sort((a, b) -> Integer.compare(b.draws, a.draws));

            // 构建响应数据
            JSONObject leaderboard = new JSONObject();

            // 获奖次数排行榜
            JSONArray winsArray = new JSONArray();
            for (int i = 0; i < Math.min(10, winsList.size()); i++) {
                PlayerStats stats = winsList.get(i);
                if (stats.wins > 0) { // 只显示有获奖记录的玩家
                    JSONObject playerObj = new JSONObject();
                    playerObj.put("player", stats.player);
                    playerObj.put("wins", stats.wins);
                    playerObj.put("total_draws", stats.draws);
                    winsArray.add(playerObj);
                }
            }
            leaderboard.put("wins", winsArray);

            // 抽奖次数排行榜
            JSONArray drawsArray = new JSONArray();
            for (int i = 0; i < Math.min(10, drawsList.size()); i++) {
                PlayerStats stats = drawsList.get(i);
                if (stats.draws > 0) { // 只显示有抽奖记录的玩家
                    JSONObject playerObj = new JSONObject();
                    playerObj.put("player", stats.player);
                    playerObj.put("draws", stats.draws);
                    playerObj.put("wins", stats.wins);
                    drawsArray.add(playerObj);
                }
            }
            leaderboard.put("draws", drawsArray);

            // 积分排行榜
            JSONArray pointsArray = new JSONArray();
            if (plugin.getPointsManager() != null) {
                // 获取所有玩家的积分数据
                Map<String, Integer> allPoints = plugin.getPointsManager().getAllPlayerPoints();

                // 转换为列表并排序
                List<Map.Entry<String, Integer>> pointsList = new ArrayList<>(allPoints.entrySet());
                pointsList.sort((a, b) -> Integer.compare(b.getValue(), a.getValue()));

                // 构建积分排行榜
                for (int i = 0; i < Math.min(10, pointsList.size()); i++) {
                    Map.Entry<String, Integer> entry = pointsList.get(i);
                    if (entry.getValue() > 0) { // 只显示有积分的玩家
                        JSONObject playerObj = new JSONObject();
                        playerObj.put("player", entry.getKey());
                        playerObj.put("points", entry.getValue());
                        pointsArray.add(playerObj);
                    }
                }
            }
            leaderboard.put("points", pointsArray);

            response.put("success", true);
            response.put("leaderboard", leaderboard);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取排行榜失败: " + e.getMessage());
            plugin.getLogger().severe("获取公开排行榜时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存首页设置
     */
    private void handleSaveHomepageSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String homepageTitle = (String) request.get("homepage_title");
            String homepageSubtitle = (String) request.get("homepage_subtitle");
            String homepageDescription = (String) request.get("homepage_description");
            String homepageBackgroundImage = (String) request.get("homepage_background_image");
            Boolean showLotteryCard = (Boolean) request.get("show_lottery_card");
            Boolean showLeaderboardCard = (Boolean) request.get("show_leaderboard_card");
            Boolean showShopCard = (Boolean) request.get("show_shop_card");
            Boolean showPunishmentCard = (Boolean) request.get("show_punishment_card");
            Boolean showSystemStatus = (Boolean) request.get("show_system_status");

            // 确保所有值都不为null，提供安全的默认值
            String safeTitle = (homepageTitle != null) ? homepageTitle.trim() : webServer.getTitle();
            String safeSubtitle = (homepageSubtitle != null) ? homepageSubtitle.trim() : "欢迎来到游戏娱乐中心";
            String safeDescription = (homepageDescription != null) ? homepageDescription.trim()
                    : "🎯 参与精彩抽奖活动，赢取丰厚奖品\n🏆 查看排行榜，与其他玩家一较高下\n🛒 使用积分购买心仪的游戏道具";
            String safeBackgroundImage = (homepageBackgroundImage != null) ? homepageBackgroundImage.trim() : "";

            // 获取首页背景透明度设置
            Integer homepageBackgroundOpacity = (Integer) request.get("homepage_background_opacity");
            int safeHomepageOpacity = 30; // 默认值
            if (homepageBackgroundOpacity != null) {
                safeHomepageOpacity = homepageBackgroundOpacity;
                // 确保透明度在有效范围内
                if (safeHomepageOpacity < 0 || safeHomepageOpacity > 80) {
                    safeHomepageOpacity = 30;
                }
            }

            boolean safeLotteryCard = (showLotteryCard != null) ? showLotteryCard : true;
            boolean safeLeaderboardCard = (showLeaderboardCard != null) ? showLeaderboardCard : true;
            boolean safeShopCard = (showShopCard != null) ? showShopCard : true;
            boolean safePunishmentCard = (showPunishmentCard != null) ? showPunishmentCard : true;
            boolean safeSystemStatus = (showSystemStatus != null) ? showSystemStatus : true;

            // 保存到配置文件
            plugin.getConfig().set("website.homepage.title", safeTitle);
            plugin.getConfig().set("website.homepage.subtitle", safeSubtitle);
            plugin.getConfig().set("website.homepage.description", safeDescription);
            plugin.getConfig().set("website.homepage.background-image", safeBackgroundImage);
            plugin.getConfig().set("website.homepage.background-opacity", safeHomepageOpacity);
            plugin.getConfig().set("website.homepage.show-lottery-card", safeLotteryCard);
            plugin.getConfig().set("website.homepage.show-leaderboard-card", safeLeaderboardCard);
            plugin.getConfig().set("website.homepage.show-shop-card", safeShopCard);
            plugin.getConfig().set("litebans.display.show-punishment-card", safePunishmentCard);
            plugin.getConfig().set("website.homepage.show-system-status", safeSystemStatus);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("首页设置保存成功: 标题=" + safeTitle + ", 副标题=" + safeSubtitle);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存首页配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "首页设置保存成功");

            plugin.getLogger().info("管理员更新了首页设置");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存首页设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存首页设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存背景设置
     */
    private void handleSaveBackgroundSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            String backgroundImage = (String) request.get("background_image");
            String loginBackgroundImage = (String) request.get("login_background_image");
            String loginLogoUrl = (String) request.get("login_logo_url");
            String leaderboardBackgroundImage = (String) request.get("leaderboard_background_image");

            // 确保所有值都不为null，提供安全的默认值
            String safeBackgroundImage = (backgroundImage != null) ? backgroundImage.trim() : "";
            String safeLoginBg = (loginBackgroundImage != null) ? loginBackgroundImage.trim() : "";
            String safeLoginLogo = (loginLogoUrl != null) ? loginLogoUrl.trim() : "";
            String safeLeaderboardBg = (leaderboardBackgroundImage != null) ? leaderboardBackgroundImage.trim() : "";

            // 获取处罚记录背景图设置
            String punishmentBg = "";
            try {
                punishmentBg = request.get("punishment_background_image").toString();
            } catch (Exception e) {
                punishmentBg = "";
            }
            String safePunishmentBg = punishmentBg.length() > 500 ? "" : punishmentBg;

            // 获取抽奖页面背景透明度设置
            int backgroundOpacity = 30; // 默认值
            try {
                Object opacityObj = request.get("background_opacity");
                if (opacityObj instanceof Number) {
                    backgroundOpacity = ((Number) opacityObj).intValue();
                    // 确保透明度在有效范围内
                    if (backgroundOpacity < 0 || backgroundOpacity > 80) {
                        backgroundOpacity = 30;
                    }
                }
            } catch (Exception e) {
                backgroundOpacity = 30;
            }

            // 获取排行榜背景透明度设置
            int leaderboardOpacity = 30; // 默认值
            try {
                Object opacityObj = request.get("leaderboard_background_opacity");
                if (opacityObj instanceof Number) {
                    leaderboardOpacity = ((Number) opacityObj).intValue();
                    // 确保透明度在有效范围内
                    if (leaderboardOpacity < 0 || leaderboardOpacity > 80) {
                        leaderboardOpacity = 30;
                    }
                }
            } catch (Exception e) {
                leaderboardOpacity = 30;
            }

            // 获取处罚记录背景透明度设置
            int punishmentOpacity = 30; // 默认值
            try {
                Object opacityObj = request.get("punishment_background_opacity");
                if (opacityObj instanceof Number) {
                    punishmentOpacity = ((Number) opacityObj).intValue();
                    // 确保透明度在有效范围内
                    if (punishmentOpacity < 0 || punishmentOpacity > 80) {
                        punishmentOpacity = 30;
                    }
                }
            } catch (Exception e) {
                punishmentOpacity = 30;
            }

            // 获取登录页面背景透明度设置
            int loginOpacity = 30; // 默认值
            try {
                Object opacityObj = request.get("login_background_opacity");
                if (opacityObj instanceof Number) {
                    loginOpacity = ((Number) opacityObj).intValue();
                    // 确保透明度在有效范围内
                    if (loginOpacity < 0 || loginOpacity > 80) {
                        loginOpacity = 30;
                    }
                }
            } catch (Exception e) {
                loginOpacity = 30;
            }

            // 获取积分商店背景透明度设置
            int shopOpacity = 30; // 默认值
            try {
                Object opacityObj = request.get("shop_background_opacity");
                if (opacityObj instanceof Number) {
                    shopOpacity = ((Number) opacityObj).intValue();
                    // 确保透明度在有效范围内
                    if (shopOpacity < 0 || shopOpacity > 80) {
                        shopOpacity = 30;
                    }
                }
            } catch (Exception e) {
                shopOpacity = 30;
            }

            // 保存到配置文件
            plugin.getConfig().set("website.background-image", safeBackgroundImage);
            plugin.getConfig().set("web-server.login-background-image", safeLoginBg);
            plugin.getConfig().set("web-server.login-logo-url", safeLoginLogo);
            plugin.getConfig().set("website.leaderboard-background-image", safeLeaderboardBg);
            plugin.getConfig().set("website.punishment-background-image", safePunishmentBg);
            plugin.getConfig().set("website.background-opacity", backgroundOpacity);
            plugin.getConfig().set("website.leaderboard-background-opacity", leaderboardOpacity);
            plugin.getConfig().set("website.punishment-background-opacity", punishmentOpacity);
            plugin.getConfig().set("web-server.login-background-opacity", loginOpacity);
            plugin.getConfig().set("website.shop-background-opacity", shopOpacity);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("背景设置保存成功");
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存背景配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "背景设置保存成功");

            plugin.getLogger().info("管理员更新了背景设置");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存背景设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存背景设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存界面设置
     */
    private void handleSaveInterfaceSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            String sessionId = (String) request.get("session_id");

            // 检查权限：API密钥或有界面设置权限的会话
            boolean hasPermission = false;
            if (apiKey != null && webServer.getAdminKey().equals(apiKey)) {
                hasPermission = true; // API密钥拥有所有权限
            } else if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
                // 检查会话用户是否有界面设置权限
                AdminLoginHandler loginHandler = new AdminLoginHandler(plugin, webServer);
                String username = loginHandler.getUsernameFromSession(sessionId);
                if (username != null) {
                    AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);
                    hasPermission = accountHandler.hasInterfaceSettingsPermission(username);
                }
            }

            if (!hasPermission) {
                response.put("success", false);
                response.put("message", "权限不足，需要界面设置权限");
                return;
            }

            // 获取所有界面设置参数
            String backgroundImage = (String) request.get("background_image");
            String leaderboardBackgroundImage = (String) request.get("leaderboard_background_image");
            Boolean enableOpeningAnimation = (Boolean) request.get("enable_opening_animation");
            Object animationDurationObj = request.get("animation_duration");
            String animationStyle = (String) request.get("animation_style");
            String customAnimationUrl = (String) request.get("custom_animation_url");
            Boolean enableSoundEffects = (Boolean) request.get("enable_sound_effects");
            String openingSoundUrl = (String) request.get("opening_sound_url");
            String winningSoundUrl = (String) request.get("winning_sound_url");
            Object soundVolumeObj = request.get("sound_volume");

            // 安全地转换数值类型
            double animationDuration = 3.0;
            if (animationDurationObj instanceof Number) {
                animationDuration = ((Number) animationDurationObj).doubleValue();
            }

            int soundVolume = 50;
            if (soundVolumeObj instanceof Number) {
                soundVolume = ((Number) soundVolumeObj).intValue();
            }

            // 确保所有值都不为null，提供安全的默认值
            String safeBackgroundImage = (backgroundImage != null) ? backgroundImage.trim() : "";
            String safeLeaderboardBg = (leaderboardBackgroundImage != null) ? leaderboardBackgroundImage.trim() : "";
            boolean safeOpeningAnimation = (enableOpeningAnimation != null) ? enableOpeningAnimation : true;
            String safeAnimationStyle = (animationStyle != null && !animationStyle.trim().isEmpty())
                    ? animationStyle.trim()
                    : "classic";
            String safeCustomUrl = (customAnimationUrl != null) ? customAnimationUrl.trim() : "";
            boolean safeSoundEffects = (enableSoundEffects != null) ? enableSoundEffects : true;
            String safeOpeningSound = (openingSoundUrl != null) ? openingSoundUrl.trim() : "";
            String safeWinningSound = (winningSoundUrl != null) ? winningSoundUrl.trim() : "";

            // 验证动画持续时间
            if (animationDuration < 500 || animationDuration > 10000) {
                animationDuration = 3000;
            }

            // 验证音量
            if (soundVolume < 0 || soundVolume > 100) {
                soundVolume = 50;
            }

            // 验证动画样式
            if (!safeAnimationStyle.matches("^(classic|modern|custom)$")) {
                safeAnimationStyle = "classic";
            }

            // 保存背景设置
            plugin.getConfig().set("website.background-image", safeBackgroundImage);
            plugin.getConfig().set("website.leaderboard-background-image", safeLeaderboardBg);

            // 保存动画设置
            plugin.getConfig().set("website.opening-animation.enabled", safeOpeningAnimation);
            plugin.getConfig().set("website.opening-animation.duration", animationDuration);
            plugin.getConfig().set("website.opening-animation.style", safeAnimationStyle);
            plugin.getConfig().set("website.opening-animation.custom-url", safeCustomUrl);

            // 保存音效设置
            plugin.getConfig().set("website.sound-effects.enabled", safeSoundEffects);
            plugin.getConfig().set("website.sound-effects.opening-sound", safeOpeningSound);
            plugin.getConfig().set("website.sound-effects.winning-sound", safeWinningSound);
            plugin.getConfig().set("website.sound-effects.volume", soundVolume);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("界面设置保存成功: 动画=" + safeOpeningAnimation +
                        ", 样式=" + safeAnimationStyle + ", 音效=" + safeSoundEffects + ", 音量=" + soundVolume);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存界面配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "界面设置保存成功");

            plugin.getLogger().info("管理员更新了界面设置");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存界面设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存界面设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理封禁设置保存
     */
    private void handleSavePunishmentSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取参数
            String punishmentBackgroundImage = (String) request.get("punishment_background_image");
            String adminLogoUrl = (String) request.get("admin_logo_url");
            String userLogoUrl = (String) request.get("user_logo_url");

            // 获取透明度设置
            int punishmentBackgroundOpacity = 30; // 默认值
            try {
                Object opacityObj = request.get("punishment_background_opacity");
                if (opacityObj instanceof Number) {
                    punishmentBackgroundOpacity = ((Number) opacityObj).intValue();
                }
            } catch (Exception e) {
                punishmentBackgroundOpacity = 30;
            }

            // 安全处理URL（防止XSS）
            String safePunishmentBg = (punishmentBackgroundImage != null)
                    ? punishmentBackgroundImage.trim().replaceAll("[<>\"']", "")
                    : "";
            String safeAdminLogo = (adminLogoUrl != null) ? adminLogoUrl.trim().replaceAll("[<>\"']", "") : "";
            String safeUserLogo = (userLogoUrl != null) ? userLogoUrl.trim().replaceAll("[<>\"']", "") : "";

            // 验证透明度范围
            punishmentBackgroundOpacity = Math.max(0, Math.min(80, punishmentBackgroundOpacity));

            // 保存到配置文件
            plugin.getConfig().set("website.punishment-background-image", safePunishmentBg);
            plugin.getConfig().set("web-server.admin-logo-url", safeAdminLogo);
            plugin.getConfig().set("web-server.user-logo-url", safeUserLogo);
            plugin.getConfig().set("website.punishment-background-opacity", punishmentBackgroundOpacity);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("封禁设置保存成功");
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存封禁配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "封禁设置保存成功");

            plugin.getLogger().info("管理员更新了封禁设置");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存封禁设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存封禁设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理背景图片上传
     */
    private void handleUploadBackgroundImage(HttpExchange exchange, JSONObject response) {
        try {
            // 验证管理员权限
            String query = exchange.getRequestURI().getQuery();
            String apiKey = null;
            String type = null;
            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2) {
                        if ("api_key".equals(keyValue[0])) {
                            apiKey = keyValue[1];
                        } else if ("type".equals(keyValue[0])) {
                            type = keyValue[1];
                        }
                    }
                }
            }

            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 创建web目录
            File webDir = new File(plugin.getDataFolder(), "web");
            if (!webDir.exists()) {
                webDir.mkdirs();
            }

            // 读取上传的文件数据
            String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
            if (contentType == null || !contentType.startsWith("multipart/form-data")) {
                response.put("success", false);
                response.put("message", "请使用multipart/form-data格式上传文件");
                return;
            }

            // 解析multipart数据
            InputStream inputStream = exchange.getRequestBody();
            byte[] requestData = readAllBytes(inputStream);

            // 简单的multipart解析
            String boundary = extractBoundary(contentType);
            if (boundary == null) {
                response.put("success", false);
                response.put("message", "无法解析文件上传数据");
                return;
            }

            FileUploadResult uploadResult = parseMultipartData(requestData, boundary);
            if (uploadResult == null || uploadResult.fileData == null) {
                response.put("success", false);
                response.put("message", "未找到上传的文件");
                return;
            }

            // 验证文件类型
            String fileName = uploadResult.fileName;
            if (fileName == null || !isValidImageFile(fileName)) {
                response.put("success", false);
                response.put("message", "只支持JPG、PNG、GIF格式的图片文件");
                return;
            }

            // 验证文件大小（10MB）
            if (uploadResult.fileData.length > 10 * 1024 * 1024) {
                response.put("success", false);
                response.put("message", "文件大小不能超过10MB");
                return;
            }

            // 生成文件名
            String fileExtension = getFileExtension(fileName);
            String newFileName;
            String configKey;

            if (type != null && type.equals("leaderboard")) {
                newFileName = "leaderboard-bg" + fileExtension;
                configKey = "website.leaderboard-background-image";
            } else if (type != null && type.equals("login")) {
                newFileName = "login-bg" + fileExtension;
                configKey = "web-server.login-background-image";
            } else if (type != null && type.equals("shop")) {
                newFileName = "shop-bg" + fileExtension;
                configKey = "website.shop-background-image";
            } else if (type != null && type.equals("homepage")) {
                newFileName = "homepage-bg" + fileExtension;
                configKey = "website.homepage.background-image";
            } else if (type != null && type.equals("punishment")) {
                newFileName = "punishment-bg" + fileExtension;
                configKey = "website.punishment-background-image";
            } else if (type != null && type.equals("admin")) {
                newFileName = "admin-bg" + fileExtension;
                configKey = "web-server.admin-background-image";
            } else {
                newFileName = "lottery-bg" + fileExtension;
                configKey = "website.background-image";
            }

            File targetFile = new File(webDir, newFileName);

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(uploadResult.fileData);
            }

            // 更新配置
            String fileUrl = "/static/" + newFileName;
            plugin.getConfig().set(configKey, fileUrl);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("背景图片配置保存成功: " + configKey + " = " + fileUrl);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存背景图片配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "文件上传成功");
            response.put("file_url", fileUrl);
            response.put("file_name", newFileName);

            plugin.getLogger().info("管理员上传了背景图片: " + newFileName);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "上传文件失败: " + e.getMessage());
            plugin.getLogger().severe("处理文件上传时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 读取InputStream的所有字节
     */
    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[8192];
        int bytesRead;
        java.io.ByteArrayOutputStream output = new java.io.ByteArrayOutputStream();
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            output.write(buffer, 0, bytesRead);
        }
        return output.toByteArray();
    }

    /**
     * 从Content-Type中提取boundary
     */
    private String extractBoundary(String contentType) {
        plugin.getLogger().info("Content-Type: " + contentType);
        String[] parts = contentType.split(";");
        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("boundary=")) {
                String boundary = part.substring("boundary=".length());
                // 移除可能的引号
                if (boundary.startsWith("\"") && boundary.endsWith("\"")) {
                    boundary = boundary.substring(1, boundary.length() - 1);
                }
                plugin.getLogger().info("提取的boundary: " + boundary);
                return boundary;
            }
        }
        plugin.getLogger().warning("未找到boundary");
        return null;
    }

    /**
     * 解析multipart数据
     */
    private FileUploadResult parseMultipartData(byte[] data, String boundary) {
        try {
            plugin.getLogger().info("开始解析multipart数据，数据长度: " + data.length + ", boundary: " + boundary);

            // 查找头部分隔符
            byte[] headerSeparator = "\r\n\r\n".getBytes(StandardCharsets.UTF_8);
            byte[] boundaryBytes = ("--" + boundary).getBytes(StandardCharsets.UTF_8);

            // 找到第一个boundary
            int boundaryStart = indexOf(data, boundaryBytes);
            if (boundaryStart == -1) {
                plugin.getLogger().warning("未找到起始boundary");
                return null;
            }

            // 跳过boundary和\r\n
            int headerStart = boundaryStart + boundaryBytes.length;
            if (headerStart + 2 < data.length && data[headerStart] == '\r' && data[headerStart + 1] == '\n') {
                headerStart += 2;
            }

            // 找到头部结束位置（\r\n\r\n）
            int headerEnd = indexOf(data, headerSeparator, headerStart);
            if (headerEnd == -1) {
                plugin.getLogger().warning("未找到头部结束标记");
                return null;
            }

            // 提取头部
            byte[] headerBytes = new byte[headerEnd - headerStart];
            System.arraycopy(data, headerStart, headerBytes, 0, headerBytes.length);
            String headerStr = new String(headerBytes, StandardCharsets.UTF_8);

            plugin.getLogger().info("头部内容: " + headerStr);

            // 解析文件名
            String fileName = null;
            String[] headerLines = headerStr.split("\r\n");
            for (String line : headerLines) {
                if (line.contains("Content-Disposition:") && line.contains("filename=")) {
                    int start = line.indexOf("filename=\"");
                    if (start != -1) {
                        start += 10; // "filename=\"".length()
                        int end = line.indexOf("\"", start);
                        if (end > start) {
                            fileName = line.substring(start, end);
                            plugin.getLogger().info("找到文件名: " + fileName);
                            break;
                        }
                    }
                }
            }

            if (fileName == null) {
                plugin.getLogger().warning("未找到文件名");
                return null;
            }

            // 文件数据开始位置
            int fileDataStart = headerEnd + headerSeparator.length;

            // 找到下一个boundary（文件数据结束位置）
            int fileDataEnd = indexOf(data, ("\r\n--" + boundary).getBytes(StandardCharsets.UTF_8), fileDataStart);
            if (fileDataEnd == -1) {
                // 尝试查找结束boundary
                fileDataEnd = indexOf(data, ("\r\n--" + boundary + "--").getBytes(StandardCharsets.UTF_8),
                        fileDataStart);
            }
            if (fileDataEnd == -1) {
                plugin.getLogger().warning("未找到文件数据结束位置");
                return null;
            }

            // 提取文件数据
            int fileDataLength = fileDataEnd - fileDataStart;
            if (fileDataLength <= 0) {
                plugin.getLogger().warning("文件数据长度无效: " + fileDataLength);
                return null;
            }

            byte[] fileData = new byte[fileDataLength];
            System.arraycopy(data, fileDataStart, fileData, 0, fileDataLength);

            plugin.getLogger().info("成功提取文件数据，大小: " + fileData.length + " 字节");
            return new FileUploadResult(fileName, fileData);

        } catch (Exception e) {
            plugin.getLogger().warning("解析multipart数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 在字节数组中查找子数组的位置
     */
    private int indexOf(byte[] array, byte[] target) {
        return indexOf(array, target, 0);
    }

    private int indexOf(byte[] array, byte[] target, int start) {
        if (target.length == 0)
            return start;

        for (int i = start; i <= array.length - target.length; i++) {
            boolean found = true;
            for (int j = 0; j < target.length; j++) {
                if (array[i + j] != target[j]) {
                    found = false;
                    break;
                }
            }
            if (found)
                return i;
        }
        return -1;
    }

    /**
     * 处理Logo文件上传
     */
    private void handleUploadLogo(HttpExchange exchange, JSONObject response) {
        try {
            // 验证管理员权限
            String query = exchange.getRequestURI().getQuery();
            String apiKey = null;
            String type = null;
            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2) {
                        if ("api_key".equals(keyValue[0])) {
                            apiKey = keyValue[1];
                        } else if ("type".equals(keyValue[0])) {
                            type = keyValue[1];
                        }
                    }
                }
            }

            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 创建web目录
            File webDir = new File(plugin.getDataFolder(), "web");
            if (!webDir.exists()) {
                webDir.mkdirs();
            }

            // 读取上传的文件数据
            String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
            if (contentType == null || !contentType.startsWith("multipart/form-data")) {
                response.put("success", false);
                response.put("message", "请使用multipart/form-data格式上传文件");
                return;
            }

            // 解析multipart数据
            InputStream inputStream = exchange.getRequestBody();
            byte[] requestData = readAllBytes(inputStream);

            // 简单的multipart解析
            String boundary = extractBoundary(contentType);
            if (boundary == null) {
                response.put("success", false);
                response.put("message", "无法解析文件上传数据");
                return;
            }

            FileUploadResult uploadResult = parseMultipartData(requestData, boundary);
            if (uploadResult == null || uploadResult.fileData == null) {
                response.put("success", false);
                response.put("message", "未找到上传的文件");
                return;
            }

            // 验证文件类型（Logo支持更多格式）
            String fileName = uploadResult.fileName;
            if (fileName == null || !isValidLogoFile(fileName)) {
                response.put("success", false);
                response.put("message", "只支持JPG、PNG、GIF、SVG格式的Logo文件");
                return;
            }

            // 验证文件大小（2MB）
            if (uploadResult.fileData.length > 2 * 1024 * 1024) {
                response.put("success", false);
                response.put("message", "Logo文件大小不能超过2MB");
                return;
            }

            // 生成文件名
            String fileExtension = getFileExtension(fileName);
            String newFileName;
            String configKey;

            if ("admin".equals(type)) {
                newFileName = "admin-logo" + fileExtension;
                configKey = "web-server.admin-logo-url";
            } else if ("user".equals(type)) {
                newFileName = "user-logo" + fileExtension;
                configKey = "web-server.user-logo-url";
            } else {
                // 默认为登录Logo（向后兼容）
                newFileName = "login-logo" + fileExtension;
                configKey = "web-server.login-logo-url";
            }

            File targetFile = new File(webDir, newFileName);

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(uploadResult.fileData);
            }

            // 更新配置
            String fileUrl = "/static/" + newFileName;
            plugin.getConfig().set(configKey, fileUrl);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("Logo文件配置保存成功: " + configKey + " = " + fileUrl);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存Logo文件配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "Logo文件上传成功");
            response.put("file_url", fileUrl);
            response.put("file_name", newFileName);

            plugin.getLogger().info("管理员上传了Logo文件: " + newFileName);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "上传Logo文件失败: " + e.getMessage());
            plugin.getLogger().severe("处理Logo文件上传时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理音效文件上传
     */
    private void handleUploadSoundFile(HttpExchange exchange, JSONObject response) {
        try {
            // 验证管理员权限
            String query = exchange.getRequestURI().getQuery();
            String apiKey = null;
            String type = null;
            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2) {
                        if ("api_key".equals(keyValue[0])) {
                            apiKey = keyValue[1];
                        } else if ("type".equals(keyValue[0])) {
                            type = keyValue[1];
                        }
                    }
                }
            }

            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 创建web目录
            File webDir = new File(plugin.getDataFolder(), "web");
            if (!webDir.exists()) {
                webDir.mkdirs();
            }

            // 读取上传的文件数据
            String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
            if (contentType == null || !contentType.startsWith("multipart/form-data")) {
                response.put("success", false);
                response.put("message", "请使用multipart/form-data格式上传文件");
                return;
            }

            // 解析multipart数据
            InputStream inputStream = exchange.getRequestBody();
            byte[] requestData = readAllBytes(inputStream);

            // 简单的multipart解析
            String boundary = extractBoundary(contentType);
            if (boundary == null) {
                response.put("success", false);
                response.put("message", "无法解析文件上传数据");
                return;
            }

            FileUploadResult uploadResult = parseMultipartData(requestData, boundary);
            if (uploadResult == null || uploadResult.fileData == null) {
                response.put("success", false);
                response.put("message", "未找到上传的文件");
                return;
            }

            // 验证文件类型
            String fileName = uploadResult.fileName;
            if (fileName == null || !isValidSoundFile(fileName)) {
                response.put("success", false);
                response.put("message", "只支持MP3、WAV、OGG格式的音效文件");
                return;
            }

            // 验证文件大小（2MB）
            if (uploadResult.fileData.length > 2 * 1024 * 1024) {
                response.put("success", false);
                response.put("message", "音效文件大小不能超过2MB");
                return;
            }

            // 生成文件名
            String fileExtension = getFileExtension(fileName);
            String newFileName = (type != null && type.equals("winning") ? "winning-sound" : "opening-sound")
                    + fileExtension;
            File targetFile = new File(webDir, newFileName);

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(uploadResult.fileData);
            }

            // 更新配置
            String configKey = type != null && type.equals("winning") ? "website.sound-effects.winning-sound"
                    : "website.sound-effects.opening-sound";
            String fileUrl = "/static/" + newFileName;
            plugin.getConfig().set(configKey, fileUrl);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("音效文件配置保存成功: " + configKey + " = " + fileUrl);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存音效文件配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "音效文件上传成功");
            response.put("file_url", fileUrl);
            response.put("file_name", newFileName);

            plugin.getLogger().info("管理员上传了音效文件: " + newFileName);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "上传音效文件失败: " + e.getMessage());
            plugin.getLogger().severe("处理音效文件上传时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理登录Logo上传
     */
    private void handleUploadLoginLogo(HttpExchange exchange, JSONObject response) {
        try {
            // 验证管理员权限
            String query = exchange.getRequestURI().getQuery();
            String apiKey = null;
            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2 && "api_key".equals(keyValue[0])) {
                        apiKey = keyValue[1];
                        break;
                    }
                }
            }

            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 创建web目录
            File webDir = new File(plugin.getDataFolder(), "web");
            if (!webDir.exists()) {
                webDir.mkdirs();
            }

            // 读取上传的文件数据
            String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
            if (contentType == null || !contentType.startsWith("multipart/form-data")) {
                response.put("success", false);
                response.put("message", "请使用multipart/form-data格式上传文件");
                return;
            }

            // 解析multipart数据
            InputStream inputStream = exchange.getRequestBody();
            byte[] requestData = readAllBytes(inputStream);

            // 简单的multipart解析
            String boundary = extractBoundary(contentType);
            if (boundary == null) {
                response.put("success", false);
                response.put("message", "无法解析文件上传数据");
                return;
            }

            FileUploadResult uploadResult = parseMultipartData(requestData, boundary);
            if (uploadResult == null || uploadResult.fileData == null) {
                response.put("success", false);
                response.put("message", "未找到上传的文件");
                return;
            }

            // 验证文件类型
            String fileName = uploadResult.fileName;
            if (fileName == null || !isValidLogoFile(fileName)) {
                response.put("success", false);
                response.put("message", "只支持JPG、PNG、GIF、SVG格式的Logo文件");
                return;
            }

            // 验证文件大小（2MB）
            if (uploadResult.fileData.length > 2 * 1024 * 1024) {
                response.put("success", false);
                response.put("message", "Logo文件大小不能超过2MB");
                return;
            }

            // 生成文件名
            String fileExtension = getFileExtension(fileName);
            String newFileName = "login-logo" + fileExtension;
            File targetFile = new File(webDir, newFileName);

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(uploadResult.fileData);
            }

            // 更新配置
            String fileUrl = "/static/" + newFileName;
            plugin.getConfig().set("web-server.login-logo-url", fileUrl);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("Logo文件配置保存成功: " + fileUrl);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存Logo文件配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "Logo文件上传成功");
            response.put("file_url", fileUrl);
            response.put("file_name", newFileName);

            plugin.getLogger().info("管理员上传了登录Logo文件: " + newFileName);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "上传Logo文件失败: " + e.getMessage());
            plugin.getLogger().severe("处理Logo文件上传时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理动画文件上传
     */
    private void handleUploadAnimationFile(HttpExchange exchange, JSONObject response) {
        try {
            // 验证管理员权限
            String query = exchange.getRequestURI().getQuery();
            String apiKey = null;
            if (query != null) {
                String[] params = query.split("&");
                for (String param : params) {
                    String[] keyValue = param.split("=");
                    if (keyValue.length == 2 && "api_key".equals(keyValue[0])) {
                        apiKey = keyValue[1];
                        break;
                    }
                }
            }

            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 创建web目录
            File webDir = new File(plugin.getDataFolder(), "web");
            if (!webDir.exists()) {
                webDir.mkdirs();
            }

            // 读取上传的文件数据
            String contentType = exchange.getRequestHeaders().getFirst("Content-Type");
            if (contentType == null || !contentType.startsWith("multipart/form-data")) {
                response.put("success", false);
                response.put("message", "请使用multipart/form-data格式上传文件");
                return;
            }

            // 解析multipart数据
            InputStream inputStream = exchange.getRequestBody();
            byte[] requestData = readAllBytes(inputStream);

            // 简单的multipart解析
            String boundary = extractBoundary(contentType);
            if (boundary == null) {
                response.put("success", false);
                response.put("message", "无法解析文件上传数据");
                return;
            }

            FileUploadResult uploadResult = parseMultipartData(requestData, boundary);
            if (uploadResult == null || uploadResult.fileData == null) {
                response.put("success", false);
                response.put("message", "未找到上传的文件");
                return;
            }

            // 验证文件类型
            String fileName = uploadResult.fileName;
            if (fileName == null || !isValidAnimationFile(fileName)) {
                response.put("success", false);
                response.put("message", "只支持GIF、MP4、WebM格式的动画文件");
                return;
            }

            // 验证文件大小（5MB）
            if (uploadResult.fileData.length > 5 * 1024 * 1024) {
                response.put("success", false);
                response.put("message", "动画文件大小不能超过5MB");
                return;
            }

            // 生成文件名（添加时间戳避免缓存问题）
            String fileExtension = getFileExtension(fileName);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String newFileName = "custom-animation-" + timestamp + fileExtension;
            File targetFile = new File(webDir, newFileName);

            // 删除旧的自定义动画文件
            String oldCustomUrl = plugin.getConfig().getString("website.opening-animation.custom-url", "");
            if (!oldCustomUrl.isEmpty() && oldCustomUrl.startsWith("/static/custom-animation")) {
                String oldFileName = oldCustomUrl.substring("/static/".length());
                File oldFile = new File(webDir, oldFileName);
                if (oldFile.exists()) {
                    oldFile.delete();
                    plugin.getLogger().info("删除旧的自定义动画文件: " + oldFileName);
                }
            }

            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(targetFile)) {
                fos.write(uploadResult.fileData);
            }

            // 更新配置
            String fileUrl = "/static/" + newFileName;
            plugin.getConfig().set("website.opening-animation.custom-url", fileUrl);
            plugin.getConfig().set("website.opening-animation.style", "custom");

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("动画文件配置保存成功: " + fileUrl);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存动画文件配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "动画文件上传成功");
            response.put("file_url", fileUrl);
            response.put("file_name", newFileName);

            plugin.getLogger().info("管理员上传了自定义动画文件: " + newFileName);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "上传动画文件失败: " + e.getMessage());
            plugin.getLogger().severe("处理动画文件上传时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 验证是否为有效的图片文件
     */
    private boolean isValidImageFile(String fileName) {
        if (fileName == null)
            return false;
        String lowerName = fileName.toLowerCase();
        return lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg") ||
                lowerName.endsWith(".png") || lowerName.endsWith(".gif");
    }

    /**
     * 验证是否为有效的音效文件
     */
    private boolean isValidSoundFile(String fileName) {
        if (fileName == null)
            return false;
        String lowerName = fileName.toLowerCase();
        return lowerName.endsWith(".mp3") || lowerName.endsWith(".wav") || lowerName.endsWith(".ogg");
    }

    /**
     * 验证是否为有效的动画文件
     */
    private boolean isValidAnimationFile(String fileName) {
        if (fileName == null)
            return false;
        String lowerName = fileName.toLowerCase();
        return lowerName.endsWith(".gif") || lowerName.endsWith(".mp4") || lowerName.endsWith(".webm");
    }

    /**
     * 验证是否为有效的Logo文件
     */
    private boolean isValidLogoFile(String fileName) {
        if (fileName == null)
            return false;
        String lowerName = fileName.toLowerCase();
        return lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg") ||
                lowerName.endsWith(".png") || lowerName.endsWith(".gif") || lowerName.endsWith(".svg");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null)
            return ".jpg";
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot > 0) {
            return fileName.substring(lastDot);
        }
        return ".jpg";
    }

    /**
     * 文件上传结果类
     */
    private static class FileUploadResult {
        final String fileName;
        final byte[] fileData;

        FileUploadResult(String fileName, byte[] fileData) {
            this.fileName = fileName;
            this.fileData = fileData;
        }
    }

    /**
     * 玩家统计数据类
     */
    private static class PlayerStats {
        String player;
        int wins = 0;
        int draws = 0;

        PlayerStats(String player) {
            this.player = player;
        }
    }

    /**
     * 处理获取用户积分
     */
    private void handleGetUserPoints(JSONObject request, JSONObject response) {
        try {
            String username = (String) request.get("username");

            if (username == null || username.isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            int points = plugin.getPointsManager().getPlayerPoints(username);

            response.put("success", true);
            response.put("points", points);
            response.put("username", username);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取积分失败: " + e.getMessage());
            plugin.getLogger().severe("获取用户积分时出错: " + e.getMessage());
        }
    }

    /**
     * 处理获取商店物品
     */
    private void handleGetShopItems(JSONObject request, JSONObject response) {
        try {
            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            JSONArray items = new JSONArray();

            // 从积分商店管理器获取物品列表
            Map<String, cn.acebrand.acekeysystem.points.PointsShopItem> shopItems = plugin.getPointsShopManager()
                    .getEnabledItems();
            for (Map.Entry<String, cn.acebrand.acekeysystem.points.PointsShopItem> entry : shopItems.entrySet()) {
                cn.acebrand.acekeysystem.points.PointsShopItem item = entry.getValue();
                JSONObject itemObj = new JSONObject();
                itemObj.put("id", item.getId());
                itemObj.put("name", item.getName());
                itemObj.put("description", item.getDescription());
                itemObj.put("cost", item.getCost());
                itemObj.put("icon", item.getIcon());
                itemObj.put("stock", item.getStock());
                itemObj.put("maxPurchasePerPlayer", item.getMaxPurchasePerPlayer());
                itemObj.put("resetIntervalHours", item.getResetIntervalHours());
                items.add(itemObj);
            }

            response.put("success", true);
            response.put("items", items);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取商店物品失败: " + e.getMessage());
            plugin.getLogger().severe("获取商店物品时出错: " + e.getMessage());
        }
    }

    /**
     * 处理购买物品
     */
    private void handlePurchaseItem(JSONObject request, JSONObject response) {
        try {
            String username = (String) request.get("username");
            String itemId = (String) request.get("item_id");

            // 获取数量参数，默认为1
            int quantity = 1;
            Object quantityObj = request.get("quantity");
            if (quantityObj instanceof Number) {
                quantity = ((Number) quantityObj).intValue();
            }

            if (username == null || username.isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            if (itemId == null || itemId.isEmpty()) {
                response.put("success", false);
                response.put("message", "物品ID不能为空");
                return;
            }

            if (quantity <= 0 || quantity > 99) {
                response.put("success", false);
                response.put("message", "购买数量必须在1-99之间");
                return;
            }

            // 使用积分商店管理器处理购买
            cn.acebrand.acekeysystem.points.PointsShopManager.PurchaseResult result = plugin.getPointsShopManager()
                    .purchaseItem(username, itemId, quantity);

            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "购买失败: " + e.getMessage());
            plugin.getLogger().severe("处理购买请求时出错: " + e.getMessage());
        }
    }

    /**
     * 处理获取管理员商店物品
     */
    private void handleGetAdminShopItems(JSONObject request, JSONObject response) {
        try {
            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            JSONArray items = new JSONArray();

            // 从积分商店管理器获取所有物品列表（包括禁用的）
            Map<String, cn.acebrand.acekeysystem.points.PointsShopItem> shopItems = plugin.getPointsShopManager()
                    .getAllItems();
            for (Map.Entry<String, cn.acebrand.acekeysystem.points.PointsShopItem> entry : shopItems.entrySet()) {
                cn.acebrand.acekeysystem.points.PointsShopItem item = entry.getValue();
                JSONObject itemObj = new JSONObject();
                itemObj.put("id", item.getId());
                itemObj.put("name", item.getName());
                itemObj.put("description", item.getDescription());
                itemObj.put("cost", item.getCost());
                itemObj.put("icon", item.getIcon());
                itemObj.put("stock", item.getStock());
                itemObj.put("maxPurchasePerPlayer", item.getMaxPurchasePerPlayer());
                itemObj.put("resetIntervalHours", item.getResetIntervalHours());
                itemObj.put("enabled", item.isEnabled());

                JSONArray commandsArray = new JSONArray();
                if (item.getCommands() != null) {
                    for (String command : item.getCommands()) {
                        commandsArray.add(command);
                    }
                }
                itemObj.put("commands", commandsArray);

                items.add(itemObj);
            }

            response.put("success", true);
            response.put("items", items);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取商店物品失败: " + e.getMessage());
            plugin.getLogger().severe("获取管理员商店物品时出错: " + e.getMessage());
        }
    }

    /**
     * 处理保存商店物品
     */
    private void handleSaveShopItem(JSONObject request, JSONObject response) {
        try {
            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            String itemId = (String) request.get("item_id");
            String name = (String) request.get("name");
            String description = (String) request.get("description");

            // 安全地转换数值类型，处理Long到Integer的转换
            Integer cost = null;
            Object costObj = request.get("cost");
            if (costObj instanceof Number) {
                cost = ((Number) costObj).intValue();
            }

            String icon = (String) request.get("icon");

            Integer stock = null;
            Object stockObj = request.get("stock");
            if (stockObj instanceof Number) {
                stock = ((Number) stockObj).intValue();
            }

            Integer maxPurchase = null;
            Object maxPurchaseObj = request.get("max_purchase_per_player");
            if (maxPurchaseObj instanceof Number) {
                maxPurchase = ((Number) maxPurchaseObj).intValue();
            }

            Integer resetInterval = null;
            Object resetIntervalObj = request.get("reset_interval_hours");
            if (resetIntervalObj instanceof Number) {
                resetInterval = ((Number) resetIntervalObj).intValue();
            }

            Boolean enabled = (Boolean) request.get("enabled");
            JSONArray commandsArray = (JSONArray) request.get("commands");

            // 验证必填字段
            if (name == null || name.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "物品名称不能为空");
                return;
            }

            if (description == null || description.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "物品描述不能为空");
                return;
            }

            if (cost == null || cost < 1) {
                response.put("success", false);
                response.put("message", "积分消耗必须大于0");
                return;
            }

            // 创建或更新物品
            cn.acebrand.acekeysystem.points.PointsShopItem item = new cn.acebrand.acekeysystem.points.PointsShopItem();

            // 如果是新物品，生成ID
            if (itemId == null || itemId.trim().isEmpty()) {
                itemId = "item_" + System.currentTimeMillis();
            }

            item.setId(itemId);
            item.setName(name.trim());
            item.setDescription(description.trim());
            item.setCost(cost);
            item.setIcon(icon != null ? icon.trim() : "📦");
            item.setStock(stock != null ? stock : -1);
            item.setMaxPurchasePerPlayer(maxPurchase != null ? maxPurchase : -1);
            item.setResetIntervalHours(resetInterval != null ? resetInterval : -1);
            item.setEnabled(enabled != null ? enabled : true);

            // 处理命令列表
            List<String> commands = new ArrayList<>();
            if (commandsArray != null) {
                for (Object cmdObj : commandsArray) {
                    if (cmdObj instanceof String) {
                        String cmd = ((String) cmdObj).trim();
                        if (!cmd.isEmpty()) {
                            commands.add(cmd);
                        }
                    }
                }
            }
            item.setCommands(commands);

            // 保存物品
            plugin.getPointsShopManager().addOrUpdateItem(item);

            response.put("success", true);
            response.put("message", "保存成功");
            response.put("item_id", itemId);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存失败: " + e.getMessage());
            plugin.getLogger().severe("保存商店物品时出错: " + e.getMessage());
        }
    }

    /**
     * 处理删除商店物品
     */
    private void handleDeleteShopItem(JSONObject request, JSONObject response) {
        try {
            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            String itemId = (String) request.get("item_id");

            if (itemId == null || itemId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "物品ID不能为空");
                return;
            }

            boolean success = plugin.getPointsShopManager().removeItem(itemId);

            if (success) {
                response.put("success", true);
                response.put("message", "删除成功");
            } else {
                response.put("success", false);
                response.put("message", "物品不存在或删除失败");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "删除失败: " + e.getMessage());
            plugin.getLogger().severe("删除商店物品时出错: " + e.getMessage());
        }
    }

    /**
     * 处理切换商店物品状态
     */
    private void handleToggleShopItemStatus(JSONObject request, JSONObject response) {
        try {
            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            String itemId = (String) request.get("item_id");
            Boolean enabled = (Boolean) request.get("enabled");

            if (itemId == null || itemId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "物品ID不能为空");
                return;
            }

            if (enabled == null) {
                response.put("success", false);
                response.put("message", "状态参数不能为空");
                return;
            }

            // 获取现有物品
            cn.acebrand.acekeysystem.points.PointsShopItem item = plugin.getPointsShopManager().getItem(itemId);
            if (item == null) {
                response.put("success", false);
                response.put("message", "物品不存在");
                return;
            }

            // 更新状态
            item.setEnabled(enabled);
            plugin.getPointsShopManager().addOrUpdateItem(item);

            response.put("success", true);
            response.put("message", enabled ? "物品已启用" : "物品已禁用");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "切换状态失败: " + e.getMessage());
            plugin.getLogger().severe("切换商店物品状态时出错: " + e.getMessage());
        }
    }

    /**
     * 处理获取玩家购买信息
     */
    private void handleGetPlayerPurchaseInfo(JSONObject request, JSONObject response) {
        try {
            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            String username = (String) request.get("username");
            if (username == null || username.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            // 获取玩家UUID
            Player player = plugin.getServer().getPlayer(username);
            UUID playerUuid;
            if (player != null) {
                playerUuid = player.getUniqueId();
            } else {
                // 如果玩家不在线，尝试从离线玩家获取UUID
                OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(username);
                if (offlinePlayer.hasPlayedBefore()) {
                    playerUuid = offlinePlayer.getUniqueId();
                } else {
                    response.put("success", false);
                    response.put("message", "玩家不存在");
                    return;
                }
            }

            JSONObject purchaseInfo = new JSONObject();

            // 获取所有商店物品的购买信息
            Map<String, cn.acebrand.acekeysystem.points.PointsShopItem> shopItems = plugin.getPointsShopManager()
                    .getAllItems();
            for (Map.Entry<String, cn.acebrand.acekeysystem.points.PointsShopItem> entry : shopItems.entrySet()) {
                String itemId = entry.getKey();
                cn.acebrand.acekeysystem.points.PointsShopItem item = entry.getValue();

                JSONObject itemPurchaseInfo = new JSONObject();

                // 获取已购买次数
                int purchasedCount = plugin.getPointsShopManager().getPlayerPurchaseCount(playerUuid, itemId);
                itemPurchaseInfo.put("purchased_count", purchasedCount);

                // 获取剩余购买次数
                int remainingPurchases = plugin.getPointsShopManager().getRemainingPurchases(playerUuid, itemId);
                itemPurchaseInfo.put("remaining_purchases", remainingPurchases);

                // 获取下次重置时间
                long nextResetTime = plugin.getPointsShopManager().getNextResetTime(playerUuid, itemId);
                itemPurchaseInfo.put("next_reset_time", nextResetTime);

                // 计算重置倒计时（秒）
                if (nextResetTime > 0) {
                    long currentTime = System.currentTimeMillis();
                    long resetCountdown = Math.max(0, (nextResetTime - currentTime) / 1000);
                    itemPurchaseInfo.put("reset_countdown_seconds", resetCountdown);
                } else {
                    itemPurchaseInfo.put("reset_countdown_seconds", -1);
                }

                purchaseInfo.put(itemId, itemPurchaseInfo);
            }

            response.put("success", true);
            response.put("purchase_info", purchaseInfo);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取购买信息失败: " + e.getMessage());
            plugin.getLogger().severe("获取玩家购买信息时出错: " + e.getMessage());
        }
    }

    /**
     * 处理生成绑定码请求
     */
    private void handleGenerateBindCode(JSONObject request, JSONObject response) {
        try {
            // 验证API密钥
            String apiKey = (String) request.get("api_key");
            String expectedKey = plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE");
            if (!expectedKey.equals(apiKey)) {
                response.put("success", false);
                response.put("message", "API密钥无效");
                return;
            }

            // 生成会话ID（如果没有提供）
            String sessionId = (String) request.get("session_id");
            if (sessionId == null || sessionId.trim().isEmpty()) {
                sessionId = "session_" + System.currentTimeMillis() + "_"
                        + java.util.UUID.randomUUID().toString().substring(0, 8);
            }

            // 生成唯一的绑定码
            String bindCode = generateUniqueBindCode();

            // 存储绑定码（5分钟有效期）
            long expiryTime = System.currentTimeMillis() + (5 * 60 * 1000); // 5分钟
            plugin.getBindingDataManager().storeBindCode(bindCode, sessionId, expiryTime);
            plugin.getBindingDataManager().storeSession(sessionId, bindCode);

            response.put("success", true);
            response.put("bind_code", bindCode);
            response.put("session_id", sessionId);
            response.put("expiry_time", expiryTime);
            response.put("message", "绑定码生成成功");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "生成绑定码失败: " + e.getMessage());
            plugin.getLogger().severe("生成绑定码时出错: " + e.getMessage());
        }
    }

    /**
     * 处理验证绑定码请求（游戏内使用）
     */
    private void handleVerifyBindCode(JSONObject request, JSONObject response) {
        try {
            String bindCode = (String) request.get("bind_code");
            String username = (String) request.get("username");

            if (bindCode == null || bindCode.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "绑定码不能为空");
                return;
            }

            if (username == null || username.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            bindCode = bindCode.trim();
            username = username.trim();

            // 检查绑定码是否存在
            if (!plugin.getBindingDataManager().bindCodeExists(bindCode)) {
                response.put("success", false);
                response.put("message", "绑定码不存在");
                return;
            }

            // 检查绑定码是否已使用
            if (plugin.getBindingDataManager().isBindCodeUsed(bindCode)) {
                response.put("success", false);
                response.put("message", "绑定码已被使用");
                return;
            }

            // 检查绑定码是否过期
            if (plugin.getBindingDataManager().isBindCodeExpired(bindCode)) {
                response.put("success", false);
                response.put("message", "绑定码已过期");
                return;
            }

            // 获取玩家UUID
            Player player = plugin.getServer().getPlayer(username);
            UUID playerUuid;
            if (player != null) {
                playerUuid = player.getUniqueId();
            } else {
                OfflinePlayer offlinePlayer = plugin.getServer().getOfflinePlayer(username);
                if (offlinePlayer.hasPlayedBefore()) {
                    playerUuid = offlinePlayer.getUniqueId();
                } else {
                    response.put("success", false);
                    response.put("message", "玩家不存在");
                    return;
                }
            }

            // 标记绑定码为已使用
            plugin.getBindingDataManager().markBindCodeAsUsed(bindCode, username, playerUuid);

            // 保存玩家绑定信息
            plugin.getBindingDataManager().storePlayerBinding(playerUuid, username, bindCode);

            response.put("success", true);
            response.put("message", "绑定成功");
            response.put("username", username);
            response.put("player_uuid", playerUuid.toString());

            plugin.getLogger().info("玩家 " + username + " 使用绑定码 " + bindCode + " 绑定成功");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "验证绑定码失败: " + e.getMessage());
            plugin.getLogger().severe("验证绑定码时出错: " + e.getMessage());
        }
    }

    /**
     * 处理获取仪表板统计数据
     */
    private void handleGetDashboardStats(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取卡密统计
            ConfigurationSection keysSection = plugin.getKeysConfig().getConfigurationSection("keys");
            JSONObject keyStats = new JSONObject();

            if (keysSection != null) {
                int total = 0;
                int available = 0;
                int used = 0;
                int assigned = 0;
                int expired = 0;

                for (String key : keysSection.getKeys(false)) {
                    total++;
                    boolean isUsed = keysSection.getBoolean(key + ".used", false);
                    boolean isAssigned = keysSection.getBoolean(key + ".assigned", false);
                    boolean isExpired = keysSection.getBoolean(key + ".expired", false);

                    if (isUsed) {
                        used++;
                    } else if (isExpired) {
                        expired++;
                    } else if (isAssigned) {
                        assigned++;
                    } else {
                        available++;
                    }
                }

                keyStats.put("total", total);
                keyStats.put("available", available);
                keyStats.put("used", used);
                keyStats.put("assigned", assigned);
                keyStats.put("expired", expired);
            } else {
                keyStats.put("total", 0);
                keyStats.put("available", 0);
                keyStats.put("used", 0);
                keyStats.put("assigned", 0);
                keyStats.put("expired", 0);
            }

            // 获取中奖记录数量
            ConfigurationSection winnersSection = plugin.getKeysConfig().getConfigurationSection("winners");
            int winnersCount = 0;
            if (winnersSection != null) {
                winnersCount = winnersSection.getKeys(false).size();
            }

            // 获取服务器状态
            String serverStatus = webServer.isRunning() ? "运行中" : "已停止";

            response.put("success", true);
            response.put("keys", keyStats);
            response.put("winners", winnersCount);
            response.put("server_status", serverStatus);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取仪表板数据失败: " + e.getMessage());
            plugin.getLogger().severe("获取仪表板数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取绑定状态请求
     */
    private void handleGetBindStatus(JSONObject request, JSONObject response) {
        try {
            // 验证API密钥
            String apiKey = (String) request.get("api_key");
            String expectedKey = plugin.getConfig().getString("website.api-key", "MCTV_KEY_2024_SECURE");
            if (!expectedKey.equals(apiKey)) {
                response.put("success", false);
                response.put("message", "API密钥无效");
                return;
            }

            String sessionId = (String) request.get("session_id");
            if (sessionId == null || sessionId.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "会话ID不能为空");
                return;
            }

            // 检查是否有绑定码与此会话关联
            String bindCode = plugin.getBindingDataManager().getSessionBindCode(sessionId);
            if (bindCode == null) {
                response.put("success", false);
                response.put("is_bound", false);
                response.put("message", "未找到绑定信息");
                return;
            }

            // 检查绑定码是否已被使用
            if (plugin.getBindingDataManager().isBindCodeUsed(bindCode)) {
                String username = plugin.getBindingDataManager().getBindCodeUser(bindCode);
                String playerUuidStr = plugin.getBindingDataManager().getBindCodePlayerUuid(bindCode);

                response.put("success", true);
                response.put("is_bound", true);
                response.put("username", username);
                response.put("player_uuid", playerUuidStr);
                response.put("message", "已绑定");

                // 添加绑定时间信息和配置信息
                if (playerUuidStr != null) {
                    try {
                        UUID playerUuid = UUID.fromString(playerUuidStr);
                        long bindTime = plugin.getBindingDataManager().getPlayerBindTime(playerUuid);
                        if (bindTime > 0) {
                            response.put("bind_time", bindTime);
                        }
                    } catch (IllegalArgumentException e) {
                        // 忽略UUID格式错误
                    }
                }

                // 添加配置的时间限制信息
                response.put("unbind_cooldown_config", getUnbindCooldownConfigString());
            } else {
                response.put("success", true);
                response.put("is_bound", false);
                response.put("message", "等待绑定");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取绑定状态失败: " + e.getMessage());
            plugin.getLogger().severe("获取绑定状态时出错: " + e.getMessage());
        }
    }

    /**
     * 处理解绑账户
     */
    private void handleUnbindAccount(JSONObject request, JSONObject response) {
        try {
            String sessionId = (String) request.get("session_id");
            String username = (String) request.get("username");

            // 优先使用用户名，如果没有则尝试通过会话ID查找
            UUID playerUuid = null;
            String actualUsername = null;

            if (username != null && !username.trim().isEmpty()) {
                // 直接通过用户名查找
                actualUsername = username.trim();
                playerUuid = plugin.getServer().getOfflinePlayer(actualUsername).getUniqueId();

                // 检查玩家是否已绑定
                if (!plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
                    response.put("success", false);
                    response.put("message", "该玩家未绑定");
                    return;
                }
            } else if (sessionId != null && !sessionId.isEmpty()) {
                // 通过会话ID查找（兼容旧逻辑）
                String bindCode = plugin.getBindingDataManager().getSessionBindCode(sessionId);
                if (bindCode == null) {
                    response.put("success", false);
                    response.put("message", "未找到绑定信息");
                    return;
                }

                // 检查绑定码是否已被使用
                if (!plugin.getBindingDataManager().isBindCodeUsed(bindCode)) {
                    response.put("success", false);
                    response.put("message", "账户尚未绑定");
                    return;
                }

                // 获取绑定的玩家信息
                actualUsername = plugin.getBindingDataManager().getBindCodeUser(bindCode);
                String playerUuidStr = plugin.getBindingDataManager().getBindCodePlayerUuid(bindCode);

                if (actualUsername == null || playerUuidStr == null) {
                    response.put("success", false);
                    response.put("message", "绑定信息不完整");
                    return;
                }

                try {
                    playerUuid = UUID.fromString(playerUuidStr);
                } catch (IllegalArgumentException e) {
                    response.put("success", false);
                    response.put("message", "玩家UUID格式错误");
                    return;
                }
            } else {
                response.put("success", false);
                response.put("message", "缺少用户名或会话ID");
                return;
            }

            // 检查是否可以解绑（时间限制）
            if (!plugin.getBindingDataManager().canUnbindPlayer(playerUuid)) {
                long bindTime = plugin.getBindingDataManager().getPlayerBindTime(playerUuid);
                String cooldownConfig = getUnbindCooldownConfigString();

                response.put("success", false);
                response.put("message", "绑定后" + cooldownConfig + "内不能解绑");
                response.put("bind_time", bindTime);
                response.put("unbind_cooldown_config", cooldownConfig);
                response.put("current_time", System.currentTimeMillis());
                return;
            }

            // 解绑玩家账户
            plugin.getBindingDataManager().unbindPlayer(playerUuid);

            response.put("success", true);
            response.put("message", "账户解绑成功");

            plugin.getLogger().info("玩家 " + actualUsername + " 的账户已解绑");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "解绑失败: " + e.getMessage());
            plugin.getLogger().severe("处理解绑请求时出错: " + e.getMessage());
        }
    }

    /**
     * 处理验证绑定状态请求
     */
    private void handleVerifyBinding(JSONObject request, JSONObject response) {
        try {
            String username = (String) request.get("username");

            if (username == null || username.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "用户名不能为空");
                return;
            }

            username = username.trim();

            // 检查玩家是否存在绑定信息
            UUID playerUuid = plugin.getServer().getOfflinePlayer(username).getUniqueId();
            boolean isBound = plugin.getBindingDataManager().isPlayerBound(playerUuid);

            if (isBound) {
                // 获取绑定时间信息
                long bindTime = plugin.getBindingDataManager().getPlayerBindTime(playerUuid);

                response.put("success", true);
                response.put("is_bound", true);
                response.put("username", username);
                response.put("player_uuid", playerUuid.toString());
                response.put("bind_time", bindTime);
                response.put("message", "绑定有效");
            } else {
                response.put("success", true);
                response.put("is_bound", false);
                response.put("message", "未绑定");
            }

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "验证绑定状态失败: " + e.getMessage());
            plugin.getLogger().severe("验证绑定状态时出错: " + e.getMessage());
        }
    }

    /**
     * 生成唯一的绑定码
     */
    private String generateUniqueBindCode() {
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        StringBuilder code = new StringBuilder();
        java.util.Random random = new java.util.Random();

        do {
            code.setLength(0);
            for (int i = 0; i < 6; i++) {
                code.append(chars.charAt(random.nextInt(chars.length())));
            }
        } while (plugin.getBindingDataManager().bindCodeExists(code.toString()));

        return code.toString();
    }

    /**
     * 获取解绑冷却时间配置字符串
     */
    private String getUnbindCooldownConfigString() {
        if (plugin.getConfig().contains("binding.unbind-cooldown.value")) {
            int value = plugin.getConfig().getInt("binding.unbind-cooldown.value", 24);
            String unit = plugin.getConfig().getString("binding.unbind-cooldown.unit", "hours");

            if ("seconds".equalsIgnoreCase(unit)) {
                return value + "秒";
            } else {
                return value + "小时";
            }
        }

        // 兼容旧配置格式
        int unbindCooldownHours = plugin.getConfig().getInt("binding.unbind-cooldown-hours", 24);
        return unbindCooldownHours + "小时";
    }

    /**
     * 处理保存高级设置
     */
    private void handleSaveAdvancedSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            String sessionId = (String) request.get("session_id");

            // 检查权限：API密钥或有界面设置权限的会话
            boolean hasPermission = false;
            if (apiKey != null && webServer.getAdminKey().equals(apiKey)) {
                hasPermission = true; // API密钥拥有所有权限
            } else if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
                // 检查会话用户是否有界面设置权限
                AdminLoginHandler loginHandler = new AdminLoginHandler(plugin, webServer);
                String username = loginHandler.getUsernameFromSession(sessionId);
                if (username != null) {
                    AccountManagementHandler accountHandler = new AccountManagementHandler(plugin, webServer);
                    hasPermission = accountHandler.hasInterfaceSettingsPermission(username);
                }
            }

            if (!hasPermission) {
                response.put("success", false);
                response.put("message", "权限不足，需要界面设置权限");
                return;
            }

            // 获取高级设置参数并提供默认值
            String interfaceTheme = (String) request.get("interface_theme");
            String primaryColor = (String) request.get("primary_color");
            String accentColor = (String) request.get("accent_color");
            Boolean enablePageAnimations = (Boolean) request.get("enable_page_animations");
            String pageLoadAnimation = (String) request.get("page_load_animation");
            String animationSpeed = (String) request.get("animation_speed");

            // 确保所有值都不为null，提供安全的默认值
            String safeInterfaceTheme = (interfaceTheme != null && !interfaceTheme.trim().isEmpty())
                    ? interfaceTheme.trim()
                    : "default";
            String safePrimaryColor = (primaryColor != null && !primaryColor.trim().isEmpty()) ? primaryColor.trim()
                    : "#667eea";
            String safeAccentColor = (accentColor != null && !accentColor.trim().isEmpty()) ? accentColor.trim()
                    : "#764ba2";
            boolean safeEnableAnimations = (enablePageAnimations != null) ? enablePageAnimations : true;
            String safeLoadAnimation = (pageLoadAnimation != null && !pageLoadAnimation.trim().isEmpty())
                    ? pageLoadAnimation.trim()
                    : "fade";
            String safeAnimationSpeed = (animationSpeed != null && !animationSpeed.trim().isEmpty())
                    ? animationSpeed.trim()
                    : "normal";

            // 验证颜色格式
            if (!safePrimaryColor.matches("^#[0-9A-Fa-f]{6}$")) {
                safePrimaryColor = "#667eea";
            }
            if (!safeAccentColor.matches("^#[0-9A-Fa-f]{6}$")) {
                safeAccentColor = "#764ba2";
            }

            // 验证主题值
            if (!safeInterfaceTheme.matches("^(default|modern|classic)$")) {
                safeInterfaceTheme = "default";
            }

            // 验证动画值
            if (!safeLoadAnimation.matches("^(fade|slide|zoom|none)$")) {
                safeLoadAnimation = "fade";
            }
            if (!safeAnimationSpeed.matches("^(slow|normal|fast)$")) {
                safeAnimationSpeed = "normal";
            }

            // 保存界面主题设置
            plugin.getConfig().set("website.interface.theme", safeInterfaceTheme);
            plugin.getConfig().set("website.interface.primary-color", safePrimaryColor);
            plugin.getConfig().set("website.interface.accent-color", safeAccentColor);

            // 保存页面动画设置
            plugin.getConfig().set("website.interface.enable-animations", safeEnableAnimations);
            plugin.getConfig().set("website.interface.load-animation", safeLoadAnimation);
            plugin.getConfig().set("website.interface.animation-speed", safeAnimationSpeed);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("高级设置保存成功: 主题=" + safeInterfaceTheme +
                        ", 主色=" + safePrimaryColor + ", 强调色=" + safeAccentColor +
                        ", 动画=" + safeEnableAnimations);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存配置文件时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "高级设置保存成功");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存高级设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存高级设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理保存积分商店设置
     */
    private void handleSaveShopSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取积分商店设置参数
            String shopBackgroundImage = (String) request.get("shop_background_image");
            String shopTitle = (String) request.get("shop_title");
            String shopDescription = (String) request.get("shop_description");
            String shopLayout = (String) request.get("shop_layout");
            Object itemsPerPageObj = request.get("items_per_page");
            Boolean enableShopAnimations = (Boolean) request.get("enable_shop_animations");

            // 安全地转换数值类型
            int itemsPerPage = 12;
            if (itemsPerPageObj instanceof Number) {
                itemsPerPage = ((Number) itemsPerPageObj).intValue();
            }

            // 确保所有值都不为null，提供安全的默认值
            String safeBackgroundImage = (shopBackgroundImage != null) ? shopBackgroundImage.trim() : "";
            String safeTitle = (shopTitle != null && !shopTitle.trim().isEmpty()) ? shopTitle.trim() : "积分商店";
            String safeDescription = (shopDescription != null && !shopDescription.trim().isEmpty())
                    ? shopDescription.trim()
                    : "使用积分购买各种游戏道具和特权";
            String safeLayout = (shopLayout != null && !shopLayout.trim().isEmpty()) ? shopLayout.trim() : "grid";
            boolean safeAnimations = (enableShopAnimations != null) ? enableShopAnimations : true;

            // 验证布局值
            if (!safeLayout.matches("^(grid|list)$")) {
                safeLayout = "grid";
            }

            // 验证每页项目数
            if (itemsPerPage < 1 || itemsPerPage > 100) {
                itemsPerPage = 12;
            }

            // 保存积分商店设置
            plugin.getConfig().set("website.shop-background-image", safeBackgroundImage);
            plugin.getConfig().set("website.shop-title", safeTitle);
            plugin.getConfig().set("website.shop-description", safeDescription);
            plugin.getConfig().set("website.shop-layout", safeLayout);
            plugin.getConfig().set("website.shop-items-per-page", itemsPerPage);
            plugin.getConfig().set("website.shop-animations", safeAnimations);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("积分商店设置保存成功: 标题=" + safeTitle +
                        ", 布局=" + safeLayout + ", 每页=" + itemsPerPage + "项");
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存积分商店配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "积分商店设置保存成功");

            plugin.getLogger().info("管理员更新了积分商店设置");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存积分商店设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存积分商店设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理批量删除商店物品
     */
    private void handleBatchDeleteShopItems(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            JSONArray itemIds = (JSONArray) request.get("item_ids");
            if (itemIds == null || itemIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "物品ID列表不能为空");
                return;
            }

            int deletedCount = 0;
            for (Object itemIdObj : itemIds) {
                String itemId = (String) itemIdObj;
                if (itemId != null && !itemId.trim().isEmpty()) {
                    boolean success = plugin.getPointsShopManager().removeItem(itemId);
                    if (success) {
                        deletedCount++;
                    }
                }
            }

            response.put("success", true);
            response.put("message", "批量删除完成");
            response.put("deleted_count", deletedCount);

            plugin.getLogger().info("管理员批量删除了 " + deletedCount + " 个商店物品");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量删除失败: " + e.getMessage());
            plugin.getLogger().severe("批量删除商店物品时出错: " + e.getMessage());
        }
    }

    /**
     * 处理批量更新商店物品状态
     */
    private void handleBatchUpdateShopStatus(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取积分商店管理器
            if (plugin.getPointsShopManager() == null) {
                response.put("success", false);
                response.put("message", "积分商店系统未初始化");
                return;
            }

            JSONArray itemIds = (JSONArray) request.get("item_ids");
            Boolean enabled = (Boolean) request.get("enabled");

            if (itemIds == null || itemIds.isEmpty()) {
                response.put("success", false);
                response.put("message", "物品ID列表不能为空");
                return;
            }

            if (enabled == null) {
                response.put("success", false);
                response.put("message", "状态参数不能为空");
                return;
            }

            int updatedCount = 0;
            for (Object itemIdObj : itemIds) {
                String itemId = (String) itemIdObj;
                if (itemId != null && !itemId.trim().isEmpty()) {
                    // 获取现有物品
                    cn.acebrand.acekeysystem.points.PointsShopItem item = plugin.getPointsShopManager().getItem(itemId);
                    if (item != null) {
                        // 更新状态
                        item.setEnabled(enabled);
                        plugin.getPointsShopManager().addOrUpdateItem(item);
                        updatedCount++;
                    }
                }
            }

            response.put("success", true);
            response.put("message", "批量更新完成");
            response.put("updated_count", updatedCount);

            String action = enabled ? "启用" : "禁用";
            plugin.getLogger().info("管理员批量" + action + "了 " + updatedCount + " 个商店物品");

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "批量更新失败: " + e.getMessage());
            plugin.getLogger().severe("批量更新商店物品状态时出错: " + e.getMessage());
        }
    }

    /**
     * 处理保存管理员主题设置
     */
    private void handleSaveAdminThemeSettings(JSONObject request, JSONObject response) {
        try {
            // 验证管理员权限
            String apiKey = (String) request.get("api_key");
            if (!webServer.getAdminKey().equals(apiKey)) {
                response.put("success", false);
                response.put("message", "权限不足");
                return;
            }

            // 获取主题设置参数
            String adminThemeMode = (String) request.get("admin_theme_mode");
            Boolean enableDarkMode = (Boolean) request.get("enable_dark_mode");
            Boolean allowThemeSwitch = (Boolean) request.get("allow_theme_switch");

            // 确保所有值都不为null，提供安全的默认值
            String safeThemeMode = (adminThemeMode != null && !adminThemeMode.trim().isEmpty()) ? adminThemeMode.trim()
                    : "light";
            boolean safeDarkMode = (enableDarkMode != null) ? enableDarkMode : true;
            boolean safeThemeSwitch = (allowThemeSwitch != null) ? allowThemeSwitch : true;

            // 验证主题模式值
            if (!safeThemeMode.matches("^(light|dark|auto)$")) {
                safeThemeMode = "light";
            }

            // 保存管理员主题设置
            plugin.getConfig().set("web-server.admin-theme.default-mode", safeThemeMode);
            plugin.getConfig().set("web-server.admin-theme.dark-mode-enabled", safeDarkMode);
            plugin.getConfig().set("web-server.admin-theme.allow-theme-switch", safeThemeSwitch);

            // 安全保存配置
            try {
                plugin.saveConfig();
                plugin.getLogger().info("管理员主题设置保存成功: 模式=" + safeThemeMode +
                        ", 夜间模式=" + safeDarkMode + ", 允许切换=" + safeThemeSwitch);
            } catch (Exception saveException) {
                plugin.getLogger().severe("保存管理员主题配置时出错: " + saveException.getMessage());
                throw new RuntimeException("配置文件保存失败", saveException);
            }

            response.put("success", true);
            response.put("message", "管理员主题设置保存成功");

            plugin.getLogger().info("管理员更新了主题设置: 模式=" + adminThemeMode +
                    ", 夜间模式=" + enableDarkMode + ", 允许切换=" + allowThemeSwitch);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "保存管理员主题设置失败: " + e.getMessage());
            plugin.getLogger().severe("保存管理员主题设置时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取封禁记录
     */
    private void handleGetBanRecords(JSONObject request, JSONObject response) {
        try {
            // 验证API密钥
            String apiKey = (String) request.get("api_key");
            if (!isValidApiKey(apiKey)) {
                response.put("success", false);
                response.put("message", "API密钥无效");
                return;
            }

            // 检查封禁管理器是否可用
            if (plugin.getBanManager() == null || !plugin.getBanManager().isEnabled()) {
                response.put("success", false);
                response.put("message", "封禁记录功能未启用");
                return;
            }

            // 获取分页参数
            int page = 1;
            int pageSize = 10;

            Object pageObj = request.get("page");
            if (pageObj instanceof Number) {
                page = Math.max(1, ((Number) pageObj).intValue());
            }

            Object pageSizeObj = request.get("page_size");
            if (pageSizeObj instanceof Number) {
                pageSize = Math.max(5, Math.min(50, ((Number) pageSizeObj).intValue()));
            }

            // 获取封禁记录
            List<cn.acebrand.acekeysystem.ban.BanRecord> records = plugin.getBanManager().getBanRecords(page, pageSize);
            int totalRecords = plugin.getBanManager().getTotalBanCount();
            int totalPages = (int) Math.ceil((double) totalRecords / pageSize);

            // 转换为JSON格式
            JSONArray recordsArray = new JSONArray();
            for (cn.acebrand.acekeysystem.ban.BanRecord record : records) {
                JSONObject recordObj = new JSONObject();
                recordObj.put("id", record.getId());
                recordObj.put("player_name", record.getPlayerName());
                recordObj.put("uuid", record.getUuid());
                recordObj.put("reason", record.getReason());
                recordObj.put("banned_by_name", record.getBannedByName());
                recordObj.put("time", record.getFormattedTime());
                recordObj.put("until", record.getFormattedUntil());
                recordObj.put("status", record.getStatusText());
                recordObj.put("active", record.isActive());
                recordObj.put("ban_type", record.getBanTypeText());
                recordsArray.add(recordObj);
            }

            response.put("success", true);
            response.put("records", recordsArray);
            response.put("page", page);
            response.put("page_size", pageSize);
            response.put("total_records", totalRecords);
            response.put("total_pages", totalPages);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取封禁记录失败: " + e.getMessage());
            plugin.getLogger().severe("获取封禁记录时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理获取封禁统计信息
     */
    private void handleGetBanStatistics(JSONObject request, JSONObject response) {
        try {
            // 验证API密钥
            String apiKey = (String) request.get("api_key");
            if (!isValidApiKey(apiKey)) {
                response.put("success", false);
                response.put("message", "API密钥无效");
                return;
            }

            // 检查封禁管理器是否可用
            if (plugin.getBanManager() == null || !plugin.getBanManager().isEnabled()) {
                response.put("success", false);
                response.put("message", "封禁记录功能未启用");
                return;
            }

            // 获取统计信息
            Map<String, Object> statistics = plugin.getBanManager().getBanStatistics();

            response.put("success", true);
            response.put("statistics", statistics);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "获取封禁统计信息失败: " + e.getMessage());
            plugin.getLogger().severe("获取封禁统计信息时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理搜索封禁记录
     */
    private void handleSearchBanRecords(JSONObject request, JSONObject response) {
        try {
            // 验证API密钥
            String apiKey = (String) request.get("api_key");
            if (!isValidApiKey(apiKey)) {
                response.put("success", false);
                response.put("message", "API密钥无效");
                return;
            }

            // 检查封禁管理器是否可用
            if (plugin.getBanManager() == null || !plugin.getBanManager().isEnabled()) {
                response.put("success", false);
                response.put("message", "封禁记录功能未启用");
                return;
            }

            // 获取搜索参数
            String searchQuery = (String) request.get("search_query");
            if (searchQuery == null || searchQuery.trim().isEmpty()) {
                response.put("success", false);
                response.put("message", "搜索关键词不能为空");
                return;
            }

            // 获取分页参数
            int page = 1;
            int pageSize = 10;

            Object pageObj = request.get("page");
            if (pageObj instanceof Number) {
                page = Math.max(1, ((Number) pageObj).intValue());
            }

            Object pageSizeObj = request.get("page_size");
            if (pageSizeObj instanceof Number) {
                pageSize = Math.max(5, Math.min(50, ((Number) pageSizeObj).intValue()));
            }

            // 搜索封禁记录
            List<cn.acebrand.acekeysystem.ban.BanRecord> records = plugin.getBanManager()
                    .searchBanRecordsByPlayer(searchQuery.trim(), page, pageSize);

            // 转换为JSON格式
            JSONArray recordsArray = new JSONArray();
            for (cn.acebrand.acekeysystem.ban.BanRecord record : records) {
                JSONObject recordObj = new JSONObject();
                recordObj.put("id", record.getId());
                recordObj.put("player_name", record.getPlayerName());
                recordObj.put("uuid", record.getUuid());
                recordObj.put("reason", record.getReason());
                recordObj.put("banned_by_name", record.getBannedByName());
                recordObj.put("time", record.getFormattedTime());
                recordObj.put("until", record.getFormattedUntil());
                recordObj.put("status", record.getStatusText());
                recordObj.put("active", record.isActive());
                recordObj.put("ban_type", record.getBanTypeText());
                recordsArray.add(recordObj);
            }

            response.put("success", true);
            response.put("records", recordsArray);
            response.put("search_query", searchQuery);
            response.put("page", page);
            response.put("page_size", pageSize);
            response.put("found_count", records.size());

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "搜索封禁记录失败: " + e.getMessage());
            plugin.getLogger().severe("搜索封禁记录时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpExchange exchange, int statusCode, String message) throws IOException {
        JSONObject error = new JSONObject();
        error.put("success", false);
        error.put("message", message);
        sendJsonResponse(exchange, statusCode, error);
    }
}
