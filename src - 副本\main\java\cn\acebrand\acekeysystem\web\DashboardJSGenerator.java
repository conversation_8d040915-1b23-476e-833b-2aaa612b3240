package cn.acebrand.acekeysystem.web;

/**
 * 仪表板JavaScript生成器
 * 负责生成仪表板相关的JavaScript代码
 */
public class DashboardJSGenerator {
    private final String adminKey;

    public DashboardJSGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成仪表板JavaScript代码
     */
    public String generateDashboardJS() {
        return "\n// ==================== 仪表板功能 ====================\n" +
                "// 仪表板自动检测变量\n" +
                "let lastDashboardDataHash = null;\n" +
                "let dashboardAutoUpdateInterval = null;\n" +
                "let isDashboardManualRefresh = false;\n" +
                "\n" +
                "// 启动仪表板自动检测\n" +
                "function startDashboardAutoUpdate() {\n" +
                "    if (dashboardAutoUpdateInterval) {\n" +
                "        clearInterval(dashboardAutoUpdateInterval);\n" +
                "    }\n" +
                "    \n" +
                "    dashboardAutoUpdateInterval = setInterval(() => {\n" +
                "        loadDashboard(true); // 静默检测\n" +
                "    }, 5000); // 每5秒检测一次\n" +
                "    \n" +
                "    updateDashboardAutoUpdateStatus(true);\n" +
                "}\n" +
                "\n" +
                "// 停止仪表板自动检测\n" +
                "function stopDashboardAutoUpdate() {\n" +
                "    if (dashboardAutoUpdateInterval) {\n" +
                "        clearInterval(dashboardAutoUpdateInterval);\n" +
                "        dashboardAutoUpdateInterval = null;\n" +
                "    }\n" +
                "    updateDashboardAutoUpdateStatus(false);\n" +
                "}\n" +
                "\n" +
                "// 更新仪表板自动检测状态显示\n" +
                "function updateDashboardAutoUpdateStatus(isActive) {\n" +
                "    const statusElement = document.getElementById('dashboardAutoUpdateStatus');\n" +
                "    if (statusElement) {\n" +
                "        if (isActive) {\n" +
                "            statusElement.textContent = '🔄 自动检测中';\n" +
                "            statusElement.className = 'status-indicator active';\n" +
                "        } else {\n" +
                "            statusElement.textContent = '⏸️ 已暂停';\n" +
                "            statusElement.className = 'status-indicator inactive';\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 加载仪表板数据\n" +
                "function loadDashboard(silent = false) {\n" +
                "    if (!silent && !isDashboardManualRefresh) {\n" +
                "        // 只在非静默模式下显示加载提示\n" +
                "        // showMessage('正在加载仪表板数据...', 'info');\n" +
                "    }\n" +
                "    \n" +
                "    fetch('/api', {\n" +
                "        method: 'POST',\n" +
                "        headers: { 'Content-Type': 'application/json' },\n" +
                "        body: JSON.stringify({\n" +
                "            action: 'get_dashboard_stats',\n" +
                "            api_key: '" + adminKey + "'\n" +
                "        })\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.success) {\n" +
                "            // 计算数据哈希值用于检测变化\n" +
                "            const dataHash = calculateDataHash(data);\n" +
                "            \n" +
                "            // 检查数据是否有变化\n" +
                "            if (silent && lastDashboardDataHash && lastDashboardDataHash === dataHash) {\n" +
                "                // 数据没有变化，不更新界面\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            // 数据有变化或首次加载，更新界面\n" +
                "            lastDashboardDataHash = dataHash;\n" +
                "            updateDashboardStats(data.stats);\n" +
                "            \n" +
                "            // 如果是静默检测到变化，显示提示\n" +
                "            if (silent && !isDashboardManualRefresh) {\n" +
                "                showMessage('🔄 检测到仪表板数据更新，已自动刷新', 'info');\n" +
                "            }\n" +
                "        } else {\n" +
                "            if (!silent) {\n" +
                "                showMessage('❌ 加载仪表板数据失败: ' + data.message, 'error');\n" +
                "            }\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        if (!silent) {\n" +
                "            showMessage('❌ 网络错误: ' + error.message, 'error');\n" +
                "        }\n" +
                "    })\n" +
                "    .finally(() => {\n" +
                "        isDashboardManualRefresh = false;\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新仪表板统计数据\n" +
                "function updateDashboardStats(stats) {\n" +
                "    if (!stats) return;\n" +
                "    \n" +
                "    // 更新总卡密数\n" +
                "    const totalKeysElement = document.getElementById('totalKeysCount');\n" +
                "    if (totalKeysElement && stats.total_keys !== undefined) {\n" +
                "        totalKeysElement.textContent = stats.total_keys;\n" +
                "    }\n" +
                "    \n" +
                "    // 更新可用卡密数\n" +
                "    const availableKeysElement = document.getElementById('availableKeysCount');\n" +
                "    if (availableKeysElement && stats.available_keys !== undefined) {\n" +
                "        availableKeysElement.textContent = stats.available_keys;\n" +
                "    }\n" +
                "    \n" +
                "    // 更新已使用卡密数\n" +
                "    const usedKeysElement = document.getElementById('usedKeysCount');\n" +
                "    if (usedKeysElement && stats.used_keys !== undefined) {\n" +
                "        usedKeysElement.textContent = stats.used_keys;\n" +
                "    }\n" +
                "    \n" +
                "    // 更新中奖记录数\n" +
                "    const winnersElement = document.getElementById('totalWinnersCount');\n" +
                "    if (winnersElement && stats.total_winners !== undefined) {\n" +
                "        winnersElement.textContent = stats.total_winners;\n" +
                "    }\n" +
                "    \n" +
                "    // 更新Web服务器状态\n" +
                "    const serverStatusElement = document.getElementById('webServerStatus');\n" +
                "    if (serverStatusElement && stats.server_status !== undefined) {\n" +
                "        serverStatusElement.textContent = stats.server_status ? '运行中' : '已停止';\n" +
                "        serverStatusElement.className = 'stat-value status ' + (stats.server_status ? 'running' : 'stopped');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 刷新仪表板\n" +
                "function refreshDashboard() {\n" +
                "    isDashboardManualRefresh = true;\n" +
                "    loadDashboard();\n" +
                "    showMessage('仪表板数据已刷新', 'success');\n" +
                "}\n" +
                "\n" +
                "// 页面加载时初始化仪表板\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    if (document.getElementById('dashboardContainer')) {\n" +
                "        loadDashboard();\n" +
                "        startDashboardAutoUpdate();\n" +
                "    }\n" +
                "});\n";
    }

    /**
     * 生成仪表板CSS样式
     */
    public String generateDashboardCSS() {
        return "\n/* ==================== 仪表板样式 ====================*/\n" +
                ".dashboard-stats-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\n" +
                "    gap: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".dashboard-stat-card {\n" +
                "    background: var(--bg-card);\n" +
                "    padding: 25px;\n" +
                "    border-radius: 16px;\n" +
                "    box-shadow: 0 8px 32px var(--shadow-light);\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 1px solid var(--border-color);\n" +
                "    position: relative;\n" +
                "    overflow: hidden;\n" +
                "}\n" +
                "\n" +
                ".dashboard-stat-card:hover {\n" +
                "    transform: translateY(-4px);\n" +
                "    box-shadow: 0 12px 40px var(--shadow-medium);\n" +
                "}\n" +
                "\n" +
                ".stat-header {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".stat-icon {\n" +
                "    font-size: 32px;\n" +
                "    margin-right: 15px;\n" +
                "    width: 50px;\n" +
                "    height: 50px;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    border-radius: 12px;\n" +
                "    background: rgba(102, 126, 234, 0.1);\n" +
                "}\n" +
                "\n" +
                ".stat-icon.total { background: rgba(102, 126, 234, 0.1); }\n" +
                ".stat-icon.available { background: rgba(76, 175, 80, 0.1); }\n" +
                ".stat-icon.used { background: rgba(244, 67, 54, 0.1); }\n" +
                ".stat-icon.winners { background: rgba(255, 193, 7, 0.1); }\n" +
                ".stat-icon.server { background: rgba(33, 150, 243, 0.1); }\n" +
                ".stat-icon.version { background: rgba(156, 39, 176, 0.1); }\n" +
                "\n" +
                ".stat-title {\n" +
                "    font-size: 16px;\n" +
                "    font-weight: 600;\n" +
                "    color: var(--text-primary);\n" +
                "}\n" +
                "\n" +
                ".stat-value {\n" +
                "    font-size: 36px;\n" +
                "    font-weight: bold;\n" +
                "    color: var(--text-primary);\n" +
                "    margin-bottom: 8px;\n" +
                "    line-height: 1;\n" +
                "}\n" +
                "\n" +
                ".stat-value.status.running {\n" +
                "    color: var(--success-color);\n" +
                "    font-size: 18px;\n" +
                "}\n" +
                "\n" +
                ".stat-value.status.stopped {\n" +
                "    color: var(--error-color);\n" +
                "    font-size: 18px;\n" +
                "}\n" +
                "\n" +
                ".stat-value.version-text {\n" +
                "    font-size: 18px;\n" +
                "    color: var(--accent-color);\n" +
                "}\n" +
                "\n" +
                ".stat-subtitle {\n" +
                "    font-size: 14px;\n" +
                "    color: var(--text-secondary);\n" +
                "    margin: 0;\n" +
                "}\n" +
                "\n" +
                ".auto-update-status {\n" +
                "    margin-top: 10px;\n" +
                "}\n" +
                "\n" +
                ".status-indicator {\n" +
                "    display: inline-block;\n" +
                "    padding: 6px 12px;\n" +
                "    border-radius: 20px;\n" +
                "    font-size: 12px;\n" +
                "    font-weight: 500;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".status-indicator.active {\n" +
                "    background: rgba(76, 175, 80, 0.1);\n" +
                "    color: #4caf50;\n" +
                "    border: 1px solid rgba(76, 175, 80, 0.3);\n" +
                "}\n" +
                "\n" +
                ".status-indicator.inactive {\n" +
                "    background: rgba(255, 152, 0, 0.1);\n" +
                "    color: #ff9800;\n" +
                "    border: 1px solid rgba(255, 152, 0, 0.3);\n" +
                "}\n";
    }
}
