package cn.acebrand.acekeysystem.web;

/**
 * 账号管理页面生成器
 * 负责生成账号管理相关的HTML页面
 */
public class AccountManagementPageGenerator {
        private final String adminKey;

        public AccountManagementPageGenerator(String adminKey) {
                this.adminKey = adminKey;
        }

        /**
         * 生成账号管理页面
         */
        public String generateAccountsPage() {
                StringBuilder html = new StringBuilder();

                html.append("            <div class=\"page-header\">\n");
                html.append("                <h1>👥 账号管理</h1>\n");
                html.append("                <p>管理管理员账号和权限</p>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 导航标签页 -->\n");
                html.append("            <div class=\"tab-navigation\">\n");
                html.append("                <button class=\"tab-btn active\" onclick=\"switchTab('accounts')\">👥 账号管理</button>\n");
                html.append("                <button class=\"tab-btn\" onclick=\"switchTab('roles')\">🎭 角色管理</button>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 账号管理标签页 -->\n");
                html.append("            <div id=\"accountsTab\" class=\"account-tab-content active\">\n");
                html.append("                <!-- 操作工具栏 -->\n");
                html.append("                <div class=\"toolbar\">\n");
                html.append("                    <div class=\"toolbar-left\">\n");
                html.append("                        <button onclick=\"showCreateAccountModal()\" class=\"btn btn-primary\">➕ 创建账号</button>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"toolbar-right\">\n");
                html.append("                        <span class=\"account-count\">总计: <strong id=\"totalAccountsCount\">0</strong> 个账号</span>\n");
                html.append("                        <button onclick=\"refreshAccounts()\" class=\"btn btn-info\">🔄 刷新</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 账号列表 -->\n");
                html.append("                <div class=\"content-card\">\n");
                html.append("                    <div class=\"card-header\">\n");
                html.append("                        <h3>管理员账号列表</h3>\n");
                html.append("                        <div class=\"search-box\">\n");
                html.append("                            <input type=\"text\" id=\"accountSearchInput\" placeholder=\"搜索用户名...\" class=\"form-input\" onkeyup=\"filterAccounts()\">\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"accounts-container\" id=\"accountsContainer\">\n");
                html.append("                        <div class=\"loading-spinner\">正在加载账号列表...</div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");
                html.append("            <!-- 角色管理标签页 -->\n");
                html.append("            <div id=\"rolesTab\" class=\"account-tab-content\">\n");
                html.append("                <!-- 操作工具栏 -->\n");
                html.append("                <div class=\"toolbar\">\n");
                html.append("                    <div class=\"toolbar-left\">\n");
                html.append("                        <button onclick=\"showCreateRoleModal()\" class=\"btn btn-primary\">➕ 创建角色</button>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"toolbar-right\">\n");
                html.append("                        <span class=\"role-count\">总计: <strong id=\"totalRolesCount\">0</strong> 个角色</span>\n");
                html.append("                        <button onclick=\"refreshRoles()\" class=\"btn btn-info\">🔄 刷新</button>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("                \n");
                html.append("                <!-- 角色列表 -->\n");
                html.append("                <div class=\"content-card\">\n");
                html.append("                    <div class=\"card-header\">\n");
                html.append("                        <h3>角色模板列表</h3>\n");
                html.append("                        <div class=\"search-box\">\n");
                html.append("                            <input type=\"text\" id=\"roleSearchInput\" placeholder=\"搜索角色名称...\" class=\"form-input\" onkeyup=\"filterRoles()\">\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"roles-container\" id=\"rolesContainer\">\n");
                html.append("                        <div class=\"loading-spinner\">正在加载角色列表...</div>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                // 添加弹窗
                html.append(generateCreateAccountModal());
                html.append(generateEditAccountModal());
                html.append(generateChangePasswordModal());
                html.append(generateCreateRoleModal());
                html.append(generateEditRoleModal());
                html.append(generateAssignRoleModal());

                return html.toString();
        }

        /**
         * 生成创建账号弹窗
         */
        private String generateCreateAccountModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 创建账号弹窗 -->\n");
                html.append("            <div id=\"createAccountModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>创建新账号</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeCreateAccountModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"createAccountForm\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newUsername\">用户名:</label>\n");
                html.append("                                <input type=\"text\" id=\"newUsername\" class=\"form-input\" required>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newPassword\">密码:</label>\n");
                html.append("                                <input type=\"password\" id=\"newPassword\" class=\"form-input\" required minlength=\"6\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newRole\">角色:</label>\n");
                html.append("                                <select id=\"newRole\" class=\"form-input\" onchange=\"onRoleChange('new')\">\n");
                html.append("                                    <option value=\"admin\">管理员</option>\n");
                html.append("                                    <option value=\"super_admin\">超级管理员</option>\n");
                html.append("                                </select>\n");
                html.append("                                <small>选择角色，权限将自动根据角色配置设置</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>权限设置:</label>\n");
                html.append("                                <div class=\"permission-checkboxes\">\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newPunishmentPermission\" disabled>\n");
                html.append("                                        <span>封禁管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newAccountManagementPermission\" disabled>\n");
                html.append("                                        <span>账号管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newSystemSettingsPermission\" disabled>\n");
                html.append("                                        <span>系统设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newInterfaceSettingsPermission\" disabled>\n");
                html.append("                                        <span>界面设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newDataViewPermission\" disabled checked>\n");
                html.append("                                        <span>数据查看权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newKeyManagementPermission\" disabled>\n");
                html.append("                                        <span>卡密管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRewardManagementPermission\" disabled>\n");
                html.append("                                        <span>奖品管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newPointsShopPermission\" disabled>\n");
                html.append("                                        <span>积分商店权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newWinnersViewPermission\" disabled>\n");
                html.append("                                        <span>中奖记录权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newStatisticsViewPermission\" disabled>\n");
                html.append("                                        <span>统计分析权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                </div>\n");
                html.append("                                <small>权限将根据选择的角色自动设置（复选框仅用于预览）</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-actions\">\n");
                html.append("                                <button type=\"button\" onclick=\"closeCreateAccountModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append("                                <button type=\"submit\" class=\"btn btn-primary\">创建账号</button>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                return html.toString();
        }

        /**
         * 生成编辑账号弹窗
         */
        private String generateEditAccountModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 编辑账号弹窗 -->\n");
                html.append("            <div id=\"editAccountModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>编辑账号</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeEditAccountModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"editAccountForm\">\n");
                html.append("                            <input type=\"hidden\" id=\"editUsername\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"editRole\">角色:</label>\n");
                html.append("                                <select id=\"editRole\" class=\"form-input\" onchange=\"onRoleChange('edit')\">\n");
                html.append("                                    <option value=\"admin\">管理员</option>\n");
                html.append("                                    <option value=\"super_admin\">超级管理员</option>\n");
                html.append("                                </select>\n");
                html.append("                                <small>选择角色，权限将自动根据角色配置设置</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>权限设置:</label>\n");
                html.append("                                <div class=\"permission-checkboxes\">\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editPunishmentPermission\" disabled>\n");
                html.append("                                        <span>封禁管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editAccountManagementPermission\" disabled>\n");
                html.append("                                        <span>账号管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editSystemSettingsPermission\" disabled>\n");
                html.append("                                        <span>系统设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editInterfaceSettingsPermission\" disabled>\n");
                html.append("                                        <span>界面设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editDataViewPermission\" disabled>\n");
                html.append("                                        <span>数据查看权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editKeyManagementPermission\" disabled>\n");
                html.append("                                        <span>卡密管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRewardManagementPermission\" disabled>\n");
                html.append("                                        <span>奖品管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editPointsShopPermission\" disabled>\n");
                html.append("                                        <span>积分商店权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editWinnersViewPermission\" disabled>\n");
                html.append("                                        <span>中奖记录权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editStatisticsViewPermission\" disabled>\n");
                html.append("                                        <span>统计分析权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                </div>\n");
                html.append("                                <small>权限将根据选择的角色自动设置（复选框仅用于预览）</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-actions\">\n");
                html.append("                                <button type=\"button\" onclick=\"closeEditAccountModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append("                                <button type=\"submit\" class=\"btn btn-primary\">保存更改</button>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                return html.toString();
        }

        /**
         * 生成修改密码弹窗
         */
        private String generateChangePasswordModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 修改密码弹窗 -->\n");
                html.append("            <div id=\"changePasswordModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>修改密码</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeChangePasswordModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"changePasswordForm\">\n");
                html.append("                            <input type=\"hidden\" id=\"changePasswordUsername\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newPasswordInput\">新密码:</label>\n");
                html.append("                                <input type=\"password\" id=\"newPasswordInput\" class=\"form-input\" required minlength=\"6\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"confirmPasswordInput\">确认密码:</label>\n");
                html.append("                                <input type=\"password\" id=\"confirmPasswordInput\" class=\"form-input\" required minlength=\"6\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-actions\">\n");
                html.append("                                <button type=\"button\" onclick=\"closeChangePasswordModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append("                                <button type=\"submit\" class=\"btn btn-primary\">修改密码</button>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");

                return html.toString();
        }

        /**
         * 生成创建角色弹窗
         */
        private String generateCreateRoleModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 创建角色弹窗 -->\n");
                html.append("            <div id=\"createRoleModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>创建新角色</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeCreateRoleModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"createRoleForm\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newRoleId\">角色ID:</label>\n");
                html.append("                                <input type=\"text\" id=\"newRoleId\" class=\"form-input\" required placeholder=\"例如: content_manager\">\n");
                html.append("                                <small>角色ID用于系统内部识别，只能包含字母、数字和下划线</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newRoleName\">角色名称:</label>\n");
                html.append("                                <input type=\"text\" id=\"newRoleName\" class=\"form-input\" required placeholder=\"例如: 内容管理员\">\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"newRoleDescription\">角色描述:</label>\n");
                html.append("                                <textarea id=\"newRoleDescription\" class=\"form-input\" rows=\"3\" placeholder=\"描述该角色的职责和权限范围\"></textarea>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>权限设置:</label>\n");
                html.append("                                <div class=\"permission-checkboxes\">\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRolePunishmentPermission\">\n");
                html.append("                                        <span>封禁管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleAccountManagementPermission\">\n");
                html.append("                                        <span>账号管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleSystemSettingsPermission\">\n");
                html.append("                                        <span>系统设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleInterfaceSettingsPermission\">\n");
                html.append("                                        <span>界面设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleDataViewPermission\" checked>\n");
                html.append("                                        <span>数据查看权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleKeyManagementPermission\">\n");
                html.append("                                        <span>卡密管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleRewardManagementPermission\">\n");
                html.append("                                        <span>奖品管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRolePointsShopPermission\">\n");
                html.append("                                        <span>积分商店权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleWinnersViewPermission\">\n");
                html.append("                                        <span>中奖记录权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"newRoleStatisticsViewPermission\">\n");
                html.append("                                        <span>统计分析权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                </div>\n");
                html.append("                                <small>选择该角色拥有的权限</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-actions\">\n");
                html.append("                                <button type=\"button\" onclick=\"closeCreateRoleModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append("                                <button type=\"submit\" class=\"btn btn-primary\">创建角色</button>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                return html.toString();
        }

        /**
         * 生成编辑角色弹窗
         */
        private String generateEditRoleModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 编辑角色弹窗 -->\n");
                html.append("            <div id=\"editRoleModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>编辑角色</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeEditRoleModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"editRoleForm\">\n");
                html.append("                            <input type=\"hidden\" id=\"editRoleId\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"editRoleName\">角色名称:</label>\n");
                html.append("                                <input type=\"text\" id=\"editRoleName\" class=\"form-input\" required>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"editRoleDescription\">角色描述:</label>\n");
                html.append("                                <textarea id=\"editRoleDescription\" class=\"form-input\" rows=\"3\"></textarea>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>权限设置:</label>\n");
                html.append("                                <div class=\"permission-checkboxes\">\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRolePunishmentPermission\">\n");
                html.append("                                        <span>封禁管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleAccountManagementPermission\">\n");
                html.append("                                        <span>账号管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleSystemSettingsPermission\">\n");
                html.append("                                        <span>系统设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleInterfaceSettingsPermission\">\n");
                html.append("                                        <span>界面设置权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleDataViewPermission\">\n");
                html.append("                                        <span>数据查看权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleKeyManagementPermission\">\n");
                html.append("                                        <span>卡密管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleRewardManagementPermission\">\n");
                html.append("                                        <span>奖品管理权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRolePointsShopPermission\">\n");
                html.append("                                        <span>积分商店权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleWinnersViewPermission\">\n");
                html.append("                                        <span>中奖记录权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                    <label class=\"checkbox-label\">\n");
                html.append("                                        <input type=\"checkbox\" id=\"editRoleStatisticsViewPermission\">\n");
                html.append("                                        <span>统计分析权限</span>\n");
                html.append("                                    </label>\n");
                html.append("                                </div>\n");
                html.append("                                <small>选择该角色拥有的权限</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-actions\">\n");
                html.append("                                <button type=\"button\" onclick=\"closeEditRoleModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append("                                <button type=\"submit\" class=\"btn btn-primary\">保存更改</button>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");
                html.append("            \n");

                return html.toString();
        }

        /**
         * 生成分配角色弹窗
         */
        private String generateAssignRoleModal() {
                StringBuilder html = new StringBuilder();

                html.append("            <!-- 分配角色弹窗 -->\n");
                html.append("            <div id=\"assignRoleModal\" class=\"modal\" style=\"display: none;\">\n");
                html.append("                <div class=\"modal-content\">\n");
                html.append("                    <div class=\"modal-header\">\n");
                html.append("                        <h3>分配角色</h3>\n");
                html.append("                        <span class=\"close\" onclick=\"closeAssignRoleModal()\">&times;</span>\n");
                html.append("                    </div>\n");
                html.append("                    <div class=\"modal-body\">\n");
                html.append("                        <form id=\"assignRoleForm\">\n");
                html.append("                            <input type=\"hidden\" id=\"assignRoleUsername\">\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>用户名:</label>\n");
                html.append("                                <p id=\"assignRoleUsernameDisplay\" class=\"form-text\"></p>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label for=\"assignRoleSelect\">选择角色:</label>\n");
                html.append("                                <select id=\"assignRoleSelect\" class=\"form-input\" required>\n");
                html.append("                                    <option value=\"\">请选择角色...</option>\n");
                html.append("                                </select>\n");
                html.append("                                <small>选择要分配给该用户的角色，角色的权限将自动应用到用户</small>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-group\">\n");
                html.append("                                <label>角色权限预览:</label>\n");
                html.append("                                <div id=\"rolePermissionsPreview\" class=\"permissions-preview\">\n");
                html.append("                                    <p class=\"text-muted\">请先选择角色</p>\n");
                html.append("                                </div>\n");
                html.append("                            </div>\n");
                html.append("                            <div class=\"form-actions\">\n");
                html.append("                                <button type=\"button\" onclick=\"closeAssignRoleModal()\" class=\"btn btn-secondary\">取消</button>\n");
                html.append("                                <button type=\"submit\" class=\"btn btn-primary\">分配角色</button>\n");
                html.append("                            </div>\n");
                html.append("                        </form>\n");
                html.append("                    </div>\n");
                html.append("                </div>\n");
                html.append("            </div>\n");

                return html.toString();
        }
}
