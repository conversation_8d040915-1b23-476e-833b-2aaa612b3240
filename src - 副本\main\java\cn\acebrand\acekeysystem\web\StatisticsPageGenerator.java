package cn.acebrand.acekeysystem.web;

/**
 * 统计分析页面生成器
 * 负责生成管理后台的统计分析页面HTML和JavaScript代码
 */
public class StatisticsPageGenerator {

    private final String adminKey;

    public StatisticsPageGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成统计分析页面HTML
     */
    public String generateStatisticsPage() {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"page-header\">\n");
        html.append("                <h1>📈 统计分析</h1>\n");
        html.append("                <p>系统使用情况统计和数据分析</p>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 导航标签页
        html.append("            <div class=\"statistics-nav\">\n");
        html.append("                <div class=\"nav-tabs\">\n");
        html.append(
                "                    <button class=\"nav-tab active\" onclick=\"showStatisticsTab('overview')\">📊 数据概览</button>\n");
        html.append(
                "                    <button class=\"nav-tab\" onclick=\"showStatisticsTab('trends')\">📈 使用趋势</button>\n");
        html.append(
                "                    <button class=\"nav-tab\" onclick=\"showStatisticsTab('players')\">🏆 玩家排行</button>\n");
        html.append(
                "                    <button class=\"nav-tab\" onclick=\"showStatisticsTab('search')\">🔍 玩家搜索</button>\n");
        html.append(
                "                    <button class=\"nav-tab\" onclick=\"showStatisticsTab('system')\">⚙️ 系统信息</button>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 数据概览标签页
        html.append("            <div id=\"tab-overview\" class=\"statistics-tab-content active\">\n");
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <div class=\"card-header\">\n");
        html.append("                        <h3>📊 数据概览</h3>\n");
        html.append("                        <p>系统卡密使用情况总览</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stats-overview\" id=\"statsOverview\">\n");
        html.append("                        <div class=\"loading-spinner\">正在加载统计数据...</div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 使用趋势标签页
        html.append("            <div id=\"tab-trends\" class=\"statistics-tab-content\">\n");
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <div class=\"card-header\">\n");
        html.append("                        <h3>📈 每日使用趋势</h3>\n");
        html.append("                        <p>最近7天的卡密使用情况变化</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"chart-container\">\n");
        html.append("                        <canvas id=\"dailyUsageChart\" width=\"800\" height=\"400\"></canvas>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 玩家排行标签页
        html.append("            <div id=\"tab-players\" class=\"statistics-tab-content\">\n");
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <div class=\"card-header\">\n");
        html.append("                        <h3>🏆 活跃玩家排行榜</h3>\n");
        html.append("                        <p>按卡密使用次数排序的玩家排行</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"top-players-container\" id=\"topPlayersContainer\">\n");
        html.append("                        <div class=\"loading-spinner\">正在加载玩家数据...</div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 玩家搜索标签页
        html.append("            <div id=\"tab-search\" class=\"statistics-tab-content\">\n");
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <div class=\"card-header\">\n");
        html.append("                        <h3>🔍 玩家详细搜索</h3>\n");
        html.append("                        <p>搜索指定玩家的详细卡密使用记录</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"search-section\">\n");
        html.append("                        <div class=\"search-form\">\n");
        html.append("                            <div class=\"form-group inline\">\n");
        html.append("                                <label for=\"playerSearchInput\">玩家名:</label>\n");
        html.append(
                "                                <input type=\"text\" id=\"playerSearchInput\" placeholder=\"输入玩家名...\" class=\"form-input\">\n");
        html.append(
                "                                <button onclick=\"searchPlayer()\" class=\"btn btn-primary\">🔍 搜索</button>\n");
        html.append("                            </div>\n");
        html.append("                            <div class=\"search-tips\">\n");
        html.append("                                <p>💡 提示：支持模糊搜索，输入部分玩家名即可</p>\n");
        html.append("                            </div>\n");
        html.append("                        </div>\n");
        html.append(
                "                        <div class=\"player-results\" id=\"playerResults\" style=\"display: none;\">\n");
        html.append("                            <!-- 搜索结果将在这里显示 -->\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 系统信息标签页
        html.append("            <div id=\"tab-system\" class=\"statistics-tab-content\">\n");
        html.append("                <div class=\"content-card\">\n");
        html.append("                    <div class=\"card-header\">\n");
        html.append("                        <h3>⚙️ 系统配置信息</h3>\n");
        html.append("                        <p>当前系统运行状态和配置参数</p>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"system-info\" id=\"systemInfo\">\n");
        html.append("                        <div class=\"loading-spinner\">正在加载系统信息...</div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");

        // 隐藏的统计容器，用于JavaScript加载数据
        html.append("            <div id=\"statisticsContainer\" style=\"display: none;\"></div>\n");

        return html.toString();
    }

    /**
     * 生成统计分析相关的JavaScript代码
     */
    public String generateStatisticsJavaScript() {
        StringBuilder js = new StringBuilder();

        js.append("// 统计分析相关函数\n");
        js.append("function loadStatistics() {\n");
        js.append("    fetch('/api', {\n");
        js.append("        method: 'POST',\n");
        js.append("        headers: { 'Content-Type': 'application/json' },\n");
        js.append("        body: JSON.stringify({\n");
        js.append("            action: 'get_statistics',\n");
        js.append("            api_key: '").append(adminKey).append("'\n");
        js.append("        })\n");
        js.append("    })\n");
        js.append("    .then(response => response.json())\n");
        js.append("    .then(data => {\n");
        js.append("        if (data.success) {\n");
        js.append("            displayStatistics(data.statistics);\n");
        js.append("        } else {\n");
        js.append("            showResult('加载统计数据失败: ' + data.message, 'error');\n");
        js.append("        }\n");
        js.append("    })\n");
        js.append("    .catch(error => {\n");
        js.append("        console.error('Error:', error);\n");
        js.append("        showResult('加载统计数据时出错', 'error');\n");
        js.append("    });\n");
        js.append("}\n");
        js.append("\n");

        js.append("function displayStatistics(stats) {\n");
        js.append("    // 保存统计数据到全局变量\n");
        js.append("    window.statisticsData = stats;\n");
        js.append("\n");
        js.append("    // 显示统计概览（数据概览标签页）\n");
        js.append("    const statsOverview = document.getElementById('statsOverview');\n");
        js.append("    if (statsOverview) {\n");
        js.append("        statsOverview.innerHTML = `\n");
        js.append("            <div class=\"stats-grid\">\n");
        js.append("                <div class=\"stat-card\">\n");
        js.append("                    <div class=\"stat-icon\">📋</div>\n");
        js.append("                    <div class=\"stat-info\">\n");
        js.append("                        <div class=\"stat-number\">${stats.basic.total}</div>\n");
        js.append("                        <div class=\"stat-label\">总卡密数</div>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\"stat-card\">\n");
        js.append("                    <div class=\"stat-icon\">✅</div>\n");
        js.append("                    <div class=\"stat-info\">\n");
        js.append("                        <div class=\"stat-number\">${stats.basic.unused}</div>\n");
        js.append("                        <div class=\"stat-label\">未分配</div>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\"stat-card\">\n");
        js.append("                    <div class=\"stat-icon\">🎫</div>\n");
        js.append("                    <div class=\"stat-info\">\n");
        js.append("                        <div class=\"stat-number\">${stats.basic.assigned}</div>\n");
        js.append("                        <div class=\"stat-label\">已分配</div>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\"stat-card\">\n");
        js.append("                    <div class=\"stat-icon\">❌</div>\n");
        js.append("                    <div class=\"stat-info\">\n");
        js.append("                        <div class=\"stat-number\">${stats.basic.used}</div>\n");
        js.append("                        <div class=\"stat-label\">已使用</div>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\"stat-card\">\n");
        js.append("                    <div class=\"stat-icon\">⏰</div>\n");
        js.append("                    <div class=\"stat-info\">\n");
        js.append("                        <div class=\"stat-number\">${stats.basic.expired}</div>\n");
        js.append("                        <div class=\"stat-label\">已失效</div>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("                <div class=\"stat-card\">\n");
        js.append("                    <div class=\"stat-icon\">♾️</div>\n");
        js.append("                    <div class=\"stat-info\">\n");
        js.append("                        <div class=\"stat-number\">${stats.basic.never_expire}</div>\n");
        js.append("                        <div class=\"stat-label\">永不失效</div>\n");
        js.append("                    </div>\n");
        js.append("                </div>\n");
        js.append("            </div>\n");
        js.append("        `;\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 显示活跃玩家排行（玩家排行标签页）\n");
        js.append("    displayTopPlayers(stats.top_players);\n");
        js.append("\n");
        js.append("    // 显示系统信息（系统信息标签页）\n");
        js.append("    displaySystemInfo(stats.system);\n");
        js.append("\n");
        js.append("    // 如果当前在趋势标签页，显示图表\n");
        js.append("    const trendsTab = document.getElementById('tab-trends');\n");
        js.append("    if (trendsTab && trendsTab.classList.contains('active')) {\n");
        js.append("        setTimeout(() => displayDailyUsageChart(stats.daily_usage), 100);\n");
        js.append("    }\n");
        js.append("}\n");
        js.append("\n");

        js.append("function displayDailyUsageChart(dailyData) {\n");
        js.append("    const canvas = document.getElementById('dailyUsageChart');\n");
        js.append("    if (!canvas) return;\n");
        js.append("\n");
        js.append("    const ctx = canvas.getContext('2d');\n");
        js.append("    const width = canvas.width;\n");
        js.append("    const height = canvas.height;\n");
        js.append("\n");
        js.append("    // 清空画布\n");
        js.append("    ctx.clearRect(0, 0, width, height);\n");
        js.append("\n");
        js.append("    if (!dailyData || dailyData.length === 0) {\n");
        js.append("        ctx.fillStyle = '#666';\n");
        js.append("        ctx.font = '16px Arial';\n");
        js.append("        ctx.textAlign = 'center';\n");
        js.append("        ctx.fillText('暂无数据', width / 2, height / 2);\n");
        js.append("        return;\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 计算最大值\n");
        js.append("    const maxValue = Math.max(...dailyData.map(d => d.count), 1);\n");
        js.append("    const padding = 40;\n");
        js.append("    const chartWidth = width - padding * 2;\n");
        js.append("    const chartHeight = height - padding * 2;\n");
        js.append("\n");
        js.append("    // 绘制坐标轴\n");
        js.append("    ctx.strokeStyle = '#ddd';\n");
        js.append("    ctx.lineWidth = 1;\n");
        js.append("    ctx.beginPath();\n");
        js.append("    ctx.moveTo(padding, padding);\n");
        js.append("    ctx.lineTo(padding, height - padding);\n");
        js.append("    ctx.lineTo(width - padding, height - padding);\n");
        js.append("    ctx.stroke();\n");
        js.append("\n");
        js.append("    // 绘制数据点和线条\n");
        js.append("    const stepX = chartWidth / (dailyData.length - 1);\n");
        js.append("    ctx.strokeStyle = '#007bff';\n");
        js.append("    ctx.fillStyle = '#007bff';\n");
        js.append("    ctx.lineWidth = 2;\n");
        js.append("\n");
        js.append("    ctx.beginPath();\n");
        js.append("    for (let i = 0; i < dailyData.length; i++) {\n");
        js.append("        const x = padding + i * stepX;\n");
        js.append("        const y = height - padding - (dailyData[i].count / maxValue) * chartHeight;\n");
        js.append("\n");
        js.append("        if (i === 0) {\n");
        js.append("            ctx.moveTo(x, y);\n");
        js.append("        } else {\n");
        js.append("            ctx.lineTo(x, y);\n");
        js.append("        }\n");
        js.append("\n");
        js.append("        // 绘制数据点\n");
        js.append("        ctx.fillRect(x - 2, y - 2, 4, 4);\n");
        js.append("\n");
        js.append("        // 绘制日期标签\n");
        js.append("        ctx.fillStyle = '#666';\n");
        js.append("        ctx.font = '12px Arial';\n");
        js.append("        ctx.textAlign = 'center';\n");
        js.append("        ctx.fillText(dailyData[i].date, x, height - padding + 15);\n");
        js.append("        ctx.fillStyle = '#007bff';\n");
        js.append("    }\n");
        js.append("    ctx.stroke();\n");
        js.append("}\n");
        js.append("\n");

        return js.toString();
    }

    /**
     * 生成玩家相关的JavaScript函数
     */
    public String generatePlayerFunctions() {
        StringBuilder js = new StringBuilder();

        js.append("function displayTopPlayers(topPlayers) {\n");
        js.append("    const container = document.getElementById('topPlayersContainer');\n");
        js.append("    if (!container) return;\n");
        js.append("\n");
        js.append("    if (!topPlayers || topPlayers.length === 0) {\n");
        js.append("        container.innerHTML = '<p class=\"no-data\">暂无玩家数据</p>';\n");
        js.append("        return;\n");
        js.append("    }\n");
        js.append("\n");
        js.append("    // 按使用次数排序\n");
        js.append("    topPlayers.sort((a, b) => b.count - a.count);\n");
        js.append("\n");
        js.append("    let html = '<div class=\"top-players-list\">';\n");
        js.append("    for (let i = 0; i < Math.min(10, topPlayers.length); i++) {\n");
        js.append("        const player = topPlayers[i];\n");
        js.append("        const rank = i + 1;\n");
        js.append("        const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;\n");
        js.append("        \n");
        js.append("        html += `\n");
        js.append("            <div class=\"player-rank-item\">\n");
        js.append("                <span class=\"rank\">${medal}</span>\n");
        js.append("                <span class=\"player-name\">${player.player}</span>\n");
        js.append("                <span class=\"usage-count\">${player.count} 次</span>\n");
        js.append(
                "                <button class=\"btn btn-small\" onclick=\"searchPlayerByName('${player.player}')\">详情</button>\n");
        js.append("            </div>\n");
        js.append("        `;\n");
        js.append("    }\n");
        js.append("    html += '</div>';\n");
        js.append("    container.innerHTML = html;\n");
        js.append("}\n");
        js.append("\n");

        js.append("function displaySystemInfo(systemInfo) {\n");
        js.append("    const container = document.getElementById('systemInfo');\n");
        js.append("    if (!container) return;\n");
        js.append("\n");
        js.append(
                "    const expireTime = systemInfo.expire_seconds === -1 ? '永不失效' : formatTime(systemInfo.expire_seconds);\n");
        js.append("    const checkInterval = formatTime(systemInfo.check_interval);\n");
        js.append("\n");
        js.append("    container.innerHTML = `\n");
        js.append("        <div class=\"system-info-grid\">\n");
        js.append("            <div class=\"info-item\">\n");
        js.append("                <span class=\"info-label\">插件版本:</span>\n");
        js.append("                <span class=\"info-value\">${systemInfo.plugin_version}</span>\n");
        js.append("            </div>\n");
        js.append("            <div class=\"info-item\">\n");
        js.append("                <span class=\"info-label\">失效时间:</span>\n");
        js.append("                <span class=\"info-value\">${expireTime}</span>\n");
        js.append("            </div>\n");
        js.append("            <div class=\"info-item\">\n");
        js.append("                <span class=\"info-label\">检查间隔:</span>\n");
        js.append("                <span class=\"info-value\">${checkInterval}</span>\n");
        js.append("            </div>\n");
        js.append("            <div class=\"info-item\">\n");
        js.append("                <span class=\"info-label\">服务器时间:</span>\n");
        js.append(
                "                <span class=\"info-value\">${new Date(systemInfo.server_time).toLocaleString()}</span>\n");
        js.append("            </div>\n");
        js.append("        </div>\n");
        js.append("    `;\n");
        js.append("}\n");
        js.append("\n");

        js.append("function formatTime(seconds) {\n");
        js.append("    if (seconds < 60) return seconds + '秒';\n");
        js.append("    if (seconds < 3600) return Math.floor(seconds / 60) + '分钟';\n");
        js.append("    if (seconds < 86400) return Math.floor(seconds / 3600) + '小时';\n");
        js.append("    return Math.floor(seconds / 86400) + '天';\n");
        js.append("}\n");
        js.append("\n");

        return js.toString();
    }
}
