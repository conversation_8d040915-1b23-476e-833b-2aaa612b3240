package cn.acebrand.acekeysystem.web;

/**
 * 积分商店JavaScript代码生成器
 * 负责生成积分商店管理页面的JavaScript函数
 */
public class PointsShopJSGenerator {

        private final String adminKey;

        public PointsShopJSGenerator(String adminKey) {
                this.adminKey = adminKey;
        }

        /**
         * 生成编辑商店物品的JavaScript函数
         */
        public String generateEditShopItemFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 编辑商店物品\n");
                js.append("function editShopItem(itemId) {\n");
                js.append("    const item = shopItems.find(i => i.id === itemId);\n");
                js.append("    if (!item) {\n");
                js.append("        showResult('物品不存在', 'error');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    currentEditingItem = item;\n");
                js.append("    document.getElementById('editShopItemModalTitle').textContent = '编辑商店物品';\n");
                js.append("    document.getElementById('shopItemId').value = item.id;\n");
                js.append("    document.getElementById('shopItemName').value = item.name;\n");
                js.append("    document.getElementById('shopItemDescription').value = item.description;\n");
                js.append("    document.getElementById('shopItemCost').value = item.cost;\n");
                js.append("    document.getElementById('shopItemIcon').value = item.icon || '';\n");
                js.append("    document.getElementById('shopItemStock').value = item.stock;\n");
                js.append("    document.getElementById('shopItemMaxPurchase').value = item.maxPurchasePerPlayer;\n");
                js.append("    document.getElementById('shopItemResetInterval').value = item.resetIntervalHours;\n");
                js.append("    document.getElementById('shopItemEnabled').checked = item.enabled;\n");
                js.append("    \n");
                js.append("    // 设置指令\n");
                js.append("    const commands = item.commands || [];\n");
                js.append("    document.getElementById('shopItemCommands').value = commands.join('\\n');\n");
                js.append("    \n");
                js.append("    document.getElementById('editShopItemModal').style.display = 'block';\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成删除商店物品的JavaScript函数
         */
        public String generateDeleteShopItemFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 删除商店物品\n");
                js.append("function deleteShopItem(itemId) {\n");
                js.append("    const item = shopItems.find(i => i.id === itemId);\n");
                js.append("    if (!item) {\n");
                js.append("        showResult('物品不存在', 'error');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '🗑️ 删除商品',\n");
                js.append("        `确定要删除商品 <strong>\"${item.name}\"</strong> 吗？<br><br><span style=\"color: #ffeb3b;\">⚠️ 此操作不可恢复！</span>`,\n");
                js.append("        () => {\n");
                js.append("        fetch('/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'delete_shop_item',\n");
                js.append("                api_key: '").append(adminKey).append("',\n");
                js.append("                item_id: itemId\n");
                js.append("            })\n");
                js.append("        })\n");
                js.append("        .then(response => response.json())\n");
                js.append("        .then(data => {\n");
                js.append("            if (data.success) {\n");
                js.append("                showResult('✅ 物品删除成功', 'success');\n");
                js.append("                loadShopItems();\n");
                js.append("            } else {\n");
                js.append("                showResult('❌ ' + data.message, 'error');\n");
                js.append("            }\n");
                js.append("        })\n");
                js.append("        .catch(error => {\n");
                js.append("            showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("        });\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成切换商店物品状态的JavaScript函数
         */
        public String generateToggleStatusFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 切换商店物品启用/禁用状态\n");
                js.append("function toggleShopItemStatus(itemId) {\n");
                js.append("    const item = shopItems.find(i => i.id === itemId);\n");
                js.append("    if (!item) {\n");
                js.append("        showResult('物品不存在', 'error');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    const newStatus = !item.enabled;\n");
                js.append("    const statusText = newStatus ? '启用' : '禁用';\n");
                js.append("    \n");
                js.append("    showConfirmModal(\n");
                js.append("        '🛒 ' + statusText + '商店物品',\n");
                js.append("        '确定要' + statusText + '物品 \"' + item.name + '\" 吗？',\n");
                js.append("        function() {\n");
                js.append("        fetch('/api', {\n");
                js.append("            method: 'POST',\n");
                js.append("            headers: { 'Content-Type': 'application/json' },\n");
                js.append("            body: JSON.stringify({\n");
                js.append("                action: 'toggle_shop_item_status',\n");
                js.append("                api_key: '").append(adminKey).append("',\n");
                js.append("                item_id: itemId,\n");
                js.append("                enabled: newStatus\n");
                js.append("            })\n");
                js.append("        })\n");
                js.append("        .then(response => response.json())\n");
                js.append("        .then(data => {\n");
                js.append("            if (data.success) {\n");
                js.append("                showResult('✅ 物品状态更新成功', 'success');\n");
                js.append("                loadShopItems();\n");
                js.append("            } else {\n");
                js.append("                showResult('❌ ' + data.message, 'error');\n");
                js.append("            }\n");
                js.append("        })\n");
                js.append("        .catch(error => {\n");
                js.append("            showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("        });\n");
                js.append("        }\n");
                js.append("    );\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成保存商店物品的JavaScript函数
         */
        public String generateSaveShopItemFunction() {
                StringBuilder js = new StringBuilder();

                js.append("// 关闭编辑弹窗\n");
                js.append("function closeEditShopItemModal() {\n");
                js.append("    document.getElementById('editShopItemModal').style.display = 'none';\n");
                js.append("    currentEditingItem = null;\n");
                js.append("}\n");
                js.append("\n");

                js.append("// 保存商店物品\n");
                js.append("function saveShopItem(event) {\n");
                js.append("    event.preventDefault();\n");
                js.append("    \n");
                js.append("    const itemId = document.getElementById('shopItemId').value || 'item_' + Date.now();\n");
                js.append("    const name = document.getElementById('shopItemName').value.trim();\n");
                js.append("    const description = document.getElementById('shopItemDescription').value.trim();\n");
                js.append("    const cost = parseInt(document.getElementById('shopItemCost').value);\n");
                js.append("    const icon = document.getElementById('shopItemIcon').value.trim();\n");
                js.append("    const stock = parseInt(document.getElementById('shopItemStock').value) || -1;\n");
                js.append("    const maxPurchase = parseInt(document.getElementById('shopItemMaxPurchase').value) || -1;\n");
                js.append(
                                "    const resetInterval = parseInt(document.getElementById('shopItemResetInterval').value) || -1;\n");
                js.append("    const enabled = document.getElementById('shopItemEnabled').checked;\n");
                js.append("    const commandsText = document.getElementById('shopItemCommands').value.trim();\n");
                js.append("    \n");
                js.append("    if (!name || !description || !cost || cost <= 0) {\n");
                js.append("        showResult('请填写所有必填字段，积分消耗必须大于0', 'error');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append(
                                "    const commands = commandsText.split('\\n').filter(cmd => cmd.trim() !== '').map(cmd => cmd.trim());\n");
                js.append("    if (commands.length === 0) {\n");
                js.append("        showResult('请至少添加一条执行指令', 'error');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    const itemData = {\n");
                js.append("        action: 'save_shop_item',\n");
                js.append("        api_key: '").append(adminKey).append("',\n");
                js.append("        item_id: itemId,\n");
                js.append("        name: name,\n");
                js.append("        description: description,\n");
                js.append("        cost: cost,\n");
                js.append("        icon: icon || '📦',\n");
                js.append("        stock: stock,\n");
                js.append("        max_purchase_per_player: maxPurchase,\n");
                js.append("        reset_interval_hours: resetInterval,\n");
                js.append("        enabled: enabled,\n");
                js.append("        commands: commands\n");
                js.append("    };\n");
                js.append("    \n");
                js.append("    fetch('/api', {\n");
                js.append("        method: 'POST',\n");
                js.append("        headers: { 'Content-Type': 'application/json' },\n");
                js.append("        body: JSON.stringify(itemData)\n");
                js.append("    })\n");
                js.append("    .then(response => response.json())\n");
                js.append("    .then(data => {\n");
                js.append("        if (data.success) {\n");
                js.append("            showResult('✅ 商店物品保存成功', 'success');\n");
                js.append("            closeEditShopItemModal();\n");
                js.append("            loadShopItems();\n");
                js.append("        } else {\n");
                js.append("            showResult('❌ ' + data.message, 'error');\n");
                js.append("        }\n");
                js.append("    })\n");
                js.append("    .catch(error => {\n");
                js.append("        showResult('❌ 网络错误: ' + error.message, 'error');\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }

        /**
         * 生成辅助函数
         */
        public String generateUtilityFunctions() {
                StringBuilder js = new StringBuilder();

                js.append("// 搜索过滤商店物品\n");
                js.append("function filterShopItems() {\n");
                js.append("    const searchTerm = document.getElementById('shopSearchInput').value.toLowerCase();\n");
                js.append("    const container = document.getElementById('shopItemsContainer');\n");
                js.append("    if (!container) return;\n");
                js.append("    \n");
                js.append("    const filteredItems = shopItems.filter(item => \n");
                js.append("        item.name.toLowerCase().includes(searchTerm) || \n");
                js.append("        item.description.toLowerCase().includes(searchTerm)\n");
                js.append("    );\n");
                js.append("    \n");
                js.append("    // 临时保存原始数据\n");
                js.append("    const originalItems = shopItems;\n");
                js.append("    shopItems = filteredItems;\n");
                js.append("    displayShopItems();\n");
                js.append("    shopItems = originalItems;\n");
                js.append("}\n");
                js.append("\n");

                // 图标选择器功能
                js.append("// 当前选择图标的目标输入框\n");
                js.append("let currentIconTarget = null;\n");
                js.append("\n");
                js.append("// 积分商店管理页面专用图标数据\n");
                js.append("const shopIconData = {\n");
                js.append("    trophy: ['🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '👑', '🎗️'],\n");
                js.append("    gem: ['💎', '💍', '💰', '🪙', '💳', '💵', '💴', '💶', '💷', '🔮', '⭐', '🌟', '✨'],\n");
                js.append("    tool: ['🔧', '🔨', '⚒️', '🛠️', '⚔️', '🗡️', '🏹', '🛡️', '🔱', '⚡', '🔥', '❄️'],\n");
                js.append(
                                "    food: ['🍖', '🍗', '🥩', '🍞', '🥖', '🧀', '🥚', '🍳', '🥓', '🍎', '🍊', '🍌', '🍇', '🍓', '🥝', '🍑', '🍒', '🥥', '🍰', '🎂', '🧁', '🍪', '🍫', '🍬', '🍭'],\n");
                js.append(
                                "    other: ['🎁', '🎀', '🎊', '🎉', '🎈', '🎯', '🎲', '🃏', '🎪', '🎭', '🎨', '🎵', '🎶', '🎸', '🎺', '🎻', '🥁', '🎤', '🎧', '📱', '💻', '⌚', '📷', '🔍', '🔒', '🗝️', '🚀', '⚓', '🌈', '☀️', '🌙', '⭐', '🌍', '🌎', '🌏']\n");
                js.append("};\n");
                js.append("\n");

                js.append("function showIconSelector(targetInputId) {\n");
                js.append("    // 设置当前目标输入框\n");
                js.append("    console.log('showIconSelector called with targetInputId:', targetInputId);\n");
                js.append("    if (targetInputId) {\n");
                js.append("        currentIconTarget = document.getElementById(targetInputId);\n");
                js.append("        console.log('Found target element:', currentIconTarget);\n");
                js.append("    } else {\n");
                js.append("        // 默认为图标输入框\n");
                js.append("        currentIconTarget = document.getElementById('shopItemIcon');\n");
                js.append("        console.log('Using default target element:', currentIconTarget);\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    if (!currentIconTarget) {\n");
                js.append("        console.error('Target input element not found!');\n");
                js.append("        return;\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    document.getElementById('iconSelectorModal').style.display = 'block';\n");
                js.append("    showIconCategory('all');\n");
                js.append("}\n");
                js.append("\n");

                js.append("function closeIconSelector() {\n");
                js.append("    document.getElementById('iconSelectorModal').style.display = 'none';\n");
                js.append("}\n");
                js.append("\n");

                js.append("function showIconCategory(category) {\n");
                js.append("    // 更新分类按钮状态\n");
                js.append("    document.querySelectorAll('.category-btn').forEach(btn => btn.classList.remove('active'));\n");
                js.append("    event.target.classList.add('active');\n");
                js.append("    \n");
                js.append("    // 获取要显示的图标\n");
                js.append("    let icons = [];\n");
                js.append("    if (category === 'all') {\n");
                js.append("        icons = Object.values(shopIconData).flat();\n");
                js.append("    } else {\n");
                js.append("        icons = shopIconData[category] || [];\n");
                js.append("    }\n");
                js.append("    \n");
                js.append("    // 生成图标网格\n");
                js.append("    const iconGrid = document.getElementById('iconGrid');\n");
                js.append("    iconGrid.innerHTML = '';\n");
                js.append("    \n");
                js.append("    icons.forEach(icon => {\n");
                js.append("        const iconItem = document.createElement('div');\n");
                js.append("        iconItem.className = 'icon-item';\n");
                js.append("        iconItem.textContent = icon;\n");
                js.append("        iconItem.title = '点击选择: ' + icon;\n");
                js.append("        iconItem.onclick = () => selectIcon(icon);\n");
                js.append("        iconGrid.appendChild(iconItem);\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                js.append("function selectIcon(icon) {\n");
                js.append("    console.log('selectIcon called with icon:', icon);\n");
                js.append("    console.log('currentIconTarget:', currentIconTarget);\n");
                js.append("    \n");
                js.append("    // 复制到剪贴板\n");
                js.append("    navigator.clipboard.writeText(icon).then(() => {\n");
                js.append("        showResult('✅ 图标 ' + icon + ' 已选择', 'success');\n");
                js.append("        \n");
                js.append("        // 插入到当前目标输入框\n");
                js.append("        if (currentIconTarget) {\n");
                js.append("            const targetId = currentIconTarget.id;\n");
                js.append("            console.log('Target element ID:', targetId);\n");
                js.append("            \n");
                js.append("            if (targetId === 'shopItemName') {\n");
                js.append("                // 对于物品名称，在光标位置插入图标\n");
                js.append("                const cursorPos = currentIconTarget.selectionStart;\n");
                js.append("                const currentValue = currentIconTarget.value;\n");
                js.append(
                                "                const newValue = currentValue.slice(0, cursorPos) + icon + ' ' + currentValue.slice(cursorPos);\n");
                js.append("                currentIconTarget.value = newValue;\n");
                js.append("                currentIconTarget.focus();\n");
                js.append(
                                "                currentIconTarget.setSelectionRange(cursorPos + icon.length + 1, cursorPos + icon.length + 1);\n");
                js.append("            } else {\n");
                js.append("                // 对于图标输入框，直接替换内容\n");
                js.append("                currentIconTarget.value = icon;\n");
                js.append("                currentIconTarget.focus();\n");
                js.append("            }\n");
                js.append("        } else {\n");
                js.append("            // 兼容旧版本，默认插入到图标输入框\n");
                js.append("            const iconInput = document.getElementById('shopItemIcon');\n");
                js.append("            if (iconInput) {\n");
                js.append("                iconInput.value = icon;\n");
                js.append("                iconInput.focus();\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        closeIconSelector();\n");
                js.append("    }).catch(err => {\n");
                js.append("        console.error('复制失败:', err);\n");
                js.append("        showResult('❌ 复制失败，但图标已插入', 'warning');\n");
                js.append("        \n");
                js.append("        // 即使复制失败，也要插入图标\n");
                js.append("        if (currentIconTarget) {\n");
                js.append("            const targetId = currentIconTarget.id;\n");
                js.append("            \n");
                js.append("            if (targetId === 'shopItemName') {\n");
                js.append("                const cursorPos = currentIconTarget.selectionStart;\n");
                js.append("                const currentValue = currentIconTarget.value;\n");
                js.append(
                                "                const newValue = currentValue.slice(0, cursorPos) + icon + ' ' + currentValue.slice(cursorPos);\n");
                js.append("                currentIconTarget.value = newValue;\n");
                js.append("                currentIconTarget.focus();\n");
                js.append(
                                "                currentIconTarget.setSelectionRange(cursorPos + icon.length + 1, cursorPos + icon.length + 1);\n");
                js.append("            } else {\n");
                js.append("                currentIconTarget.value = icon;\n");
                js.append("                currentIconTarget.focus();\n");
                js.append("            }\n");
                js.append("        }\n");
                js.append("        \n");
                js.append("        closeIconSelector();\n");
                js.append("    });\n");
                js.append("}\n");
                js.append("\n");

                return js.toString();
        }
}
