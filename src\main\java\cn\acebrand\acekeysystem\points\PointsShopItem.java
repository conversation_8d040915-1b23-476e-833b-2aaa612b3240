package cn.acebrand.acekeysystem.points;

import java.util.List;

/**
 * 积分商店物品
 * 表示可以用积分兑换的物品
 */
public class PointsShopItem {

    private String id;
    private String name;
    private String description;
    private int cost;
    private String icon;
    private List<String> commands;
    private boolean enabled;
    private int stock;
    private int maxPurchasePerPlayer;
    private int resetIntervalHours; // 限购重置间隔（小时），-1表示永不重置

    /**
     * 构造函数
     */
    public PointsShopItem(String id, String name, String description, int cost, String icon,
            List<String> commands, boolean enabled, int stock, int maxPurchasePerPlayer, int resetIntervalHours) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.cost = cost;
        this.icon = icon;
        this.commands = commands;
        this.enabled = enabled;
        this.stock = stock;
        this.maxPurchasePerPlayer = maxPurchasePerPlayer;
        this.resetIntervalHours = resetIntervalHours;
    }

    /**
     * 默认构造函数
     */
    public PointsShopItem() {
        this.enabled = true;
        this.stock = -1; // -1表示无限库存
        this.maxPurchasePerPlayer = -1; // -1表示无限制
        this.resetIntervalHours = -1; // -1表示永不重置
    }

    // Getter和Setter方法

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public List<String> getCommands() {
        return commands;
    }

    public void setCommands(List<String> commands) {
        this.commands = commands;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public int getStock() {
        return stock;
    }

    public void setStock(int stock) {
        this.stock = stock;
    }

    public int getMaxPurchasePerPlayer() {
        return maxPurchasePerPlayer;
    }

    public void setMaxPurchasePerPlayer(int maxPurchasePerPlayer) {
        this.maxPurchasePerPlayer = maxPurchasePerPlayer;
    }

    public int getResetIntervalHours() {
        return resetIntervalHours;
    }

    public void setResetIntervalHours(int resetIntervalHours) {
        this.resetIntervalHours = resetIntervalHours;
    }

    /**
     * 检查是否有库存
     */
    public boolean hasStock() {
        return stock == -1 || stock > 0;
    }

    /**
     * 减少库存
     */
    public void decreaseStock() {
        if (stock > 0) {
            stock--;
        }
    }

    /**
     * 增加库存
     */
    public void increaseStock(int amount) {
        if (stock == -1) {
            return; // 无限库存不需要增加
        }
        stock += amount;
    }

    /**
     * 检查是否有命令
     */
    public boolean hasCommands() {
        return commands != null && !commands.isEmpty();
    }

    /**
     * 克隆物品
     */
    public PointsShopItem clone() {
        PointsShopItem cloned = new PointsShopItem();
        cloned.setId(this.id);
        cloned.setName(this.name);
        cloned.setDescription(this.description);
        cloned.setCost(this.cost);
        cloned.setIcon(this.icon);
        cloned.setCommands(this.commands);
        cloned.setEnabled(this.enabled);
        cloned.setStock(this.stock);
        cloned.setMaxPurchasePerPlayer(this.maxPurchasePerPlayer);
        return cloned;
    }

    @Override
    public String toString() {
        return "PointsShopItem{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", cost=" + cost +
                ", icon='" + icon + '\'' +
                ", enabled=" + enabled +
                ", stock=" + stock +
                ", maxPurchasePerPlayer=" + maxPurchasePerPlayer +
                '}';
    }
}
