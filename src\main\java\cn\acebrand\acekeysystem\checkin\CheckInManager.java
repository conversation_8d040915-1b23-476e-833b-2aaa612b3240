package cn.acebrand.acekeysystem.checkin;

import cn.acebrand.acekeysystem.AceKeySystem;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 签到系统管理器
 * 负责处理玩家签到、奖励发放和数据管理
 */
public class CheckInManager {

    private final AceKeySystem plugin;
    private File checkInFile;
    private FileConfiguration checkInConfig;
    private File rewardsFile;
    private FileConfiguration rewardsConfig;

    // 日期格式化器
    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private final SimpleDateFormat monthFormat = new SimpleDateFormat("yyyy-MM");

    public CheckInManager(AceKeySystem plugin) {
        this.plugin = plugin;
        initializeFiles();
        loadConfiguration();
    }

    /**
     * 初始化配置文件
     */
    private void initializeFiles() {
        // 签到数据文件
        checkInFile = new File(plugin.getDataFolder(), "checkin_data.yml");
        if (!checkInFile.exists()) {
            try {
                checkInFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建签到数据文件: " + e.getMessage());
            }
        }
        checkInConfig = YamlConfiguration.loadConfiguration(checkInFile);

        // 奖励配置文件
        rewardsFile = new File(plugin.getDataFolder(), "checkin_rewards.yml");
        boolean needsDefaultRewards = !rewardsFile.exists();

        if (needsDefaultRewards) {
            try {
                rewardsFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建签到奖励文件: " + e.getMessage());
            }
        }

        // 先加载配置文件
        rewardsConfig = YamlConfiguration.loadConfiguration(rewardsFile);

        // 如果是新文件，创建默认奖励
        if (needsDefaultRewards) {
            createDefaultRewards();
        }
    }

    /**
     * 加载配置
     */
    private void loadConfiguration() {
        try {
            checkInConfig = YamlConfiguration.loadConfiguration(checkInFile);
            rewardsConfig = YamlConfiguration.loadConfiguration(rewardsFile);
        } catch (Exception e) {
            plugin.getLogger().severe("加载签到配置失败: " + e.getMessage());
        }
    }

    /**
     * 创建默认奖励配置
     */
    private void createDefaultRewards() {
        // 每日签到奖励
        rewardsConfig.set("daily_rewards.1.points", 10);
        rewardsConfig.set("daily_rewards.1.commands",
                Arrays.asList("tellraw {player} {\"text\":\"签到成功！获得10积分\",\"color\":\"green\"}"));

        rewardsConfig.set("daily_rewards.7.points", 50);
        rewardsConfig.set("daily_rewards.7.commands", Arrays.asList(
                "give {player} diamond 1",
                "tellraw {player} {\"text\":\"连续签到7天！获得钻石奖励\",\"color\":\"gold\"}"));

        rewardsConfig.set("daily_rewards.15.points", 100);
        rewardsConfig.set("daily_rewards.15.commands", Arrays.asList(
                "give {player} diamond 3",
                "tellraw {player} {\"text\":\"连续签到15天！获得豪华奖励\",\"color\":\"gold\"}"));

        rewardsConfig.set("daily_rewards.30.points", 200);
        rewardsConfig.set("daily_rewards.30.commands", Arrays.asList(
                "give {player} diamond 5",
                "give {player} emerald 10",
                "tellraw {player} {\"text\":\"连续签到30天！获得超级奖励\",\"color\":\"gold\"}"));

        // 月度签到奖励
        String currentMonth = monthFormat.format(new Date());
        for (int day = 1; day <= 31; day++) {
            int points = day * 2;
            rewardsConfig.set("monthly_rewards." + currentMonth + "." + day + ".points", points);
            rewardsConfig.set("monthly_rewards." + currentMonth + "." + day + ".commands",
                    Arrays.asList("tellraw {player} {\"text\":\"第" + day + "天签到奖励！\",\"color\":\"yellow\"}"));
        }

        saveRewardsConfig();
    }

    /**
     * 玩家签到
     */
    public CheckInResult checkIn(String playerName) {
        try {
            plugin.getLogger().info("开始处理玩家签到: " + playerName);

            // 首先检查玩家是否已绑定
            UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
            if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
                plugin.getLogger().info("玩家 " + playerName + " 未绑定账号");
                return new CheckInResult(false, "您还未绑定账号，请先在积分商店绑定后再签到！", 0, 0);
            }

            String today = dateFormat.format(new Date());
            String playerPath = "players." + playerName;
            plugin.getLogger().info("签到日期: " + today + ", 玩家路径: " + playerPath);

            // 检查今天是否已签到
            if (checkInConfig.getBoolean(playerPath + ".dates." + today, false)) {
                plugin.getLogger().info("玩家 " + playerName + " 今天已经签到过了");
                return new CheckInResult(false, "今天已经签到过了！", 0, 0);
            }

            // 获取连续签到天数
            int consecutiveDays = getConsecutiveDays(playerName);
            consecutiveDays++; // 今天签到后增加1天
            plugin.getLogger().info("玩家 " + playerName + " 连续签到天数: " + consecutiveDays);

            // 记录签到
            checkInConfig.set(playerPath + ".dates." + today, true);
            checkInConfig.set(playerPath + ".last_checkin", today);
            checkInConfig.set(playerPath + ".consecutive_days", consecutiveDays);
            checkInConfig.set(playerPath + ".total_days", checkInConfig.getInt(playerPath + ".total_days", 0) + 1);

            plugin.getLogger().info("保存签到配置...");
            saveCheckInConfig();

            // 获取当前日期
            Calendar calendar = Calendar.getInstance();
            int currentDay = calendar.get(Calendar.DAY_OF_MONTH);
            plugin.getLogger().info("当前日期: " + currentDay);

            // 获取每日奖励配置
            CheckInReward dailyReward = getDailyReward(currentDay);
            if (dailyReward == null) {
                dailyReward = new CheckInReward(10, new ArrayList<>()); // 默认奖励
            }
            plugin.getLogger().info("每日奖励: " + dailyReward.getPoints() + " 积分");

            // 发放每日奖励
            plugin.getLogger().info("发放每日奖励...");
            giveReward(playerName, dailyReward);

            // 发放连续签到奖励
            CheckInReward consecutiveReward = getConsecutiveReward(consecutiveDays);
            if (consecutiveReward != null) {
                plugin.getLogger().info("发放连续签到奖励: " + consecutiveReward.getPoints() + " 积分");
                giveReward(playerName, consecutiveReward);
            }

            int totalPoints = dailyReward.getPoints() + (consecutiveReward != null ? consecutiveReward.getPoints() : 0);
            plugin.getLogger().info("签到完成，总积分: " + totalPoints);

            return new CheckInResult(true, "签到成功！连续签到" + consecutiveDays + "天，获得" + totalPoints + "积分",
                    consecutiveDays, totalPoints);

        } catch (Exception e) {
            plugin.getLogger().severe("玩家 " + playerName + " 签到时发生错误: " + e.getMessage());
            e.printStackTrace();
            return new CheckInResult(false, "签到失败，请稍后重试", 0, 0);
        }
    }

    /**
     * 获取连续签到天数
     */
    public int getConsecutiveDays(String playerName) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return 0;
        }
        return checkInConfig.getInt("players." + playerName + ".consecutive_days", 0);
    }

    /**
     * 重新计算连续签到天数（包括补签）
     * 从今天开始往前计算，找到最长的连续签到天数
     */
    private int calculateConsecutiveDays(String playerName) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return 0;
        }

        String playerPath = "players." + playerName;
        Calendar calendar = Calendar.getInstance();
        int consecutiveDays = 0;

        // 从今天开始往前检查每一天
        for (int i = 0; i < 31; i++) { // 最多检查31天（一个月）
            String checkDate = dateFormat.format(calendar.getTime());

            // 检查这一天是否签到过（包括正常签到和补签）
            if (checkInConfig.getBoolean(playerPath + ".dates." + checkDate, false)) {
                consecutiveDays++;
                // 往前推一天
                calendar.add(Calendar.DAY_OF_MONTH, -1);
            } else {
                // 如果某一天没有签到，连续性就断了
                break;
            }
        }

        plugin.getLogger().info("玩家 " + playerName + " 重新计算的连续签到天数: " + consecutiveDays);
        return consecutiveDays;
    }

    /**
     * 获取总签到天数
     */
    public int getTotalDays(String playerName) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return 0;
        }
        return checkInConfig.getInt("players." + playerName + ".total_days", 0);
    }

    /**
     * 检查今天是否已签到
     */
    public boolean hasCheckedInToday(String playerName) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return false;
        }
        String today = dateFormat.format(new Date());
        return checkInConfig.getBoolean("players." + playerName + ".dates." + today, false);
    }

    /**
     * 获取连续签到奖励
     */
    private CheckInReward getConsecutiveReward(int consecutiveDays) {
        ConfigurationSection consecutiveRewards = rewardsConfig.getConfigurationSection("consecutive_rewards");
        if (consecutiveRewards == null) {
            return null;
        }

        // 查找最接近的连续奖励配置
        int bestMatch = 0;
        for (String dayStr : consecutiveRewards.getKeys(false)) {
            try {
                int rewardDay = Integer.parseInt(dayStr);
                if (rewardDay <= consecutiveDays && rewardDay > bestMatch) {
                    bestMatch = rewardDay;
                }
            } catch (NumberFormatException ignored) {
            }
        }

        if (bestMatch > 0) {
            ConfigurationSection rewardSection = consecutiveRewards.getConfigurationSection(String.valueOf(bestMatch));
            if (rewardSection != null) {
                int points = rewardSection.getInt("points", 0);
                List<String> commands = rewardSection.getStringList("commands");
                return new CheckInReward(points, commands);
            }
        }

        return null;
    }

    /**
     * 发放奖励
     */
    private void giveReward(String playerName, CheckInReward reward) {
        try {
            // 发放积分
            if (reward.getPoints() > 0) {
                plugin.getLogger().info("为玩家 " + playerName + " 发放 " + reward.getPoints() + " 积分");
                plugin.getPointsManager().addPlayerPoints(playerName, reward.getPoints());
                plugin.getLogger().info("积分发放成功");
            }

            // 执行命令（需要在主线程中执行）
            for (String command : reward.getCommands()) {
                try {
                    String processedCommand = command.replace("{player}", playerName);
                    plugin.getLogger().info("调度奖励命令到主线程: " + processedCommand);

                    // 使用Bukkit调度器在主线程中执行命令
                    plugin.getServer().getScheduler().runTask(plugin, () -> {
                        try {
                            boolean result = plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(),
                                    processedCommand);
                            plugin.getLogger().info("命令执行结果: " + result + " - " + processedCommand);
                        } catch (Exception e) {
                            plugin.getLogger().warning("主线程执行命令失败: " + processedCommand + ", 错误: " + e.getMessage());
                        }
                    });
                } catch (Exception e) {
                    plugin.getLogger().warning("调度奖励命令失败: " + command + ", 错误: " + e.getMessage());
                }
            }
        } catch (Exception e) {
            plugin.getLogger().severe("发放奖励时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存签到配置
     */
    private void saveCheckInConfig() {
        try {
            checkInConfig.save(checkInFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存签到配置失败: " + e.getMessage());
        }
    }

    /**
     * 保存奖励配置
     */
    private void saveRewardsConfig() {
        try {
            rewardsConfig.save(rewardsFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存奖励配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取奖励配置
     */
    public FileConfiguration getRewardsConfig() {
        return rewardsConfig;
    }

    /**
     * 获取签到配置
     */
    public FileConfiguration getCheckInConfig() {
        return checkInConfig;
    }

    /**
     * 获取玩家绑定状态信息
     */
    public PlayerBindingStatus getPlayerBindingStatus(String playerName) {
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);

        if (playerUuid == null) {
            return new PlayerBindingStatus(false, "玩家不存在", 0, 0);
        }

        boolean isBound = plugin.getBindingDataManager().isPlayerBound(playerUuid);
        if (!isBound) {
            return new PlayerBindingStatus(false, "未绑定", 0, 0);
        }

        long bindTime = plugin.getBindingDataManager().getPlayerBindTime(playerUuid);
        long nextUnbindTime = plugin.getBindingDataManager().getNextUnbindTime(playerUuid);

        return new PlayerBindingStatus(true, "已绑定", bindTime, nextUnbindTime);
    }

    /**
     * 获取指定日期的奖励配置
     */
    public CheckInReward getDailyReward(int day) {
        if (day < 1 || day > 31) {
            return null;
        }

        String path = "daily_rewards." + day;
        int points = rewardsConfig.getInt(path + ".points", 10);
        List<String> commands = rewardsConfig.getStringList(path + ".commands");

        return new CheckInReward(points, commands);
    }

    /**
     * 设置指定日期的奖励配置
     */
    public void setDailyReward(int day, int points, List<String> commands) {
        if (day < 1 || day > 31) {
            return;
        }

        String path = "daily_rewards." + day;
        rewardsConfig.set(path + ".points", points);
        rewardsConfig.set(path + ".commands", commands);

        saveRewardsConfig();
    }

    /**
     * 获取玩家的签到日历数据
     */
    public List<String> getPlayerCheckinDays(String playerName) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return new ArrayList<>();
        }

        List<String> checkinDays = new ArrayList<>();
        String playerPath = "players." + playerName + ".dates";

        if (checkInConfig.contains(playerPath)) {
            ConfigurationSection datesSection = checkInConfig.getConfigurationSection(playerPath);
            if (datesSection != null) {
                for (String date : datesSection.getKeys(false)) {
                    if (datesSection.getBoolean(date, false)) {
                        checkinDays.add(date);
                    }
                }
            }
        }

        return checkinDays;
    }

    /**
     * 获取玩家的补签卡数量
     */
    public int getMakeupCardCount(String playerName) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return 0;
        }
        return checkInConfig.getInt("players." + playerName + ".makeup_cards", 0);
    }

    /**
     * 给玩家添加补签卡
     */
    public void addMakeupCards(String playerName, int count) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return;
        }

        int currentCount = getMakeupCardCount(playerName);
        checkInConfig.set("players." + playerName + ".makeup_cards", currentCount + count);
        saveCheckInConfig();
    }

    /**
     * 使用补签卡进行补签
     */
    public CheckInResult makeupCheckIn(String playerName, int day) {
        // 检查玩家是否已绑定
        UUID playerUuid = plugin.getBindingDataManager().findPlayerUuidByUsername(playerName);
        if (playerUuid == null || !plugin.getBindingDataManager().isPlayerBound(playerUuid)) {
            return new CheckInResult(false, "您还未绑定账号，请先在积分商店绑定后再补签！", 0, 0);
        }

        // 检查是否有补签卡
        int cardCount = getMakeupCardCount(playerName);
        if (cardCount <= 0) {
            return new CheckInResult(false, "您没有补签卡，请先购买补签卡！", 0, 0);
        }

        // 检查日期是否有效
        if (day < 1 || day > 31) {
            return new CheckInResult(false, "补签日期无效！", 0, 0);
        }

        // 获取当前月份
        Calendar calendar = Calendar.getInstance();
        String currentMonth = monthFormat.format(calendar.getTime());

        // 构造要补签的日期
        String targetDate;
        if (day < 10) {
            targetDate = currentMonth + "-0" + day;
        } else {
            targetDate = currentMonth + "-" + day;
        }

        String playerPath = "players." + playerName;

        // 检查该日期是否已经签到过
        if (checkInConfig.getBoolean(playerPath + ".dates." + targetDate, false)) {
            return new CheckInResult(false, "第" + day + "天已经签到过了，无需补签！", 0, 0);
        }

        // 检查是否是未来的日期
        Calendar targetCalendar = Calendar.getInstance();
        targetCalendar.set(Calendar.DAY_OF_MONTH, day);
        if (targetCalendar.after(Calendar.getInstance())) {
            return new CheckInResult(false, "不能补签未来的日期！", 0, 0);
        }

        // 扣除补签卡
        checkInConfig.set(playerPath + ".makeup_cards", cardCount - 1);

        // 记录补签
        checkInConfig.set(playerPath + ".dates." + targetDate, true);
        checkInConfig.set(playerPath + ".total_days", checkInConfig.getInt(playerPath + ".total_days", 0) + 1);

        // 重新计算连续签到天数（补签后可能会改变连续性）
        int newConsecutiveDays = calculateConsecutiveDays(playerName);
        checkInConfig.set(playerPath + ".consecutive_days", newConsecutiveDays);

        saveCheckInConfig();

        // 获取每日奖励配置
        CheckInReward dailyReward = getDailyReward(day);
        if (dailyReward == null) {
            dailyReward = new CheckInReward(10, new ArrayList<>()); // 默认奖励
        }

        // 发放奖励
        giveReward(playerName, dailyReward);

        return new CheckInResult(true, "补签成功！获得" + dailyReward.getPoints() + "积分，剩余补签卡：" + (cardCount - 1) + "张",
                newConsecutiveDays, dailyReward.getPoints());
    }

    /**
     * 重新加载配置
     */
    public void reload() {
        loadConfiguration();
    }

    /**
     * 签到结果类
     */
    public static class CheckInResult {
        private final boolean success;
        private final String message;
        private final int consecutiveDays;
        private final int pointsEarned;

        public CheckInResult(boolean success, String message, int consecutiveDays, int pointsEarned) {
            this.success = success;
            this.message = message;
            this.consecutiveDays = consecutiveDays;
            this.pointsEarned = pointsEarned;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getMessage() {
            return message;
        }

        public int getConsecutiveDays() {
            return consecutiveDays;
        }

        public int getPointsEarned() {
            return pointsEarned;
        }
    }

    /**
     * 签到奖励类
     */
    public static class CheckInReward {
        private final int points;
        private final List<String> commands;

        public CheckInReward(int points, List<String> commands) {
            this.points = points;
            this.commands = commands != null ? commands : new ArrayList<>();
        }

        public int getPoints() {
            return points;
        }

        public List<String> getCommands() {
            return commands;
        }
    }

    /**
     * 玩家绑定状态类
     */
    public static class PlayerBindingStatus {
        private final boolean isBound;
        private final String status;
        private final long bindTime;
        private final long nextUnbindTime;

        public PlayerBindingStatus(boolean isBound, String status, long bindTime, long nextUnbindTime) {
            this.isBound = isBound;
            this.status = status;
            this.bindTime = bindTime;
            this.nextUnbindTime = nextUnbindTime;
        }

        public boolean isBound() {
            return isBound;
        }

        public String getStatus() {
            return status;
        }

        public long getBindTime() {
            return bindTime;
        }

        public long getNextUnbindTime() {
            return nextUnbindTime;
        }
    }
}
