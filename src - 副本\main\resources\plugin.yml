name: AceKeySystem
version: 1.0-SNAPSHOT
main: cn.acebrand.acekeysystem.AceKeySystem
api-version: '1.13'

commands:
  acekeys:
    description: 批量生成指定数量的卡密
    usage: |-
      /acekeys <数量> [显示信息]
      示例: /acekeys 10 恭喜获得VIP奖励！
    permission: acekeysystem.admin
  acereload:
    description: 重新加载插件配置
    usage: /acereload
    permission: acekeysystem.admin
  acecount:
    description: 查看剩余卡密数量
    usage: /acecount
    permission: acekeysystem.admin
  aceget:
    description: 获取一个卡密（自动从系统中删除）
    usage: /aceget
    permission: acekeysystem.use
  aceitem:
    description: 获取一个卡密兑换券（右键使用获得卡密）
    usage: |-
      /aceitem [玩家名]
      示例: /aceitem 给自己
      示例: /aceitem Steve 给指定玩家
    permission: acekeysystem.use
  acesync:
    description: 手动同步卡密到网站
    usage: /acesync
    permission: acekeysystem.admin
  acerewards:
    description: 检查并执行网站上的抽奖奖励
    usage: /acerewards
    permission: acekeysystem.admin
  acehelp:
    description: 显示AceKeySystem插件的所有指令帮助
    usage: /acehelp
    permission: acekeysystem.admin
  acepoints:
    description: 积分系统管理命令
    usage: |-
      /acepoints check [玩家名] - 查看积分
      /acepoints add <玩家名> <数量> - 增加积分（管理员）
      /acepoints remove <玩家名> <数量> - 扣除积分（管理员）
      /acepoints set <玩家名> <数量> - 设置积分（管理员）
      /acepoints top - 查看积分排行榜
    permission: acekeysystem.use
  acebind:
    description: 绑定积分商店账户
    usage: |-
      /acebind <绑定码>
      在积分商店网页上获取绑定码，然后使用此命令绑定账户

permissions:
  acekeysystem.admin:
    description: 允许使用AceKeySystem的所有管理命令
    default: op
  acekeysystem.use:
    description: 允许使用 /aceget 和 /aceitem 命令获取卡密
    default: op
