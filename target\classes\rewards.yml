# 抽奖奖励配置文件
# 这个文件专门用于管理所有的抽奖奖励
# 修改后可以通过 /reloadkeyconfig 命令重载

# 奖励配置说明:
# id: 奖励的唯一标识符
# name: 奖励显示名称
# description: 奖励描述
# weight: 权重，影响抽中概率
# probability: 显示概率百分比（仅用于显示，实际概率由权重决定）
# commands: 执行的命令列表，支持变量替换

# 支持的变量:
# {player} - 玩家名称
# {player_name} - 玩家名称
# {player_uuid} - 玩家UUID
# {player_displayname} - 玩家显示名称
# {world} - 玩家所在世界
# {x}, {y}, {z} - 玩家坐标
# 如果安装了PlaceholderAPI，还支持所有PAPI变量，如 %player_level%, %vault_eco_balance% 等

rewards:
  # 钻石礼包 - 稀有奖励
  diamond_pack:
    name: "💎 钻石礼包"
    description: "获得10个珍贵的钻石和特殊消息"
    weight: 15
    probability: 15.0
    commands:
      - "give {player} diamond 10"
      - "tellraw {player} {\"text\":\"🎉 恭喜获得钻石礼包！\",\"color\":\"aqua\",\"bold\":true}"
      - "playsound entity.player.levelup master {player} ~ ~ ~ 1 1"

  # 金锭大礼包 - 常见奖励
  gold_pack:
    name: "🥇 金锭大礼包"
    description: "获得20个金锭，财富满满"
    weight: 25
    probability: 25.0
    commands:
      - "give {player} gold_ingot 20"
      - "tellraw {player} {\"text\":\"💰 恭喜获得金锭大礼包！\",\"color\":\"gold\"}"
      - "playsound entity.experience_orb.pickup master {player} ~ ~ ~ 1 1"

  # 经验宝瓶 - 常见奖励
  exp_bottles:
    name: "🧪 经验宝瓶"
    description: "获得5瓶经验，快速升级"
    weight: 30
    probability: 30.0
    commands:
      - "give {player} experience_bottle 5"
      - "tellraw {player} {\"text\":\"✨ 恭喜获得经验宝瓶！\",\"color\":\"green\"}"
      - "playsound entity.experience_orb.pickup master {player} ~ ~ ~ 1 0.8"

  # 绿宝石珍藏 - 稀有奖励
  emerald_treasure:
    name: "💚 绿宝石珍藏"
    description: "获得5个稀有绿宝石"
    weight: 15
    probability: 15.0
    commands:
      - "give {player} emerald 5"
      - "tellraw {player} {\"text\":\"💚 恭喜获得绿宝石珍藏！\",\"color\":\"dark_green\"}"
      - "playsound block.note_block.chime master {player} ~ ~ ~ 1 1"

  # 铁锭套装 - 普通奖励
  iron_set:
    name: "⚔️ 铁锭套装"
    description: "获得15个铁锭"
    weight: 20
    probability: 20.0
    commands:
      - "give {player} iron_ingot 15"
      - "tellraw {player} {\"text\":\"⚔️ 恭喜获得铁锭套装！\",\"color\":\"gray\"}"

  # 食物大礼包 - 普通奖励
  food_pack:
    name: "🍖 食物大礼包"
    description: "获得各种美味食物"
    weight: 25
    probability: 25.0
    commands:
      - "give {player} cooked_beef 16"
      - "give {player} bread 32"
      - "give {player} golden_apple 2"
      - "tellraw {player} {\"text\":\"🍖 恭喜获得食物大礼包！\",\"color\":\"yellow\"}"

  # 工具礼包 - 稀有奖励
  tool_pack:
    name: "🔧 工具礼包"
    description: "获得附魔工具"
    weight: 10
    probability: 10.0
    commands:
      - "give {player} diamond_pickaxe{Enchantments:[{id:\"minecraft:efficiency\",lvl:3},{id:\"minecraft:unbreaking\",lvl:2}]} 1"
      - "give {player} diamond_axe{Enchantments:[{id:\"minecraft:efficiency\",lvl:3}]} 1"
      - "tellraw {player} {\"text\":\"🔧 恭喜获得工具礼包！\",\"color\":\"blue\"}"
      - "playsound entity.player.levelup master {player} ~ ~ ~ 1 1.2"

  # 建筑材料包 - 普通奖励
  building_pack:
    name: "🧱 建筑材料包"
    description: "获得各种建筑材料"
    weight: 30
    probability: 30.0
    commands:
      - "give {player} stone 64"
      - "give {player} oak_planks 64"
      - "give {player} glass 32"
      - "tellraw {player} {\"text\":\"🧱 恭喜获得建筑材料包！\",\"color\":\"brown\"}"

  # 超级大奖 - 极稀有奖励
  super_prize:
    name: "🌟 超级大奖"
    description: "获得钻石装备套装和大量资源"
    weight: 5
    probability: 5.0
    commands:
      - "give {player} diamond_helmet{Enchantments:[{id:\"minecraft:protection\",lvl:4}]} 1"
      - "give {player} diamond_chestplate{Enchantments:[{id:\"minecraft:protection\",lvl:4}]} 1"
      - "give {player} diamond_leggings{Enchantments:[{id:\"minecraft:protection\",lvl:4}]} 1"
      - "give {player} diamond_boots{Enchantments:[{id:\"minecraft:protection\",lvl:4}]} 1"
      - "give {player} diamond_sword{Enchantments:[{id:\"minecraft:sharpness\",lvl:5}]} 1"
      - "give {player} diamond 32"
      - "tellraw {player} {\"text\":\"🌟 恭喜获得超级大奖！！！\",\"color\":\"light_purple\",\"bold\":true}"
      - "tellraw @a {\"text\":\"{player} 获得了超级大奖！\",\"color\":\"gold\",\"bold\":true}"
      - "playsound entity.ender_dragon.death master {player} ~ ~ ~ 1 1"

  # 谢谢参与 - 安慰奖
  nothing:
    name: "😅 谢谢参与"
    description: "很遗憾，这次没有获得奖励，但不要灰心！"
    weight: 15
    probability: 15.0
    commands:
      - "tellraw {player} {\"text\":\"😅 谢谢参与，下次再来！\",\"color\":\"gray\"}"
      - "playsound entity.villager.no master {player} ~ ~ ~ 0.5 1"

# 奖励池统计信息（自动计算，无需手动修改）
# 总权重: 190
# 奖励数量: 10
# 最高概率: 30.0% (经验宝瓶, 建筑材料包)
# 最低概率: 5.0% (超级大奖)
