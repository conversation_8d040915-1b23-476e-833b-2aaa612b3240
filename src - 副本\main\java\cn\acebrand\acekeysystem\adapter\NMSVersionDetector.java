package cn.acebrand.acekeysystem.adapter;

import org.bukkit.Bukkit;
import org.bukkit.plugin.Plugin;

/**
 * NMS版本检测器
 * 自动检测服务器版本并选择合适的适配器
 */
public class NMSVersionDetector {

    private static final String[] SUPPORTED_VERSIONS = {
            // 1.21.x 系列
            "v1_21_R4", "v1_21_R3", "v1_21_R2", "v1_21_R1",
            // 1.20.x 系列
            "v1_20_R6", "v1_20_R5", "v1_20_R4", "v1_20_R3", "v1_20_R2", "v1_20_R1",
            // 1.19.x 系列
            "v1_19_R3", "v1_19_R2", "v1_19_R1",
            // 1.18.x 系列
            "v1_18_R2", "v1_18_R1",
            // 1.17.x 系列
            "v1_17_R1",
            // 1.16.x 系列
            "v1_16_R3", "v1_16_R2", "v1_16_R1",
            // 1.15.x 系列
            "v1_15_R1",
            // 1.14.x 系列
            "v1_14_R1",
            // 1.13.x 系列
            "v1_13_R2", "v1_13_R1",
            // 1.12.x 及更早版本
            "v1_12_R1", "v1_11_R1", "v1_10_R1", "v1_9_R2", "v1_9_R1", "v1_8_R3"
    };

    private final String serverVersion;
    private final int majorVersion;
    private final int minorVersion;
    private final int revision;
    private final boolean isModern;
    private final Plugin plugin;

    public NMSVersionDetector(Plugin plugin) {
        this.plugin = plugin;

        // 获取服务器版本
        String packageName = Bukkit.getServer().getClass().getPackage().getName();
        this.serverVersion = packageName.substring(packageName.lastIndexOf('.') + 1);

        // 解析版本号
        VersionInfo versionInfo = parseVersion(serverVersion);
        this.majorVersion = versionInfo.major;
        this.minorVersion = versionInfo.minor;
        this.revision = versionInfo.revision;

        // 判断是否为现代版本 (1.13+)
        this.isModern = majorVersion > 1 || (majorVersion == 1 && minorVersion >= 13);

        plugin.getLogger().info("检测到服务器版本: " + serverVersion +
                " (MC " + majorVersion + "." + minorVersion +
                ", " + (isModern ? "现代" : "传统") + "版本)");

        // 特殊版本提示
        if (majorVersion == 1 && minorVersion >= 21) {
            plugin.getLogger().info("检测到1.21+版本，使用最新API特性");
        } else if (majorVersion == 1 && minorVersion >= 20) {
            plugin.getLogger().info("检测到1.20.x版本，完全支持现代特性");
        }
    }

    /**
     * 创建合适的版本适配器
     */
    public VersionAdapter createAdapter() {
        try {
            if (isModern) {
                // 尝试创建现代版本适配器
                return createModernAdapter();
            } else {
                // 创建传统版本适配器
                return new LegacyVersionAdapter();
            }
        } catch (Exception e) {
            plugin.getLogger().warning("创建版本适配器失败，尝试降级处理: " + e.getMessage());

            // 降级处理：如果现代版本适配器失败，尝试传统版本
            if (isModern) {
                plugin.getLogger().info("现代版本适配器初始化失败，降级使用传统版本适配器");
                return new LegacyVersionAdapter();
            }

            throw new RuntimeException("无法创建任何版本适配器", e);
        }
    }

    /**
     * 创建现代版本适配器
     */
    private VersionAdapter createModernAdapter() {
        // 检查是否支持PersistentDataContainer
        if (checkPersistentDataContainerSupport()) {
            return new ModernVersionAdapter(plugin);
        } else {
            plugin.getLogger().warning("当前版本不支持PersistentDataContainer，使用传统适配器");
            return new LegacyVersionAdapter();
        }
    }

    /**
     * 检查PersistentDataContainer支持
     */
    private boolean checkPersistentDataContainerSupport() {
        try {
            Class.forName("org.bukkit.persistence.PersistentDataContainer");
            Class.forName("org.bukkit.persistence.PersistentDataType");
            Class.forName("org.bukkit.NamespacedKey");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 解析版本字符串
     */
    private VersionInfo parseVersion(String version) {
        try {
            // 格式: v1_20_R1
            if (version.startsWith("v")) {
                String[] parts = version.substring(1).split("_");
                if (parts.length >= 3) {
                    int major = Integer.parseInt(parts[0]);
                    int minor = Integer.parseInt(parts[1]);
                    int revision = Integer.parseInt(parts[2].substring(1)); // 去掉R
                    return new VersionInfo(major, minor, revision);
                }
            }

            // 如果解析失败，尝试从Bukkit版本获取
            String bukkitVersion = Bukkit.getBukkitVersion();
            plugin.getLogger().info("从Bukkit版本解析: " + bukkitVersion);

            // 格式: 1.20.1-R0.1-SNAPSHOT
            if (bukkitVersion.contains("-")) {
                String versionPart = bukkitVersion.split("-")[0];
                String[] parts = versionPart.split("\\.");
                if (parts.length >= 2) {
                    int major = Integer.parseInt(parts[0]);
                    int minor = Integer.parseInt(parts[1]);
                    int patch = parts.length > 2 ? Integer.parseInt(parts[2]) : 0;
                    return new VersionInfo(major, minor, patch);
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("解析版本失败: " + e.getMessage());
        }

        // 默认假设为最新现代版本
        plugin.getLogger().warning("无法解析版本，假设为1.21.4");
        return new VersionInfo(1, 21, 4);
    }

    /**
     * 获取服务器版本信息
     */
    public String getServerVersion() {
        return serverVersion;
    }

    public int getMajorVersion() {
        return majorVersion;
    }

    public int getMinorVersion() {
        return minorVersion;
    }

    public int getRevision() {
        return revision;
    }

    public boolean isModernVersion() {
        return isModern;
    }

    /**
     * 检查是否为支持的版本
     */
    public boolean isSupportedVersion() {
        for (String supported : SUPPORTED_VERSIONS) {
            if (supported.equals(serverVersion)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取版本兼容性信息
     */
    public String getCompatibilityInfo() {
        StringBuilder info = new StringBuilder();
        info.append("服务器版本: ").append(serverVersion).append("\n");
        info.append("Minecraft版本: ").append(majorVersion).append(".").append(minorVersion).append(".").append(revision)
                .append("\n");

        // 版本类型详细信息
        String versionType;
        if (majorVersion == 1 && minorVersion >= 21) {
            versionType = "最新版本 (1.21+)";
        } else if (majorVersion == 1 && minorVersion >= 20) {
            versionType = "现代版本 (1.20.x)";
        } else if (majorVersion == 1 && minorVersion >= 13) {
            versionType = "现代版本 (1.13-1.19)";
        } else {
            versionType = "传统版本 (1.12-)";
        }
        info.append("版本类型: ").append(versionType).append("\n");

        info.append("官方支持: ").append(isSupportedVersion() ? "是" : "否").append("\n");
        info.append("PersistentDataContainer: ").append(checkPersistentDataContainerSupport() ? "支持" : "不支持")
                .append("\n");
        info.append("支持版本范围: 1.8.8 - 1.21.4");
        return info.toString();
    }

    /**
     * 版本信息类
     */
    private static class VersionInfo {
        final int major;
        final int minor;
        final int revision;

        VersionInfo(int major, int minor, int revision) {
            this.major = major;
            this.minor = minor;
            this.revision = revision;
        }
    }
}
