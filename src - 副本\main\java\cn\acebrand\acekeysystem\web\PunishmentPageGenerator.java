package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;
import cn.acebrand.acekeysystem.punishment.PunishmentRecord;

import java.util.List;
import java.util.Map;

/**
 * 处罚页面生成器
 */
public class PunishmentPageGenerator {

    private final AceKeySystem plugin;

    public PunishmentPageGenerator(AceKeySystem plugin) {
        this.plugin = plugin;
    }

    /**
     * 生成背景CSS
     */
    private String generateBackgroundCSS() {
        String backgroundImage = plugin.getConfig().getString("website.punishment-background-image", "");
        if (backgroundImage != null && !backgroundImage.trim().isEmpty()) {
            // 获取透明度设置
            int opacity = plugin.getConfig().getInt("website.punishment-background-opacity", 30);
            // 将透明度转换为0-1之间的值
            double opacityValue = opacity / 100.0;

            // 使用配置的透明度
            return "    background: linear-gradient(rgba(102, 126, 234, " + opacityValue + "), rgba(118, 75, 162, "
                    + (opacityValue + 0.1) + ")), url('" +
                    backgroundImage.trim() + "') center/cover no-repeat fixed;\n";
        } else {
            return "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n";
        }
    }

    /**
     * 生成处罚记录页面HTML
     */
    public String generatePunishmentPage(PunishmentRecord.PunishmentType type, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics, String searchQuery) {
        return generatePunishmentPage(type, records, currentPage, totalPages, statistics, searchQuery, false);
    }

    /**
     * 生成处罚记录页面HTML（支持玩家视图）
     */
    public String generatePunishmentPage(PunishmentRecord.PunishmentType type, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics, String searchQuery, boolean isPlayerView) {
        return generatePunishmentPage(type, records, currentPage, totalPages, statistics, searchQuery, isPlayerView,
                null, null);
    }

    /**
     * 生成处罚记录页面HTML（支持过滤器）
     */
    public String generatePunishmentPage(PunishmentRecord.PunishmentType type, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics, String searchQuery, boolean isPlayerView,
            String playerFilter, String staffFilter) {
        StringBuilder html = new StringBuilder();

        String pageTitle = type != null ? type.getDisplayName() + "记录" : "处罚历史";
        String pageIcon = type != null ? type.getIcon() : "📋";

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(pageTitle).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generatePunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");

        // 检查是否有背景图片，如果有则添加特殊类
        String backgroundImage = plugin.getConfig().getString("website.punishment-background-image", "");
        if (backgroundImage != null && !backgroundImage.trim().isEmpty()) {
            html.append("<body class=\"has-background-image\">\n");
        } else {
            html.append("<body>\n");
        }

        // 顶部导航栏（类似 next-litebans）
        html.append(generateSiteHeader(type, statistics, searchQuery));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 页面标题
        if (isPlayerView && searchQuery != null && !searchQuery.isEmpty()) {
            // 玩家视图：在原页面结构中显示玩家信息
            html.append(generatePlayerHeaderInPage(searchQuery, records, type));
        } else {
            html.append("        <div class=\"page-header\">\n");
            html.append("            <h1>").append(pageIcon).append(" ").append(pageTitle).append("</h1>\n");
            html.append("            <p class=\"page-description\">查看服务器").append(pageTitle).append("</p>\n");
            html.append("            <div class=\"nav-buttons\">\n");
            html.append("                <a href=\"/\" class=\"nav-btn\">← 返回首页</a>\n");
            if (type != null) {
                html.append("                <a href=\"/punishments\" class=\"nav-btn\">📋 所有记录</a>\n");
            }
            html.append("            </div>\n");
            html.append("        </div>\n");
        }

        // 统计信息（始终显示，除非是玩家专属页面）
        if (!isPlayerView && statistics != null && !statistics.isEmpty()) {
            html.append(generateStatisticsSection(statistics, type));
        }

        // 过滤器显示（如果有过滤参数，无论是否玩家视图都显示）
        html.append(generateFiltersSection(playerFilter, staffFilter));

        // 处罚记录表格
        html.append(generatePunishmentTable(records));

        // 分页导航
        if (totalPages > 1) {
            html.append(generatePagination(currentPage, totalPages, searchQuery, type));
        }

        html.append("    </div>\n");

        // JavaScript
        html.append("    <script>\n");
        html.append(generatePunishmentPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成顶部导航栏（完全按照 next-litebans 布局）
     */
    private String generateSiteHeader(PunishmentRecord.PunishmentType currentType, Map<String, Object> statistics,
            String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("    <header class=\"site-header\">\n");
        html.append("        <div class=\"header-container\">\n");

        // 左侧：Logo 和主导航
        html.append("            <div class=\"header-left\">\n");
        html.append("                <a href=\"/\" class=\"logo-link\">\n");

        // 获取普通用户Logo配置
        String userLogoUrl = plugin.getConfig().getString("web-server.user-logo-url", "");
        if (userLogoUrl != null && !userLogoUrl.trim().isEmpty()) {
            html.append("                    <img src=\"").append(userLogoUrl).append("\" alt=\"LiteBans Logo\" class=\"logo-image\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='inline'\">\n");
        } else {
            html.append("                    <img src=\"/static/logo.webp\" alt=\"LiteBans Logo\" class=\"logo-image\" onerror=\"this.style.display='none'; this.nextElementSibling.style.display='inline'\">\n");
        }

        html.append("                    <span class=\"logo-text\">LiteBans</span>\n");
        html.append("                </a>\n");
        html.append("                <nav class=\"main-nav\">\n");
        html.append(generateMainNavigation(currentType, statistics));
        html.append("                </nav>\n");
        html.append("            </div>\n");

        // 右侧：搜索框和功能按钮
        html.append("            <div class=\"header-right\">\n");
        html.append("                <nav class=\"header-actions\">\n");
        html.append(generateHeaderSearch(searchQuery));
        html.append(
                "                    <button type=\"button\" class=\"theme-toggle-btn\" onclick=\"toggleTheme()\" title=\"切换主题\">\n");
        html.append("                        <span class=\"theme-icon\">🌙</span>\n");
        html.append("                    </button>\n");
        html.append("                </nav>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");
        html.append("    </header>\n");

        return html.toString();
    }

    /**
     * 生成主导航链接（类似 next-litebans MainNav）
     */
    private String generateMainNavigation(PunishmentRecord.PunishmentType currentType, Map<String, Object> statistics) {
        StringBuilder html = new StringBuilder();

        // 各种处罚类型的导航链接
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            String typeName = type.name().toLowerCase();
            boolean isActive = type == currentType;

            html.append("                    <a href=\"/punishments/").append(typeName).append("\" class=\"nav-link");
            if (isActive) {
                html.append(" active");
            }
            html.append("\">\n");
            html.append("                        ").append(type.getDisplayName()).append("\n");

            // 添加徽章显示数量
            Object count = statistics != null ? statistics.get("total_" + typeName) : null;
            if (count != null) {
                String badgeVariant = isActive ? "default" : "secondary";
                html.append("                        <span class=\"nav-badge badge-").append(badgeVariant)
                        .append("\" data-type=\"").append(typeName).append("\" data-stat=\"total_").append(typeName)
                        .append("\">")
                        .append(count).append("</span>\n");
            }

            html.append("                    </a>\n");
        }

        return html.toString();
    }

    /**
     * 生成头部搜索框（类似 next-litebans PlayerInput）
     */
    private String generateHeaderSearch(String searchQuery) {
        StringBuilder html = new StringBuilder();

        html.append("                    <div class=\"header-search\">\n");
        html.append(
                "                        <form method=\"get\" onsubmit=\"return handleSearchSubmit(event)\" class=\"search-form-header\">\n");
        html.append("                            <div class=\"search-input-wrapper\">\n");
        html.append("                                <input type=\"text\" name=\"player\" placeholder=\"搜索玩家...\" ");
        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("value=\"").append(escapeHtml(searchQuery)).append("\" ");
        }
        html.append(
                "class=\"header-search-input\" oninput=\"updatePlayerAvatar(this.value)\" onkeydown=\"handleSearchEnter(event)\">\n");
        html.append(
                "                                <img id=\"headerPlayerAvatar\" src=\"\" alt=\"玩家头像\" class=\"header-player-avatar\" style=\"display: none;\">\n");
        html.append("                            </div>\n");
        html.append("                        </form>\n");
        html.append("                    </div>\n");

        return html.toString();
    }

    /**
     * 在原页面结构中生成玩家信息头部（类似 next-litebans）
     */
    private String generatePlayerHeaderInPage(String playerName, List<PunishmentRecord> records,
            PunishmentRecord.PunishmentType currentType) {
        StringBuilder html = new StringBuilder();

        // 获取玩家统计信息 - 需要从数据库获取完整统计，而不是只从当前页面记录
        int banCount = 0, muteCount = 0, warnCount = 0, kickCount = 0;

        // 如果处罚管理器可用，获取准确的统计信息
        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            try {
                // 获取该玩家的所有类型处罚记录数量
                banCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.BAN, playerName, 1, 1000).size();
                muteCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.MUTE, playerName, 1, 1000).size();
                warnCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.WARN, playerName, 1, 1000).size();
                kickCount = plugin.getPunishmentManager()
                        .searchPunishmentRecords(PunishmentRecord.PunishmentType.KICK, playerName, 1, 1000).size();
            } catch (Exception e) {
                // 如果获取失败，回退到从当前记录计算
                for (PunishmentRecord record : records) {
                    String typeText = record.getPunishmentTypeText();
                    if (typeText != null) {
                        if (typeText.contains("封禁") || typeText.toLowerCase().contains("ban")) {
                            banCount++;
                        } else if (typeText.contains("禁言") || typeText.toLowerCase().contains("mute")) {
                            muteCount++;
                        } else if (typeText.contains("警告") || typeText.toLowerCase().contains("warn")) {
                            warnCount++;
                        } else if (typeText.contains("踢出") || typeText.toLowerCase().contains("kick")) {
                            kickCount++;
                        }
                    }
                }
            }
        } else {
            // 数据库不可用时，从当前记录计算
            for (PunishmentRecord record : records) {
                String typeText = record.getPunishmentTypeText();
                if (typeText != null) {
                    if (typeText.contains("封禁") || typeText.toLowerCase().contains("ban")) {
                        banCount++;
                    } else if (typeText.contains("禁言") || typeText.toLowerCase().contains("mute")) {
                        muteCount++;
                    } else if (typeText.contains("警告") || typeText.toLowerCase().contains("warn")) {
                        warnCount++;
                    } else if (typeText.contains("踢出") || typeText.toLowerCase().contains("kick")) {
                        kickCount++;
                    }
                }
            }
        }

        // 使用类似 next-litebans 的布局，但适配到我们的页面结构
        html.append("        <div class=\"player-view-header\">\n");
        html.append("            <div class=\"flex h-full flex-col items-center gap-4 py-8\">\n");
        html.append("                <div class=\"space-y-2 md:flex md:space-x-4\">\n");

        // 检查是否是控制台
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName)
                || "[Console]".equals(playerName);

        if (isConsolePlayer) {
            // 控制台头像（使用 console-bust.webp）
            html.append("                    <img src=\"/static/console-bust.webp\" ");
            html.append("alt=\"控制台\" ");
            html.append("width=\"192\" height=\"192\" class=\"mx-auto console-bust-large\" ");
            html.append("onerror=\"this.src='/static/console.webp'\">\n");
        } else {
            // 半身皮肤图片（多个API备选）
            String bustUrl = "https://vzge.me/bust/512/" + playerName + ".png";
            html.append("                    <img src=\"").append(bustUrl).append("\" alt=\"")
                    .append(escapeHtml(playerName)).append("\" width=\"192\" height=\"192\" class=\"mx-auto\" ")
                    .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(playerName)
                    .append("/192'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://crafatar.com/renders/body/")
                    .append(playerName).append("?size=192&overlay'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://minotar.net/armor/body/")
                    .append(playerName).append("/192';};}\">\n");
        }

        // 玩家详情区域
        html.append("                    <div class=\"md:w-[350px] md:py-4 space-y-1\">\n");
        if (isConsolePlayer) {
            html.append(
                    "                        <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">控制台</h1>\n");
        } else {
            html.append(
                    "                        <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">")
                    .append(escapeHtml(playerName)).append("</h1>\n");
        }
        html.append("                        <div class=\"flex space-x-2 whitespace-nowrap\">\n");

        // 徽章（完全按照 next-litebans 样式，始终显示所有类型，当前类型高亮）

        // 封禁徽章
        String banBadgeClass = (currentType == PunishmentRecord.PunishmentType.BAN) ? "badge badge-primary"
                : "badge badge-secondary";
        if (banCount > 0) {
            html.append("                            <a href=\"/punishments/ban?player=").append(escapeHtml(playerName))
                    .append("\" class=\"").append(banBadgeClass)
                    .append("\" data-type=\"ban\" data-stat=\"total_ban\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">🚫</span> <span class=\"badge-count\">")
                    .append(banCount)
                    .append("</span> 封禁\n");
            html.append("                            </a>\n");
        }

        // 禁言徽章
        String muteBadgeClass = (currentType == PunishmentRecord.PunishmentType.MUTE) ? "badge badge-primary"
                : "badge badge-secondary";
        if (muteCount > 0) {
            html.append("                            <a href=\"/punishments/mute?player=")
                    .append(escapeHtml(playerName)).append("\" class=\"").append(muteBadgeClass)
                    .append("\" data-type=\"mute\" data-stat=\"total_mute\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">🔇</span> <span class=\"badge-count\">")
                    .append(muteCount)
                    .append("</span> 禁言\n");
            html.append("                            </a>\n");
        }

        // 警告徽章
        String warnBadgeClass = (currentType == PunishmentRecord.PunishmentType.WARN) ? "badge badge-primary"
                : "badge badge-secondary";
        if (warnCount > 0) {
            html.append("                            <a href=\"/punishments/warn?player=")
                    .append(escapeHtml(playerName)).append("\" class=\"").append(warnBadgeClass)
                    .append("\" data-type=\"warn\" data-stat=\"total_warn\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">⚠️</span> <span class=\"badge-count\">")
                    .append(warnCount)
                    .append("</span> 警告\n");
            html.append("                            </a>\n");
        }

        // 踢出徽章
        String kickBadgeClass = (currentType == PunishmentRecord.PunishmentType.KICK) ? "badge badge-primary"
                : "badge badge-secondary";
        if (kickCount > 0) {
            html.append("                            <a href=\"/punishments/kick?player=")
                    .append(escapeHtml(playerName)).append("\" class=\"").append(kickBadgeClass)
                    .append("\" data-type=\"kick\" data-stat=\"total_kick\">\n");
            html.append(
                    "                                <span class=\"badge-icon\">👢</span> <span class=\"badge-count\">")
                    .append(kickCount)
                    .append("</span> 踢出\n");
            html.append("                            </a>\n");
        }

        // 如果没有任何记录，显示所有记录链接
        if (banCount == 0 && muteCount == 0 && warnCount == 0 && kickCount == 0) {
            String allBadgeClass = (currentType == null) ? "badge badge-primary" : "badge badge-secondary";
            html.append("                            <a href=\"/punishments?player=").append(escapeHtml(playerName))
                    .append("\" class=\"").append(allBadgeClass).append("\">\n");
            html.append("                                <span class=\"badge-icon\">📋</span> 查看所有记录\n");
            html.append("                            </a>\n");
        }

        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        // 添加过滤器显示（如果有过滤参数）
        // 注意：这里只显示玩家过滤器，因为这是玩家专属页面
        html.append(generateFiltersSection(playerName, null));

        return html.toString();
    }

    /**
     * 生成完整的玩家专属页面（完全按照 next-litebans 布局）
     */
    private String generatePlayerPage(String playerName, List<PunishmentRecord> records,
            int currentPage, int totalPages, Map<String, Object> statistics) {
        StringBuilder html = new StringBuilder();

        // HTML 头部
        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(escapeHtml(playerName)).append(" - 玩家信息</title>\n");
        html.append("    <style>\n");
        html.append(generatePunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");

        // 检查是否有背景图片，如果有则添加特殊类
        String backgroundImage = plugin.getConfig().getString("website.punishment-background-image", "");
        if (backgroundImage != null && !backgroundImage.trim().isEmpty()) {
            html.append("<body class=\"has-background-image\">\n");
        } else {
            html.append("<body>\n");
        }
        html.append("    <div class=\"container\">\n");

        // 玩家信息头部
        html.append(generatePlayerHeader(playerName, records));

        // 处罚记录表格
        html.append(generatePunishmentTable(records));

        // 分页
        if (totalPages > 1) {
            html.append(generatePagination(currentPage, totalPages,
                    "?search=" + escapeHtml(playerName) + "&player_view=true", null));
        }

        html.append("            </section>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");
        html.append("    <script>\n");
        html.append(generatePunishmentPageJS());
        html.append("    </script>\n");
        html.append("</body>\n");
        html.append("</html>\n");

        return html.toString();
    }

    /**
     * 生成玩家专属页面头部（完全按照 next-litebans 布局）
     */
    private String generatePlayerHeader(String playerName, List<PunishmentRecord> records) {
        StringBuilder html = new StringBuilder();

        // 获取玩家统计信息
        int banCount = 0, muteCount = 0, warnCount = 0, kickCount = 0;
        for (PunishmentRecord record : records) {
            String typeText = record.getPunishmentTypeText();
            if (typeText != null) {
                if (typeText.contains("封禁") || typeText.toLowerCase().contains("ban")) {
                    banCount++;
                } else if (typeText.contains("禁言") || typeText.toLowerCase().contains("mute")) {
                    muteCount++;
                } else if (typeText.contains("警告") || typeText.toLowerCase().contains("warn")) {
                    warnCount++;
                } else if (typeText.contains("踢出") || typeText.toLowerCase().contains("kick")) {
                    kickCount++;
                }
            }
        }

        // 使用 next-litebans 的确切布局结构
        html.append("        <div class=\"flex h-full flex-col items-center gap-4 py-8 md:py-12 md:pb-8 lg:py-18\">\n");
        html.append("            <div class=\"space-y-2 md:flex md:space-x-4\">\n");

        // 半身皮肤图片（完全按照 next-litebans）
        String bustUrl = "https://vzge.me/bust/512/" + playerName + ".png";
        html.append("                <img src=\"").append(bustUrl).append("\" alt=\"").append(escapeHtml(playerName))
                .append("\" width=\"192\" height=\"192\" class=\"mx-auto\" onerror=\"this.src='https://mc-heads.net/body/")
                .append(playerName).append("/192'\">\n");

        // 玩家详情区域
        html.append("                <div class=\"md:w-[350px] md:py-4 space-y-1\">\n");
        html.append(
                "                    <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">")
                .append(escapeHtml(playerName)).append("</h1>\n");
        html.append("                    <div class=\"flex space-x-2 whitespace-nowrap\">\n");

        // 徽章（完全按照 next-litebans 样式）
        if (banCount > 0) {
            html.append("                        <a href=\"/punishments/ban?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"ban\" data-stat=\"total_ban\">\n");
            html.append("                            <span class=\"badge-icon\">🚫</span> <span class=\"badge-count\">")
                    .append(banCount)
                    .append("</span> 封禁\n");
            html.append("                        </a>\n");
        }

        if (muteCount > 0) {
            html.append("                        <a href=\"/punishments/mute?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"mute\" data-stat=\"total_mute\">\n");
            html.append("                            <span class=\"badge-icon\">🔇</span> <span class=\"badge-count\">")
                    .append(muteCount)
                    .append("</span> 禁言\n");
            html.append("                        </a>\n");
        }

        if (warnCount > 0) {
            html.append("                        <a href=\"/punishments/warn?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"warn\" data-stat=\"total_warn\">\n");
            html.append("                            <span class=\"badge-icon\">⚠️</span> <span class=\"badge-count\">")
                    .append(warnCount)
                    .append("</span> 警告\n");
            html.append("                        </a>\n");
        }

        if (kickCount > 0) {
            html.append("                        <a href=\"/punishments/kick?search=").append(escapeHtml(playerName))
                    .append("\" class=\"badge badge-secondary\" data-type=\"kick\" data-stat=\"total_kick\">\n");
            html.append("                            <span class=\"badge-icon\">👢</span> <span class=\"badge-count\">")
                    .append(kickCount)
                    .append("</span> 踢出\n");
            html.append("                        </a>\n");
        }

        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");

        // 表格区域（保持原有的表格显示）
        html.append("            <section class=\"w-full lg:w-[1024px]\">\n");

        return html.toString();
    }

    /**
     * 生成导航标签页
     */
    private String generateNavigationTabs(PunishmentRecord.PunishmentType currentType) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"nav-tabs\">\n");

        // 所有记录标签
        html.append("            <a href=\"/punishments\" class=\"tab-btn");
        if (currentType == null)
            html.append(" active");
        html.append("\">📋 所有记录</a>\n");

        // 各种类型的标签
        for (PunishmentRecord.PunishmentType type : PunishmentRecord.PunishmentType.values()) {
            html.append("            <a href=\"/punishments/").append(type.name().toLowerCase())
                    .append("\" class=\"tab-btn");
            if (type == currentType)
                html.append(" active");
            html.append("\">").append(type.getIcon()).append(" ").append(type.getDisplayName()).append("</a>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成统计信息区域
     */
    private String generateStatisticsSection(Map<String, Object> statistics, PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"statistics-section\">\n");
        html.append("            <div class=\"stats-grid\">\n");

        if (type != null) {
            // 单一类型的统计
            String typeName = type.name().toLowerCase();

            Object total = statistics.get("total_" + typeName);
            if (total != null) {
                // 总数卡片可以点击跳转到对应类型页面
                html.append("                <a href=\"/punishments/").append(typeName)
                        .append("\" class=\"stat-card-link\">\n");
                html.append("                    <div class=\"stat-card\" style=\"border-left-color: ")
                        .append(type.getColor()).append("\">\n");
                html.append("                        <div class=\"stat-icon\">").append(type.getIcon())
                        .append("</div>\n");
                html.append("                        <div class=\"stat-info\">\n");
                html.append("                            <div class=\"stat-value\">").append(total).append("</div>\n");
                html.append("                            <div class=\"stat-label\">总").append(type.getDisplayName())
                        .append("数</div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }

            Object active = statistics.get("active_" + typeName);
            if (active != null) {
                // 生效中的记录也可以点击，但添加过滤参数
                html.append("                <a href=\"/punishments/").append(typeName)
                        .append("?active=true\" class=\"stat-card-link\">\n");
                html.append("                    <div class=\"stat-card active\">\n");
                html.append("                        <div class=\"stat-icon\">🔴</div>\n");
                html.append("                        <div class=\"stat-info\">\n");
                html.append("                            <div class=\"stat-value\">").append(active).append("</div>\n");
                html.append("                            <div class=\"stat-label\">生效中</div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }

            Object today = statistics.get("today_" + typeName);
            if (today != null) {
                // 今日新增也可以点击，添加日期过滤参数
                html.append("                <a href=\"/punishments/").append(typeName)
                        .append("?today=true\" class=\"stat-card-link\">\n");
                html.append("                    <div class=\"stat-card today\">\n");
                html.append("                        <div class=\"stat-icon\">📅</div>\n");
                html.append("                        <div class=\"stat-info\">\n");
                html.append("                            <div class=\"stat-value\">").append(today).append("</div>\n");
                html.append("                            <div class=\"stat-label\">今日新增</div>\n");
                html.append("                        </div>\n");
                html.append("                    </div>\n");
                html.append("                </a>\n");
            }
        } else {
            // 所有类型的统计
            for (PunishmentRecord.PunishmentType punishmentType : PunishmentRecord.PunishmentType.values()) {
                String typeName = punishmentType.name().toLowerCase();
                Object total = statistics.get("total_" + typeName);
                if (total != null) {
                    // 使用链接包装整个卡片（类似 next-litebans PunishmentTypeCard）
                    html.append("                <a href=\"/punishments/").append(typeName)
                            .append("\" class=\"stat-card-link\">\n");
                    html.append("                    <div class=\"stat-card\" style=\"border-left-color: ")
                            .append(punishmentType.getColor()).append("\">\n");
                    html.append("                        <div class=\"stat-icon\">").append(punishmentType.getIcon())
                            .append("</div>\n");
                    html.append("                        <div class=\"stat-info\">\n");
                    html.append("                            <div class=\"stat-value\">").append(total)
                            .append("</div>\n");
                    html.append("                            <div class=\"stat-label\">")
                            .append(punishmentType.getDisplayName()).append("</div>\n");
                    html.append("                        </div>\n");
                    html.append("                    </div>\n");
                    html.append("                </a>\n");
                }
            }
        }

        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成搜索区域
     */
    private String generateSearchSection(String searchQuery, PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"search-section\">\n");
        html.append(
                "            <form class=\"search-form\" method=\"get\" onsubmit=\"return handleSearchSubmit(event)\">\n");
        html.append("                <div class=\"search-input-group\">\n");
        html.append("                    <div class=\"search-input-container\">\n");
        html.append(
                "                        <input type=\"text\" name=\"player\" id=\"playerSearchInput\" placeholder=\"搜索玩家名称或UUID...\" ");
        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("value=\"").append(escapeHtml(searchQuery)).append("\" ");
        }
        html.append(
                "class=\"search-input\" oninput=\"updatePlayerAvatar(this.value)\" onkeydown=\"handleSearchEnter(event)\">\n");
        html.append(
                "                        <img id=\"playerAvatarPreview\" src=\"\" alt=\"玩家头像\" class=\"player-avatar-preview\" style=\"display: none;\">\n");
        html.append("                    </div>\n");
        html.append("                    <button type=\"submit\" class=\"search-btn\">🔍 搜索</button>\n");
        html.append(
                "                    <button type=\"button\" class=\"theme-toggle-btn\" onclick=\"toggleTheme()\" title=\"切换主题\">\n");
        html.append("                        <span class=\"theme-icon\">🌙</span>\n");
        html.append("                    </button>\n");
        html.append("                </div>\n");
        html.append("            </form>\n");

        if (searchQuery != null && !searchQuery.isEmpty()) {
            html.append("            <div class=\"search-result-info\">\n");
            html.append("                <span>玩家记录: \"").append(escapeHtml(searchQuery)).append("\"</span>\n");
            String clearUrl = type != null ? "/punishments/" + type.name().toLowerCase() : "/punishments";
            html.append("                <a href=\"").append(clearUrl).append("\" class=\"clear-search\">查看所有记录</a>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成处罚记录表格
     */
    private String generatePunishmentTable(List<PunishmentRecord> records) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"table-section\">\n");

        if (records.isEmpty()) {
            html.append("            <div class=\"no-records\">\n");
            html.append("                <div class=\"no-records-icon\">📝</div>\n");

            // 检查是否是数据库连接问题
            if (plugin.getPunishmentManager() == null || !plugin.getPunishmentManager().isEnabled()) {
                html.append("                <h3>数据库未连接</h3>\n");
                html.append("                <p>LiteBans数据库连接失败，无法显示处罚记录</p>\n");
                html.append("                <p class=\"db-status\">请检查数据库配置或联系管理员</p>\n");
            } else {
                html.append("                <h3>暂无处罚记录</h3>\n");
                html.append("                <p>当前没有找到任何处罚记录</p>\n");
            }

            html.append("            </div>\n");
        } else {
            html.append("            <div class=\"table-container\">\n");
            html.append("                <table class=\"punishment-table\">\n");
            html.append("                    <thead>\n");
            html.append("                        <tr>\n");
            html.append("                            <th>类型</th>\n");
            html.append("                            <th>玩家</th>\n");
            html.append("                            <th>执行者</th>\n");
            html.append("                            <th>处罚原因</th>\n");
            html.append("                            <th>处罚时间</th>\n");
            html.append("                            <th>到期时间</th>\n");
            html.append("                            <th>状态</th>\n");
            html.append("                            <th>详情</th>\n");
            html.append("                        </tr>\n");
            html.append("                    </thead>\n");
            html.append("                    <tbody>\n");

            String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
            boolean showAvatars = plugin.getConfig().getBoolean("litebans.display.show-player-avatars", true);

            for (PunishmentRecord record : records) {
                html.append("                        <tr class=\"punishment-row\">\n");

                // 类型列
                html.append("                            <td class=\"type-cell\">\n");
                html.append("                                <span class=\"type-badge\" style=\"background-color: ")
                        .append(record.getTypeColor()).append("\">");
                html.append(record.getTypeIcon()).append(" ").append(escapeHtml(record.getPunishmentTypeText()))
                        .append("</span>\n");
                html.append("                            </td>\n");

                // 玩家列（按照 next-litebans 布局：头像在上，名字在下，居中对齐，可点击过滤）
                html.append("                            <td class=\"player-cell\">\n");
                String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";

                // 构建玩家过滤链接（保留现有参数，按照 next-litebans AvatarName 逻辑）
                html.append("                                <a href=\"#\" onclick=\"addFilter('player', '")
                        .append(escapeHtml(playerName)).append("'); return false;\" class=\"avatar-name-link\">\n");
                html.append("                                    <div class=\"avatar-name-container\">\n");
                if (showAvatars && record.getUuid() != null && !record.getUuid().isEmpty()) {
                    html.append("                                        <img src=\"")
                            .append(record.getPlayerAvatarUrl(avatarProvider))
                            .append("\" alt=\"头像\" class=\"player-avatar\" onerror=\"this.style.display='none'\">\n");
                }
                html.append("                                        <div class=\"player-name\">")
                        .append(escapeHtml(playerName))
                        .append("</div>\n");
                html.append("                                    </div>\n");
                html.append("                                </a>\n");
                html.append("                            </td>\n");

                // 执行者（按照 next-litebans 布局：头像在上，名字在下，居中对齐，可点击过滤）
                html.append("                            <td class=\"staff-cell\">\n");
                String staffName = record.getBannedByName();
                String staffUuid = record.getBannedByUuid();
                boolean isConsole = false;

                if (staffName == null) {
                    staffName = "系统";
                    isConsole = true;
                } else if ("Console".equalsIgnoreCase(staffName)) {
                    staffName = "控制台";
                    isConsole = true;
                }

                // 构建执行者过滤链接（保留现有参数，按照 next-litebans AvatarName 逻辑）
                String staffFilterValue;
                if (isConsole) {
                    staffFilterValue = "Console";
                } else if (staffUuid != null && !staffUuid.isEmpty()) {
                    staffFilterValue = staffUuid;
                } else {
                    staffFilterValue = staffName;
                }

                html.append("                                <a href=\"#\" onclick=\"addFilter('staff', '")
                        .append(escapeHtml(staffFilterValue))
                        .append("'); return false;\" class=\"avatar-name-link\">\n");
                html.append("                                    <div class=\"avatar-name-container\">\n");
                if (!isConsole && showAvatars && staffUuid != null && !staffUuid.isEmpty()) {
                    String avatarUrl = generateAvatarUrl(staffUuid, avatarProvider);
                    html.append("                                        <img src=\"")
                            .append(avatarUrl)
                            .append("\" alt=\"头像\" class=\"staff-avatar\" onerror=\"this.style.display='none'\">\n");
                } else if (isConsole) {
                    html.append(
                            "                                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"staff-avatar console-avatar\" onerror=\"this.style.display='none'\">\n");
                }
                html.append("                                        <div class=\"staff-name\">")
                        .append(escapeHtml(staffName)).append("</div>\n");
                html.append("                                    </div>\n");
                html.append("                                </a>\n");
                html.append("                            </td>\n");

                // 处罚原因
                html.append("                            <td class=\"reason-cell\">\n");
                html.append("                                <span class=\"reason-text\" title=\"")
                        .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因"))
                        .append("\">").append(escapeHtml(record.getShortReason())).append("</span>\n");
                html.append("                            </td>\n");

                // 处罚时间
                html.append("                            <td class=\"time-cell\">\n");
                html.append("                                ").append(escapeHtml(record.getFormattedTime()))
                        .append("\n");
                html.append("                            </td>\n");

                // 到期时间（动态显示）
                html.append("                            <td class=\"until-cell\">\n");
                // 踢出和警告是瞬时操作，没有到期时间
                if (record.getType() == PunishmentRecord.PunishmentType.KICK ||
                        record.getType() == PunishmentRecord.PunishmentType.WARN) {
                    html.append("                                <span class=\"until-na\">不适用</span>\n");
                } else if (record.getUntil() != null && record.getUntil().getYear() <= 2100) {
                    // 非永久处罚，显示动态时间
                    html.append("                                <div class=\"flex items-center justify-center\">\n");
                    if (record.isActive()) {
                        html.append(
                                "                                    <span class=\"status-dot status-active\" title=\"生效中\"></span>\n");
                    } else {
                        html.append(
                                "                                    <span class=\"status-dot status-expired\" title=\"已过期\"></span>\n");
                    }
                    // 添加动态时间显示
                    long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant()
                            .toEpochMilli();
                    html.append("                                    <span class=\"relative-time\" data-timestamp=\"")
                            .append(timestamp).append("\">");
                    html.append(escapeHtml(record.getFormattedUntil())).append("</span>\n");
                    html.append("                                </div>\n");
                } else {
                    // 永久处罚，直接显示"永久"
                    html.append("                                永久\n");
                }
                html.append("                            </td>\n");

                // 状态
                html.append("                            <td class=\"status-cell\">\n");
                // 踢出和警告是瞬时操作，状态显示为"已完成"
                if (record.getType() == PunishmentRecord.PunishmentType.KICK ||
                        record.getType() == PunishmentRecord.PunishmentType.WARN) {
                    html.append("                                <span class=\"status-badge status-completed\">已完成</span>\n");
                } else {
                    html.append("                                <span class=\"status-badge ")
                            .append(record.getStatusClass())
                            .append("\">").append(escapeHtml(record.getStatusText())).append("</span>\n");
                }
                html.append("                            </td>\n");

                // 详情按钮（按照 next-litebans PunishmentInfoButton）
                html.append("                            <td class=\"info-cell\">\n");
                String punishmentType = record.getType() != null ? record.getType().name().toLowerCase() : "punishment";
                html.append("                                <a href=\"/punishments/").append(punishmentType)
                        .append("/").append(record.getId()).append("\" class=\"info-btn\" title=\"查看详细信息\">\n");
                html.append(
                        "                                    <svg class=\"info-icon\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\">\n");
                html.append(
                        "                                        <path d=\"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6\"></path>\n");
                html.append(
                        "                                        <polyline points=\"15,3 21,3 21,9\"></polyline>\n");
                html.append(
                        "                                        <line x1=\"10\" y1=\"14\" x2=\"21\" y2=\"3\"></line>\n");
                html.append("                                    </svg>\n");
                html.append("                                </a>\n");
                html.append("                            </td>\n");

                html.append("                        </tr>\n");
            }

            html.append("                    </tbody>\n");
            html.append("                </table>\n");
            html.append("            </div>\n");
        }

        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * 生成分页导航
     */
    private String generatePagination(int currentPage, int totalPages, String searchQuery,
            PunishmentRecord.PunishmentType type) {
        StringBuilder html = new StringBuilder();

        html.append("        <div class=\"pagination-section\">\n");
        html.append("            <div class=\"pagination\">\n");

        String baseUrl = type != null ? "/punishments/" + type.name().toLowerCase() : "/punishments";
        String queryParam = "";
        if (searchQuery != null && !searchQuery.isEmpty()) {
            queryParam = "&search=" + escapeUrl(searchQuery);
        }

        // 上一页
        if (currentPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage - 1)
                    .append(queryParam).append("\" class=\"page-btn prev-btn\">← 上一页</a>\n");
        }

        // 页码
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            html.append("                <a href=\"").append(baseUrl).append("?page=1").append(queryParam)
                    .append("\" class=\"page-btn\">1</a>\n");
            if (startPage > 2) {
                html.append("                <span class=\"page-dots\">...</span>\n");
            }
        }

        for (int i = startPage; i <= endPage; i++) {
            if (i == currentPage) {
                html.append("                <span class=\"page-btn current\">").append(i).append("</span>\n");
            } else {
                html.append("                <a href=\"").append(baseUrl).append("?page=").append(i)
                        .append(queryParam).append("\" class=\"page-btn\">").append(i).append("</a>\n");
            }
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                html.append("                <span class=\"page-dots\">...</span>\n");
            }
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(totalPages)
                    .append(queryParam).append("\" class=\"page-btn\">").append(totalPages).append("</a>\n");
        }

        // 下一页
        if (currentPage < totalPages) {
            html.append("                <a href=\"").append(baseUrl).append("?page=").append(currentPage + 1)
                    .append(queryParam).append("\" class=\"page-btn next-btn\">下一页 →</a>\n");
        }

        html.append("            </div>\n");
        html.append("            <div class=\"page-info\">\n");
        html.append("                第 ").append(currentPage).append(" 页，共 ").append(totalPages).append(" 页\n");
        html.append("            </div>\n");
        html.append("        </div>\n");

        return html.toString();
    }

    /**
     * HTML转义
     */
    private String escapeHtml(String text) {
        if (text == null)
            return "";
        return text.replace("&", "&amp;")
                .replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;");
    }

    /**
     * URL转义
     */
    private String escapeUrl(String text) {
        if (text == null)
            return "";
        try {
            return java.net.URLEncoder.encode(text, "UTF-8");
        } catch (Exception e) {
            return text;
        }
    }

    /**
     * 生成处罚页面CSS样式
     */
    private String generatePunishmentPageCSS() {
        return "/* ==================== CSS 变量定义 ==================== */\n" +
                ":root {\n" +
                "    --background: 0 0% 100%;\n" +
                "    --foreground: 240 10% 3.9%;\n" +
                "    --card: 0 0% 100%;\n" +
                "    --card-foreground: 240 10% 3.9%;\n" +
                "    --muted: 240 4.8% 95.9%;\n" +
                "    --muted-foreground: 240 3.8% 46.1%;\n" +
                "    --border: 240 5.9% 90%;\n" +
                "    --input: 240 5.9% 90%;\n" +
                "    --primary: 240 5.9% 10%;\n" +
                "    --primary-foreground: 0 0% 98%;\n" +
                "    --secondary: 240 4.8% 95.9%;\n" +
                "    --secondary-foreground: 240 5.9% 10%;\n" +
                "    --accent: 240 4.8% 95.9%;\n" +
                "    --accent-foreground: 240 5.9% 10%;\n" +
                "}\n" +
                "\n" +
                ".dark {\n" +
                "    --background: 240 10% 3.9%;\n" +
                "    --foreground: 0 0% 98%;\n" +
                "    --card: 240 10% 3.9%;\n" +
                "    --card-foreground: 0 0% 98%;\n" +
                "    --muted: 240 3.7% 15.9%;\n" +
                "    --muted-foreground: 240 5% 64.9%;\n" +
                "    --border: 240 3.7% 15.9%;\n" +
                "    --input: 240 3.7% 15.9%;\n" +
                "    --primary: 0 0% 98%;\n" +
                "    --primary-foreground: 240 5.9% 10%;\n" +
                "    --secondary: 240 3.7% 15.9%;\n" +
                "    --secondary-foreground: 0 0% 98%;\n" +
                "    --accent: 240 3.7% 15.9%;\n" +
                "    --accent-foreground: 0 0% 98%;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 基础样式 ==================== */\n" +
                "* {\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                "body {\n" +
                "    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n" +
                generateBackgroundCSS() +
                "    min-height: 100vh;\n" +
                "    color: #333;\n" +
                "    transition: background-color 0.3s ease, color 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".container {\n" +
                "    max-width: 1400px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 20px;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 顶部导航栏样式（Next-LiteBans 风格）==================== */\n" +
                ".site-header {\n" +
                "    position: sticky;\n" +
                "    top: 0;\n" +
                "    z-index: 50;\n" +
                "    width: 100%;\n" +
                "    border-bottom: 1px solid hsl(var(--border) / 0.4);\n" +
                "    background: hsl(var(--background) / 0.95);\n" +
                "    backdrop-filter: blur(8px);\n" +
                "    -webkit-backdrop-filter: blur(8px);\n" +
                "}\n" +
                "\n" +
                ".header-container {\n" +
                "    display: flex;\n" +
                "    height: 3.5rem;\n" +
                "    max-width: 1400px;\n" +
                "    margin: 0 auto;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    padding: 0 20px;\n" +
                "}\n" +
                "\n" +
                ".header-left {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    margin-right: 1rem;\n" +
                "}\n" +
                "\n" +
                ".logo-link {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "    margin-right: 1.5rem;\n" +
                "    text-decoration: none;\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".logo-image {\n" +
                "    width: 24px;\n" +
                "    height: 24px;\n" +
                "    margin-right: 0.5rem;\n" +
                "    border: none;\n" +
                "    outline: none;\n" +
                "    box-shadow: none;\n" +
                "    background: transparent;\n" +
                "}\n" +
                "\n" +
                ".logo-text {\n" +
                "    font-weight: 700;\n" +
                "    font-size: 1.125rem;\n" +
                "    display: none;\n" +
                "}\n" +
                "\n" +
                ".main-nav {\n" +
                "    display: none;\n" +
                "    align-items: center;\n" +
                "    gap: 1.5rem;\n" +
                "    font-size: 0.875rem;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 768px) {\n" +
                "    .main-nav {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".nav-link {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    color: hsl(var(--foreground) / 0.6);\n" +
                "    text-decoration: none;\n" +
                "    transition: color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".nav-link:hover {\n" +
                "    color: hsl(var(--foreground) / 0.8);\n" +
                "}\n" +
                "\n" +
                ".nav-link.active {\n" +
                "    color: hsl(var(--foreground) / 0.8);\n" +
                "}\n" +
                "\n" +
                ".nav-badge {\n" +
                "    display: none;\n" +
                "    padding: 0 0.25rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    border-radius: 0.375rem;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .nav-badge {\n" +
                "        display: inline-flex;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".badge-default {\n" +
                "    background: hsl(var(--primary));\n" +
                "    color: hsl(var(--primary-foreground));\n" +
                "}\n" +
                "\n" +
                ".header-right {\n" +
                "    display: flex;\n" +
                "    flex: 1;\n" +
                "    justify-content: flex-end;\n" +
                "}\n" +
                "\n" +
                ".header-actions {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "}\n" +
                "\n" +
                ".header-search {\n" +
                "    position: relative;\n" +
                "    height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".search-form-header {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".search-input-wrapper {\n" +
                "    position: relative;\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".header-search-input {\n" +
                "    display: flex;\n" +
                "    height: 2.5rem;\n" +
                "    width: 100%;\n" +
                "    border-radius: 0.375rem;\n" +
                "    border: 1px solid hsl(var(--input));\n" +
                "    background: hsl(var(--background));\n" +
                "    padding: 0.5rem 0.75rem;\n" +
                "    padding-right: 2.5rem;\n" +
                "    font-size: 0.875rem;\n" +
                "    color: hsl(var(--foreground));\n" +
                "    transition: all 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".header-search-input::placeholder {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".header-search-input:focus {\n" +
                "    outline: none;\n" +
                "    ring: 2px;\n" +
                "    ring-color: hsl(var(--ring));\n" +
                "    ring-offset: 2px;\n" +
                "}\n" +
                "\n" +
                ".header-player-avatar {\n" +
                "    position: absolute;\n" +
                "    right: 0.25rem;\n" +
                "    top: 50%;\n" +
                "    transform: translateY(-50%);\n" +
                "    width: 2rem;\n" +
                "    height: 2rem;\n" +
                "    border-radius: 0.25rem;\n" +
                "    z-index: 10;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 页面标题 ==================== */\n" +
                ".page-header {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 30px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".page-header h1 {\n" +
                "    font-size: 2.5em;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 10px;\n" +
                "}\n" +
                "\n" +
                ".page-description {\n" +
                "    font-size: 1.1em;\n" +
                "    color: #666;\n" +
                "    margin-bottom: 20px;\n" +
                "}\n" +
                "\n" +
                ".player-view-header {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                "/* ==================== Next-LiteBans 样式复制 ==================== */\n" +
                ".flex {\n" +
                "    display: flex;\n" +
                "}\n" +
                "\n" +
                ".h-full {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".flex-col {\n" +
                "    flex-direction: column;\n" +
                "}\n" +
                "\n" +
                ".items-center {\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".gap-4 {\n" +
                "    gap: 1rem;\n" +
                "}\n" +
                "\n" +
                ".py-8 {\n" +
                "    padding-top: 2rem;\n" +
                "    padding-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".space-y-2 > * + * {\n" +
                "    margin-top: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".mx-auto {\n" +
                "    margin-left: auto;\n" +
                "    margin-right: auto;\n" +
                "}\n" +
                "\n" +
                ".space-y-1 > * + * {\n" +
                "    margin-top: 0.25rem;\n" +
                "}\n" +
                "\n" +
                ".text-center {\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".text-4xl {\n" +
                "    font-size: 2.25rem;\n" +
                "    line-height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".font-bold {\n" +
                "    font-weight: 700;\n" +
                "}\n" +
                "\n" +
                ".leading-tight {\n" +
                "    line-height: 1.25;\n" +
                "}\n" +
                "\n" +
                ".tracking-tighter {\n" +
                "    letter-spacing: -0.05em;\n" +
                "}\n" +
                "\n" +
                ".space-x-2 > * + * {\n" +
                "    margin-left: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".whitespace-nowrap {\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".w-full {\n" +
                "    width: 100%;\n" +
                "}\n" +
                "\n" +
                ".badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    border-radius: 0.375rem;\n" +
                "    padding: 0.125rem 0.5rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    font-weight: 600;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    border: 1px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".badge-primary {\n" +
                "    background-color: hsl(210 40% 85%);\n" +
                "    color: hsl(215.4 16.3% 46.9%);\n" +
                "    border: 1px solid hsl(210 40% 70%);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".badge-primary:hover {\n" +
                "    background-color: hsl(210 40% 80%);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".badge-secondary {\n" +
                "    background-color: hsl(var(--secondary));\n" +
                "    color: hsl(var(--secondary-foreground));\n" +
                "    opacity: 0.5;\n" +
                "}\n" +
                "\n" +
                ".badge-secondary:hover {\n" +
                "    opacity: 1;\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".badge-icon {\n" +
                "    width: 1rem;\n" +
                "    height: 1rem;\n" +
                "    margin-right: 0.25rem;\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 */\n" +
                "@media (min-width: 768px) {\n" +
                "    .md\\:py-12 {\n" +
                "        padding-top: 3rem;\n" +
                "        padding-bottom: 3rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:pb-8 {\n" +
                "        padding-bottom: 2rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:flex {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:space-x-4 > * + * {\n" +
                "        margin-left: 1rem;\n" +
                "        margin-top: 0;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:w-\\[350px\\] {\n" +
                "        width: 350px;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:py-4 {\n" +
                "        padding-top: 1rem;\n" +
                "        padding-bottom: 1rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:text-left {\n" +
                "        text-align: left;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .lg\\:py-18 {\n" +
                "        padding-top: 4.5rem;\n" +
                "        padding-bottom: 4.5rem;\n" +
                "    }\n" +
                "    \n" +
                "    .lg\\:leading-\\[1\\.1\\] {\n" +
                "        line-height: 1.1;\n" +
                "    }\n" +
                "    \n" +
                "    .lg\\:w-\\[1024px\\] {\n" +
                "        width: 1024px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 640px) {\n" +
                "    .sm\\:text-5xl {\n" +
                "        font-size: 3rem;\n" +
                "        line-height: 1;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".nav-buttons {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 15px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".nav-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 10px 20px;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".nav-btn:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                "/* ==================== 导航标签页 ==================== */\n" +
                ".nav-tabs {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 20px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    display: flex;\n" +
                "    gap: 10px;\n" +
                "    flex-wrap: wrap;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".tab-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 12px 20px;\n" +
                "    background: #f8f9fa;\n" +
                "    color: #666;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 2px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".tab-btn:hover {\n" +
                "    background: #e9ecef;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".tab-btn.active {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                "/* ==================== 统计信息 ==================== */\n" +
                ".statistics-section {\n" +
                "    margin-bottom: 30px;\n" +
                "}\n" +
                "\n" +
                ".stats-grid {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\n" +
                "    gap: 20px;\n" +
                "}\n" +
                "\n" +
                ".stat-card-link {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    display: block;\n" +
                "    transition: all 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".stat-card-link:hover {\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "}\n" +
                "\n" +
                ".stat-card {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 20px;\n" +
                "    border-left: 5px solid #667eea;\n" +
                "    transition: all 0.3s ease;\n" +
                "    cursor: pointer;\n" +
                "}\n" +
                "\n" +
                ".stat-card:hover {\n" +
                "    transform: translateY(-5px);\n" +
                "    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".stat-card-link:hover .stat-card {\n" +
                "    transform: translateY(-5px) scale(1.02);\n" +
                "    box-shadow: 0 15px 45px rgba(0, 0, 0, 0.2);\n" +
                "}\n" +
                "\n" +
                ".stat-card.active {\n" +
                "    border-left-color: #e74c3c;\n" +
                "}\n" +
                "\n" +
                ".stat-card.today {\n" +
                "    border-left-color: #f39c12;\n" +
                "}\n" +
                "\n" +
                ".stat-icon {\n" +
                "    font-size: 2.5em;\n" +
                "    opacity: 0.8;\n" +
                "}\n" +
                "\n" +
                ".stat-info {\n" +
                "    flex: 1;\n" +
                "}\n" +
                "\n" +
                ".stat-value {\n" +
                "    font-size: 2em;\n" +
                "    font-weight: 700;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 5px;\n" +
                "}\n" +
                "\n" +
                ".stat-label {\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 搜索区域 ==================== */\n" +
                ".search-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    margin-bottom: 30px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".search-form {\n" +
                "    margin-bottom: 15px;\n" +
                "}\n" +
                "\n" +
                ".search-input-group {\n" +
                "    display: flex;\n" +
                "    gap: 15px;\n" +
                "    align-items: center;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".search-input-container {\n" +
                "    position: relative;\n" +
                "    flex: 1;\n" +
                "    min-width: 300px;\n" +
                "}\n" +
                "\n" +
                ".search-input {\n" +
                "    width: 100%;\n" +
                "    padding: 15px 50px 15px 20px;\n" +
                "    border: 2px solid #e9ecef;\n" +
                "    border-radius: 25px;\n" +
                "    font-size: 1em;\n" +
                "    transition: all 0.3s ease;\n" +
                "    background: white;\n" +
                "}\n" +
                "\n" +
                ".search-input:hover {\n" +
                "    border-color: #d1d5db;\n" +
                "    background-color: #f9fafb;\n" +
                "}\n" +
                "\n" +
                ".player-avatar-preview {\n" +
                "    position: absolute;\n" +
                "    right: 10px;\n" +
                "    top: 50%;\n" +
                "    transform: translateY(-50%);\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "    background: #f8f9fa;\n" +
                "    z-index: 10;\n" +
                "}\n" +
                "\n" +
                ".search-input:focus {\n" +
                "    outline: none;\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);\n" +
                "}\n" +
                "\n" +
                ".search-btn {\n" +
                "    padding: 15px 30px;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border: none;\n" +
                "    border-radius: 25px;\n" +
                "    font-weight: 600;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".search-btn:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                ".theme-toggle-btn {\n" +
                "    padding: 15px;\n" +
                "    background: rgba(255, 255, 255, 0.9);\n" +
                "    border: 2px solid #e9ecef;\n" +
                "    border-radius: 50%;\n" +
                "    cursor: pointer;\n" +
                "    transition: all 0.3s ease;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 50px;\n" +
                "    height: 50px;\n" +
                "    font-size: 1.2em;\n" +
                "}\n" +
                "\n" +
                ".theme-toggle-btn:hover {\n" +
                "    background: #f8f9fa;\n" +
                "    border-color: #667eea;\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);\n" +
                "}\n" +
                "\n" +
                ".theme-icon {\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".theme-toggle-btn:hover .theme-icon {\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".search-result-info {\n" +
                "    display: flex;\n" +
                "    justify-content: space-between;\n" +
                "    align-items: center;\n" +
                "    padding: 10px 15px;\n" +
                "    background: #f8f9fa;\n" +
                "    border-radius: 10px;\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".clear-search {\n" +
                "    color: #e74c3c;\n" +
                "    text-decoration: none;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".clear-search:hover {\n" +
                "    text-decoration: underline;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 表格区域 ==================== */\n" +
                ".table-section {\n" +
                "    margin-bottom: 8px;\n" +
                "}\n" +
                "\n" +
                ".table-container {\n" +
                "    position: relative;\n" +
                "    width: 100%;\n" +
                "    overflow: auto;\n" +
                "    border-radius: 12px;\n" +
                "    border: 1px solid hsl(240 5.9% 90%);\n" +
                "    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n" +
                "    background: white;\n" +
                "}\n" +
                "\n" +
                "@media (max-width: 1023px) {\n" +
                "    .table-container {\n" +
                "        border-radius: 0;\n" +
                "        border-left: none;\n" +
                "        border-right: none;\n" +
                "        border-top: 1px solid hsl(240 5.9% 90%);\n" +
                "        border-bottom: 1px solid hsl(240 5.9% 90%);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".punishment-table {\n" +
                "    width: 100%;\n" +
                "    caption-side: bottom;\n" +
                "    font-size: 0.875rem;\n" +
                "    border-collapse: collapse;\n" +
                "    margin: 0;\n" +
                "}\n" +
                "\n" +
                ".punishment-table th,\n" +
                ".punishment-table td {\n" +
                "    padding: 16px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    border-bottom: 1px solid hsl(240 5.9% 90%);\n" +
                "    font-size: 1.1em;\n" +
                "}\n" +
                "\n" +
                ".punishment-table tbody tr:last-child td {\n" +
                "    border-bottom: none;\n" +
                "}\n" +
                "\n" +
                ".punishment-table th {\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    font-weight: 500;\n" +
                "    color: hsl(240 3.8% 46.1%);\n" +
                "    font-size: 0.75rem;\n" +
                "    text-transform: uppercase;\n" +
                "    letter-spacing: 0.025em;\n" +
                "    height: 48px;\n" +
                "    border-bottom: 1px solid hsl(240 5.9% 90%);\n" +
                "}\n" +
                "\n" +
                ".punishment-row {\n" +
                "    transition: background-color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".punishment-row:hover,\n" +
                ".player-page-container .punishment-row:hover {\n" +
                "    background-color: hsl(240 4.8% 95.9% / 0.3) !important;\n" +
                "}\n" +
                "\n" +
                ".type-cell {\n" +
                "    width: 80px;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".type-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 8px 14px;\n" +
                "    border-radius: 20px;\n" +
                "    color: white;\n" +
                "    font-size: 0.95em;\n" +
                "    font-weight: 600;\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".player-cell {\n" +
                "    width: 120px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".player-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: 1px solid hsl(240 5.9% 90%);\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    display: block;\n" +
                "    margin: 0 auto 6px auto;\n" +
                "}\n" +
                "\n" +
                ".player-info {\n" +
                "    flex: 1;\n" +
                "    min-width: 0;\n" +
                "}\n" +
                "\n" +
                "/* 表格中的玩家名字样式 - 使用更具体的选择器避免冲突 */\n" +
                ".punishment-table .player-name {\n" +
                "    font-weight: 600;\n" +
                "    color: #2d3748;\n" +
                "    font-size: 0.9em;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 100px;\n" +
                "    margin: 0 auto;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".player-uuid {\n" +
                "    font-size: 0.8em;\n" +
                "    color: #666;\n" +
                "    font-family: monospace;\n" +
                "}\n" +
                "\n" +
                ".reason-cell {\n" +
                "    min-width: 200px;\n" +
                "    max-width: 300px;\n" +
                "    text-align: center;\n" +
                "    padding: 16px;\n" +
                "}\n" +
                "\n" +
                ".reason-text {\n" +
                "    display: inline-block;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    color: #4a5568;\n" +
                "    font-size: 0.9em;\n" +
                "    text-align: center;\n" +
                "    max-width: 100%;\n" +
                "}\n" +
                "\n" +
                ".staff-cell {\n" +
                "    width: 120px;\n" +
                "    text-align: center;\n" +
                "    vertical-align: middle;\n" +
                "    padding: 12px 8px;\n" +
                "}\n" +
                "\n" +
                ".staff-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: 1px solid hsl(240 5.9% 90%);\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    display: block;\n" +
                "    margin: 0 auto 6px auto;\n" +
                "}\n" +
                "\n" +
                ".console-avatar {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 6px;\n" +
                "    border: 1px solid hsl(240 5.9% 90%);\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    display: block;\n" +
                "    margin: 0 auto 6px auto;\n" +
                "    object-fit: cover;\n" +
                "}\n" +
                "\n" +
                ".console-bust-large {\n" +
                "    border-radius: 8px;\n" +
                "    border: 2px solid hsl(240 5.9% 90%);\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    object-fit: cover;\n" +
                "}\n" +
                "\n" +
                "/* 表格中的执行者名字样式 - 使用更具体的选择器避免冲突 */\n" +
                ".punishment-table .staff-name {\n" +
                "    font-weight: 500;\n" +
                "    color: #666;\n" +
                "    font-size: 0.9em;\n" +
                "    white-space: nowrap;\n" +
                "    overflow: hidden;\n" +
                "    text-overflow: ellipsis;\n" +
                "    max-width: 100px;\n" +
                "    margin: 0 auto;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                "/* 头像和名字容器（按照 next-litebans 布局）*/\n" +
                ".avatar-name-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                "/* 头像名字链接样式（按照 next-litebans AvatarName 组件）*/\n" +
                ".avatar-name-link {\n" +
                "    display: block;\n" +
                "    text-decoration: none;\n" +
                "    color: inherit;\n" +
                "    transition: all 0.2s ease;\n" +
                "    border-radius: 8px;\n" +
                "    padding: 4px;\n" +
                "}\n" +
                "\n" +
                ".avatar-name-link:hover {\n" +
                "    background-color: hsl(240 4.8% 95.9% / 0.4);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".avatar-name-link:hover .player-avatar,\n" +
                ".avatar-name-link:hover .staff-avatar {\n" +
                "    transform: scale(1.05);\n" +
                "}\n" +
                "\n" +
                ".avatar-name-link:hover .punishment-table .player-name,\n" +
                ".avatar-name-link:hover .punishment-table .staff-name {\n" +
                "    color: #2d3748;\n" +
                "    font-weight: 600;\n" +
                "}\n" +
                "\n" +
                ".time-cell,\n" +
                ".until-cell {\n" +
                "    width: 180px;\n" +
                "    font-size: 0.9em;\n" +
                "    color: #666;\n" +
                "    font-family: monospace;\n" +
                "}\n" +
                "\n" +
                ".status-cell {\n" +
                "    width: 100px;\n" +
                "}\n" +
                "\n" +
                ".status-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    padding: 6px 12px;\n" +
                "    border-radius: 15px;\n" +
                "    font-size: 0.95em;\n" +
                "    font-weight: 600;\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-active {\n" +
                "    background: linear-gradient(135deg, #27ae60, #2ecc71);\n" +
                "    color: white;\n" +
                "    border: 1px solid #27ae60;\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-expired {\n" +
                "    background: hsl(47.9 95.8% 53.1% / 0.1);\n" +
                "    color: hsl(32.6 95.4% 44%);\n" +
                "    border: 1px solid hsl(47.9 95.8% 53.1% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-removed {\n" +
                "    background: linear-gradient(135deg, #95a5a6, #7f8c8d);\n" +
                "    color: white;\n" +
                "    border: 1px solid #7f8c8d;\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-completed {\n" +
                "    background: linear-gradient(135deg, #3498db, #2980b9);\n" +
                "    color: white;\n" +
                "    border: 1px solid #2980b9;\n" +
                "}\n" +
                "\n" +
                ".until-na {\n" +
                "    color: hsl(215.4 16.3% 46.9%);\n" +
                "    font-style: italic;\n" +
                "    font-size: 0.9em;\n" +
                "}\n" +
                "\n" +
                "/* 详情按钮样式（按照 next-litebans PunishmentInfoButton）*/\n" +
                ".info-cell {\n" +
                "    text-align: center;\n" +
                "    padding: 12px 8px;\n" +
                "    width: 60px;\n" +
                "}\n" +
                "\n" +
                ".info-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    background: hsl(var(--secondary));\n" +
                "    color: hsl(var(--secondary-foreground));\n" +
                "    border-radius: 6px;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease-in-out;\n" +
                "    border: 1px solid hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".info-btn:hover {\n" +
                "    background: hsl(var(--secondary) / 0.8);\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".info-icon {\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "}\n" +
                "\n" +
                "/* 无记录状态 */\n" +
                ".no-records {\n" +
                "    text-align: center;\n" +
                "    padding: 60px 20px;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".no-records-icon {\n" +
                "    font-size: 4em;\n" +
                "    margin-bottom: 20px;\n" +
                "    opacity: 0.5;\n" +
                "}\n" +
                "\n" +
                ".no-records h3 {\n" +
                "    font-size: 1.5em;\n" +
                "    margin-bottom: 10px;\n" +
                "    color: #2d3748;\n" +
                "}\n" +
                "\n" +
                ".no-records p {\n" +
                "    font-size: 1em;\n" +
                "    color: #666;\n" +
                "}\n" +
                "\n" +
                ".db-status {\n" +
                "    color: #e74c3c !important;\n" +
                "    font-weight: 600;\n" +
                "    margin-top: 10px;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 分页导航 ==================== */\n" +
                ".pagination-section {\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 25px;\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".pagination {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    align-items: center;\n" +
                "    gap: 8px;\n" +
                "    margin-bottom: 15px;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".page-btn {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    min-width: 40px;\n" +
                "    height: 40px;\n" +
                "    padding: 0 12px;\n" +
                "    background: #f8f9fa;\n" +
                "    color: #666;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 8px;\n" +
                "    font-weight: 600;\n" +
                "    transition: all 0.3s ease;\n" +
                "    border: 1px solid #e9ecef;\n" +
                "}\n" +
                "\n" +
                ".page-btn:hover {\n" +
                "    background: #e9ecef;\n" +
                "    transform: translateY(-2px);\n" +
                "}\n" +
                "\n" +
                ".page-btn.current {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);\n" +
                "}\n" +
                "\n" +
                ".page-btn.prev-btn,\n" +
                ".page-btn.next-btn {\n" +
                "    padding: 0 16px;\n" +
                "}\n" +
                "\n" +
                ".page-dots {\n" +
                "    color: #666;\n" +
                "    font-weight: 600;\n" +
                "    padding: 0 8px;\n" +
                "}\n" +
                "\n" +
                ".page-info {\n" +
                "    color: #666;\n" +
                "    font-size: 0.9em;\n" +
                "}\n" +
                "\n" +
                "/* ==================== 响应式设计 ==================== */\n" +
                "@media (max-width: 768px) {\n" +
                "    .container {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .page-header {\n" +
                "        padding: 20px;\n" +
                "    }\n" +
                "    \n" +
                "    .page-header h1 {\n" +
                "        font-size: 2em;\n" +
                "    }\n" +
                "    \n" +
                "    .player-header {\n" +
                "        padding: 20px;\n" +
                "        flex-direction: column;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-info-container {\n" +
                "        flex-direction: column;\n" +
                "        gap: 20px;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-bust-image {\n" +
                "        width: 128px;\n" +
                "        height: 128px;\n" +
                "    }\n" +
                "    \n" +
                "    .player-name-title {\n" +
                "        font-size: 2rem;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-details-section {\n" +
                "        max-width: 100%;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-badges {\n" +
                "        justify-content: center;\n" +
                "    }\n" +
                "    \n" +
                "    .nav-tabs {\n" +
                "        padding: 15px;\n" +
                "    }\n" +
                "    \n" +
                "    .tab-btn {\n" +
                "        padding: 10px 15px;\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "    \n" +
                "    .stats-grid {\n" +
                "        grid-template-columns: 1fr;\n" +
                "    }\n" +
                "    \n" +
                "    .search-input {\n" +
                "        min-width: 250px;\n" +
                "    }\n" +
                "    \n" +
                "    .search-input-group {\n" +
                "        flex-direction: column;\n" +
                "        align-items: stretch;\n" +
                "    }\n" +
                "    \n" +
                "    .punishment-table {\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "    \n" +
                "    .punishment-table th,\n" +
                "    .punishment-table td {\n" +
                "        padding: 10px 8px;\n" +
                "    }\n" +
                "    \n" +
                "    .player-cell {\n" +
                "        width: 150px;\n" +
                "    }\n" +
                "    \n" +
                "    .reason-cell {\n" +
                "        max-width: 200px;\n" +
                "    }\n" +
                "    \n" +
                "    .time-cell,\n" +
                "    .until-cell {\n" +
                "        width: 120px;\n" +
                "        font-size: 0.8em;\n" +
                "    }\n" +
                "    \n" +
                "    .pagination {\n" +
                "        gap: 5px;\n" +
                "    }\n" +
                "    \n" +
                "    .page-btn {\n" +
                "        min-width: 35px;\n" +
                "        height: 35px;\n" +
                "        font-size: 0.9em;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* ==================== 夜间模式样式 ==================== */\n" +
                ".dark body {\n" +
                "    /* 只设置颜色，不覆盖背景图片 */\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 如果没有背景图片，则使用深色背景 */\n" +
                ".dark body:not(.has-background-image) {\n" +
                "    background: hsl(var(--background));\n" +
                "}\n" +
                "\n" +
                "/* 有背景图片时，在深色主题下添加半透明遮罩以提高可读性 */\n" +
                ".dark body.has-background-image::before {\n" +
                "    content: '';\n" +
                "    position: fixed;\n" +
                "    top: 0;\n" +
                "    left: 0;\n" +
                "    width: 100%;\n" +
                "    height: 100%;\n" +
                "    background: rgba(0, 0, 0, 0.3);\n" +
                "    pointer-events: none;\n" +
                "    z-index: -1;\n" +
                "}\n" +
                "\n" +
                ".dark .page-header {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .page-header h1 {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .page-description {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .player-view-header {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .player-header {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .player-name-title {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-badge {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .nav-tabs {\n" +
                "    background: hsl(var(--card));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .tab-btn {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .tab-btn:hover {\n" +
                "    background: hsl(var(--accent));\n" +
                "}\n" +
                "\n" +
                ".dark .stat-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    color: hsl(var(--card-foreground));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .stat-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .stat-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .search-section {\n" +
                "    background: hsl(var(--card));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .search-input {\n" +
                "    background: hsl(var(--input));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .search-input:hover {\n" +
                "    border-color: hsl(var(--border));\n" +
                "    background-color: hsl(var(--muted) / 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .search-input:focus {\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);\n" +
                "}\n" +
                "\n" +
                ".dark .theme-toggle-btn {\n" +
                "    background: hsl(var(--muted));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .theme-toggle-btn:hover {\n" +
                "    background: hsl(var(--accent));\n" +
                "    border-color: #667eea;\n" +
                "}\n" +
                "\n" +
                ".dark .search-result-info {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .table-container {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table th {\n" +
                "    background: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table td {\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-row:hover,\n" +
                ".dark .player-page-container .punishment-row:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.2) !important;\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table .player-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-table .staff-name {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .reason-text {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .time-cell,\n" +
                ".dark .until-cell {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .player-avatar,\n" +
                ".dark .staff-avatar {\n" +
                "    border-color: hsl(var(--border));\n" +
                "    background: hsl(var(--muted));\n" +
                "}\n" +
                "\n" +
                ".dark .console-avatar {\n" +
                "    background: hsl(var(--muted));\n" +
                "}\n" +
                "\n" +
                ".dark .avatar-name-link:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .avatar-name-link:hover .punishment-table .player-name,\n" +
                ".dark .avatar-name-link:hover .punishment-table .staff-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 过滤器标签样式（按照 next-litebans Filters 组件）*/\n" +
                ".filters-section {\n" +
                "    margin-bottom: 1rem;\n" +
                "}\n" +
                "\n" +
                ".filters-container {\n" +
                "    display: flex;\n" +
                "    flex-wrap: wrap;\n" +
                "    gap: 0.5rem;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .filters-container {\n" +
                "        justify-content: flex-start;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".filter-tag {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    height: 2rem;\n" +
                "    border: 1px dashed hsl(240 5.9% 90%);\n" +
                "    border-radius: 0.375rem;\n" +
                "    background: hsl(0 0% 100%);\n" +
                "    font-size: 0.875rem;\n" +
                "    margin: 0 auto;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .filter-tag {\n" +
                "        margin: 0;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".filter-label {\n" +
                "    padding: 0 0.5rem;\n" +
                "    font-weight: 500;\n" +
                "    color: hsl(240 5.9% 10%);\n" +
                "}\n" +
                "\n" +
                ".filter-separator {\n" +
                "    width: 1px;\n" +
                "    height: 1rem;\n" +
                "    background: hsl(240 5.9% 90%);\n" +
                "    margin: 0 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".filter-badge {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.25rem;\n" +
                "    padding: 0.125rem 0.25rem;\n" +
                "    background: hsl(240 4.8% 95.9%);\n" +
                "    border-radius: 0.125rem;\n" +
                "    font-weight: normal;\n" +
                "}\n" +
                "\n" +
                ".filter-avatar {\n" +
                "    width: 16px;\n" +
                "    height: 16px;\n" +
                "    border-radius: 2px;\n" +
                "}\n" +
                "\n" +
                ".filter-name {\n" +
                "    color: hsl(240 5.9% 10%);\n" +
                "    font-size: 0.875rem;\n" +
                "}\n" +
                "\n" +
                ".filter-remove {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    justify-content: center;\n" +
                "    width: 1rem;\n" +
                "    height: 1rem;\n" +
                "    margin-left: 0.5rem;\n" +
                "    margin-right: 0.25rem;\n" +
                "    background: none;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "    border-radius: 50%;\n" +
                "    transition: background-color 0.2s ease;\n" +
                "}\n" +
                "\n" +
                ".filter-remove:hover {\n" +
                "    background: hsl(0 84.2% 60.2% / 0.1);\n" +
                "}\n" +
                "\n" +
                ".remove-icon {\n" +
                "    font-size: 0.75rem;\n" +
                "    color: hsl(0 84.2% 60.2%);\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的过滤器样式 */\n" +
                ".dark .filter-tag {\n" +
                "    border-color: hsl(var(--border));\n" +
                "    background: hsl(var(--background));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-label {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-separator {\n" +
                "    background: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-badge {\n" +
                "    background: hsl(var(--muted));\n" +
                "}\n" +
                "\n" +
                ".dark .filter-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .no-records {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .no-records h3 {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .pagination-section {\n" +
                "    background: hsl(var(--card));\n" +
                "    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn {\n" +
                "    background: hsl(240 3.7% 15.9%);\n" +
                "    color: hsl(240 5% 64.9%);\n" +
                "    border-color: hsl(240 3.7% 15.9%);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn:hover {\n" +
                "    background: hsl(240 3.7% 20%);\n" +
                "    color: hsl(0 0% 98%);\n" +
                "    border-color: hsl(240 3.7% 25%);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn.current {\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    color: white;\n" +
                "    border-color: #667eea;\n" +
                "    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);\n" +
                "}\n" +
                "\n" +
                ".dark .page-btn.current:hover {\n" +
                "    background: linear-gradient(135deg, #5a6fd8, #6a4190);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);\n" +
                "}\n" +
                "\n" +
                ".dark .page-dots {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .page-info {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                "/* ==================== 详细信息页面样式 ==================== */\n" +
                ".detail-page-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "    padding: 2rem 1rem;\n" +
                "    min-height: calc(100vh - 200px);\n" +
                "}\n" +
                "\n" +
                ".detail-header {\n" +
                "    text-align: center;\n" +
                "    margin-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".detail-title {\n" +
                "    font-size: 3rem;\n" +
                "    font-weight: bold;\n" +
                "    line-height: 1.1;\n" +
                "    margin-bottom: 1rem;\n" +
                "    color: #2d3748;\n" +
                "    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".detail-badges {\n" +
                "    display: flex;\n" +
                "    justify-content: center;\n" +
                "    gap: 0.5rem;\n" +
                "    flex-wrap: wrap;\n" +
                "}\n" +
                "\n" +
                ".status-badge.status-ipban {\n" +
                "    background: hsl(271.5 81.3% 55.9% / 0.1);\n" +
                "    color: hsl(271.5 81.3% 55.9%);\n" +
                "    border: 1px solid hsl(271.5 81.3% 55.9% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".punishment-info-section {\n" +
                "    width: 100%;\n" +
                "    max-width: 1200px;\n" +
                "}\n" +
                "\n" +
                ".punishment-info-card {\n" +
                "    display: grid;\n" +
                "    grid-template-columns: 1fr 2fr 1fr;\n" +
                "    gap: 3rem;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 3rem;\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".player-info-area,\n" +
                ".staff-info-area {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "}\n" +
                "\n" +
                ".detail-info-area {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    gap: 1.5rem;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".info-area-title {\n" +
                "    font-size: 1.5rem;\n" +
                "    font-weight: bold;\n" +
                "    color: #2d3748;\n" +
                "    margin-bottom: 1rem;\n" +
                "}\n" +
                "\n" +
                ".player-body-image,\n" +
                ".staff-body-image {\n" +
                "    width: 200px;\n" +
                "    height: 400px;\n" +
                "    object-fit: contain;\n" +
                "    border-radius: 10px;\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".player-body-image:hover,\n" +
                ".staff-body-image:hover {\n" +
                "    transform: scale(1.05);\n" +
                "}\n" +
                "\n" +
                ".staff-body-image {\n" +
                "    transform: scaleX(-1);\n" +
                "}\n" +
                "\n" +
                ".staff-body-image:hover {\n" +
                "    transform: scaleX(-1.05) scaleY(1.05);\n" +
                "}\n" +
                "\n" +
                ".player-name-area,\n" +
                ".staff-name-area {\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.75rem;\n" +
                "}\n" +
                "\n" +
                ".player-avatar-large,\n" +
                ".staff-avatar-large {\n" +
                "    width: 32px;\n" +
                "    height: 32px;\n" +
                "    border-radius: 4px;\n" +
                "    border: 2px solid #e2e8f0;\n" +
                "}\n" +
                "\n" +
                "/* 玩家和执行者名字样式 - 使用更具体的选择器 */\n" +
                ".player-name-area .player-name-large,\n" +
                ".staff-name-area .staff-name-large {\n" +
                "    font-size: 1.25rem !important;\n" +
                "    font-weight: 600 !important;\n" +
                "    color: #2d3748 !important;\n" +
                "    margin: 0 !important;\n" +
                "    line-height: 1.2 !important;\n" +
                "    text-align: center !important;\n" +
                "}\n" +
                "\n" +
                "/* 确保p标签和h3标签都使用相同样式 */\n" +
                ".player-name-area p.player-name-large,\n" +
                ".staff-name-area p.staff-name-large,\n" +
                ".player-name-area h3.player-name-large,\n" +
                ".staff-name-area h3.staff-name-large {\n" +
                "    font-size: 1.25rem !important;\n" +
                "    font-weight: 600 !important;\n" +
                "    color: #2d3748 !important;\n" +
                "    margin: 0 !important;\n" +
                "    line-height: 1.2 !important;\n" +
                "    text-align: center !important;\n" +
                "}\n" +
                "\n" +
                ".detail-item {\n" +
                "    padding: 1rem 0;\n" +
                "    border-bottom: 1px solid #e2e8f0;\n" +
                "}\n" +
                "\n" +
                ".detail-item:last-child {\n" +
                "    border-bottom: none;\n" +
                "}\n" +
                "\n" +
                ".detail-label {\n" +
                "    font-size: 1.125rem;\n" +
                "    font-weight: 600;\n" +
                "    color: #4a5568;\n" +
                "    margin-bottom: 0.5rem;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".detail-value {\n" +
                "    font-size: 1rem;\n" +
                "    color: #2d3748;\n" +
                "    line-height: 1.5;\n" +
                "    word-break: break-word;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                "/* Flex 布局工具类 */\n" +
                ".flex {\n" +
                "    display: flex;\n" +
                "}\n" +
                "\n" +
                ".items-center {\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".justify-center {\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                "/* 状态点样式（按照 next-litebans PunishmentStatusDot）*/\n" +
                ".status-dot {\n" +
                "    width: 8px;\n" +
                "    height: 8px;\n" +
                "    border-radius: 50%;\n" +
                "    flex-shrink: 0;\n" +
                "    margin-right: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".status-dot.status-active {\n" +
                "    background-color: #10b981; /* green-500 */\n" +
                "}\n" +
                "\n" +
                ".status-dot.status-expired {\n" +
                "    background-color: #ef4444; /* red-500 */\n" +
                "}\n" +
                "\n" +
                ".status-dot.status-temporal {\n" +
                "    background-color: #f59e0b; /* orange-500 */\n" +
                "}\n" +
                "\n" +
                "/* 移动端详细信息样式（按照 next-litebans 响应式设计）*/\n" +
                ".mobile-details {\n" +
                "    display: none;\n" +
                "    width: 100%;\n" +
                "    max-width: 350px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 1.5rem;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 15px;\n" +
                "    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-item {\n" +
                "    margin-bottom: 1.5rem;\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-item:last-child {\n" +
                "    margin-bottom: 0;\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-label {\n" +
                "    font-size: 1.125rem;\n" +
                "    font-weight: 600;\n" +
                "    color: #4a5568;\n" +
                "    margin-bottom: 0.5rem;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".mobile-detail-value {\n" +
                "    font-size: 1rem;\n" +
                "    color: #2d3748;\n" +
                "    line-height: 1.5;\n" +
                "    word-break: break-word;\n" +
                "    display: flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的移动端详细信息样式 */\n" +
                ".dark .mobile-details {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .mobile-detail-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .mobile-detail-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的详细信息页面样式 */\n" +
                ".dark .detail-title {\n" +
                "    color: hsl(var(--foreground));\n" +
                "    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);\n" +
                "}\n" +
                "\n" +
                ".dark .punishment-info-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .info-area-title {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的玩家和执行者名字样式 */\n" +
                ".dark .player-name-area .player-name-large,\n" +
                ".dark .staff-name-area .staff-name-large,\n" +
                ".dark .player-name-area p.player-name-large,\n" +
                ".dark .staff-name-area p.staff-name-large,\n" +
                ".dark .player-name-area h3.player-name-large,\n" +
                ".dark .staff-name-area h3.staff-name-large {\n" +
                "    color: hsl(var(--foreground)) !important;\n" +
                "    font-size: 1.25rem !important;\n" +
                "    font-weight: 600 !important;\n" +
                "}\n" +
                "\n" +
                ".dark .detail-item {\n" +
                "    border-bottom-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .detail-label {\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .detail-value {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                ".dark .player-avatar-large,\n" +
                ".dark .staff-avatar-large {\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 - 详细信息页面 */\n" +
                "@media (max-width: 1024px) {\n" +
                "    .punishment-info-card {\n" +
                "        grid-template-columns: 1fr; /* 在中等屏幕上改为单列布局 */\n" +
                "        gap: 2rem;\n" +
                "        text-align: center;\n" +
                "    }\n" +
                "    \n" +
                "    .player-body-image,\n" +
                "    .staff-body-image {\n" +
                "        width: 150px;\n" +
                "        height: 300px;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (max-width: 640px) {\n" +
                "    .detail-title {\n" +
                "        font-size: 2rem;\n" +
                "    }\n" +
                "    \n" +
                "    .punishment-info-card {\n" +
                "        display: none; /* 在小屏幕上隐藏桌面版详细信息卡片 */\n" +
                "    }\n" +
                "    \n" +
                "    .mobile-details {\n" +
                "        display: block; /* 在小屏幕上显示移动版详细信息 */\n" +
                "    }\n" +
                "    \n" +
                "    .info-area-title {\n" +
                "        font-size: 1.25rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 641px) {\n" +
                "    .mobile-details {\n" +
                "        display: none; /* 在大屏幕上隐藏移动版详细信息 */\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "/* ==================== 玩家页面样式 ==================== */\n" +
                ".player-page-container {\n" +
                "    display: flex;\n" +
                "    flex-direction: column;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "    padding: 2rem 1rem;\n" +
                "    min-height: calc(100vh - 200px);\n" +
                "}\n" +
                "\n" +
                "/* Tailwind-like utility classes for player header */\n" +
                ".flex {\n" +
                "    display: flex;\n" +
                "}\n" +
                "\n" +
                ".h-full {\n" +
                "    height: 100%;\n" +
                "}\n" +
                "\n" +
                ".flex-col {\n" +
                "    flex-direction: column;\n" +
                "}\n" +
                "\n" +
                ".items-center {\n" +
                "    align-items: center;\n" +
                "}\n" +
                "\n" +
                ".gap-4 {\n" +
                "    gap: 1rem;\n" +
                "}\n" +
                "\n" +
                ".py-8 {\n" +
                "    padding-top: 2rem;\n" +
                "    padding-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".space-y-2 > * + * {\n" +
                "    margin-top: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".space-x-2 > * + * {\n" +
                "    margin-left: 0.5rem;\n" +
                "}\n" +
                "\n" +
                ".space-x-4 > * + * {\n" +
                "    margin-left: 1rem;\n" +
                "}\n" +
                "\n" +
                ".mx-auto {\n" +
                "    margin-left: auto;\n" +
                "    margin-right: auto;\n" +
                "}\n" +
                "\n" +
                ".text-center {\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".text-4xl {\n" +
                "    font-size: 2.25rem;\n" +
                "    line-height: 2.5rem;\n" +
                "}\n" +
                "\n" +
                ".font-bold {\n" +
                "    font-weight: 700;\n" +
                "}\n" +
                "\n" +
                ".leading-tight {\n" +
                "    line-height: 1.25;\n" +
                "}\n" +
                "\n" +
                ".tracking-tighter {\n" +
                "    letter-spacing: -0.05em;\n" +
                "}\n" +
                "\n" +
                ".whitespace-nowrap {\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 768px) {\n" +
                "    .md\\:flex {\n" +
                "        display: flex;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:space-x-4 > * + * {\n" +
                "        margin-left: 1rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:w-\\[350px\\] {\n" +
                "        width: 350px;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:py-4 {\n" +
                "        padding-top: 1rem;\n" +
                "        padding-bottom: 1rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:text-left {\n" +
                "        text-align: left;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:py-12 {\n" +
                "        padding-top: 3rem;\n" +
                "        padding-bottom: 3rem;\n" +
                "    }\n" +
                "    \n" +
                "    .md\\:pb-8 {\n" +
                "        padding-bottom: 2rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 640px) {\n" +
                "    .sm\\:text-5xl {\n" +
                "        font-size: 3rem;\n" +
                "        line-height: 1;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "@media (min-width: 1024px) {\n" +
                "    .lg\\:leading-\\[1\\.1\\] {\n" +
                "        line-height: 1.1;\n" +
                "    }\n" +
                "    \n" +
                "    .lg\\:py-18 {\n" +
                "        padding-top: 4.5rem;\n" +
                "        padding-bottom: 4.5rem;\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                ".player-header {\n" +
                "    width: 100%;\n" +
                "    max-width: 1200px;\n" +
                "    margin-bottom: 2rem;\n" +
                "}\n" +
                "\n" +
                ".player-info-card {\n" +
                "    display: flex;\n" +
                "    flex-direction: row;\n" +
                "    align-items: center;\n" +
                "    gap: 2rem;\n" +
                "    background: rgba(255, 255, 255, 0.95);\n" +
                "    border-radius: 20px;\n" +
                "    padding: 2rem;\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);\n" +
                "    border: 1px solid rgba(255, 255, 255, 0.2);\n" +
                "}\n" +
                "\n" +
                ".player-skin-container {\n" +
                "    flex-shrink: 0;\n" +
                "}\n" +
                "\n" +
                ".player-bust-image {\n" +
                "    width: 128px;\n" +
                "    height: 128px;\n" +
                "    border-radius: 10px;\n" +
                "    transition: transform 0.3s ease;\n" +
                "}\n" +
                "\n" +
                ".player-bust-image:hover {\n" +
                "    transform: scale(1.1);\n" +
                "}\n" +
                "\n" +
                ".player-details {\n" +
                "    flex: 1;\n" +
                "    text-align: center;\n" +
                "}\n" +
                "\n" +
                ".player-name {\n" +
                "    font-size: 2.5rem;\n" +
                "    font-weight: bold;\n" +
                "    margin-bottom: 1rem;\n" +
                "    text-align: center;\n" +
                "    background: linear-gradient(135deg, #667eea, #764ba2);\n" +
                "    -webkit-background-clip: text;\n" +
                "    -webkit-text-fill-color: transparent;\n" +
                "    background-clip: text;\n" +
                "}\n" +
                "\n" +
                ".player-badges {\n" +
                "    display: flex;\n" +
                "    gap: 0.75rem;\n" +
                "    flex-wrap: wrap;\n" +
                "    justify-content: center;\n" +
                "}\n" +
                "\n" +
                ".player-badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    gap: 0.5rem;\n" +
                "    padding: 0.5rem 1rem;\n" +
                "    border-radius: 12px;\n" +
                "    font-size: 0.875rem;\n" +
                "    font-weight: 600;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    border: 1px solid transparent;\n" +
                "}\n" +
                "\n" +
                ".player-badge:hover {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n" +
                "}\n" +
                "\n" +
                ".player-badge.active {\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);\n" +
                "    border: 2px solid currentColor;\n" +
                "}\n" +
                "\n" +
                "/* Badge styles (next-litebans compatible) */\n" +
                ".badge {\n" +
                "    display: inline-flex;\n" +
                "    align-items: center;\n" +
                "    border-radius: 9999px;\n" +
                "    padding: 0.25rem 0.75rem;\n" +
                "    font-size: 0.75rem;\n" +
                "    font-weight: 600;\n" +
                "    text-decoration: none;\n" +
                "    transition: all 0.2s ease;\n" +
                "    white-space: nowrap;\n" +
                "}\n" +
                "\n" +
                ".badge-secondary {\n" +
                "    background-color: hsl(210 40% 95%);\n" +
                "    color: hsl(215.4 16.3% 46.9%);\n" +
                "    border: 1px solid hsl(214.3 31.8% 91.4%);\n" +
                "}\n" +
                "\n" +
                ".badge-secondary:hover {\n" +
                "    background-color: hsl(210 40% 90%);\n" +
                "    transform: translateY(-1px);\n" +
                "}\n" +
                "\n" +
                ".badge-secondary.active {\n" +
                "    background-color: hsl(210 40% 85%);\n" +
                "    border-color: hsl(210 40% 70%);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n" +
                "}\n" +
                "\n" +
                ".badge-icon {\n" +
                "    margin-right: 0.25rem;\n" +
                "}\n" +
                "\n" +
                "/* Dark mode badge styles */\n" +
                ".dark .badge-secondary {\n" +
                "    background-color: hsl(var(--muted));\n" +
                "    color: hsl(var(--muted-foreground));\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .badge-secondary:hover {\n" +
                "    background-color: hsl(var(--muted) / 0.8);\n" +
                "}\n" +
                "\n" +
                ".dark .badge-secondary.active {\n" +
                "    background-color: hsl(var(--muted) / 0.6);\n" +
                "    border-color: hsl(var(--border));\n" +
                "}\n" +
                "\n" +
                ".dark .badge-primary {\n" +
                "    background-color: hsl(220 13% 18%);\n" +
                "    color: hsl(210 40% 85%);\n" +
                "    border: 1px solid hsl(220 13% 25%);\n" +
                "    transform: translateY(-1px);\n" +
                "    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.1);\n" +
                "}\n" +
                "\n" +
                ".dark .badge-primary:hover {\n" +
                "    background-color: hsl(220 13% 22%);\n" +
                "    border-color: hsl(220 13% 30%);\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.15);\n" +
                "}\n" +
                "\n" +
                ".ban-badge {\n" +
                "    background: hsl(0 84.2% 60.2% / 0.1);\n" +
                "    color: hsl(0 72.2% 50.6%);\n" +
                "    border-color: hsl(0 84.2% 60.2% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".mute-badge {\n" +
                "    background: hsl(25 95% 53% / 0.1);\n" +
                "    color: hsl(25 95% 53%);\n" +
                "    border-color: hsl(25 95% 53% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".warn-badge {\n" +
                "    background: hsl(48 96% 53% / 0.1);\n" +
                "    color: hsl(48 96% 53%);\n" +
                "    border-color: hsl(48 96% 53% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".kick-badge {\n" +
                "    background: hsl(271.5 81.3% 55.9% / 0.1);\n" +
                "    color: hsl(271.5 81.3% 55.9%);\n" +
                "    border-color: hsl(271.5 81.3% 55.9% / 0.2);\n" +
                "}\n" +
                "\n" +
                ".player-punishments-section {\n" +
                "    width: 100%;\n" +
                "    max-width: 1200px;\n" +
                "}\n" +
                "\n" +
                "/* 夜间模式下的玩家页面样式 */\n" +
                ".dark .player-info-card {\n" +
                "    background: hsl(var(--card));\n" +
                "    border-color: hsl(var(--border));\n" +
                "    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);\n" +
                "}\n" +
                "\n" +
                ".dark .player-name {\n" +
                "    color: hsl(var(--foreground));\n" +
                "}\n" +
                "\n" +
                "/* 响应式设计 - 玩家页面 */\n" +
                "@media (max-width: 768px) {\n" +
                "    .player-info-card {\n" +
                "        flex-direction: column;\n" +
                "        text-align: center;\n" +
                "        gap: 1.5rem;\n" +
                "        padding: 1.5rem;\n" +
                "    }\n" +
                "    \n" +
                "    .player-name {\n" +
                "        font-size: 2rem;\n" +
                "    }\n" +
                "    \n" +
                "    .player-badges {\n" +
                "        justify-content: center;\n" +
                "    }\n" +
                "}\n";
    }

    /**
     * 生成处罚页面JavaScript
     */
    private String generatePunishmentPageJS() {
        return "// 页面加载完成后执行\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    // 初始化主题\n" +
                "    initTheme();\n" +
                "    \n" +
                "    // 初始化工具提示\n" +
                "    initTooltips();\n" +
                "    \n" +
                "    // 初始化表格功能\n" +
                "    initTableFeatures();\n" +
                "    \n" +
                "    // 初始化搜索功能\n" +
                "    initSearchFeatures();\n" +
                "    \n" +
                "    // 初始化动态时间显示\n" +
                "    initRelativeTime();\n" +
                "});\n" +
                "\n" +
                "// 初始化主题\n" +
                "function initTheme() {\n" +
                "    const savedTheme = localStorage.getItem('punishment-theme') || 'light';\n" +
                "    const themeIcon = document.querySelector('.theme-icon');\n" +
                "    \n" +
                "    if (savedTheme === 'dark') {\n" +
                "        document.documentElement.classList.add('dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '☀️';\n" +
                "    } else {\n" +
                "        document.documentElement.classList.remove('dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '🌙';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 切换主题\n" +
                "function toggleTheme() {\n" +
                "    const isDark = document.documentElement.classList.contains('dark');\n" +
                "    const themeIcon = document.querySelector('.theme-icon');\n" +
                "    \n" +
                "    if (isDark) {\n" +
                "        document.documentElement.classList.remove('dark');\n" +
                "        localStorage.setItem('punishment-theme', 'light');\n" +
                "        if (themeIcon) themeIcon.textContent = '🌙';\n" +
                "        showNotification('已切换到浅色主题', 'success');\n" +
                "    } else {\n" +
                "        document.documentElement.classList.add('dark');\n" +
                "        localStorage.setItem('punishment-theme', 'dark');\n" +
                "        if (themeIcon) themeIcon.textContent = '☀️';\n" +
                "        showNotification('已切换到深色主题', 'success');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新玩家头像预览\n" +
                "function updatePlayerAvatar(playerName) {\n" +
                "    const avatarPreview = document.getElementById('playerAvatarPreview');\n" +
                "    const headerAvatarPreview = document.getElementById('headerPlayerAvatar');\n" +
                "    \n" +
                "    if (!playerName || playerName.trim().length < 3) {\n" +
                "        if (avatarPreview) avatarPreview.style.display = 'none';\n" +
                "        if (headerAvatarPreview) headerAvatarPreview.style.display = 'none';\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    // 清理玩家名（移除特殊字符，只保留字母数字和下划线）\n" +
                "    const cleanName = playerName.trim().replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "    \n" +
                "    if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "        const avatarUrl = `https://mc-heads.net/avatar/${cleanName}/32`;\n" +
                "        \n" +
                "        // 更新页面搜索框头像\n" +
                "        if (avatarPreview) {\n" +
                "            avatarPreview.src = avatarUrl;\n" +
                "            avatarPreview.style.display = 'block';\n" +
                "            \n" +
                "            // 处理头像加载失败\n" +
                "            avatarPreview.onerror = function() {\n" +
                "                this.style.display = 'none';\n" +
                "            };\n" +
                "        }\n" +
                "        \n" +
                "        // 更新头部搜索框头像\n" +
                "        if (headerAvatarPreview) {\n" +
                "            headerAvatarPreview.src = avatarUrl;\n" +
                "            headerAvatarPreview.style.display = 'block';\n" +
                "            \n" +
                "            // 处理头像加载失败\n" +
                "            headerAvatarPreview.onerror = function() {\n" +
                "                this.style.display = 'none';\n" +
                "            };\n" +
                "        }\n" +
                "    } else {\n" +
                "        if (avatarPreview) avatarPreview.style.display = 'none';\n" +
                "        if (headerAvatarPreview) headerAvatarPreview.style.display = 'none';\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 处理搜索表单提交（按照 next-litebans PlayerInput 逻辑）\n" +
                "function handleSearchSubmit(event) {\n" +
                "    event.preventDefault();\n" +
                "    const form = event.target;\n" +
                "    const playerName = form.querySelector('input[name=\"player\"]').value.trim();\n" +
                "    \n" +
                "    if (playerName) {\n" +
                "        // 检查是否是有效的玩家名\n" +
                "        const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "        \n" +
                "        if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "            // 按照 next-litebans 逻辑，搜索玩家时跳转到玩家专用页面\n" +
                "            checkPlayerAndRedirect(cleanName);\n" +
                "        } else {\n" +
                "            showNotification('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）', 'error');\n" +
                "        }\n" +
                "    }\n" +
                "    return false;\n" +
                "}\n" +
                "\n" +
                "// 检查玩家是否存在并跳转（按照 next-litebans checkPlayer 逻辑）\n" +
                "function checkPlayerAndRedirect(playerName) {\n" +
                "    // 显示加载状态\n" +
                "    showNotification('正在搜索玩家...', 'info');\n" +
                "    \n" +
                "    // 简化版本：直接跳转到玩家页面，让服务器端处理玩家是否存在的检查\n" +
                "    window.location.href = '/player/' + encodeURIComponent(playerName);\n" +
                "}\n" +
                "\n" +
                "// 处理搜索回车事件\n" +
                "function handleSearchEnter(event) {\n" +
                "    if (event.key === 'Enter') {\n" +
                "        event.preventDefault();\n" +
                "        const playerName = event.target.value.trim();\n" +
                "        \n" +
                "        if (playerName) {\n" +
                "            // 检查是否是有效的玩家名\n" +
                "            const cleanName = playerName.replace(/[^a-zA-Z0-9_]/g, '');\n" +
                "            \n" +
                "            if (cleanName.length >= 3 && cleanName.length <= 16) {\n" +
                "                // 按照 next-litebans 逻辑，搜索玩家时跳转到玩家专用页面\n" +
                "                checkPlayerAndRedirect(cleanName);\n" +
                "            } else {\n" +
                "                showNotification('请输入有效的玩家名（3-16个字符，只能包含字母、数字和下划线）', 'error');\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 初始化工具提示\n" +
                "function initTooltips() {\n" +
                "    const elements = document.querySelectorAll('[title]');\n" +
                "    elements.forEach(element => {\n" +
                "        element.addEventListener('mouseenter', showTooltip);\n" +
                "        element.addEventListener('mouseleave', hideTooltip);\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 显示工具提示\n" +
                "function showTooltip(event) {\n" +
                "    const element = event.target;\n" +
                "    const title = element.getAttribute('title');\n" +
                "    if (!title) return;\n" +
                "    \n" +
                "    // 创建工具提示元素\n" +
                "    const tooltip = document.createElement('div');\n" +
                "    tooltip.className = 'custom-tooltip';\n" +
                "    tooltip.textContent = title;\n" +
                "    tooltip.style.cssText = `\n" +
                "        position: absolute;\n" +
                "        background: rgba(0, 0, 0, 0.8);\n" +
                "        color: white;\n" +
                "        padding: 8px 12px;\n" +
                "        border-radius: 6px;\n" +
                "        font-size: 0.8em;\n" +
                "        z-index: 1000;\n" +
                "        pointer-events: none;\n" +
                "        max-width: 300px;\n" +
                "        word-wrap: break-word;\n" +
                "    `;\n" +
                "    \n" +
                "    document.body.appendChild(tooltip);\n" +
                "    \n" +
                "    // 定位工具提示\n" +
                "    const rect = element.getBoundingClientRect();\n" +
                "    tooltip.style.left = rect.left + 'px';\n" +
                "    tooltip.style.top = (rect.bottom + 5) + 'px';\n" +
                "    \n" +
                "    element.tooltipElement = tooltip;\n" +
                "    element.removeAttribute('title');\n" +
                "    element.originalTitle = title;\n" +
                "}\n" +
                "\n" +
                "// 隐藏工具提示\n" +
                "function hideTooltip(event) {\n" +
                "    const element = event.target;\n" +
                "    if (element.tooltipElement) {\n" +
                "        document.body.removeChild(element.tooltipElement);\n" +
                "        element.tooltipElement = null;\n" +
                "        element.setAttribute('title', element.originalTitle);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 初始化表格功能\n" +
                "function initTableFeatures() {\n" +
                "    // 表格行悬停效果现在通过CSS处理，不需要JavaScript\n" +
                "    // 这里可以添加其他表格相关的功能\n" +
                "}\n" +
                "\n" +
                "// 初始化搜索功能\n" +
                "function initSearchFeatures() {\n" +
                "    const searchInput = document.querySelector('.search-input');\n" +
                "    if (searchInput) {\n" +
                "        // 回车键搜索\n" +
                "        searchInput.addEventListener('keypress', function(event) {\n" +
                "            if (event.key === 'Enter') {\n" +
                "                event.preventDefault();\n" +
                "                this.closest('form').submit();\n" +
                "            }\n" +
                "        });\n" +
                "        \n" +
                "        // 自动聚焦\n" +
                "        if (!searchInput.value) {\n" +
                "            searchInput.focus();\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 复制UUID到剪贴板\n" +
                "function copyUUID(uuid) {\n" +
                "    if (navigator.clipboard) {\n" +
                "        navigator.clipboard.writeText(uuid).then(function() {\n" +
                "            showNotification('UUID已复制到剪贴板', 'success');\n" +
                "        }).catch(function() {\n" +
                "            fallbackCopyUUID(uuid);\n" +
                "        });\n" +
                "    } else {\n" +
                "        fallbackCopyUUID(uuid);\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 备用复制方法\n" +
                "function fallbackCopyUUID(uuid) {\n" +
                "    const textArea = document.createElement('textarea');\n" +
                "    textArea.value = uuid;\n" +
                "    document.body.appendChild(textArea);\n" +
                "    textArea.select();\n" +
                "    try {\n" +
                "        document.execCommand('copy');\n" +
                "        showNotification('UUID已复制到剪贴板', 'success');\n" +
                "    } catch (err) {\n" +
                "        showNotification('复制失败，请手动复制', 'error');\n" +
                "    }\n" +
                "    document.body.removeChild(textArea);\n" +
                "}\n" +
                "\n" +
                "// 显示通知\n" +
                "function showNotification(message, type) {\n" +
                "    const notification = document.createElement('div');\n" +
                "    notification.className = 'notification notification-' + type;\n" +
                "    notification.textContent = message;\n" +
                "    notification.style.cssText = `\n" +
                "        position: fixed;\n" +
                "        top: 20px;\n" +
                "        right: 20px;\n" +
                "        padding: 15px 20px;\n" +
                "        border-radius: 8px;\n" +
                "        color: white;\n" +
                "        font-weight: 600;\n" +
                "        z-index: 10000;\n" +
                "        animation: slideIn 0.3s ease;\n" +
                "    `;\n" +
                "    \n" +
                "    if (type === 'success') {\n" +
                "        notification.style.backgroundColor = '#10ac84';\n" +
                "    } else if (type === 'error') {\n" +
                "        notification.style.backgroundColor = '#e74c3c';\n" +
                "    }\n" +
                "    \n" +
                "    document.body.appendChild(notification);\n" +
                "    \n" +
                "    setTimeout(() => {\n" +
                "        notification.style.animation = 'slideOut 0.3s ease';\n" +
                "        setTimeout(() => {\n" +
                "            if (notification.parentNode) {\n" +
                "                document.body.removeChild(notification);\n" +
                "            }\n" +
                "        }, 300);\n" +
                "    }, 3000);\n" +
                "}\n" +
                "\n" +
                "// 添加动画样式\n" +
                "const style = document.createElement('style');\n" +
                "style.textContent = `\n" +
                "    @keyframes slideIn {\n" +
                "        from { transform: translateX(100%); opacity: 0; }\n" +
                "        to { transform: translateX(0); opacity: 1; }\n" +
                "    }\n" +
                "    @keyframes slideOut {\n" +
                "        from { transform: translateX(0); opacity: 1; }\n" +
                "        to { transform: translateX(100%); opacity: 0; }\n" +
                "    }\n" +
                "    @keyframes fadeInOut {\n" +
                "        0% { opacity: 0; transform: translateY(-10px); }\n" +
                "        10% { opacity: 1; transform: translateY(0); }\n" +
                "        90% { opacity: 1; transform: translateY(0); }\n" +
                "        100% { opacity: 0; transform: translateY(-10px); }\n" +
                "    }\n" +
                "`;\n" +
                "document.head.appendChild(style);\n" +
                "\n" +
                "// 过滤器功能（按照 next-litebans AvatarName 和 PlayerFilter 组件）\n" +
                "function addFilter(paramName, paramValue) {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.set(paramName, paramValue);\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "}\n" +
                "\n" +
                "function removePlayerFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('player');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = url.pathname;\n" +
                "    } else {\n" +
                "        window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "function removeStaffFilter() {\n" +
                "    const url = new URL(window.location);\n" +
                "    url.searchParams.delete('staff');\n" +
                "    url.searchParams.delete('page');\n" +
                "    \n" +
                "    if (url.searchParams.toString() === '') {\n" +
                "        window.location.href = url.pathname;\n" +
                "    } else {\n" +
                "        window.location.href = url.pathname + '?' + url.searchParams.toString();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 动态时间显示功能（按照 next-litebans RelativeTimeTooltip）\n" +
                "function initRelativeTime() {\n" +
                "    updateRelativeTime();\n" +
                "    // 每秒更新一次，提供实时的倒计时效果\n" +
                "    setInterval(updateRelativeTime, 1000);\n" +
                "}\n" +
                "\n" +
                "// ==================== 异步数据更新功能 ====================\n" +
                "\n" +
                "let updateInterval;\n" +
                "let lastDataHash = null;\n" +
                "let isUpdating = false;\n" +
                "\n" +
                "// 启动自动更新检测\n" +
                "function startAutoUpdate() {\n" +
                "    // 每1秒检测一次数据变化\n" +
                "    updateInterval = setInterval(checkForUpdates, 1000);\n" +
                "    console.log('处罚记录自动更新已启动（1秒间隔）');\n" +
                "}\n" +
                "\n" +
                "// 停止自动更新\n" +
                "function stopAutoUpdate() {\n" +
                "    if (updateInterval) {\n" +
                "        clearInterval(updateInterval);\n" +
                "        updateInterval = null;\n" +
                "        console.log('处罚记录自动更新已停止');\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 检测数据更新\n" +
                "function checkForUpdates() {\n" +
                "    if (isUpdating) {\n" +
                "        console.log('正在更新中，跳过本次检测');\n" +
                "        return;\n" +
                "    }\n" +
                "    \n" +
                "    isUpdating = true;\n" +
                "    \n" +
                "    // 获取当前页面的URL参数\n" +
                "    const url = new URL(window.location);\n" +
                "    \n" +
                "    // 根据页面类型构建检测URL\n" +
                "    let checkUrl;\n" +
                "    if (url.pathname.startsWith('/player/')) {\n" +
                "        // 玩家页面：提取玩家名并添加到查询参数\n" +
                "        const playerName = url.pathname.split('/')[2];\n" +
                "        const searchParams = new URLSearchParams(url.search);\n" +
                "        searchParams.set('search', playerName);\n" +
                "        checkUrl = '/punishments/check-update?' + searchParams.toString();\n" +
                "    } else {\n" +
                "        // 其他页面使用原有逻辑\n" +
                "        checkUrl = url.pathname + '/check-update' + url.search;\n" +
                "    }\n" +
                "    \n" +
                "    fetch(checkUrl, {\n" +
                "        method: 'GET',\n" +
                "        headers: {\n" +
                "            'X-Requested-With': 'XMLHttpRequest',\n" +
                "            'Cache-Control': 'no-cache'\n" +
                "        }\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .then(data => {\n" +
                "        if (data.dataHash && data.dataHash !== lastDataHash) {\n" +
                "            console.log('检测到数据变化，开始更新页面');\n" +
                "            lastDataHash = data.dataHash;\n" +
                "            updatePageContent();\n" +
                "        } else {\n" +
                "            console.log('数据无变化');\n" +
                "        }\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('检测更新时发生错误:', error);\n" +
                "    })\n" +
                "    .finally(() => {\n" +
                "        isUpdating = false;\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新页面内容\n" +
                "function updatePageContent() {\n" +
                "    const url = new URL(window.location);\n" +
                "    \n" +
                "    // 构建更新内容的URL\n" +
                "    let updateUrl;\n" +
                "    if (url.pathname.startsWith('/player/')) {\n" +
                "        // 玩家页面：直接使用当前URL\n" +
                "        updateUrl = url.pathname + url.search;\n" +
                "    } else {\n" +
                "        // 其他页面：使用当前URL\n" +
                "        updateUrl = url.pathname + url.search;\n" +
                "    }\n" +
                "    \n" +
                "    fetch(updateUrl, {\n" +
                "        method: 'GET',\n" +
                "        headers: {\n" +
                "            'X-Requested-With': 'XMLHttpRequest'\n" +
                "        }\n" +
                "    })\n" +
                "    .then(response => response.text())\n" +
                "    .then(html => {\n" +
                "        // 解析返回的HTML\n" +
                "        const parser = new DOMParser();\n" +
                "        const doc = parser.parseFromString(html, 'text/html');\n" +
                "        \n" +
                "        // 更新统计信息\n" +
                "        updateStatistics(doc);\n" +
                "        \n" +
                "        // 更新处罚记录表格\n" +
                "        updatePunishmentTable(doc);\n" +
                "        \n" +
                "        // 更新分页导航\n" +
                "        updatePagination(doc);\n" +
                "        \n" +
                "        // 重新初始化动态时间显示\n" +
                "        initRelativeTime();\n" +
                "        \n" +
                "        console.log('页面内容已更新');\n" +
                "        showUpdateNotification();\n" +
                "    })\n" +
                "    .catch(error => {\n" +
                "        console.error('更新页面内容时发生错误:', error);\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 更新统计信息\n" +
                "function updateStatistics(doc) {\n" +
                "    // 更新统计区域\n" +
                "    const newStats = doc.querySelector('.statistics-section');\n" +
                "    const currentStats = document.querySelector('.statistics-section');\n" +
                "    \n" +
                "    if (newStats && currentStats) {\n" +
                "        currentStats.innerHTML = newStats.innerHTML;\n" +
                "\n" +
                "    }\n" +
                "    \n" +
                "    // 更新导航徽章\n" +
                "    updateNavigationBadges(doc);\n" +
                "}\n" +
                "\n" +
                "// 更新导航徽章\n" +
                "function updateNavigationBadges(doc) {\n" +
                "    // 查找新页面中的所有导航徽章和玩家徽章\n" +
                "    const newBadges = doc.querySelectorAll('[data-stat], .nav-badge, .badge[data-type]');\n" +
                "    \n" +
                "    newBadges.forEach(newBadge => {\n" +
                "        const statKey = newBadge.getAttribute('data-stat');\n" +
                "        const typeKey = newBadge.getAttribute('data-type');\n" +
                "        \n" +
                "        // 提取数字（支持不同格式的徽章）\n" +
                "        let newCount;\n" +
                "        const countElement = newBadge.querySelector('.badge-count');\n" +
                "        if (countElement) {\n" +
                "            newCount = countElement.textContent;\n" +
                "        } else {\n" +
                "            // 从文本中提取数字\n" +
                "            const match = newBadge.textContent.match(/\\d+/);\n" +
                "            newCount = match ? match[0] : newBadge.textContent;\n" +
                "        }\n" +
                "        \n" +
                "        if (statKey || typeKey) {\n" +
                "            // 查找当前页面中对应的徽章\n" +
                "            const selectors = [];\n" +
                "            if (statKey) {\n" +
                "                selectors.push(`[data-stat='${statKey}']`);\n" +
                "            }\n" +
                "            if (typeKey) {\n" +
                "                selectors.push(`[data-type='${typeKey}']`);\n" +
                "                selectors.push(`.nav-badge[data-type='${typeKey}']`);\n" +
                "                selectors.push(`.badge[data-type='${typeKey}']`);\n" +
                "            }\n" +
                "            \n" +
                "            selectors.forEach(selector => {\n" +
                "                const currentBadges = document.querySelectorAll(selector);\n" +
                "                currentBadges.forEach(currentBadge => {\n" +
                "                    // 更新导航徽章\n" +
                "                    if (currentBadge.classList.contains('nav-badge')) {\n" +
                "                        currentBadge.textContent = newCount;\n" +
                "                        console.log('更新导航徽章:', typeKey, '=', newCount);\n" +
                "                    }\n" +
                "                    // 更新玩家页面徽章\n" +
                "                    else if (currentBadge.classList.contains('badge')) {\n" +
                "                        const currentCountElement = currentBadge.querySelector('.badge-count');\n" +
                "                        if (currentCountElement) {\n" +
                "                            currentCountElement.textContent = newCount;\n" +
                "                            console.log('更新玩家徽章:', typeKey, '=', newCount);\n" +
                "                        }\n" +
                "                    }\n" +
                "                });\n" +
                "            });\n" +
                "        }\n" +
                "    });\n" +
                "    \n" +
                "    // 更新所有记录徽章 - 从新页面中提取统计数据\n" +
                "    const newAllBadge = doc.querySelector('[data-type=\"all\"], [data-stat=\"total_all\"]');\n" +
                "    if (newAllBadge) {\n" +
                "        const newAllCount = newAllBadge.querySelector('.badge-count');\n" +
                "        if (newAllCount) {\n" +
                "            const totalAll = newAllCount.textContent;\n" +
                "            const playerName = window.location.pathname.split('/').pop();\n" +
                "            const allBadgeSelectors = [\n" +
                "                '[data-type=\"all\"]',\n" +
                "                '[data-stat=\"total_all\"]',\n" +
                "                `a.badge[href='/admin-player/${playerName}']`,\n" +
                "                `a.badge[href='/player/${playerName}']`,\n" +
                "                'a.badge.badge-primary:not([href*=\"type=\"])'\n" +
                "            ];\n" +
                "            \n" +
                "            allBadgeSelectors.forEach(selector => {\n" +
                "                const allBadges = document.querySelectorAll(selector);\n" +
                "                allBadges.forEach(badge => {\n" +
                "                    if (badge.classList.contains('badge')) {\n" +
                "                        const countElement = badge.querySelector('.badge-count');\n" +
                "                        if (countElement) {\n" +
                "                            countElement.textContent = totalAll;\n" +
                "                            console.log('更新所有记录徽章:', totalAll);\n" +
                "                        }\n" +
                "                    }\n" +
                "                });\n" +
                "            });\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新处罚记录表格\n" +
                "function updatePunishmentTable(doc) {\n" +
                "    const newTable = doc.querySelector('.punishment-table');\n" +
                "    const currentTable = document.querySelector('.punishment-table');\n" +
                "    \n" +
                "    if (newTable && currentTable) {\n" +
                "        // 保存当前滚动位置\n" +
                "        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n" +
                "        \n" +
                "        currentTable.innerHTML = newTable.innerHTML;\n" +
                "        \n" +
                "        // 恢复滚动位置\n" +
                "        window.scrollTo(0, scrollTop);\n" +
                "        \n" +
                "        console.log('处罚记录表格已更新');\n" +
                "    } else {\n" +
                "        // 如果没有表格，可能是无记录状态，更新整个内容区域\n" +
                "        const newContent = doc.querySelector('.content');\n" +
                "        const currentContent = document.querySelector('.content');\n" +
                "        \n" +
                "        if (newContent && currentContent) {\n" +
                "            // 只更新表格部分，保留其他内容\n" +
                "            const newTableContainer = doc.querySelector('.table-container, .no-records');\n" +
                "            const currentTableContainer = document.querySelector('.table-container, .no-records');\n" +
                "            \n" +
                "            if (newTableContainer && currentTableContainer) {\n" +
                "                currentTableContainer.outerHTML = newTableContainer.outerHTML;\n" +
                "                console.log('内容区域已更新');\n" +
                "            }\n" +
                "        }\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 更新分页导航\n" +
                "function updatePagination(doc) {\n" +
                "    const newPagination = doc.querySelector('.pagination');\n" +
                "    const currentPagination = document.querySelector('.pagination');\n" +
                "    \n" +
                "    if (newPagination && currentPagination) {\n" +
                "        currentPagination.innerHTML = newPagination.innerHTML;\n" +
                "        console.log('分页导航已更新');\n" +
                "    } else if (newPagination && !currentPagination) {\n" +
                "        // 添加新的分页导航\n" +
                "        const content = document.querySelector('.content');\n" +
                "        if (content) {\n" +
                "            content.appendChild(newPagination.cloneNode(true));\n" +
                "        }\n" +
                "    } else if (!newPagination && currentPagination) {\n" +
                "        // 移除分页导航\n" +
                "        currentPagination.remove();\n" +
                "    }\n" +
                "}\n" +
                "\n" +
                "// 显示更新通知\n" +
                "function showUpdateNotification() {\n" +
                "    // 创建一个小的更新指示器\n" +
                "    const indicator = document.createElement('div');\n" +
                "    indicator.className = 'update-indicator';\n" +
                "    indicator.innerHTML = '🔄 已更新';\n" +
                "    indicator.style.cssText = `\n" +
                "        position: fixed;\n" +
                "        top: 20px;\n" +
                "        left: 50%;\n" +
                "        transform: translateX(-50%);\n" +
                "        background: #10ac84;\n" +
                "        color: white;\n" +
                "        padding: 8px 16px;\n" +
                "        border-radius: 20px;\n" +
                "        font-size: 0.9em;\n" +
                "        z-index: 10000;\n" +
                "        animation: fadeInOut 2s ease;\n" +
                "    `;\n" +
                "    \n" +
                "    document.body.appendChild(indicator);\n" +
                "    \n" +
                "    setTimeout(() => {\n" +
                "        if (indicator.parentNode) {\n" +
                "            document.body.removeChild(indicator);\n" +
                "        }\n" +
                "    }, 2000);\n" +
                "}\n" +
                "\n" +
                "// 页面可见性变化处理\n" +
                "document.addEventListener('visibilitychange', function() {\n" +
                "    if (document.hidden) {\n" +
                "        // 页面隐藏时停止更新\n" +
                "        stopAutoUpdate();\n" +
                "    } else {\n" +
                "        // 页面显示时重新启动更新\n" +
                "        startAutoUpdate();\n" +
                "        // 立即检测一次更新\n" +
                "        setTimeout(checkForUpdates, 100);\n" +
                "    }\n" +
                "});\n" +
                "\n" +
                "// 页面卸载时清理\n" +
                "window.addEventListener('beforeunload', function() {\n" +
                "    stopAutoUpdate();\n" +
                "});\n" +
                "\n" +
                // 只有在数据库启用时才启动自动更新
                (plugin.getConfig().getBoolean("litebans.enabled", false) ? "// 启动自动更新（在页面加载完成后）\n" +
                        "document.addEventListener('DOMContentLoaded', function() {\n" +
                        "    // 快速启动，实现实时更新\n" +
                        "    setTimeout(startAutoUpdate, 500);\n" +
                        "});\n" +
                        "\n" : "")
                +
                "\n" +
                "function updateRelativeTime() {\n" +
                "    const elements = document.querySelectorAll('.relative-time');\n" +
                "    const now = new Date().getTime();\n" +
                "    \n" +
                "    elements.forEach(element => {\n" +
                "        const timestamp = parseInt(element.getAttribute('data-timestamp'));\n" +
                "        if (timestamp && timestamp > 0) {\n" +
                "            // 检查处罚状态 - 如果处罚已被撤销或过期，停止倒计时\n" +
                "            const statusDot = element.parentElement.querySelector('.status-dot');\n" +
                "            const isInactive = statusDot && (statusDot.classList.contains('status-expired') || \n" +
                "                                           statusDot.classList.contains('status-removed'));\n" +
                "            \n" +
                "            if (isInactive) {\n" +
                "                // 处罚已被撤销或过期，显示静态文本\n" +
                "                element.textContent = '已撤销';\n" +
                "                element.title = '此处罚已被撤销';\n" +
                "                return;\n" +
                "            }\n" +
                "            \n" +
                "            const isTableView = element.closest('.until-cell') !== null;\n" +
                "            const relativeText = getRelativeTimeText(timestamp, now, isTableView);\n" +
                "            if (relativeText) {\n" +
                "                if (isTableView && relativeText.includes('\\n')) {\n" +
                "                    // 表格视图：分两行显示\n" +
                "                    element.innerHTML = relativeText.replace('\\n', '<br>');\n" +
                "                } else {\n" +
                "                    // 详细视图：一行显示\n" +
                "                    element.textContent = relativeText.replace('\\n', ' ');\n" +
                "                }\n" +
                "                element.title = new Date(timestamp).toLocaleString('zh-CN');\n" +
                "            }\n" +
                "        }\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "function getRelativeTimeText(timestamp, now, isTableView = false) {\n" +
                "    const diff = timestamp - now;\n" +
                "    \n" +
                "    // 如果是过去的时间\n" +
                "    if (diff < 0) {\n" +
                "        const pastDiff = Math.abs(diff);\n" +
                "        const pastSeconds = Math.floor(pastDiff / 1000);\n" +
                "        const pastMinutes = Math.floor(pastSeconds / 60);\n" +
                "        const pastHours = Math.floor(pastMinutes / 60);\n" +
                "        const pastDays = Math.floor(pastHours / 24);\n" +
                "        \n" +
                "        if (pastDays > 0) {\n" +
                "            return `已过期 ${pastDays}天`;\n" +
                "        } else if (pastHours > 0) {\n" +
                "            return `已过期 ${pastHours}小时`;\n" +
                "        } else if (pastMinutes > 0) {\n" +
                "            return `已过期 ${pastMinutes}分钟`;\n" +
                "        } else {\n" +
                "            return '刚刚过期';\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    // 计算剩余时间\n" +
                "    const totalSeconds = Math.floor(diff / 1000);\n" +
                "    const totalMinutes = Math.floor(totalSeconds / 60);\n" +
                "    const totalHours = Math.floor(totalMinutes / 60);\n" +
                "    const totalDays = Math.floor(totalHours / 24);\n" +
                "    const totalMonths = Math.floor(totalDays / 30);\n" +
                "    const totalYears = Math.floor(totalDays / 365);\n" +
                "    \n" +
                "    // 计算各个单位的剩余值\n" +
                "    const years = totalYears;\n" +
                "    const months = Math.floor((totalDays % 365) / 30);\n" +
                "    const days = totalDays % 30;\n" +
                "    const hours = totalHours % 24;\n" +
                "    const minutes = totalMinutes % 60;\n" +
                "    const seconds = totalSeconds % 60;\n" +
                "    \n" +
                "    if (totalSeconds <= 0) {\n" +
                "        return '即将到期';\n" +
                "    }\n" +
                "    \n" +
                "    // 构建时间字符串\n" +
                "    let timeStr = '';\n" +
                "    let parts = [];\n" +
                "    \n" +
                "    // 添加年月日\n" +
                "    if (years > 0) {\n" +
                "        parts.push(`${years}年`);\n" +
                "    }\n" +
                "    if (months > 0) {\n" +
                "        parts.push(`${months}个月`);\n" +
                "    }\n" +
                "    if (days > 0) {\n" +
                "        parts.push(`${days}天`);\n" +
                "    }\n" +
                "    \n" +
                "    // 如果是表格视图且时间较短，分两行显示\n" +
                "    if (isTableView && years === 0 && months === 0) {\n" +
                "        let line1 = '';\n" +
                "        let line2 = '';\n" +
                "        \n" +
                "        if (days > 0) {\n" +
                "            line1 = `${days}天`;\n" +
                "            if (hours > 0) {\n" +
                "                line1 += `${hours}小时`;\n" +
                "            }\n" +
                "        } else if (hours > 0) {\n" +
                "            line1 = `${hours}小时`;\n" +
                "        }\n" +
                "        \n" +
                "        if (minutes > 0 || seconds > 0) {\n" +
                "            let timeParts = [];\n" +
                "            if (minutes > 0) timeParts.push(`${minutes}分`);\n" +
                "            if (seconds > 0) timeParts.push(`${seconds}秒`);\n" +
                "            line2 = timeParts.join('');\n" +
                "        }\n" +
                "        \n" +
                "        if (line1 && line2) {\n" +
                "            return timeStr + line1 + '\\n' + line2;\n" +
                "        } else if (line1) {\n" +
                "            return timeStr + line1;\n" +
                "        } else if (line2) {\n" +
                "            return timeStr + line2;\n" +
                "        }\n" +
                "    } else {\n" +
                "        // 详细视图或长时间：一行显示\n" +
                "        if (hours > 0 && years === 0 && months === 0) {\n" +
                "            parts.push(`${hours}小时`);\n" +
                "        }\n" +
                "        if (minutes > 0 && years === 0 && months === 0) {\n" +
                "            parts.push(`${minutes}分钟`);\n" +
                "        }\n" +
                "        if (seconds > 0 && years === 0 && months === 0 && days === 0) {\n" +
                "            parts.push(`${seconds}秒`);\n" +
                "        }\n" +
                "        \n" +
                "        // 最多显示3个单位\n" +
                "        if (parts.length > 3) {\n" +
                "            parts = parts.slice(0, 3);\n" +
                "        }\n" +
                "        \n" +
                "        return timeStr + parts.join('');\n" +
                "    }\n" +
                "    \n" +
                "    return '即将到期';\n" +
                "}\n";
    }

    /**
     * 生成头像URL
     */
    private String generateAvatarUrl(String uuid, String provider) {
        if (uuid == null || uuid.isEmpty()) {
            return "";
        }

        switch (provider.toLowerCase()) {
            case "crafatar":
                return "https://crafatar.com/avatars/" + uuid + "?size=32&overlay";
            case "mc-heads":
                return "https://mc-heads.net/avatar/" + uuid + "/32";
            case "minotar":
            default:
                return "https://minotar.net/helm/" + uuid + "/32";
        }
    }

    /**
     * 生成过滤器标签区域（按照 next-litebans Filters 组件）
     */
    private String generateFiltersSection(String playerFilter, String staffFilter) {
        StringBuilder html = new StringBuilder();

        // 只有当有过滤参数时才显示过滤器区域
        if ((playerFilter != null && !playerFilter.isEmpty()) || (staffFilter != null && !staffFilter.isEmpty())) {
            html.append("        <div class=\"filters-section\">\n");
            html.append("            <div class=\"filters-container\">\n");

            // 玩家过滤器
            if (playerFilter != null && !playerFilter.isEmpty()) {
                html.append("                <div class=\"filter-tag player-filter\">\n");
                html.append("                    <span class=\"filter-label\">玩家</span>\n");
                html.append("                    <div class=\"filter-separator\"></div>\n");
                html.append("                    <div class=\"filter-badge\">\n");

                // 检查是否是控制台
                boolean isConsolePlayer = "Console".equalsIgnoreCase(playerFilter) || "控制台".equals(playerFilter);
                if (isConsolePlayer) {
                    html.append(
                            "                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"filter-avatar\">\n");
                    html.append("                        <span class=\"filter-name\">控制台</span>\n");
                } else {
                    // 玩家头像
                    String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
                    String avatarUrl = generateAvatarUrl(playerFilter, avatarProvider);
                    html.append("                        <img src=\"").append(avatarUrl)
                            .append("\" alt=\"头像\" class=\"filter-avatar\" onerror=\"this.style.display='none'\">\n");
                    html.append("                        <span class=\"filter-name\">").append(escapeHtml(playerFilter))
                            .append("</span>\n");
                }

                html.append("                    </div>\n");
                html.append(
                        "                    <button class=\"filter-remove\" onclick=\"removePlayerFilter()\" title=\"移除玩家过滤器\">\n");
                html.append("                        <span class=\"remove-icon\">✕</span>\n");
                html.append("                    </button>\n");
                html.append("                </div>\n");
            }

            // 执行者过滤器
            if (staffFilter != null && !staffFilter.isEmpty()) {
                html.append("                <div class=\"filter-tag staff-filter\">\n");
                html.append("                    <span class=\"filter-label\">执行者</span>\n");
                html.append("                    <div class=\"filter-separator\"></div>\n");
                html.append("                    <div class=\"filter-badge\">\n");

                // 检查是否是控制台
                boolean isConsoleStaff = "Console".equalsIgnoreCase(staffFilter) || "控制台".equals(staffFilter);
                if (isConsoleStaff) {
                    html.append(
                            "                        <img src=\"/static/console.webp\" alt=\"控制台\" class=\"filter-avatar\">\n");
                    html.append("                        <span class=\"filter-name\">控制台</span>\n");
                } else {
                    // 执行者头像和名称
                    String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
                    String avatarUrl = generateAvatarUrl(staffFilter, avatarProvider);
                    html.append("                        <img src=\"").append(avatarUrl)
                            .append("\" alt=\"头像\" class=\"filter-avatar\" onerror=\"this.style.display='none'\">\n");

                    // 如果是UUID格式，尝试获取玩家名称
                    String displayName = staffFilter;
                    if (isUuidFormat(staffFilter)) {
                        String playerName = getPlayerNameByUuid(staffFilter);
                        if (playerName != null && !playerName.isEmpty()) {
                            displayName = playerName;
                        }
                    }
                    html.append("                        <span class=\"filter-name\">").append(escapeHtml(displayName))
                            .append("</span>\n");
                }

                html.append("                    </div>\n");
                html.append(
                        "                    <button class=\"filter-remove\" onclick=\"removeStaffFilter()\" title=\"移除执行者过滤器\">\n");
                html.append("                        <span class=\"remove-icon\">✕</span>\n");
                html.append("                    </button>\n");
                html.append("                </div>\n");
            }

            html.append("            </div>\n");
            html.append("        </div>\n");
        }

        return html.toString();
    }

    /**
     * 生成处罚详细信息页面（按照 next-litebans 详细页面布局）
     */
    public String generatePunishmentDetailPage(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        String pageTitle = record.getType() != null ? record.getType().getDisplayName() + " #" + record.getId()
                : "处罚详情 #" + record.getId();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(pageTitle).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generatePunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        // 顶部导航栏
        html.append(generateSiteHeader(record.getType(), null, null));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 详细信息页面内容（按照 next-litebans 布局）
        html.append("        <div class=\"detail-page-container\">\n");

        // 页面标题
        html.append("            <div class=\"detail-header\">\n");
        html.append("                <h1 class=\"detail-title\">").append(pageTitle).append("</h1>\n");

        // 状态徽章
        html.append("                <div class=\"detail-badges\">\n");
        if (record.getType() == PunishmentRecord.PunishmentType.BAN && record.isIpban()) {
            html.append("                    <span class=\"status-badge status-ipban\">IP封禁</span>\n");
        }
        if (record.isActive()) {
            html.append("                    <span class=\"status-badge status-active\">生效中</span>\n");
        } else {
            html.append("                    <span class=\"status-badge status-expired\">已过期</span>\n");
        }
        html.append("                </div>\n");
        html.append("            </div>\n");

        // 详细信息卡片（按照 next-litebans PunishmentInfoCard）
        html.append(generatePunishmentInfoCard(record));

        // 移动端详细信息显示（按照 next-litebans 响应式设计）
        html.append("            <div class=\"mobile-details\">\n");

        // 处罚原因
        html.append("                <div class=\"mobile-detail-item\">\n");
        html.append("                    <h3 class=\"mobile-detail-label\">📜 处罚原因</h3>\n");
        html.append("                    <p class=\"mobile-detail-value\">")
                .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因")).append("</p>\n");
        html.append("                </div>\n");

        // 处罚时间
        html.append("                <div class=\"mobile-detail-item\">\n");
        html.append("                    <h3 class=\"mobile-detail-label\">📅 处罚时间</h3>\n");
        html.append("                    <p class=\"mobile-detail-value\">")
                .append(escapeHtml(record.getFormattedTime())).append("</p>\n");
        html.append("                </div>\n");

        // 到期时间（如果有）
        if (record.getUntil() != null) {
            html.append("                <div class=\"mobile-detail-item\">\n");
            html.append("                    <h3 class=\"mobile-detail-label\">⏰ 到期时间</h3>\n");
            html.append("                    <p class=\"mobile-detail-value\">");

            if (record.isActive()) {
                html.append("<span class=\"status-dot status-active\" title=\"生效中\"></span>");
            } else {
                html.append("<span class=\"status-dot status-expired\" title=\"已过期\"></span>");
            }

            // 检查是否为永久处罚
            if (record.getUntil().getYear() > 2100) {
                // 永久处罚，直接显示"永久"
                html.append("永久</p>\n");
            } else {
                // 非永久处罚，添加动态时间显示
                long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                html.append("<span class=\"relative-time\" data-timestamp=\"").append(timestamp).append("\">");
                html.append(escapeHtml(record.getFormattedUntil())).append("</span></p>\n");
            }
            html.append("                </div>\n");
        }

        // 服务器信息
        html.append("                <div class=\"mobile-detail-item\">\n");
        html.append("                    <h3 class=\"mobile-detail-label\">🌍 服务器</h3>\n");
        html.append("                    <p class=\"mobile-detail-value\">");
        if (record.getServerOrigin() != null && !record.getServerOrigin().isEmpty()) {
            html.append(escapeHtml(record.getServerOrigin()));
        } else {
            html.append("未知");
        }
        html.append("</p>\n");
        html.append("                </div>\n");

        // 根据处罚类型添加特定信息
        if (record.getType() == PunishmentRecord.PunishmentType.WARN) {
            html.append("                <div class=\"mobile-detail-item\">\n");
            html.append("                    <h3 class=\"mobile-detail-label\">🔔 已通知</h3>\n");
            html.append("                    <p class=\"mobile-detail-value\">").append(record.isSilent() ? "否" : "是")
                    .append("</p>\n");
            html.append("                </div>\n");
        }

        html.append("            </div>\n");

        html.append("        </div>\n");
        html.append("    </div>\n");

        // JavaScript
        html.append("    <script>\n");
        html.append(generatePunishmentPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成处罚信息卡片（按照 next-litebans PunishmentInfoCard）
     */
    private String generatePunishmentInfoCard(PunishmentRecord record) {
        StringBuilder html = new StringBuilder();

        html.append("            <section class=\"punishment-info-section\">\n");
        html.append("                <div class=\"punishment-info-card\">\n");

        // 玩家信息区域
        html.append("                    <div class=\"player-info-area\">\n");
        html.append("                        <h2 class=\"info-area-title\">玩家</h2>\n");

        String playerName = record.getPlayerName() != null ? record.getPlayerName() : "未知玩家";
        boolean isConsolePlayer = "Console".equalsIgnoreCase(playerName) || "控制台".equals(playerName);

        if (isConsolePlayer) {
            html.append(
                    "                        <img src=\"/static/console-body.webp\" alt=\"控制台\" class=\"player-body-image\" onerror=\"this.src='/static/console.webp'\">\n");
        } else {
            String bustUrl = "https://vzge.me/body/512/" + playerName + ".png";
            html.append("                        <img src=\"").append(bustUrl).append("\" alt=\"")
                    .append(escapeHtml(playerName)).append("\" class=\"player-body-image\" ")
                    .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(playerName)
                    .append("/512'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://visage.surgeplay.com/body/512/")
                    .append(playerName).append("';}\">\n");
        }

        html.append("                        <div class=\"player-name-area\">\n");
        String avatarProvider = plugin.getConfig().getString("litebans.display.avatar-provider", "minotar");
        String avatarUrl = generateAvatarUrl(playerName, avatarProvider);
        html.append("                            <img src=\"").append(avatarUrl)
                .append("\" alt=\"头像\" class=\"player-avatar-large\">\n");
        html.append("                            <p class=\"player-name-large\">").append(escapeHtml(playerName))
                .append("</p>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");

        // 详细信息区域
        html.append("                    <div class=\"detail-info-area\">\n");

        // 处罚原因
        html.append("                        <div class=\"detail-item\">\n");
        html.append("                            <h3 class=\"detail-label\">📜 处罚原因</h3>\n");
        html.append("                            <p class=\"detail-value\">")
                .append(escapeHtml(record.getReason() != null ? record.getReason() : "无原因")).append("</p>\n");
        html.append("                        </div>\n");

        // 处罚时间
        html.append("                        <div class=\"detail-item\">\n");
        html.append("                            <h3 class=\"detail-label\">📅 处罚时间</h3>\n");
        html.append("                            <p class=\"detail-value\">")
                .append(escapeHtml(record.getFormattedTime())).append("</p>\n");
        html.append("                        </div>\n");

        // 到期时间（如果有）
        if (record.getUntil() != null) {
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">⏰ 到期时间</h3>\n");
            html.append("                            <p class=\"detail-value\">");

            // 添加状态点（按照 next-litebans PunishmentStatusDot）
            if (record.isActive()) {
                html.append("<span class=\"status-dot status-active\" title=\"生效中\"></span>");
            } else {
                html.append("<span class=\"status-dot status-expired\" title=\"已过期\"></span>");
            }

            // 检查是否为永久处罚
            if (record.getUntil().getYear() > 2100) {
                // 永久处罚，直接显示"永久"
                html.append("永久</p>\n");
            } else {
                // 非永久处罚，添加动态时间显示
                long timestamp = record.getUntil().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                html.append("<span class=\"relative-time\" data-timestamp=\"").append(timestamp).append("\">");
                html.append(escapeHtml(record.getFormattedUntil())).append("</span></p>\n");
            }
            html.append("                        </div>\n");
        }

        // 服务器信息（按照 next-litebans originServer）
        if (record.getServerOrigin() != null && !record.getServerOrigin().isEmpty()) {
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">🌍 服务器</h3>\n");
            html.append("                            <p class=\"detail-value\">")
                    .append(escapeHtml(record.getServerOrigin())).append("</p>\n");
            html.append("                        </div>\n");
        } else {
            // 如果没有服务器信息，显示默认值
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">🌍 服务器</h3>\n");
            html.append("                            <p class=\"detail-value\">未知</p>\n");
            html.append("                        </div>\n");
        }

        // 根据处罚类型添加特定信息
        if (record.getType() == PunishmentRecord.PunishmentType.WARN) {
            // 警告特有信息：是否已通知
            html.append("                        <div class=\"detail-item\">\n");
            html.append("                            <h3 class=\"detail-label\">🔔 已通知</h3>\n");
            html.append("                            <p class=\"detail-value\">").append(record.isSilent() ? "否" : "是")
                    .append("</p>\n");
            html.append("                        </div>\n");
        }

        html.append("                    </div>\n");

        // 执行者信息区域
        html.append("                    <div class=\"staff-info-area\">\n");
        html.append("                        <h2 class=\"info-area-title\">执行者</h2>\n");

        String staffName = record.getBannedByName();
        boolean isConsoleStaff = staffName == null || "Console".equalsIgnoreCase(staffName);

        if (isConsoleStaff) {
            html.append(
                    "                        <img src=\"/static/console-body.webp\" alt=\"控制台\" class=\"staff-body-image scale-x-[-1]\" onerror=\"this.src='/static/console.webp'\">\n");
            staffName = "控制台";
        } else {
            String staffBustUrl = "https://vzge.me/body/512/" + staffName + ".png";
            html.append("                        <img src=\"").append(staffBustUrl).append("\" alt=\"")
                    .append(escapeHtml(staffName)).append("\" class=\"staff-body-image scale-x-[-1]\" ")
                    .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(staffName)
                    .append("/512'; ")
                    .append("this.onerror=function(){this.onerror=null; this.src='https://visage.surgeplay.com/body/512/")
                    .append(staffName).append("';}\">\n");
        }

        html.append("                        <div class=\"staff-name-area\">\n");
        if (isConsoleStaff) {
            html.append(
                    "                            <img src=\"/static/console.webp\" alt=\"控制台\" class=\"staff-avatar-large\">\n");
        } else {
            String staffAvatarUrl = generateAvatarUrl(staffName, avatarProvider);
            html.append("                            <img src=\"").append(staffAvatarUrl)
                    .append("\" alt=\"头像\" class=\"staff-avatar-large\">\n");
        }
        html.append("                            <p class=\"staff-name-large\">").append(escapeHtml(staffName))
                .append("</p>\n");
        html.append("                        </div>\n");
        html.append("                    </div>\n");

        html.append("                </div>\n");
        html.append("            </section>\n");

        return html.toString();
    }

    /**
     * 生成玩家专用页面（按照 next-litebans /@{player} 页面布局）
     */
    public String generatePlayerPage(String playerName, int page, String staffFilter) {
        return generatePlayerPage(playerName, page, staffFilter, null);
    }

    /**
     * 生成玩家专用页面（支持类型过滤）
     */
    public String generatePlayerPage(String playerName, int page, String staffFilter,
            PunishmentRecord.PunishmentType typeFilter) {
        StringBuilder html = new StringBuilder();

        html.append("<!DOCTYPE html>\n");
        html.append("<html lang=\"zh-CN\">\n");
        html.append("<head>\n");
        html.append("    <meta charset=\"UTF-8\">\n");
        html.append("    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n");
        html.append("    <title>").append(escapeHtml(playerName)).append(" - ")
                .append(plugin.getConfig().getString("web-server.title", "服务器管理")).append("</title>\n");
        html.append("    <style>\n");
        html.append(generatePunishmentPageCSS());
        html.append("    </style>\n");
        html.append("</head>\n");
        html.append("<body>\n");

        // 顶部导航栏
        html.append(generateSiteHeader(null, null, null));

        // 页面容器
        html.append("    <div class=\"container\">\n");

        // 玩家页面内容（按照 next-litebans 布局）
        html.append("        <div class=\"player-page-container\">\n");

        // 玩家信息头部（按照 next-litebans 玩家页面头部）
        html.append(generatePlayerPageHeader(playerName, typeFilter));

        // 处罚记录表格
        html.append("            <section class=\"player-punishments-section\">\n");

        // 获取玩家的处罚记录
        List<PunishmentRecord> records = new java.util.ArrayList<>();
        int totalRecords = 0;

        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            if (typeFilter != null) {
                // 如果有类型过滤，使用精确的玩家过滤方法
                records = plugin.getPunishmentManager().searchPunishmentRecords(typeFilter, playerName, null, page, 20);
                totalRecords = getPlayerPunishmentCount(playerName, typeFilter);
                plugin.getLogger().info(
                        "获取玩家 " + playerName + " 的 " + typeFilter.getDisplayName() + " 记录，共 " + records.size() + " 条");
            } else {
                // 没有类型过滤，获取所有记录
                records = plugin.getPunishmentManager().getPunishmentsByPlayer(playerName, page, 20);
                totalRecords = plugin.getPunishmentManager().getPlayerPunishmentCount(playerName);
                plugin.getLogger().info("获取玩家 " + playerName + " 的所有记录，共 " + records.size() + " 条");
            }
        }

        // 如果有执行者过滤，进一步过滤记录
        if (staffFilter != null && !staffFilter.isEmpty()) {
            records = records.stream()
                    .filter(record -> {
                        String staffName = record.getBannedByName();
                        String staffUuid = record.getBannedByUuid();
                        return (staffName != null && staffName.equalsIgnoreCase(staffFilter)) ||
                                (staffUuid != null && staffUuid.equalsIgnoreCase(staffFilter)) ||
                                ("Console".equalsIgnoreCase(staffFilter)
                                        && (staffName == null || "Console".equalsIgnoreCase(staffName)));
                    })
                    .collect(java.util.stream.Collectors.toList());
        }

        // 过滤器区域
        html.append(generateFiltersSection(playerName, staffFilter));

        // 记录表格
        if (records.isEmpty()) {
            html.append("                <div class=\"no-records\">\n");
            html.append("                    <h3>暂无处罚记录</h3>\n");
            html.append("                    <p>该玩家目前没有任何处罚记录。</p>\n");
            html.append("                </div>\n");
        } else {
            html.append(generatePunishmentTable(records));
        }

        html.append("            </section>\n");
        html.append("        </div>\n");
        html.append("    </div>\n");

        // JavaScript
        html.append("    <script>\n");
        html.append(generatePunishmentPageJS());
        html.append("    </script>\n");

        html.append("</body>\n");
        html.append("</html>");

        return html.toString();
    }

    /**
     * 生成玩家页面信息头部（按照 next-litebans 玩家页面头部）
     */
    private String generatePlayerPageHeader(String playerName) {
        return generatePlayerPageHeader(playerName, null);
    }

    /**
     * 生成玩家页面信息头部（支持类型过滤高亮）
     */
    private String generatePlayerPageHeader(String playerName, PunishmentRecord.PunishmentType activeType) {
        StringBuilder html = new StringBuilder();

        // 使用 next-litebans 的确切布局结构
        html.append(
                "            <div class=\"flex h-full flex-col items-center gap-4 py-8 md:py-12 md:pb-8 lg:py-18\">\n");
        html.append("                <div class=\"space-y-2 md:flex md:space-x-4\">\n");

        // 半身皮肤图片（完全按照 next-litebans）
        String bustUrl = "https://vzge.me/bust/512/" + playerName + ".png";
        html.append("                    <img src=\"").append(bustUrl).append("\" alt=\"")
                .append(escapeHtml(playerName)).append("\" width=\"192\" height=\"192\" class=\"mx-auto\" ")
                .append("onerror=\"this.onerror=null; this.src='https://mc-heads.net/body/").append(playerName)
                .append("/192'; ")
                .append("this.onerror=function(){this.onerror=null; this.src='https://visage.surgeplay.com/bust/256/")
                .append(playerName).append("';}\">\n");

        // 玩家详情区域
        html.append("                    <div class=\"md:w-[350px] md:py-4 space-y-1\">\n");
        html.append(
                "                        <h1 class=\"text-center md:text-left text-4xl font-bold leading-tight tracking-tighter sm:text-5xl lg:leading-[1.1]\">")
                .append(escapeHtml(playerName)).append("</h1>\n");
        html.append("                        <div class=\"flex space-x-2 whitespace-nowrap\">\n");

        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            // 获取各类型处罚数量
            int banCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.BAN);
            int muteCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.MUTE);
            int warnCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.WARN);
            int kickCount = getPlayerPunishmentCount(playerName, PunishmentRecord.PunishmentType.KICK);
            int totalCount = banCount + muteCount + warnCount + kickCount;

            // 添加"所有记录"徽章
            if (totalCount > 0) {
                String badgeClass = (activeType == null) ? "badge badge-primary" : "badge badge-secondary";
                html.append("                            <a href=\"/player/").append(escapeUrl(playerName))
                        .append("\" class=\"").append(badgeClass)
                        .append("\" data-type=\"all\" data-stat=\"total_all\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">📋</span> <span class=\"badge-count\">")
                        .append(totalCount)
                        .append("</span> 所有记录\n");
                html.append("                            </a>\n");
            }

            // 徽章（完全按照 next-litebans 样式）
            if (banCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.BAN) ? " active" : "";
                html.append("                            <a href=\"/player/").append(escapeUrl(playerName))
                        .append("?type=ban\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"ban\" data-stat=\"total_ban\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">🚫</span> <span class=\"badge-count\">")
                        .append(banCount)
                        .append("</span> 封禁\n");
                html.append("                            </a>\n");
            }
            if (muteCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.MUTE) ? " active" : "";
                html.append("                            <a href=\"/player/").append(escapeUrl(playerName))
                        .append("?type=mute\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"mute\" data-stat=\"total_mute\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">🔇</span> <span class=\"badge-count\">")
                        .append(muteCount)
                        .append("</span> 禁言\n");
                html.append("                            </a>\n");
            }
            if (warnCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.WARN) ? " active" : "";
                html.append("                            <a href=\"/player/").append(escapeUrl(playerName))
                        .append("?type=warn\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"warn\" data-stat=\"total_warn\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">⚠️</span> <span class=\"badge-count\">")
                        .append(warnCount)
                        .append("</span> 警告\n");
                html.append("                            </a>\n");
            }
            if (kickCount > 0) {
                String activeClass = (activeType == PunishmentRecord.PunishmentType.KICK) ? " active" : "";
                html.append("                            <a href=\"/player/").append(escapeUrl(playerName))
                        .append("?type=kick\" class=\"badge badge-secondary").append(activeClass)
                        .append("\" data-type=\"kick\" data-stat=\"total_kick\">\n");
                html.append(
                        "                                <span class=\"badge-icon\">👢</span> <span class=\"badge-count\">")
                        .append(kickCount)
                        .append("</span> 踢出\n");
                html.append("                            </a>\n");
            }
        }

        html.append("                        </div>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");

        return html.toString();
    }

    /**
     * 获取玩家特定类型的处罚数量
     */
    private int getPlayerPunishmentCount(String playerName, PunishmentRecord.PunishmentType type) {
        if (plugin.getPunishmentManager() == null || !plugin.getPunishmentManager().isEnabled()) {
            return 0;
        }

        try {
            // 使用精确的玩家过滤方法，而不是搜索方法
            List<PunishmentRecord> records = plugin.getPunishmentManager().searchPunishmentRecords(type, playerName,
                    null, 1, 1000);
            return records.size();
        } catch (Exception e) {
            plugin.getLogger().warning("获取玩家处罚数量时发生错误: " + e.getMessage());
            return 0;
        }
    }

    /**
     * 检查字符串是否为UUID格式
     */
    private boolean isUuidFormat(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        return str.matches("[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}");
    }

    /**
     * 根据UUID获取玩家名称
     */
    private String getPlayerNameByUuid(String uuid) {
        if (uuid == null || uuid.trim().isEmpty()) {
            return null;
        }

        // 通过处罚管理器获取玩家名称
        if (plugin.getPunishmentManager() != null && plugin.getPunishmentManager().isEnabled()) {
            try {
                String playerName = plugin.getPunishmentManager().getPlayerNameByUuid(uuid);
                if (playerName != null && !playerName.isEmpty()) {
                    return playerName;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("获取玩家名称时发生错误: " + e.getMessage());
            }
        }

        // 如果无法获取玩家名称，返回UUID的前8位
        return uuid.length() >= 8 ? uuid.substring(0, 8) + "..." : uuid;
    }
}
