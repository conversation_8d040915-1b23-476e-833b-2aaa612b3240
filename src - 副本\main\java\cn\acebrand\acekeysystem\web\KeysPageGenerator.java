package cn.acebrand.acekeysystem.web;

/**
 * 卡密管理页面生成器
 * 负责生成卡密管理页面的HTML内容
 */
public class KeysPageGenerator {
    private final String adminKey;

    public KeysPageGenerator(String adminKey) {
        this.adminKey = adminKey;
    }

    /**
     * 生成卡密管理页面
     */
    public String generateKeysPage(int totalKeys) {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"page-header\">\n");
        html.append("                <h1>🔑 卡密管理</h1>\n");
        html.append("                <p>生成、查看和管理系统卡密</p>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        // 操作工具栏
        html.append(generateToolbar(totalKeys));
        
        // 状态导航
        html.append(generateStatusNavigation());
        
        // 卡密列表
        html.append(generateKeysList());

        return html.toString();
    }

    /**
     * 生成操作工具栏
     */
    private String generateToolbar(int totalKeys) {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 操作工具栏 -->\n");
        html.append("            <div class=\"toolbar\">\n");
        html.append("                <div class=\"toolbar-left\">\n");
        html.append("                    <div class=\"form-group inline\">\n");
        html.append("                        <label for=\"keyCount\">生成数量:</label>\n");
        html.append("                        <input type=\"number\" id=\"keyCount\" value=\"10\" min=\"1\" max=\"1000\" class=\"form-input small\">\n");
        html.append("                        <button onclick=\"generateKeys()\" class=\"btn btn-primary\">🔧 生成卡密</button>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"toolbar-right\">\n");
        html.append("                    <span class=\"key-count\">总计: <strong id=\"totalKeysCount\">");
        html.append(totalKeys).append("</strong> 个卡密</span>\n");
        html.append("                    <div class=\"auto-update-status\">\n");
        html.append("                        <span id=\"autoUpdateStatus\" class=\"status-indicator active\">🔄 自动检测中</span>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"sort-controls\">\n");
        html.append("                        <label for=\"sortOrder\">排序:</label>\n");
        html.append("                        <select id=\"sortOrder\" class=\"form-input small\" onchange=\"loadKeys()\">\n");
        html.append("                            <option value=\"desc\">最新在前</option>\n");
        html.append("                            <option value=\"asc\">最旧在前</option>\n");
        html.append("                        </select>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"page-size-controls\">\n");
        html.append("                        <label for=\"pageSize\">每页:</label>\n");
        html.append("                        <select id=\"pageSize\" class=\"form-input small\" onchange=\"loadKeys()\">\n");
        html.append("                            <option value=\"10\">10条</option>\n");
        html.append("                            <option value=\"20\" selected>20条</option>\n");
        html.append("                            <option value=\"50\">50条</option>\n");
        html.append("                            <option value=\"100\">100条</option>\n");
        html.append("                        </select>\n");
        html.append("                    </div>\n");
        html.append("                    <button onclick=\"selectAllKeys()\" class=\"btn btn-secondary\" id=\"selectAllBtn\">全选</button>\n");
        html.append("                    <button onclick=\"deleteSelectedKeys()\" class=\"btn btn-danger\" id=\"deleteSelectedBtn\" disabled>🗑️ 删除选中</button>\n");
        html.append("                    <button onclick=\"clearAllKeys()\" class=\"btn btn-danger\">🧹 清空所有</button>\n");
        html.append("                    <button onclick=\"refreshKeys()\" class=\"btn btn-info\">🔄 刷新</button>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        return html.toString();
    }

    /**
     * 生成状态导航
     */
    private String generateStatusNavigation() {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 状态导航 -->\n");
        html.append("            <div class=\"content-card\">\n");
        html.append("                <div class=\"status-nav\">\n");
        html.append("                    <button class=\"status-btn active\" onclick=\"filterByStatus('all')\" id=\"statusAll\">\n");
        html.append("                        📋 全部 (<span id=\"countAll\">0</span>)\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"status-btn\" onclick=\"filterByStatus('available')\" id=\"statusAvailable\">\n");
        html.append("                        ✅ 未分配 (<span id=\"countAvailable\">0</span>)\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"status-btn\" onclick=\"filterByStatus('assigned')\" id=\"statusAssigned\">\n");
        html.append("                        🎫 已分配 (<span id=\"countAssigned\">0</span>)\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"status-btn\" onclick=\"filterByStatus('used')\" id=\"statusUsed\">\n");
        html.append("                        ❌ 已使用 (<span id=\"countUsed\">0</span>)\n");
        html.append("                    </button>\n");
        html.append("                    <button class=\"status-btn\" onclick=\"filterByStatus('expired')\" id=\"statusExpired\">\n");
        html.append("                        ⏰ 已失效 (<span id=\"countExpired\">0</span>)\n");
        html.append("                    </button>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        return html.toString();
    }

    /**
     * 生成卡密列表
     */
    private String generateKeysList() {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 卡密列表 -->\n");
        html.append("            <div class=\"content-card\">\n");
        html.append("                <div class=\"card-header\">\n");
        html.append("                    <h3>卡密列表</h3>\n");
        html.append("                    <div class=\"search-box\">\n");
        html.append("                        <input type=\"text\" id=\"searchInput\" placeholder=\"搜索卡密或玩家名...\" class=\"form-input\" onkeyup=\"filterKeys()\">\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("                <div class=\"shop-cards-container\" id=\"keysContainer\">\n");
        html.append("                    <div class=\"loading-spinner\">正在加载卡密列表...</div>\n");
        html.append("                </div>\n");
        
        // 分页控件
        html.append("                <!-- 分页控件 -->\n");
        html.append("                <div class=\"pagination-container\" id=\"paginationContainer\" style=\"display: none;\">\n");
        html.append("                    <div class=\"pagination-info\">\n");
        html.append("                        <span id=\"paginationInfo\">显示第 1-20 条，共 0 条记录</span>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"pagination-controls\">\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"firstPageBtn\" onclick=\"goToPage(1)\" disabled>首页</button>\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"prevPageBtn\" onclick=\"goToPage(currentPage - 1)\" disabled>上一页</button>\n");
        html.append("                        <span class=\"page-numbers\" id=\"pageNumbers\"></span>\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"nextPageBtn\" onclick=\"goToPage(currentPage + 1)\" disabled>下一页</button>\n");
        html.append("                        <button class=\"btn btn-secondary small\" id=\"lastPageBtn\" onclick=\"goToPage(totalPages)\" disabled>末页</button>\n");
        html.append("                    </div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        
        return html.toString();
    }
}
