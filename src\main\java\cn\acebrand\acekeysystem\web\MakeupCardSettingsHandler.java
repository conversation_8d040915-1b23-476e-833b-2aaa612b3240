package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import cn.acebrand.acekeysystem.AceKeySystem;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

/**
 * 补签卡设置处理器
 */
public class MakeupCardSettingsHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;

    public MakeupCardSettingsHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();

        try {
            // 验证管理员权限
            if (!isAuthorized(exchange)) {
                sendResponse(exchange, 401, "application/json",
                        "{\"success\": false, \"message\": \"未授权访问\"}");
                return;
            }

            if ("GET".equals(method)) {
                handleGetSettings(exchange);
            } else if ("POST".equals(method)) {
                handleSaveSettings(exchange);
            } else {
                sendResponse(exchange, 405, "application/json",
                        "{\"success\": false, \"message\": \"方法不允许\"}");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("处理补签卡设置请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "application/json",
                    "{\"success\": false, \"message\": \"服务器内部错误\"}");
        }
    }

    /**
     * 验证管理员权限
     */
    private boolean isAuthorized(HttpExchange exchange) {
        // 检查会话或API密钥
        String sessionId = getSessionFromCookie(exchange);
        if (sessionId != null && AdminLoginHandler.isValidAdminSession(sessionId)) {
            return true;
        }

        // 检查API密钥（向后兼容）
        String query = exchange.getRequestURI().getQuery();
        if (query != null && query.contains("key=" + webServer.getAdminKey())) {
            return true;
        }

        return false;
    }

    /**
     * 从Cookie中获取会话ID
     */
    private String getSessionFromCookie(HttpExchange exchange) {
        String cookieHeader = exchange.getRequestHeaders().getFirst("Cookie");
        if (cookieHeader != null) {
            String[] cookies = cookieHeader.split(";");
            for (String cookie : cookies) {
                String[] parts = cookie.trim().split("=", 2);
                if (parts.length == 2 && "ADMIN_SESSION".equals(parts[0])) {
                    return parts[1];
                }
            }
        }
        return null;
    }

    /**
     * 处理获取设置请求
     */
    @SuppressWarnings("unchecked")
    private void handleGetSettings(HttpExchange exchange) throws IOException {
        JSONObject response = new JSONObject();
        JSONObject settings = new JSONObject();

        // 从配置文件读取设置，如果不存在则使用默认值
        JSONObject goldSettings = new JSONObject();
        goldSettings.put("price", plugin.getConfig().getInt("makeup_cards.gold.price", 100));
        goldSettings.put("currency_name", plugin.getConfig().getString("makeup_cards.gold.currency_name", "金币"));
        goldSettings.put("description", plugin.getConfig().getString("makeup_cards.gold.description", "使用游戏内金币购买"));
        goldSettings.put("balance_variable",
                plugin.getConfig().getString("makeup_cards.gold.balance_variable", "%vault_eco_balance%"));
        goldSettings.put("deduct_command",
                plugin.getConfig().getString("makeup_cards.gold.deduct_command", "eco take {player} {amount}"));
        goldSettings.put("enabled", plugin.getConfig().getBoolean("makeup_cards.gold.enabled", true));

        JSONObject pointsSettings = new JSONObject();
        pointsSettings.put("price", plugin.getConfig().getInt("makeup_cards.points.price", 50));
        pointsSettings.put("currency_name", plugin.getConfig().getString("makeup_cards.points.currency_name", "点券"));
        pointsSettings.put("description", plugin.getConfig().getString("makeup_cards.points.description", "使用点券购买"));
        pointsSettings.put("balance_variable",
                plugin.getConfig().getString("makeup_cards.points.balance_variable", "%playerpoints_points%"));
        pointsSettings.put("deduct_command",
                plugin.getConfig().getString("makeup_cards.points.deduct_command", "pp take {player} {amount}"));
        pointsSettings.put("enabled", plugin.getConfig().getBoolean("makeup_cards.points.enabled", true));

        JSONObject scoreSettings = new JSONObject();
        scoreSettings.put("price", plugin.getConfig().getInt("makeup_cards.score.price", 200));
        scoreSettings.put("currency_name", plugin.getConfig().getString("makeup_cards.score.currency_name", "积分"));
        scoreSettings.put("description", plugin.getConfig().getString("makeup_cards.score.description", "使用积分商店积分购买"));
        scoreSettings.put("balance_variable", "internal");
        scoreSettings.put("deduct_command", "internal");
        scoreSettings.put("enabled", plugin.getConfig().getBoolean("makeup_cards.score.enabled", true));

        JSONObject customSettings = new JSONObject();
        customSettings.put("price", plugin.getConfig().getInt("makeup_cards.custom.price", 150));
        customSettings.put("currency_name", plugin.getConfig().getString("makeup_cards.custom.currency_name", "自定义货币"));
        customSettings.put("description", plugin.getConfig().getString("makeup_cards.custom.description", "使用自定义货币购买"));
        customSettings.put("balance_variable",
                plugin.getConfig().getString("makeup_cards.custom.balance_variable", "%custom_balance%"));
        customSettings.put("deduct_command", plugin.getConfig().getString("makeup_cards.custom.deduct_command",
                "customcurrency take {player} {amount}"));
        customSettings.put("enabled", plugin.getConfig().getBoolean("makeup_cards.custom.enabled", false));

        settings.put("gold", goldSettings);
        settings.put("points", pointsSettings);
        settings.put("score", scoreSettings);
        settings.put("custom", customSettings);

        response.put("success", true);
        response.put("settings", settings);

        sendResponse(exchange, 200, "application/json", response.toJSONString());
    }

    /**
     * 处理保存设置请求
     */
    @SuppressWarnings("unchecked")
    private void handleSaveSettings(HttpExchange exchange) throws IOException {
        try {
            // 读取请求体
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(exchange.getRequestBody(), StandardCharsets.UTF_8));
            StringBuilder requestBody = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                requestBody.append(line);
            }
            reader.close();

            // 解析JSON
            JSONParser parser = new JSONParser();
            JSONObject requestData = (JSONObject) parser.parse(requestBody.toString());

            // 保存设置到配置文件
            if (requestData.containsKey("gold")) {
                JSONObject goldSettings = (JSONObject) requestData.get("gold");
                if (goldSettings.containsKey("price")) {
                    plugin.getConfig().set("makeup_cards.gold.price",
                            ((Number) goldSettings.get("price")).intValue());
                }
                if (goldSettings.containsKey("currency_name")) {
                    plugin.getConfig().set("makeup_cards.gold.currency_name",
                            (String) goldSettings.get("currency_name"));
                }
                if (goldSettings.containsKey("description")) {
                    plugin.getConfig().set("makeup_cards.gold.description",
                            (String) goldSettings.get("description"));
                }
                if (goldSettings.containsKey("balance_variable")) {
                    plugin.getConfig().set("makeup_cards.gold.balance_variable",
                            (String) goldSettings.get("balance_variable"));
                }
                if (goldSettings.containsKey("deduct_command")) {
                    plugin.getConfig().set("makeup_cards.gold.deduct_command",
                            (String) goldSettings.get("deduct_command"));
                }
                if (goldSettings.containsKey("enabled")) {
                    plugin.getConfig().set("makeup_cards.gold.enabled",
                            (Boolean) goldSettings.get("enabled"));
                }
            }

            if (requestData.containsKey("points")) {
                JSONObject pointsSettings = (JSONObject) requestData.get("points");
                if (pointsSettings.containsKey("price")) {
                    plugin.getConfig().set("makeup_cards.points.price",
                            ((Number) pointsSettings.get("price")).intValue());
                }
                if (pointsSettings.containsKey("currency_name")) {
                    plugin.getConfig().set("makeup_cards.points.currency_name",
                            (String) pointsSettings.get("currency_name"));
                }
                if (pointsSettings.containsKey("description")) {
                    plugin.getConfig().set("makeup_cards.points.description",
                            (String) pointsSettings.get("description"));
                }
                if (pointsSettings.containsKey("balance_variable")) {
                    plugin.getConfig().set("makeup_cards.points.balance_variable",
                            (String) pointsSettings.get("balance_variable"));
                }
                if (pointsSettings.containsKey("deduct_command")) {
                    plugin.getConfig().set("makeup_cards.points.deduct_command",
                            (String) pointsSettings.get("deduct_command"));
                }
                if (pointsSettings.containsKey("enabled")) {
                    plugin.getConfig().set("makeup_cards.points.enabled",
                            (Boolean) pointsSettings.get("enabled"));
                }
            }

            if (requestData.containsKey("score")) {
                JSONObject scoreSettings = (JSONObject) requestData.get("score");
                if (scoreSettings.containsKey("price")) {
                    plugin.getConfig().set("makeup_cards.score.price",
                            ((Number) scoreSettings.get("price")).intValue());
                }
                if (scoreSettings.containsKey("currency_name")) {
                    plugin.getConfig().set("makeup_cards.score.currency_name",
                            (String) scoreSettings.get("currency_name"));
                }
                if (scoreSettings.containsKey("description")) {
                    plugin.getConfig().set("makeup_cards.score.description",
                            (String) scoreSettings.get("description"));
                }
                // 积分系统的balance_variable和deduct_command固定为internal，不需要保存
                if (scoreSettings.containsKey("enabled")) {
                    plugin.getConfig().set("makeup_cards.score.enabled",
                            (Boolean) scoreSettings.get("enabled"));
                }
            }

            if (requestData.containsKey("custom")) {
                JSONObject customSettings = (JSONObject) requestData.get("custom");
                if (customSettings.containsKey("price")) {
                    plugin.getConfig().set("makeup_cards.custom.price",
                            ((Number) customSettings.get("price")).intValue());
                }
                if (customSettings.containsKey("currency_name")) {
                    plugin.getConfig().set("makeup_cards.custom.currency_name",
                            (String) customSettings.get("currency_name"));
                }
                if (customSettings.containsKey("description")) {
                    plugin.getConfig().set("makeup_cards.custom.description",
                            (String) customSettings.get("description"));
                }
                if (customSettings.containsKey("balance_variable")) {
                    plugin.getConfig().set("makeup_cards.custom.balance_variable",
                            (String) customSettings.get("balance_variable"));
                }
                if (customSettings.containsKey("deduct_command")) {
                    plugin.getConfig().set("makeup_cards.custom.deduct_command",
                            (String) customSettings.get("deduct_command"));
                }
                if (customSettings.containsKey("enabled")) {
                    plugin.getConfig().set("makeup_cards.custom.enabled",
                            (Boolean) customSettings.get("enabled"));
                }
            }

            // 保存配置文件
            plugin.saveConfig();

            JSONObject response = new JSONObject();
            response.put("success", true);
            response.put("message", "设置保存成功");

            sendResponse(exchange, 200, "application/json", response.toJSONString());

        } catch (Exception e) {
            plugin.getLogger().severe("保存补签卡设置时出错: " + e.getMessage());
            e.printStackTrace();

            JSONObject response = new JSONObject();
            response.put("success", false);
            response.put("message", "保存设置失败: " + e.getMessage());

            sendResponse(exchange, 500, "application/json", response.toJSONString());
        }
    }

    /**
     * 发送HTTP响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String response)
            throws IOException {
        exchange.getResponseHeaders().set("Content-Type", contentType + "; charset=utf-8");
        exchange.getResponseHeaders().set("Access-Control-Allow-Origin", "*");
        exchange.getResponseHeaders().set("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        exchange.getResponseHeaders().set("Access-Control-Allow-Headers", "Content-Type");

        byte[] responseBytes = response.getBytes(StandardCharsets.UTF_8);
        exchange.sendResponseHeaders(statusCode, responseBytes.length);
        exchange.getResponseBody().write(responseBytes);
        exchange.getResponseBody().close();
    }
}
