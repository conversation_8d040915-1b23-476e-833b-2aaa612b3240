package cn.acebrand.acekeysystem.web;

import cn.acebrand.acekeysystem.AceKeySystem;

/**
 * 仪表板页面生成器
 * 负责生成管理员仪表板页面的HTML内容
 */
public class DashboardPageGenerator {
    private final AceKeySystem plugin;
    private final WebServer webServer;

    public DashboardPageGenerator(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    /**
     * 生成仪表板页面
     */
    public String generateDashboardPage(int totalKeys) {
        StringBuilder html = new StringBuilder();

        html.append("            <div class=\"page-header\">\n");
        html.append("                <h1>📊 仪表板</h1>\n");
        html.append("                <p>系统概览和统计信息</p>\n");
        html.append("                <div class=\"auto-update-status\">\n");
        html.append("                    <span id=\"dashboardAutoUpdateStatus\" class=\"status-indicator active\">🔄 自动检测中</span>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        // 统计卡片
        html.append(generateStatsGrid(totalKeys));
        
        // 隐藏的仪表板容器
        html.append("            <!-- 隐藏的仪表板容器，用于JavaScript检测 -->\n");
        html.append("            <div id=\"dashboardContainer\" style=\"display: none;\"></div>\n");
        html.append("            \n");
        
        // 快速操作
        html.append(generateQuickActions());

        return html.toString();
    }

    /**
     * 生成统计卡片网格
     */
    private String generateStatsGrid(int totalKeys) {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 统计卡片 -->\n");
        html.append("            <div class=\"dashboard-stats-grid\" id=\"dashboardStatsGrid\">\n");
        
        // 总卡密数
        html.append("                <div class=\"dashboard-stat-card\">\n");
        html.append("                    <div class=\"stat-header\">\n");
        html.append("                        <div class=\"stat-icon total\">🔑</div>\n");
        html.append("                        <div class=\"stat-title\">总卡密数</div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-value\" id=\"totalKeysCount\">").append(totalKeys).append("</div>\n");
        html.append("                    <div class=\"stat-subtitle\">系统中所有卡密</div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        // 可用卡密
        html.append("                <div class=\"dashboard-stat-card\">\n");
        html.append("                    <div class=\"stat-header\">\n");
        html.append("                        <div class=\"stat-icon available\">✅</div>\n");
        html.append("                        <div class=\"stat-title\">可用卡密</div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-value\" id=\"availableKeysCount\">0</div>\n");
        html.append("                    <div class=\"stat-subtitle\">未分配未使用</div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        // 已使用
        html.append("                <div class=\"dashboard-stat-card\">\n");
        html.append("                    <div class=\"stat-header\">\n");
        html.append("                        <div class=\"stat-icon used\">❌</div>\n");
        html.append("                        <div class=\"stat-title\">已使用</div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-value\" id=\"usedKeysCount\">0</div>\n");
        html.append("                    <div class=\"stat-subtitle\">已被玩家使用</div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        // 中奖记录
        html.append("                <div class=\"dashboard-stat-card\">\n");
        html.append("                    <div class=\"stat-header\">\n");
        html.append("                        <div class=\"stat-icon winners\">🏆</div>\n");
        html.append("                        <div class=\"stat-title\">中奖记录</div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-value\" id=\"totalWinnersCount\">0</div>\n");
        html.append("                    <div class=\"stat-subtitle\">抽奖获奖次数</div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        // Web服务器状态
        html.append("                <div class=\"dashboard-stat-card\">\n");
        html.append("                    <div class=\"stat-header\">\n");
        html.append("                        <div class=\"stat-icon server\">🌐</div>\n");
        html.append("                        <div class=\"stat-title\">Web服务器</div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-value status\" id=\"webServerStatus\">");
        html.append(webServer.isRunning() ? "运行中" : "已停止");
        html.append("</div>\n");
        html.append("                    <div class=\"stat-subtitle\">服务状态</div>\n");
        html.append("                </div>\n");
        html.append("                \n");
        
        // 插件版本
        html.append("                <div class=\"dashboard-stat-card\">\n");
        html.append("                    <div class=\"stat-header\">\n");
        html.append("                        <div class=\"stat-icon version\">⚙️</div>\n");
        html.append("                        <div class=\"stat-title\">插件版本</div>\n");
        html.append("                    </div>\n");
        html.append("                    <div class=\"stat-value version-text\">");
        html.append(plugin.getDescription().getVersion());
        html.append("</div>\n");
        html.append("                    <div class=\"stat-subtitle\">当前版本号</div>\n");
        html.append("                </div>\n");
        html.append("            </div>\n");
        html.append("            \n");
        
        return html.toString();
    }

    /**
     * 生成快速操作区域
     */
    private String generateQuickActions() {
        StringBuilder html = new StringBuilder();
        
        html.append("            <!-- 快速操作 -->\n");
        html.append("            <div class=\"quick-actions\">\n");
        html.append("                <h2>快速操作</h2>\n");
        html.append("                <div class=\"action-grid\">\n");
        
        // 卡密管理
        html.append("                    <div class=\"action-card\">\n");
        html.append("                        <h3>🔑 卡密管理</h3>\n");
        html.append("                        <p>生成、查看和管理卡密</p>\n");
        html.append("                        <a href=\"/admin?key=").append(webServer.getAdminKey());
        html.append("&page=keys\" class=\"btn\">进入管理</a>\n");
        html.append("                    </div>\n");
        
        // 奖品管理
        html.append("                    <div class=\"action-card\">\n");
        html.append("                        <h3>🎁 奖品管理</h3>\n");
        html.append("                        <p>配置抽奖奖品和概率</p>\n");
        html.append("                        <a href=\"/admin?key=").append(webServer.getAdminKey());
        html.append("&page=rewards\" class=\"btn\">进入管理</a>\n");
        html.append("                    </div>\n");
        
        // 中奖记录
        html.append("                    <div class=\"action-card\">\n");
        html.append("                        <h3>🏆 中奖记录</h3>\n");
        html.append("                        <p>查看用户中奖历史</p>\n");
        html.append("                        <a href=\"/admin?key=").append(webServer.getAdminKey());
        html.append("&page=winners\" class=\"btn\">查看记录</a>\n");
        html.append("                    </div>\n");
        
        // 系统设置
        html.append("                    <div class=\"action-card\">\n");
        html.append("                        <h3>⚙️ 系统设置</h3>\n");
        html.append("                        <p>配置系统参数</p>\n");
        html.append("                        <a href=\"/admin?key=").append(webServer.getAdminKey());
        html.append("&page=settings\" class=\"btn\">进入设置</a>\n");
        html.append("                    </div>\n");
        
        html.append("                </div>\n");
        html.append("            </div>\n");
        
        return html.toString();
    }
}
