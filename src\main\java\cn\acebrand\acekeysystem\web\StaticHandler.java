package cn.acebrand.acekeysystem.web;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import cn.acebrand.acekeysystem.AceKeySystem;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URI;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 静态资源处理器
 * 处理CSS、JS、图片等静态文件
 */
public class StaticHandler implements HttpHandler {

    private final AceKeySystem plugin;
    private final WebServer webServer;

    public StaticHandler(AceKeySystem plugin, WebServer webServer) {
        this.plugin = plugin;
        this.webServer = webServer;
    }

    @Override
    public void handle(HttpExchange exchange) throws IOException {
        String method = exchange.getRequestMethod();
        URI uri = exchange.getRequestURI();
        String path = uri.getPath();

        if (webServer.isDebug()) {
            plugin.getLogger().info("静态资源请求: " + method + " " + path);
        }

        try {
            if ("GET".equals(method)) {
                handleStaticResource(exchange, path);
            } else {
                sendResponse(exchange, 405, "text/plain", "方法不允许");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("处理静态资源请求时出错: " + e.getMessage());
            e.printStackTrace();
            sendResponse(exchange, 500, "text/plain", "服务器内部错误");
        }
    }

    /**
     * 处理静态资源请求
     */
    private void handleStaticResource(HttpExchange exchange, String path) throws IOException {
        // 移除 /static 前缀
        String resourcePath = path.substring("/static".length());

        if (resourcePath.isEmpty() || resourcePath.equals("/")) {
            sendResponse(exchange, 404, "text/plain", "资源未找到");
            return;
        }

        // 根据文件扩展名确定内容类型
        String contentType = getContentType(resourcePath);

        // 首先尝试从本地文件系统加载（上传的文件）
        File localFile = new File(plugin.getDataFolder(), "web" + resourcePath);
        if (localFile.exists() && localFile.isFile()) {
            try {
                byte[] content = Files.readAllBytes(localFile.toPath());
                sendBinaryResponse(exchange, 200, contentType, content);
                return;
            } catch (Exception e) {
                plugin.getLogger().warning("读取本地文件失败: " + localFile.getPath() + " - " + e.getMessage());
            }
        }

        // 然后尝试从插件资源中加载文件
        try (InputStream inputStream = plugin.getResource("web" + resourcePath)) {
            if (inputStream != null) {
                byte[] content = readAllBytes(inputStream);
                sendBinaryResponse(exchange, 200, contentType, content);
            } else {
                // 如果资源不存在，返回默认内容
                handleDefaultResource(exchange, resourcePath, contentType);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("加载静态资源失败: " + resourcePath + " - " + e.getMessage());
            sendResponse(exchange, 404, "text/plain", "资源未找到");
        }
    }

    /**
     * 读取InputStream的所有字节（兼容Java 8）
     */
    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        byte[] buffer = new byte[8192];
        int bytesRead;
        java.io.ByteArrayOutputStream output = new java.io.ByteArrayOutputStream();
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            output.write(buffer, 0, bytesRead);
        }
        return output.toByteArray();
    }

    /**
     * 处理默认资源（当资源文件不存在时）
     */
    private void handleDefaultResource(HttpExchange exchange, String resourcePath, String contentType)
            throws IOException {
        if (resourcePath.equals("/style.css")) {
            // 返回默认CSS
            String css = getDefaultCSS();
            sendResponse(exchange, 200, contentType, css);
        } else if (resourcePath.equals("/script.js")) {
            // 返回默认JavaScript
            String js = getDefaultJS();
            sendResponse(exchange, 200, contentType, js);
        } else if (resourcePath.equals("/favicon.ico")) {
            // 返回空的favicon
            sendBinaryResponse(exchange, 200, "image/x-icon", new byte[0]);
        } else if (resourcePath.equals("/chest-open.mp3") || resourcePath.equals("/chest-open.wav")
                || resourcePath.equals("/chest-open.ogg")) {
            // 返回默认开箱音效（空音频文件）
            sendBinaryResponse(exchange, 200, getContentType(resourcePath), new byte[0]);
        } else if (resourcePath.equals("/winning-sound.mp3") || resourcePath.equals("/winning-sound.wav")
                || resourcePath.equals("/winning-sound.ogg")) {
            // 返回默认中奖音效（空音频文件）
            sendBinaryResponse(exchange, 200, getContentType(resourcePath), new byte[0]);
        } else if (resourcePath.equals("/chest-open.gif")) {
            // 返回默认GIF动画（1x1像素透明GIF）
            byte[] defaultGif = getDefaultGif();
            sendBinaryResponse(exchange, 200, "image/gif", defaultGif);
        } else if (resourcePath.equals("/background.jpg") || resourcePath.equals("/background.png")
                || resourcePath.equals("/background.jpeg")) {
            // 返回默认背景图片（1x1像素透明PNG）
            byte[] defaultBackground = getDefaultBackground();
            sendBinaryResponse(exchange, 200, getContentType(resourcePath), defaultBackground);
        } else {
            sendResponse(exchange, 404, "text/plain", "资源未找到");
        }
    }

    /**
     * 根据文件扩展名获取内容类型
     */
    private String getContentType(String path) {
        String lowerPath = path.toLowerCase();

        if (lowerPath.endsWith(".css")) {
            return "text/css; charset=utf-8";
        } else if (lowerPath.endsWith(".js")) {
            return "application/javascript; charset=utf-8";
        } else if (lowerPath.endsWith(".html") || lowerPath.endsWith(".htm")) {
            return "text/html; charset=utf-8";
        } else if (lowerPath.endsWith(".png")) {
            return "image/png";
        } else if (lowerPath.endsWith(".jpg") || lowerPath.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerPath.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerPath.endsWith(".webp")) {
            return "image/webp";
        } else if (lowerPath.endsWith(".svg")) {
            return "image/svg+xml";
        } else if (lowerPath.endsWith(".ico")) {
            return "image/x-icon";
        } else if (lowerPath.endsWith(".json")) {
            return "application/json; charset=utf-8";
        } else if (lowerPath.endsWith(".xml")) {
            return "application/xml; charset=utf-8";
        } else if (lowerPath.endsWith(".txt")) {
            return "text/plain; charset=utf-8";
        } else if (lowerPath.endsWith(".mp3")) {
            return "audio/mpeg";
        } else if (lowerPath.endsWith(".wav")) {
            return "audio/wav";
        } else if (lowerPath.endsWith(".ogg")) {
            return "audio/ogg";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * 获取默认CSS样式
     */
    private String getDefaultCSS() {
        return "/* 默认样式 */\n" +
                "* {\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    box-sizing: border-box;\n" +
                "}\n" +
                "\n" +
                "body {\n" +
                "    font-family: 'Microsoft YaHei', Arial, sans-serif;\n" +
                "    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n" +
                "    min-height: 100vh;\n" +
                "    color: #333;\n" +
                "}\n" +
                "\n" +
                ".container {\n" +
                "    max-width: 1200px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 20px;\n" +
                "}\n" +
                "\n" +
                ".card {\n" +
                "    background: var(--bg-card, white);\n" +
                "    border-radius: 15px;\n" +
                "    padding: 30px;\n" +
                "    box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n" +
                "    margin: 20px 0;\n" +
                "    border: 1px solid var(--border-color, #e1e5e9);\n" +
                "}\n" +
                "\n" +
                ".btn {\n" +
                "    display: inline-block;\n" +
                "    padding: 12px 24px;\n" +
                "    background: #667eea;\n" +
                "    color: white;\n" +
                "    text-decoration: none;\n" +
                "    border-radius: 6px;\n" +
                "    border: none;\n" +
                "    cursor: pointer;\n" +
                "    font-size: 14px;\n" +
                "    transition: all 0.3s;\n" +
                "}\n" +
                "\n" +
                ".btn:hover {\n" +
                "    background: #5a6fd8;\n" +
                "    transform: translateY(-2px);\n" +
                "    box-shadow: 0 5px 15px rgba(0,0,0,0.2);\n" +
                "}";
    }

    /**
     * 获取默认JavaScript
     */
    private String getDefaultJS() {
        return "// 默认JavaScript功能\n" +
                "\n" +
                "// 通用工具函数\n" +
                "function showMessage(message, type = 'info') {\n" +
                "    console.log('[' + type.toUpperCase() + '] ' + message);\n" +
                "    \n" +
                "    // 清理之前的消息\n" +
                "    const existingMessages = document.querySelectorAll('.message');\n" +
                "    existingMessages.forEach(msg => {\n" +
                "        if (msg.parentNode) {\n" +
                "            msg.parentNode.removeChild(msg);\n" +
                "        }\n" +
                "    });\n" +
                "    \n" +
                "    // 创建消息提示\n" +
                "    const messageDiv = document.createElement('div');\n" +
                "    messageDiv.className = 'message message-' + type;\n" +
                "    messageDiv.innerHTML = message;\n" +
                "    messageDiv.style.cssText = `\n" +
                "        position: fixed;\n" +
                "        top: 20px;\n" +
                "        right: 20px;\n" +
                "        padding: 15px 20px;\n" +
                "        border-radius: 6px;\n" +
                "        color: white;\n" +
                "        font-weight: bold;\n" +
                "        z-index: 9999;\n" +
                "        max-width: 400px;\n" +
                "        word-wrap: break-word;\n" +
                "        background: ${type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db'};\n" +
                "        box-shadow: 0 4px 12px rgba(0,0,0,0.2);\n" +
                "        animation: slideIn 0.3s ease-out;\n" +
                "    `;\n" +
                "    \n" +
                "    document.body.appendChild(messageDiv);\n" +
                "    \n" +
                "    // 3秒后自动移除\n" +
                "    setTimeout(() => {\n" +
                "        if (messageDiv.parentNode) {\n" +
                "            messageDiv.style.animation = 'slideOut 0.3s ease-in';\n" +
                "            setTimeout(() => {\n" +
                "                if (messageDiv.parentNode) {\n" +
                "                    messageDiv.parentNode.removeChild(messageDiv);\n" +
                "                }\n" +
                "            }, 300);\n" +
                "        }\n" +
                "    }, 3000);\n" +
                "}\n" +
                "\n" +
                "// 添加动画样式\n" +
                "const style = document.createElement('style');\n" +
                "style.textContent = `\n" +
                "    @keyframes slideIn {\n" +
                "        from { transform: translateX(100%); opacity: 0; }\n" +
                "        to { transform: translateX(0); opacity: 1; }\n" +
                "    }\n" +
                "    @keyframes slideOut {\n" +
                "        from { transform: translateX(0); opacity: 1; }\n" +
                "        to { transform: translateX(100%); opacity: 0; }\n" +
                "    }\n" +
                "`;\n" +
                "document.head.appendChild(style);\n" +
                "\n" +
                "// API调用工具函数\n" +
                "function callAPI(action, data = {}) {\n" +
                "    return fetch('/api', {\n" +
                "        method: 'POST',\n" +
                "        headers: {\n" +
                "            'Content-Type': 'application/json'\n" +
                "        },\n" +
                "        body: JSON.stringify({\n" +
                "            action: action,\n" +
                "            api_key: 'MCTV_KEY_2024_SECURE',\n" +
                "            ...data\n" +
                "        })\n" +
                "    })\n" +
                "    .then(response => response.json())\n" +
                "    .catch(error => {\n" +
                "        console.error('API调用失败:', error);\n" +
                "        throw error;\n" +
                "    });\n" +
                "}\n" +
                "\n" +
                "// 页面加载完成后的初始化\n" +
                "document.addEventListener('DOMContentLoaded', function() {\n" +
                "    console.log('页面加载完成');\n" +
                "});";
    }

    /**
     * 发送文本响应
     */
    private void sendResponse(HttpExchange exchange, int statusCode, String contentType, String content)
            throws IOException {
        byte[] bytes = content.getBytes("UTF-8");
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Cache-Control", "public, max-age=3600"); // 缓存1小时
        exchange.sendResponseHeaders(statusCode, bytes.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(bytes);
        }
    }

    /**
     * 发送二进制响应
     */
    private void sendBinaryResponse(HttpExchange exchange, int statusCode, String contentType, byte[] content)
            throws IOException {
        exchange.getResponseHeaders().set("Content-Type", contentType);
        exchange.getResponseHeaders().set("Cache-Control", "public, max-age=3600"); // 缓存1小时
        exchange.sendResponseHeaders(statusCode, content.length);
        try (OutputStream os = exchange.getResponseBody()) {
            os.write(content);
        }
    }

    /**
     * 获取默认GIF动画（1x1像素透明GIF）
     */
    private byte[] getDefaultGif() {
        // 1x1像素透明GIF的字节数据
        return new byte[] {
                0x47, 0x49, 0x46, 0x38, 0x39, 0x61, // GIF89a
                0x01, 0x00, 0x01, 0x00, // 1x1像素
                (byte) 0x80, 0x00, 0x00, // 全局颜色表标志
                0x00, 0x00, 0x00, // 背景色
                0x00, 0x00, 0x00, // 颜色表
                0x21, (byte) 0xF9, 0x04, // 图形控制扩展
                0x01, 0x00, 0x00, 0x00, 0x00, // 透明色索引
                0x2C, 0x00, 0x00, 0x00, 0x00, // 图像描述符
                0x01, 0x00, 0x01, 0x00, 0x00, // 1x1像素
                0x02, 0x02, 0x04, 0x01, 0x00, 0x3B // 图像数据和结束符
        };
    }

    /**
     * 获取默认的1x1像素透明PNG背景图片
     */
    private byte[] getDefaultBackground() {
        // 1x1像素透明PNG的字节数据
        return new byte[] {
                (byte) 0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
                0x00, 0x00, 0x00, 0x0D, 0x49, 0x48, 0x44, 0x52,
                0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
                0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, (byte) 0xC4,
                (byte) 0x89, 0x00, 0x00, 0x00, 0x0B, 0x49, 0x44, 0x41,
                0x54, 0x78, (byte) 0xDA, 0x63, 0x60, 0x00, 0x02,
                0x00, 0x00, 0x05, 0x00, 0x01, (byte) 0xE2, 0x26,
                0x05, (byte) 0x9B, 0x00, 0x00, 0x00, 0x00, 0x49,
                0x45, 0x4E, 0x44, (byte) 0xAE, 0x42, 0x60, (byte) 0x82
        };
    }
}
